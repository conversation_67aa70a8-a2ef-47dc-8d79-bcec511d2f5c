import { get } from 'es-toolkit/compat';
import dayjs from 'dayjs';
import { translateStringFormat } from '../helper';
import { trimAbbreviation } from '../utils';

/**
 * @typedef {{
 *   selector: (data: Object) => any,
 *   dependencies?: string[]
 * }} TickerFieldConfig
 */


/**@type {Record<string, TickerFieldConfig>} */
export const TICKER_FIELDS_CONFIG = {
  marketAbbreviation: {
    selector: data => get(data, 'market.abbreviation')
  },
  marketName: {
    selector: data => get(data, 'market.translation.value')
  },
  marketStatus: {
    selector: data => get(data, 'market.status.isOpened') ? 'Open' : 'Close'
  },
  remainingTime: {
    selector: data => get(data, 'market.status.remainingTime',0)
  },
  lastUpdatedDate: {
    selector: data => get(data, 'currentPrice.date')
  },
  bid: {
    selector: data => get(data, 'currentPrice.bid')
  },
  bidSize: {
    selector: data => get(data, 'currentPrice.bidSize')
  },
  ask: {
    selector: data => get(data, 'currentPrice.ask')
  },
  askSize: {
    selector: data => get(data, 'currentPrice.askSize')
  },
  open: {
    selector: data => get(data, 'currentPrice.open')
  },
  last: {
    selector: data => get(data, 'currentPrice.tickerData.last')
  },
  change: {
    selector: data => get(data, 'currentPrice.tickerData.change')
  },
  changePercentage: {
    selector: data => get(data, 'currentPrice.tickerData.changePercentage')
  },
  high: {
    selector: data => get(data, 'currentPrice.high')
  },
  low: {
    selector: data => get(data, 'currentPrice.low')
  },
  volume: {
    selector: data => get(data, 'currentPrice.volume')
  },
  prevClose: {
    selector: data => get(data, 'currentPrice.tickerData.prevClose')
  },
  averagePrice: {
    selector: data => get(data, 'currentPrice.vwap')
  },
  high52W: {
    selector: data => get(data, 'fifty_two_weeks.highest')
  },
  low52W: {
    selector: data => get(data, 'fifty_two_weeks.lowest')
  },
  percent52W: {

    selector: data => get(data, 'fifty_two_weeks.changePercentage')
  },
  highYTD: {
    selector: data => get(data, 'ytd.highest')
  },
  lowYTD: {
    selector: data => get(data, 'ytd.lowest')
  },
  allTimeHigh: {
    selector: data => get(data, 'all.highest')
  },
  allTimeLow: {
    selector: data => get(data, 'all.lowest')
  },
  listName: {
    selector: data => get(data, 'list.listName')
  },
  industry: {
    selector: data => get(data, 'subSector.marCat')
  },
  noShares: {
    selector: data => get(data, 'noShares')
  },
  lotSize: {
    selector: data => get(data, 'lotSize')
  },
  eps: {
    selector: data => get(data, 'eps')
  },
  totalTrades: {
    selector: data => get(data, 'lastDayIntraday.totalCount')
  },
  highYtdDate: {
    selector: data => get(data, 'ytd.highestDate')
  },
  lowYtdDate: {
    selector: data => get(data, 'ytd.lowestDate')
  },
  percentYtd: {
    selector: data => get(data, 'ytd.changePercentage')
  },
  allTimeHighDate: {
    selector: data => get(data, 'all.highestDate')
  },
  allTimeLowDate: {
    selector: data => get(data, 'all.lowestDate')
  },
  high52w: {
    dependencies: ['high52W'],
    selector: data => data
  },
  highest52wDate: {
    selector: data => get(data, 'fifty_two_weeks.highestDate')
  },
  lowest52wDate: {
    selector: data => get(data, 'fifty_two_weeks.lowestDate')
  },
  currencyCodeStr: {
    dependencies: ['currencyCode'],
    selector: (currencyCode) => translateStringFormat('currency', [currencyCode])
  },
  officialClose: {
    selector: data => get(data, 'currentPrice.officialClose')
  },

  officialCloseDate: {
    selector: data => get(data, 'currentPrice.officialCloseDate')
  },
  businessDaysStoT: {
    selector: data => get(data, 'market.businessDaysStoT')
  },
  iSIN: {
    selector: data => get(data, 'isin')
  },
  symbol: {
    selector: data => get(data, 'symbol')
  },
  currencyCode: {
    selector: data => get(data, 'currency.code')
  },
  currencyName: {
    selector: data => get(data, 'currency.name')
  },
  shareName: {
    selector: data => trimAbbreviation(data.shareName)
  },
  ticker: {
    dependencies: ['symbol'],
    selector: data => data
  },
  instrumentId: {
    selector: data => data.id
  },
  normalDailyOpen: {
    selector: data => get(data, 'market.openTimeLocal', '00:00')
  },
  normalDailyClose: {
    selector: data => get(data, 'market.closeTimeLocal', '23:59')
  },
  marketTimeZone: {
    selector: data => get(data, 'market.timezoneName')
  },
  countdownToTheOpeningBell: {
    dependencies: ['marketStatus', 'remainingTime'],
    selector: (marketStatus, remainingTime) => {
      return marketStatus === 'Open' ? null : remainingTime;
    }
  },

  countdownToTheOpeningBellDate: {
    dependencies: ['marketStatus', 'remainingTime'],
    selector: (marketStatus, remainingTime) => {
      const countdownToTheOpeningBell = marketStatus === 'Open' ? null : remainingTime;
      return countdownToTheOpeningBell ?
        dayjs().startOf('minute').add(countdownToTheOpeningBell, 'minute').toISOString() :
        undefined;
    }

  },
  countdownToTheClosingBell: {
    dependencies: ['marketStatus', 'remainingTime'],
    selector: (marketStatus, remainingTime) => {
      return marketStatus === 'Open' ? remainingTime : null;
    }
  },

  countdownToTheClosingBellDate: {
    dependencies: ['marketStatus', 'remainingTime'],
    selector: (marketStatus, remainingTime) => {
      const countdownToTheClosingBell = marketStatus === 'Open' ? remainingTime : null;
      return countdownToTheClosingBell ?
        dayjs().startOf('minute').add(countdownToTheClosingBell, 'minute').toISOString() :
        undefined;
    }

  },
  startingDate: {
    selector: data => get(data, 'historicals.nodes[0].dateTime')
  },
  volumeHistories: {
    selector: data => {
    const volume = get(data, 'volumeHistories');
      return volume;
    }
  },
  volumeChange: {
    dependencies: ['volumeHistories', 'volume'],
    selector: (volumeHistories, volume) => {
      const history = volumeHistories.nodes.slice(1);
      const historyLength = history.length;
      if (historyLength === 0 || !historyLength) {
        return null;
      }
      const averageVolume = history.reduce((acc, item) => acc + item.volume, 0) / historyLength;
      return volume / averageVolume;
    }
  },
  fetchedTime: {
    selector: () => new Date().toISOString()
  },
  openTimeLocal: {
    selector: data => get(data, 'market.openTimeLocal')
  },
  closeTimeLocal: {
    selector: data => get(data, 'market.closeTimeLocal')
  },
  timezoneName: {
    selector: data => get(data, 'market.timezoneName')
  },
  marketBusinessDaysStoT: {
    selector: data => get(data, 'market.businessDaysStoT')
  },
  // Share detail
  marketCap: {
    dependencies: ['noShares', 'last'],
    selector: (noShares, last) => last * noShares
  },
  pE: {
    dependencies: ['eps', 'last'],
    selector: (eps, last) => eps === 0 ? 0 : last / eps
  },
  vWAP: {
    selector: item => get(item, 'currentPrice.vwap')
  },
  todayTurnover: {
    selector: item => get(item, 'currentPrice.todayTurnover')
  },
  yTD: {
    selector: item => get(item, 'ytd.changePercentage')
  },
  currency: {
    selector: item => get(item, 'currency.name')
  },
  timezoneIANA: {
    selector: item => get(item, 'market.timezone.nameIANA')
  }
};


export const buildOptimizedTickerQuery = (variables, operationName = 'Ticker') => {
  const fields = [];

  // Base instrument fields
  const addField = (flag, field) => flag && fields.push(field);
  addField(variables.shareName, 'shareName');
  addField(variables.instrumentId, 'id');
  addField(variables.symbol, 'symbol');
  addField(variables.iSIN, 'isin');


  // Market block
  const marketFields = [
    variables.marketName && 'translation { cultureName value }',
    variables.openTimeLocal && 'openTimeLocal',
    variables.closeTimeLocal && 'closeTimeLocal',
    variables.timezoneName && 'timezoneName',
    variables.marketStatus && 'status { isOpened remainingTime }',
    variables.normalDailyClose && 'closeTimeLocal',
    variables.normalDailyOpen && 'openTimeLocal',
    variables.timezoneIANA && 'timezone { nameIANA }'
  ].filter(Boolean);

  if (variables.marketAbbreviation) marketFields.push('abbreviation');
  if (variables.businessDaysStoT) marketFields.push('businessDaysStoT');
  addField(marketFields.length > 0, `market { ${marketFields.join('\n')} }`);


  // Currency block
  const currencyFields = [];
  if (variables.currencyCode) currencyFields.push('code');
  if (variables.currencyName || variables.currency) currencyFields.push('name');
  if (currencyFields.length) fields.push(`currency { ${currencyFields.join('\n')} }`);

  // Current Price block
  const currentPriceFields = [
    variables.open && 'open',
    variables.lastUpdatedDate && 'date',
    variables.bid && 'bid',
    variables.bidSize && 'bidSize',
    variables.ask && 'ask',
    variables.askSize && 'askSize',
    variables.high && 'high',
    variables.low && 'low',
    variables.volume && 'volume',
    variables.vWAP && 'vwap',
    variables.todayTurnover && 'todayTurnover',
    variables.officialClose && 'officialClose',
    variables.officialCloseDate && 'officialCloseDate'
  ].filter(Boolean);
  const tickerDataFields = ['last'];
  if(variables.change) tickerDataFields.push('change');
  if(variables.changePercentage) tickerDataFields.push('changePercentage');
  if(variables.prevClose) tickerDataFields.push('prevClose');
  currentPriceFields.push(`tickerData { ${tickerDataFields.join('\n')} }`);
  if (currentPriceFields.length) fields.push(`currentPrice { ${currentPriceFields.join('\n')} }`);

  // Performance blocks
  const buildPerfBlock = (period, periodEnum, fieldsConfig) => {
    const perfFields = Object.entries(fieldsConfig)
      .filter(([varName]) => variables[varName])
      .map(([_, field]) => field);
    return perfFields.length ? `${period}: performance(period: ${periodEnum}) { ${perfFields.join('\n')} }` : '';
  };

  const perfBlocks = [
    buildPerfBlock('fifty_two_weeks', 'FIFTY_TWO_WEEKS', {
      high52W: 'highest',
      low52W: 'lowest',
      percent52W: 'changePercentage',
      highest52wDate: 'highestDate',
      lowest52wDate: 'lowestDate'
    }),
    buildPerfBlock('ytd', 'YTD', {
      highYTD: 'highest',
      lowYTD: 'lowest',
      yTD: 'changePercentage',
      highYtdDate: 'highestDate',
      lowYtdDate: 'lowestDate'
    }),
    buildPerfBlock('all', 'ALL_TIME', {
      allTimeHigh: 'highest',
      allTimeLow: 'lowest',
      allTimeHighDate: 'highestDate',
      allTimeLowDate: 'lowestDate'
    })
  ].filter(Boolean);
  fields.push(...perfBlocks);

  // Additional fields
  addField(variables.listName, 'list { listName }');
  addField(variables.industry, 'subSector { marCat }');
  addField(variables.noShares, 'noShares');
  addField(variables.lotSize, 'lotSize');
  addField(variables.eps, 'eps');
  addField(variables.totalTrades, 'lastDayIntraday(first: 99999) { totalCount }');
  addField(variables.startingDate, 'historicals(order: { dateTime: ASC }, first: 1) { nodes { dateTime } }');

  addField(variables.volumeHistories, 'volumeHistories: historicals(first: 11) { nodes { volume } }');


  return (`query ${operationName}(
    $ids: [Int!]!
    $adjClose: Boolean
    $toCurrency: String
  ) {
    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
      ${fields.join('\n')}
    }
  }`);
};

/**
 * Recursively resolves all dependencies for given fields
 * @param {string[]} fields - Initial fields to check
 * @param {Record<string, TickerFieldConfig>} config - Field configuration object
 * @returns {string[]} Array of all required fields including dependencies
 */
export const resolveFieldDependencies = (fields, config = TICKER_FIELDS_CONFIG) => {
  const resolvedFields = new Set(fields);

  const addDependencies = (field) => {
    const fieldConfig = config[field];
    if(!fieldConfig) throw new Error(`Field ${field} not found in config`);
    if (!fieldConfig?.dependencies) return;

    for (const dep of fieldConfig.dependencies) {
      if (!resolvedFields.has(dep)) {
        resolvedFields.add(dep);
        // Recursively check dependencies of dependencies
        addDependencies(dep);
      }
    }
  };

  // Process each field
  for (const field of fields) {
    resolvedFields.add(field);
    addDependencies(field);
  }

  return Object.fromEntries(Array.from(resolvedFields).map(item => [item, true]));
};

/**
 *
 * @param {string} fieldName
 * @param {Set<string>} [stack]
 */
function fieldResolver(fieldName, stack) {
  if(stack) {
    if(stack.has(fieldName)) throw new Error(`Field: ${fieldName} circle dependency detected`);
  } else {
    stack = new Set([fieldName]);
  }

  if(!(fieldName in TICKER_FIELDS_CONFIG)) throw new Error(`Field: ${fieldName} do not exist!`);

  const field = TICKER_FIELDS_CONFIG[fieldName];

  if(field.dependencies && field.dependencies.length > 0) {
    return field.dependencies.map(item => fieldResolver(item, stack)).flat();
  }

  return [field];
}

export function normalizeGraphqlData(fieldNames, source) {
  const result = {};
  for(const fieldName of fieldNames) {

    const field = TICKER_FIELDS_CONFIG[fieldName];

    if(field.dependencies) {
      result[fieldName] = field.selector.apply(undefined, fieldResolver(fieldName).map(item => item.selector(source)));
    } else {
      result[fieldName] = field.selector(source);
    }
  }

  return result;
}
