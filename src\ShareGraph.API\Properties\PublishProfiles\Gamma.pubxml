<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>AnyCPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://gamma.euroland.com/tools/sharegraph3-api/graphql?companycode=dk-cbg</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>False</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>https://ee-v-gamma1.euroland.com:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>Default Web Site/tools/sharegraph3-api</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>False</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <_SavePWD>True</_SavePWD>
    <UserName>EE-V-GAMMA1\WDeployAdmin2</UserName>
    <AllowUntrustedCertificate>True</AllowUntrustedCertificate>
    <DeployEnv>Gamma</DeployEnv>
    <EnvironmentName>Gamma</EnvironmentName>
  </PropertyGroup>
  <ItemGroup>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>Config\\Company</AbsolutePath>
    </MsDeploySkipRules>
  </ItemGroup>
</Project>
