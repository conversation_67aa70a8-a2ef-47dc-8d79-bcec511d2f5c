import { useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { fetch52Weeks } from '../actions/52WeeksAction';
import { AppContext } from '../AppContext';
import SharePrice52Weeks from '../entities/SharePrice52Weeks';
import { dynamicSort } from '../helper';
import { useSharePriceOrder } from './useSharePriceOrder';
import { useChangeQuoteCurrency } from './useSelectedCurrency';
import { useFormatNumberByInstrument } from './useFormatNumberByInstrument';

const useSharePrice52Weeks = () => {
  const { loading, instruments, fetchError } = useSelector(state => state.week52) || {};
  const dispatch = useDispatch();
  const settings = useContext(AppContext);
  const { sharePriceOrders } = useSharePriceOrder();
  const {
    formatNumberByInstrument
  }= useFormatNumberByInstrument();

  const configColumns = settings.performance.enable52WTableColumns || [];

  useEffect(() => {
    dispatch(fetch52Weeks());
  }, []);

  useChangeQuoteCurrency(() => {
    dispatch(fetch52Weeks());
  });

  return [
    {
      instruments,
      sharePrice52Weeks: instruments
        .map(
          instrument =>
            new SharePrice52Weeks({
              ...instrument,
              order: sharePriceOrders[instrument.instrumentId]
            }, formatNumberByInstrument)
        )
        .sort(dynamicSort('order')),
      getState: {
        loading,
        error: fetchError
      },
      configColumns,
      sharePriceOrders
    }
  ];
};

export default useSharePrice52Weeks;
