import { useLayoutEffect, useState } from 'react';

import { dynamicSort } from '../helper';
import useResize from './useResize';

const useLayoutCarousel = (carouselRef, { data, settings }) => {
  const [dataOrderedCarousel, setDataOrderedCarousel] = useState([]);
  const { width } = useResize();

  useLayoutEffect(() => {
    if (!carouselRef.current) return;
    const carouselOffsetWidth = carouselRef.current.offsetWidth;
    const mediaRows = parseInt(getComputedStyle(carouselRef.current).getPropertyValue('--carousel-rows'));
    let rows = mediaRows || 1;
    
    if (!mediaRows && carouselOffsetWidth < 769) {
      rows =  2;
    }
    if (!mediaRows && carouselOffsetWidth  < 428) {
      rows = 4;
    }
    const arrMapping = (data || [])
      .map(x => {
        return {
          ...x,
          order: settings.find(y => y.id === x.instrumentId).order
        };
      })
      .sort(dynamicSort('order'))
      .reduce((result, curr, index) => {
        const i = Math.floor(index / rows);
        if (!Array.isArray(result[i])) {
          result[i] = [curr];
          return result;
        }
        result[i].push(curr);
        return result;
      }, []);
    setDataOrderedCarousel(arrMapping);
  }, [data, width]);

  return [{ dataOrderedCarousel }];
};

export default useLayoutCarousel;
