import { useMemo, useCallback } from 'react';
import { createPatternSvg, debounceLast, generateChartPatternFromNumber } from '../../utils';
import {
  marketStatusByValue,
  classNames,
  translateStringFormat,
  convertChangePercentDecimal
} from '../../helper';
import { usePrevious } from '../../customHooks';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import {useDelay} from '../../customHooks/useDelayUpdate';
import TickerName from '../TickerName';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import { useSelector } from 'react-redux';



const PeerIndicesItem = ({ data: _data, isSelected, settingColor = '#E3E3E3', onItemClick = () => {} }) => {
  const { formatNumberByInstrument, formatNumberChangeByInstrument } = useFormatNumberByInstrument();
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER);
  const prevData = usePrevious(data);
  const isBlindMode = useSelector(state => state.blindMode.isBlindMode);

  const { change, marketStatus } = data || {};

  const lastValue = useMemo(() => {
    if (!prevData) return;
    return data.last - prevData.last;
  }, [data]);

  const handleClickItem = () => {
    onItemClick(data.instrumentId);
  };

  const handlePressKey = useCallback(debounceLast(function (event) {
    // Keypress other than Enter and Space
    if (event.nativeEvent instanceof KeyboardEvent && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }
    event.preventDefault();
    handleClickItem();
  }, 200), [data.instrumentId]);

  return (
    <div
      onKeyDown={handlePressKey}
      onClick={handleClickItem}
      tabIndex="0"
      role="button"
      aria-pressed={isSelected}
      className={classNames(
        {
          'indicator-up': change > 0,
          'indicator-down': change < 0,
          'indicator-neutral': change === 0,
          selected: isSelected,
          'animate--increase': lastValue > 0,
          'animate--decrease': lastValue < 0,
          'animate--neutral': lastValue === 0
        },
        'peer-indices-item',
        marketStatusByValue(marketStatus)
      )}
      style={{
        '--instrument-setting-color': settingColor
      }}
    >
      <div className="peer-indices-item__heading">
        <div className="peer-indices-item__ticker-code" style={{backgroundColor: settingColor}}>{data.ticker}</div>
        <div className="peer-indices-item__name-icon">
          <i className="fs fs-checked-checkbox peer-indices-item__custom-checkbox" style={{
            color: settingColor
          }} />
          <span className="fs-checkbox peer-indices-item__unchecked-checkbox">
            <span className="path1" />
            <span className="path2" />
          </span>
          <span className="peer-indices-item__name-icon-box" />
          <span className="peer-indices-item__name"><TickerName instrumentId={data.instrumentId} marketAbbreviation={data.marketAbbreviation} shareName={data.shareName} /></span>
        </div>
        {isBlindMode && (
          <div>
            {createPatternSvg({
              pattern: generateChartPatternFromNumber(data.instrumentId),
              color: settingColor
            })}
          </div>
        )}
      </div>
      <div className="peer-indices-item__bottom">
        <p className="peer-indices-item__last-price-wrapper">
          <span className="peer-indices-item__last-price">
            {formatNumberByInstrument(data.last, data.instrumentId)}
          </span>
          <span className="peer-indices-item__currency">
            {data.currencyCode
              ? translateStringFormat('currency', [data.currencyCode])
              : ''}
          </span>
        </p>
        <p className="peer-indices-item__change">
          <i className="fs fs-triangle-up" />
          <i className="fs fs-triangle-down" />
          <span className="peer-indices-item__change-value ticker__change-value">
            {formatNumberChangeByInstrument(change, data.instrumentId)} ({convertChangePercentDecimal(data.changePercentage)}
            %)
          </span>
        </p>

      </div>
    </div>
  );
};

export default PeerIndicesItem;
