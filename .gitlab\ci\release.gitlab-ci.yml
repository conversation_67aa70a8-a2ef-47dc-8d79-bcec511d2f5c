"Staging Release":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: release
  # prevent a job from downloading artifacts of `build` job
  dependencies: []
  script:
    - 'call "./build/release.bat"'
  only:
    refs:
      - next

"Production Release":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: release
  # prevent a job from downloading artifacts of `build` job
  dependencies: []
  script:
    - 'call "./build/release.bat"'
  only:
    refs:
      - master
