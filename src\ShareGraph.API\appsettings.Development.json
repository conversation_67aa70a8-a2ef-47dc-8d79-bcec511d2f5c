{"urls": "https://*:5001", "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "GeneralSettingsPath": "Config", "ToolSettingsPath": "Config", "OpifexToolCompanySettingsPath": "..\\..\\..\\opifex-v2\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\ShareGraph3\\Config\\Company", "OpifexGeneralSettingDirectory": "..\\..\\..\\opifex-v2\\opifex-api\\src\\Opifex.API\\src\\wwwroot\\Config\\Company", "OpifexPreviewEnabled": true, "InternalSDataApiUrl": "https://localhost:5005/graphql", "ConnectionStrings": {"TranslationDbContext": "Server=************;Database=shark;User=uShark;PWD=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;"}}