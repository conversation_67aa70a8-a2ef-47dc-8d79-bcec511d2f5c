import {freeze} from 'immer';
import appConfig from '../services/app-config';

function cacheFactory () {
  const _memo = new WeakMap();
  return (
    /**
     * 
     * @param {() => any} callback 
     * @param {any} dependency 
     */
    function (callback, dependency) {
      if(!dependency) return callback();
      if(!_memo.has(dependency)) {
        _memo.set(dependency, callback()); 
      }
      return _memo.get(dependency);
    }
  );
}

const cache = cacheFactory();
/**
 * @typedef {import('../../../types/configuration-app').TConfigPeer} TConfigPeer
 * @typedef {import('../../../types/configuration-app').TConfigIndex} TConfigIndex
 * @typedef {import('../../../types/configuration-app').TConfigInstrument} TConfigInstrument
 *
 */

/**
 * @returns {{
 *  [id: TConfigPeer['id']]: TConfigPeer
 * }}
 */
export const getPeersSetting = () => {
  const configSettings = appConfig.get();
  return cache(() => freeze(configSettings.peers.peers.reduce((s, ins) => {
    s[ins.id] = ins;
    return s;
  }, {}), true), configSettings.peers.peers);
};

/**
 *
 * @returns {{
 *   [id: TConfigIndex['id']]: TConfigIndex
 * }}
 */
export const getIndicesSetting = () => {
  const configSettings = appConfig.get();

  return cache(() => freeze(configSettings.indices.indices.reduce((s, ins) => {
    s[ins.id] = ins;
    return s;
  }, {}), true), configSettings.indices.indices);
};

/**
 *
 * @returns {{
 *   [id: TConfigInstrument['id']]: TConfigInstrument
 * }}
 */
export const getTickersSetting = () => {
  const configSettings = appConfig.get();
  return cache(() => freeze(configSettings.instruments.reduce((s, ins) => {
    s[ins.id] = ins;
    return s;
  }, {}), true), configSettings.instruments);
};


/**
 *
 * @returns {{
*   [id: TConfigInstrument['id']]: TConfigInstrument
* }}
*/
export const getTickerSetting = () => {
 const configSettings = appConfig.get();
 return cache(() => freeze(configSettings.ticker, true), configSettings.ticker);
};

/**
 *
 * @returns {{
 *     [id: number | string]: TConfigPeer | TConfigIndex | TConfigInstrument;
 * }}
 */
export const getAllInstrumentsSetting = () => {
  return {
    ...getPeersSetting(),
    ...getIndicesSetting(),
    ...getTickersSetting()
  };
};

/**
 * @typedef {import('../reducers/currencyReducers').CurrencyOption} CurrencyOption
 * @returns {{
*   [code: CurrencyOption['code']]: CurrencyOption
* }}
*/
export const getCurrencyOptionSetting = () => {
 const configSettings = appConfig.get();
 const currencies = configSettings.currencies?.currencies || [];
 return cache(() => freeze(currencies.reduce((s, currency) => {
   s[currency.code] = currency;
   return s;
 }, {}), true), configSettings.currencies);
};