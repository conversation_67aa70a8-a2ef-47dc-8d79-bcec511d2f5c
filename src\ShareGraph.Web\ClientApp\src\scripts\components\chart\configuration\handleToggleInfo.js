import {CIQ} from 'chartiq/js/standard';

export default function handleToggleInfo(value) {
  let { context } = this;
  const multiChartContextEl =
    context.topNode.parentElement.closest('cq-context');
  if (multiChartContextEl && !context.topNode.isMultiChartHost) {
    context = multiChartContextEl.CIQ.UI.context;
  }

  const handleToggle = (show) => {
    this.value = show;
    const charts = (this.getCharts && this.getCharts()) || [context.stx];
    charts.forEach(({ container }) => {
      [
        ...container.querySelectorAll('cq-hu-static'),
        ...container.querySelectorAll('cq-hu-dynamic'),
        ...container.querySelectorAll('stx-hu-tooltip')
      ].forEach((element) => {
        if (show) {
          element.removeAttribute('force-hidden');
        } else {
          element.setAttribute('force-hidden', '');
        }
      });
    });

    this.classList.toggle('active', show);
  };

  if (!this.getCharts && context.topNode.getCharts)
    this.getCharts = context.topNode.getCharts;
  const { layout } = context.stx;

  
  if (!layout.headsUp || typeof layout.headsUp !== 'object') {
    if (['dynamic', 'floating', 'static'].includes(layout.headsUp))
      layout.headsUp = { [layout.headsUp]: true };
    else layout.headsUp = {};
  }
  if (!this.listener) {
    const {
      stx: { layout: listenerLayout },
      stx
    } = multiChartContextEl ? multiChartContextEl.CIQ.UI.context : context;
    this.listener = (obj) => {
      let show =
        obj.value &&
        (['dynamic', 'floating', 'static'].includes(obj.value) ||
          obj.value.dynamic ||
          obj.value.floating ||
          obj.value.static);

      if(obj.value?.toggleInfo !== undefined) {
        show = obj.value.toggleInfo;
      }

      if (
        obj.value &&
        (obj.value === 'dynamic' ||
          obj.value.dynamic ||
          obj.value === 'floating' ||
          obj.value.floating)
      ) {
        this.context.stx.centerCrosshairs();
        this.context.stx.doDisplayCrosshairs();
      }
      // set keystroke hub context to active chart to allow arrow keys to move cursor or pan the chart
      if (stx.container.closest('cq-context-wrapper.active')) {
        document.body.keystrokeHub.context = this.context;
      }

      handleToggle(show);
    };
    CIQ.UI.observeProperty('headsUp', listenerLayout, this.listener);
    this.observeInfo = {
      member: 'headsUp',
      obj: listenerLayout,
      listener: this.listener
    };
    return;
  }
  // handleToggle(value);

  layout.headsUp = {
    ...layout.headsUp,
    toggleInfo: value
  };
}
