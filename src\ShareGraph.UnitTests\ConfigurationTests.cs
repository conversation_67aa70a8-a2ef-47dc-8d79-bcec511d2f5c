using Euroland.FlipIT.ShareGraph.API.Queries;
using Euroland.FlipIT.ShareGraph.API.Types;
using HotChocolate;
using HotChocolate.Execution;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Xunit;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using ShareGraph.UnitTests.Mock;
using Newtonsoft.Json;
using ShareGraph.UnitTests.Models;
using System.Globalization;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Moq;
using System.Linq;
using Microsoft.Extensions.Hosting;
using Euroland.FlipIT.ShareGraph.API.Services;

namespace ShareGraph.UnitTests
{
    public class ConfigurationTests
    {
        [Fact]
        public async Task GetTicker_TickerConfigurationIsReturned()
        {
            // arrange
            var settingManager = new SettingManagerMock();
            var xmlSettingFactory = new DumpXmlSettingProviderFactory(
                @"<settings>
                    <Ticker>
		                <EnabledFormat>GRAPH</EnabledFormat>
		                <TickerType>SINGLE_TICKER_1</TickerType>
	                </Ticker>
                </settings>");

            settingManager.Accept(xmlSettingFactory);
            var setting = settingManager.Create();

            var cCode = "dk-cbg";
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            mockHttpContextAccessor
                .Setup(m => m.HttpContext.Request.Query[It.IsAny<string>()])
                .Returns(cCode);

            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<ISetting>(_ => setting);
            serviceCollection.AddScoped<IHttpContextAccessor>(_ => mockHttpContextAccessor.Object);

            var mockToolSettingService = new Mock<ITickerTemplateService>();
            mockToolSettingService
              .Setup(m => m.BuildTickerTemplate(It.IsAny<string>(), It.IsAny<string>()))
              .Returns(Task.FromResult("sampleTickerTemplate"));

            serviceCollection.AddTransient<ITickerTemplateService>(_ => mockToolSettingService.Object);
            
            IRequestExecutor executor = await serviceCollection
                .AddGraphQL()
                .AddQueryType(d => d.Name("Query"))
                .AddTypeExtension<ConfigurationQueries>()
                .AddType<ConfigurationType>()
                .AddType<SettingType>()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query{
                  configuration{
                    setting{
                      ticker{
                        enabledFormat
                        tickerType
                      }
                    }
                  }
                }");

            // assert
            List<string> expected_enabled = new List<string>() { "GRAPH" };
            string expected_tickerType = "SINGLE_TICKER_1";
            var actual = JsonConvert.DeserializeObject<ResolveResult>(result.ToJson())
                .Data.Configuration.Setting.Ticker;

            // assert
            Assert.Equal(expected_enabled, actual.EnabledFormat);
            Assert.Equal(expected_tickerType, actual.TickerType);
        }

        [Fact]
        public async Task GetFormatsIn_EnGbCulture_EnGbFormatsIsReturned()
        {
            // arrange
            var settingManager = new SettingManagerMock();
            var xmlSettingFactory = new DumpXmlSettingProviderFactory(
                @"<settings>
                    <Format>
                        <en-GB>
                            <TickerDateTimeFormat>MMM DD, hh:mm:ss A [UTC+2]</TickerDateTimeFormat>
			                <ShortDate xml:space='preserve'>MM/dd/yyyy</ShortDate>
                            <DecimalDigits> 5 </DecimalDigits>
                            <PercentDigits> 2 </PercentDigits>
                        </en-GB>
                        <ar-AE>
                            <DecimalDigits> 3 </DecimalDigits>
                            <PercentDigits> 3 </PercentDigits>
                        </ar-AE>
	                </Format>
                </settings>");

            settingManager.Accept(xmlSettingFactory);
            var setting = settingManager.Create();

            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<ISetting>(_ => setting);

            CultureInfo.CurrentCulture = new CultureInfo("ar-AE");

            IRequestExecutor executor = await serviceCollection
                .AddGraphQL()
                .AddQueryType(d => d.Name("Query"))
                    .AddTypeExtension<ConfigurationQueries>()
                .AddType<ConfigurationType>()
                .AddType<SettingType>()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query{
                  configuration{
                    setting{
                      format{
                        decimalDigits
                        percentDigits
                      }
                    }
                  }
                }");

            // assert
            var expected_decimalDigits = 3;
            var expected_percentDigits = 3;
            var actual = JsonConvert.DeserializeObject<ResolveResult>(result.ToJson())
                .Data.Configuration.Setting.Format;

            // assert
            Assert.Equal(expected_decimalDigits, actual.DecimalDigits);
            Assert.Equal(expected_percentDigits, actual.PercentDigits);
        }

        [Fact]
        public async Task GetShareDetails_Only_SupportedItem_IsReturned()
        {
            // arrange
            var settingManager = new SettingManagerMock();
            var xmlSettingFactory = new DumpXmlSettingProviderFactory(
                @"<settings>
                    <ShareDetails>
                      <Enabled>True</Enabled>
                      <ShareDataItems>TIME,InvalidItem</ShareDataItems>
                      <DisplayOnSelectedTicker>True</DisplayOnSelectedTicker>
                      <DisplayType>Grid</DisplayType>
                      <PartialDisplay>True</PartialDisplay>
                  </ShareDetails>
                </settings>");

            settingManager.Accept(xmlSettingFactory);
            var setting = settingManager.Create();

            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<ISetting>(_ => setting);

            IRequestExecutor executor = await serviceCollection
                .AddGraphQL()
                .AddQueryType(d => d.Name("Query"))
                    .AddTypeExtension<ConfigurationQueries>()
                .AddType<ConfigurationType>()
                .AddType<SettingType>()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query{
                  configuration{
                    setting{
                      shareDetails{
                        shareDataItems
                      }
                    }
                  }
                }");

            // assert
            var actual = JsonConvert.DeserializeObject<ResolveResult>(result.ToJson())
                .Data.Configuration.Setting.ShareDetails;

            Assert.Contains("TIME", actual.ShareDataItems);
            Assert.DoesNotContain("InvalidItem", actual.ShareDataItems);
        }

        [Fact]
        public async Task GetPeers_Return_Empty_If_Peers_Module_Is_Disabled()
        {
            // arrange
            var settingManager = new SettingManagerMock();
            var xmlSettingFactory = new DumpXmlSettingProviderFactory(
                @"<settings>
                    <Peers>
                      <Enabled>False</Enabled>
                    </Peers>
                </settings>");

            settingManager.Accept(xmlSettingFactory);
            var setting = settingManager.Create();

            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<ISetting>(_ => setting);

            IRequestExecutor executor = await serviceCollection
                .AddGraphQL()
                .AddQueryType(d => d.Name("Query"))
                    .AddTypeExtension<ConfigurationQueries>()
                .AddType<ConfigurationType>()
                .AddType<SettingType>()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query{
                  configuration{
                    setting{
                      peers{
                        peers{
                          id
                          color
                        }
                      }
                    }
                  }
                }");

            // assert
            var actual = JsonConvert.DeserializeObject<ResolveResult>(result.ToJson())
                .Data.Configuration.Setting.Peers;

            Assert.Null(actual.Peers);
        }

        [Fact]
        public async Task GetPeers_If_Color_Is_Not_Configured_A_Random_Color_Is_Returned()
        {
            // arrange
            var settingManager = new SettingManagerMock();
            var xmlSettingFactory = new DumpXmlSettingProviderFactory(
                @"<settings>
                    <Peers>
                      <Enabled>True</Enabled>
                      <Peer>
                        <Id>18612</Id>
                        <Enabled>false</Enabled>
                        <Order>1</Order>
                        <TickerName>
                          <en-GB>CARL A (COP).</en-GB>
                          <ar-AE>CARL B (COP).</ar-AE>
                        </TickerName>
                        <CurrencyCode>
                          <en-GB>DKK</en-GB>
                          <ar-AE>DKK</ar-AE>
                        </CurrencyCode>
                      </Peer>
                    </Peers>
                </settings>");

            settingManager.Accept(xmlSettingFactory);
            var setting = settingManager.Create();

            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<ISetting>(_ => setting);

            IRequestExecutor executor = await serviceCollection
                .AddGraphQL()
                .AddQueryType(d => d.Name("Query"))
                    .AddTypeExtension<ConfigurationQueries>()
                .AddType<ConfigurationType>()
                .AddType<SettingType>()
                .BuildRequestExecutorAsync();

            // act
            IExecutionResult result = await executor.ExecuteAsync(@"
                query{
                  configuration{
                    setting{
                      peers{
                        peers{
                          color
                        }
                      }
                    }
                  }
                }");

            // assert
            var actual = JsonConvert.DeserializeObject<ResolveResult>(result.ToJson())
                .Data.Configuration.Setting.Peers;

            Assert.NotNull(actual.Peers.First().Color);
        }
      
    }
}
