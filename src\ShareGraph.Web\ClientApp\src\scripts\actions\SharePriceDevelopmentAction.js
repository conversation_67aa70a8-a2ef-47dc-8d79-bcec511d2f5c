import fetchApi from '../services/fetch-api';
import { appSettings } from '../../appSettings';
import { getAllInstrumentsSetting, getTickerSetting, getTickersSetting } from '../configs/configuration-app';
import { getCurrencyText } from '../helper';
import { GET_SHARE_PRICE_DEVELOPMENT } from '../graphql-queries/sharePriceDevelopmentQuery';
import { client, clientRT } from '../services/graphql-client';
import { trimAbbreviation } from '../utils';
import appConfig from '../services/app-config';

function getsharePriceDevelopmentData({realTimeInstrumentIds = [], notRealTimeInstrumentIds = [], toCurrency}) {
  const fields = [
    'instrumentId',
    'shareName',
    'currencyCode',
    'last',
    'change',
    'high52W',
    'low52W',
    'allTimeHigh',
    'allTimeLow',
    'week',
    'month',
    'threeMonthChange',
    'sixMonthsChange',
    'yTD',
    'percent52W',
    'threeYearsChange',
    'fiveYearsChange',
    'marketAbbreviation',
    'tenYearsChange'
  ];
  return fetchApi(appSettings.sDataApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
       ${realTimeInstrumentIds.length ? `realtime: instruments(
          instrumentIds: [${realTimeInstrumentIds.join(',')}]
          isRT: true
          ${toCurrency ? `toCurrency: "${toCurrency}"` : ''}
          ){
          ${fields.join(',')}
        }` : ''}
        ${notRealTimeInstrumentIds.length ? `notRealtime: instruments(
          instrumentIds: [${notRealTimeInstrumentIds.join(',')}]
          isRT: false
          ${toCurrency ? `toCurrency: "${toCurrency}"` : ''}
          ){
          ${fields.join(',')}
        }` : ''}
      }`
    })
  })
    .then(res => res.json());
}

const convertNewRespDataToOldRespData = responseJson => {
  return responseJson.data.instrumentByIds.map(instrument => {
    const {
      id,
      shareName,
      currency,
      currentPrice,
      market,
      _52W,
      _allTime,
      _week,
      _month,
      _threeMonths,
      _sixMonths,
      _ytd,
      _threeYears,
      _fiveYears,
      _tenYears
    } = instrument;
    return {
      instrumentId: id,
      shareName: trimAbbreviation(shareName),
      currencyCode: currency?.code,
      last: currentPrice?.last,
      change: currentPrice?.change,
      high52W: _52W?.highest,
      low52W: _52W?.lowest,
      allTimeHigh: _allTime?.highest,
      allTimeLow: _allTime?.lowest,
      week: _week?.changePercentage,
      month: _month?.changePercentage,
      threeMonthChange: _threeMonths?.changePercentage,
      sixMonthsChange: _sixMonths?.changePercentage,
      yTD: _ytd?.changePercentage,
      percent52W: _52W?.changePercentage,
      threeYearsChange: _threeYears?.changePercentage,
      fiveYearsChange: _fiveYears?.changePercentage,
      marketAbbreviation: market?.abbreviation,
      tenYearsChange: _tenYears?.changePercentage
    };
  });
};

async function getSharePriceDevelopmentDataNewQuery({
  instrumentIds = [],
  toCurrency,
  isRT = false
}) {
  if (!instrumentIds.length) return;
  const clientInstance = isRT ? clientRT : client;
  const allInstrumentsSetting = getAllInstrumentsSetting();

  const ids = instrumentIds.filter(id => !allInstrumentsSetting[id].enabledAdjustPrice);
  const adjIds = instrumentIds.filter(id => allInstrumentsSetting[id].enabledAdjustPrice);

  const promiseAll = [];

  if(ids.length > 0) promiseAll.push(
    clientInstance.query(GET_SHARE_PRICE_DEVELOPMENT, {
      ids,
      adjClose: false,
      toCurrency
    })
  );

  if(adjIds.length > 0) promiseAll.push(
    clientInstance.query(GET_SHARE_PRICE_DEVELOPMENT, {
      ids: adjIds,
      adjClose: true,
      toCurrency
    })
  );

  return (await Promise.all(promiseAll)).map(res => convertNewRespDataToOldRespData(res)).flat();

  // const responseData = await clientInstance.query(GET_SHARE_PRICE_DEVELOPMENT, {
  //   ids: instrumentIds.filter(id => !allInstrumentsSetting[id].enabledAdjustPrice),
  //   adjIds: instrumentIds.filter(id => allInstrumentsSetting[id].enabledAdjustPrice),
  //   toCurrency
  // });

  // const data = convertNewRespDataToOldRespData(responseData);
  // return data;
}

// sharePriceDevelopment Actions
export const FETCH_SHAREPRICE_DEVELOPMENT_BEGIN = 'FETCH_SHAREPRICE_DEVELOPMENT_BEGIN';
export const FETCH_SHAREPRICE_DEVELOPMENT_SUCCESS = 'FETCH_SHAREPRICE_DEVELOPMENT_SUCCESS';
export const FETCH_SHAREPRICE_DEVELOPMENT_FAILURE = 'FETCH_SHAREPRICE_DEVELOPMENT_FAILURE';

export const fetchsharePriceDevelopmentBegin = () => ({
  type: FETCH_SHAREPRICE_DEVELOPMENT_BEGIN
});

export const fetchsharePriceDevelopmentSuccess = (instruments = []) => {
  const allInstrumentsSetting = getAllInstrumentsSetting();
  return (dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);

    const sortedInstruments  = Object.keys(allInstrumentsSetting).map(instrumentId=>{
      const instrument = instruments.find(instrument=>instrument.instrumentId === Number(instrumentId));
      return instrument? {...instrument, currencyCode: instrument.currencyCode ? 
        (selectedCurrencyText || allInstrumentsSetting[instrument.instrumentId]?.currencyCode || instrument.currencyCode) : ''}
         : null;
    }).filter(ins => !!ins);

    return dispatch({
      type: FETCH_SHAREPRICE_DEVELOPMENT_SUCCESS,
      payload: {
        instruments: sortedInstruments
      }
    });
  };
};

export const fetchsharePriceDevelopmentFailure = (error) => ({
  type: FETCH_SHAREPRICE_DEVELOPMENT_FAILURE,
  payload: { error }
});

const filterInstrumentIdByRealTime = (instruments = []) => {
  return instruments.reduce((acc, instrument) => {
    if (instrument.isRT) {
      acc.realTimeInstrumentIds.push(instrument.instrumentId);
    } else {
      acc.notRealTimeInstrumentIds.push(instrument.instrumentId);
    }
    return acc;
  }, { realTimeInstrumentIds: [], notRealTimeInstrumentIds: [] });
};


export function fetchSharePriceDevelopment(instrumentIds = []) {
  return async (dispatch, getState) => {
    dispatch(fetchsharePriceDevelopmentBegin());
    const excludeInstrumentIds = appConfig.get().performance.excludeIds||[];
    let realTimeInstrumentIds = [];
    let notRealTimeInstrumentIds = [];

    if (!instrumentIds || !instrumentIds.length) {
      const {realTimeInstrumentIds:rtPeerIds, notRealTimeInstrumentIds:nrtPeerIds} = filterInstrumentIdByRealTime(getState().peers.instruments);
      const {realTimeInstrumentIds:rtIndicesIds, notRealTimeInstrumentIds:nrtIndicesIds} = filterInstrumentIdByRealTime(getState().indices.instruments);
      const {realTimeInstrumentIds:rtTickerIds, notRealTimeInstrumentIds:nrtTickerIds} = filterInstrumentIdByRealTime(getState().tickers.instruments);
      realTimeInstrumentIds = [...rtPeerIds, ...rtIndicesIds, ...rtTickerIds];
      notRealTimeInstrumentIds = [...nrtPeerIds, ...nrtIndicesIds, ...nrtTickerIds];
    }

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    // We can do fake fetching sharePriceDevelopment data by return "fakegetsharePriceDevelopmentData()" as well.
    // return getsharePriceDevelopmentData({realTimeInstrumentIds, notRealTimeInstrumentIds, toCurrency})
    //   .then(json => {
    //     let data = [];
    //         if(json.data.realtime) {
    //             data = data.concat(json.data.realtime);
    //         }
    //         if(json.data.notRealtime) {
    //             data = data.concat(json.data.notRealtime);
    //         }
    //     dispatch(fetchsharePriceDevelopmentSuccess(data));
    //     return json.data.instruments;
    //   })
    //   .catch(err =>
    //     dispatch(fetchsharePriceDevelopmentFailure(err))
    //   );

    const allInstrumentsSetting = getAllInstrumentsSetting();

    const realTime = {
      ids: realTimeInstrumentIds.filter(
        (id) =>
          !allInstrumentsSetting[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      ),
      adjIds: realTimeInstrumentIds.filter(
        (id) =>
          allInstrumentsSetting[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      )
    };

    const notRealTime = {
      ids: notRealTimeInstrumentIds.filter(
        (id) =>
          !allInstrumentsSetting[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      ),
      adjIds: notRealTimeInstrumentIds.filter(
        (id) =>
          allInstrumentsSetting[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      )
    };

    const promiseAll = [];

    if(realTime.ids.length > 0) promiseAll.push(
      clientRT.query(GET_SHARE_PRICE_DEVELOPMENT, {
        ids: realTime.ids,
        adjClose: false,
        toCurrency
      })
    );

    if(realTime.adjIds.length > 0) promiseAll.push(
      clientRT.query(GET_SHARE_PRICE_DEVELOPMENT, {
        ids: realTime.adjIds,
        adjClose: true,
        toCurrency
      })
    );

    if(notRealTime.ids.length > 0) promiseAll.push(
      clientRT.query(GET_SHARE_PRICE_DEVELOPMENT, {
        ids: notRealTime.ids,
        adjClose: false,
        toCurrency
      })
    );

    if(notRealTime.adjIds.length > 0) promiseAll.push(
      clientRT.query(GET_SHARE_PRICE_DEVELOPMENT, {
        ids: notRealTime.adjIds,
        adjClose: true,
        toCurrency
      })
    );

    try {
      const instruments = (await Promise.all(promiseAll))
      .map(res => convertNewRespDataToOldRespData(res))
      .flat();

      dispatch(fetchsharePriceDevelopmentSuccess(instruments));
    } catch (err) {
      dispatch(fetchsharePriceDevelopmentFailure(err));
      console.error(err);
      throw err;
    }
  };
}
