import {useContext, useEffect, useRef} from 'react';
import {AppContext} from '../../AppContext';
import {useStore} from 'react-redux';
import { getInstrumentNameFromInstrumentIdHelper } from '../../helper';
import useStocksRealtime from '../../real-time/useStockRealtime';
import { CIQ } from './chartiq-import';
import useStockSnapshot from '../../real-time/useStockSnapshot';
import useAppSelector from '../../customHooks/useAppSelector';
import {watchStoreHookCreator} from '../../real-time/hook-creator';


const useWatchTickers = watchStoreHookCreator(state => ({
  updateStrategy: state.tickers.updateStrategy,
  instruments: state.tickers.instruments,
  refreshStarted: state.tickers.refreshStarted
}));

const resetMillisecond = (date) => {
  const cloneDate = new Date(date);
  cloneDate.setSeconds(0,0);
  return cloneDate;
};

export default function useDataRealTime({ chartEngine, mainTickerId }) {
  const settings = useContext(AppContext);
  const store = useStore();
  const state = store.getState();
  const tickerUpdateStrategy = useAppSelector(state => state.tickers.updateStrategy);
  const peerUpdateStrategy = useAppSelector(state => state.peers.updateStrategy);
  const indicesUpdateStrategy = useAppSelector(state => state.indices.updateStrategy);
  function getUpdateStrategyById (id) {
    if(id in tickerUpdateStrategy) return tickerUpdateStrategy[id];
    if(id in peerUpdateStrategy) return peerUpdateStrategy[id];
    if(id in indicesUpdateStrategy) return indicesUpdateStrategy[id];

    throw new Error(`Id ${id} do not exist!`);
  }
  const selectedPeerIds = useAppSelector(state => state.peers.selectedPeerIds);
  const selectedIndiciesIds = useAppSelector(state => state.indices.selectedIndicesIds);
  const isDailyInterval = () => CIQ.ChartEngine.isDailyInterval(chartEngine.layout.interval);
  const getInstrumentType = (instrumentId) => {
    const isPeers = state.peers.instruments.find(
      (ins) => ins.instrumentId === instrumentId
    );

    if (isPeers) return 'P';

    const isIndices = state.indices.instruments.find(
      (ins) => ins.instrumentId === instrumentId
    );

    if (isIndices) return 'I';

    return 'T';
  };


  useStocksRealtime(source => {
    if(!chartEngine) return;
    if(!chartEngine.chart.query) return;
    if(getUpdateStrategyById(source.id) !== 'socket') return;
    if(chartEngine.quoteDriver?.loadingNewChart === true) return;
    const instrumentId = source.id;
    const isMainTickerId = instrumentId === mainTickerId;

    const date = new Date(source.date);
    date.setMilliseconds(0);
    const updateData = {
      Close: source.price,
      Date: date.toISOString()
    };

    if (isMainTickerId) {
      chartEngine.mainSeriesRenderer.supportsAnimation = true;
      if(!isDailyInterval()) {
        chartEngine.updateChartData(
          [{ ...updateData, Volume: source.volume }],
          null,
          { useAsLastSale: { aggregatedVolume: false } }
        );
      } else {
        chartEngine.updateChartData([
          updateData
        ], null);
      }
      chartEngine.mainSeriesRenderer.supportsAnimation = false;
      return;
    }

    // Update Peers And Indices
    const instrumentType = getInstrumentType(instrumentId);
    const instrumentName = getInstrumentNameFromInstrumentIdHelper({instrumentId, instrumentType, state, settings});
    chartEngine.updateChartData(updateData, null, {secondarySeries: instrumentName });
  }, [mainTickerId, ...selectedPeerIds, ...selectedIndiciesIds]);

  useStockSnapshot((source) => {
    if(!chartEngine) return;
    if(!chartEngine.chart.query) return;
    if(chartEngine.quoteDriver?.loadingNewChart === true) return;
    if(getUpdateStrategyById(source.id) !== 'socket') 
    
    if(source.volume == null) return;
    if(!isDailyInterval()) return;

    const date = new Date(source.date);
    date.setMilliseconds(0);
    const updateData = {
      Volume: source.volume,
      Date: date.toISOString()
    };

    chartEngine.updateChartData([
      updateData
    ], null, {useAsLastSale: {aggregatedVolume: true}});
  }, [mainTickerId]);

  // for selected ticker
  useWatchTickers(({updateStrategy, instruments, refreshStarted}) => {
    if(!mainTickerId) return;
    if(!refreshStarted) return;

    const strategy = updateStrategy[mainTickerId];
    if(!strategy) return;
    if(strategy === 'socket') return;

    const mainInstrument = instruments?.find(item => item.instrumentId === mainTickerId);
    if(!mainInstrument) return;

    return {
      last: mainInstrument.last,
      lastUpdatedDate: mainInstrument.lastUpdatedDate,
      volume: mainInstrument.volume
    };
  }, data => {
    if(!data) return;
    if(!chartEngine) return;
    const currentQuote = chartEngine.getFirstLastDataRecord(chartEngine.chart.dataSet, 'Close', true);
    if(!currentQuote) return;
    const { DT, Volume, Close } = currentQuote;
    if(resetMillisecond(new Date(data.lastUpdatedDate)).getTime() < resetMillisecond(DT).getTime()) return;
    const dailyInterval = isDailyInterval();
    if(Close === data.last) {
      if(dailyInterval) {
        if(dailyInterval && Volume === data.volume) return;
      } else return;
    }
    const updateData = {
      Close: data.last,
      Date: resetMillisecond(new Date(data.lastUpdatedDate)).toISOString(),
      Volume: 0
    };

    const updateDataSetting = {useAsLastSale: {aggregatedVolume: false}};

    if(isDailyInterval()) {
      updateData.Volume = data.volume;
      updateDataSetting.useAsLastSale.aggregatedVolume = true;
    }
    chartEngine.updateChartData([
      updateData
    ], null, updateDataSetting);
  });

}
