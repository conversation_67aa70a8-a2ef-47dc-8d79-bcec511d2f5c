﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WatchListAPI.Entities.SharkDb
{
    [Table("Market")]
    public class Market
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public short MarketNumber { get; set; }

        public byte? RegionID { get; set; }

        public short? ParentID { get; set; }

        [Column(TypeName = "char(8)")]
        public string? MarketOpenTimeLocal { get; set; }

        [Column(TypeName = "char(8)")]
        public string? MarketCloseTimeLocal { get; set; }

        public short TimeDiff { get; set; }

        [Column(TypeName = "varchar(50)")]
        public string? DataSource { get; set; }

        [Column(TypeName = "varchar(10)")]
        [Required]
        public string Delay { get; set; } = null!;

        [Column(TypeName = "varchar(50)")]
        [Required]
        public string TimeZone { get; set; } = null!;

        [Required]
        public int TimezoneID { get; set; }

        [Required]
        public bool BusinessDaysStoT { get; set; }

        [Required]
        public int TranslationId { get; set; }

        [Required]
        public int CityId { get; set; }

        [Column(TypeName = "varchar(10)")]
        public string? Abbreviation { get; set; }
    }
}
