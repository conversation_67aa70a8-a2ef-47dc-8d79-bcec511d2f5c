import {trimAbbreviation} from '../utils';
import {gql} from './utils';
import { get } from 'es-toolkit/compat';
import { appSettings } from '../../appSettings';
import appConfig from '../services/app-config';

export const buildShareDetailsQuery = (variables) => {
  const fields = [];
  // Base fields
  fields.push('id');
  fields.push('shareName');

  // Market block
  const marketFields = [];
  if (variables.marketAbbreviationInc) marketFields.push('abbreviation');
  if (variables.marketNameInc) marketFields.push('translation { value }');
  if (variables.marketStatusInc) marketFields.push('status { isOpened }');
  if (marketFields.length > 0) {
    fields.push(`market { ${marketFields.join('\n')} }`);
  }

  // Current Price block
  const currentPriceFields = [];
  if (variables.lastUpdatedDateInc) currentPriceFields.push('date');
  if (variables.bidInc) currentPriceFields.push('bid');
  if (variables.bidSizeInc) currentPriceFields.push('bidSize');
  if (variables.askInc) currentPriceFields.push('ask');
  if (variables.askSizeInc) currentPriceFields.push('askSize');
  if (variables.openInc) currentPriceFields.push('open');
  if (variables.lastInc) currentPriceFields.push('last');
  if (variables.changeInc) currentPriceFields.push('change');
  if (variables.changePercentageInc) currentPriceFields.push('changePercentage');
  if (variables.highInc) currentPriceFields.push('high');
  if (variables.lowInc) currentPriceFields.push('low');
  if (variables.volumeInc) currentPriceFields.push('volume');
  if (variables.prevCloseInc) currentPriceFields.push('prevClose');
  if (variables.vWAPInc) currentPriceFields.push('vwap');
  if (variables.todayTurnoverInc) currentPriceFields.push('todayTurnover');
  if (currentPriceFields.length > 0) {
    fields.push(`currentPrice { ${currentPriceFields.join('\n')} }`);
  }

  // Currency block
  if (variables.currencyCodeInc) {
    fields.push('currency { code }');
  }

  // Direct fields
  if (variables.iSINInc) fields.push('isin');
  if (variables.tickerInc) fields.push('symbol');

  // Performance blocks
  const buildPerformanceBlock = (period,periodParam, blocks) => {
    const perfFields = [];
    Object.keys(blocks).forEach(key => {
      if (variables[key]) perfFields.push(blocks[key]);
    });
    return perfFields.length > 0
      ? `${period}: performance(period: ${periodParam}) { ${perfFields.join('\n')} }`
      : '';
  };

  const fiftyTwoWeeks = buildPerformanceBlock('fifty_two_weeks','FIFTY_TWO_WEEKS', {
    highest52wInc: 'highest',
    lowest52wInc: 'lowest',
    percent52WInc: 'changePercentage',
    highest52wDateInc: 'highestDate',
    lowest52wDateInc: 'lowestDate'
  });
  const ytd = buildPerformanceBlock('ytd','YTD',{
    highYTDInc: 'highest',
    lowYTDInc: 'lowest',
    yTDInc: 'changePercentage',
    highYtdDateInc: 'highestDate',
    lowYtdDateInc: 'lowestDate'
  });
  const allTime = buildPerformanceBlock('all','ALL_TIME',
    {
      allTimeHighInc: 'highest',
      allTimeLowInc: 'lowest',
      allTimeHighDateInc: 'highestDate',
      allTimeLowDateInc: 'lowestDate'}
  );

  if (fiftyTwoWeeks) fields.push(fiftyTwoWeeks);
  if (ytd) fields.push(ytd);
  if (allTime) fields.push(allTime);

  // List and sector blocks
  let language = appSettings.language?.trim() || 'en-GB';
  
  if (variables.listNameInc) fields.push(`list(cultureName: "${language}") { listName: name }`);
  if (variables.industryInc) fields.push(`subSector(cultureName: "${language}") { marCat }`);
  // Remaining direct fields
  if (variables.noSharesInc) fields.push('noShares');
  if (variables.lotSizeInc) fields.push('lotSize');
  if (variables.epsInc) fields.push('eps');
  if (variables.totalTradesInc) fields.push('lastDayIntraday(first: 99999) { totalCount }');

  return gql(`query ShareDetail(
    $ids: [Int!]!
    $adjClose: Boolean
    $toCurrency: String
  ) {
    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
      ${fields.join('\n')}
    }
  }`);
};

export const shareDetailMapping = {
  INSTRUMENT_ID: {
    dataField: 'instrumentId',
    selector: item => item.id
  },
  SHARE_NAME: {
    dataField: 'shareName',
    selector: item => trimAbbreviation(item.shareName)
  },
  MARKET_ABBREVIATION: {
    dataField: 'marketAbbreviation',
    selector: item => get(item, 'market.abbreviation')
  },
  TIME: {
    dataField: 'lastUpdatedDate',
    selector: item => get(item, 'currentPrice.date'),
    label: 'timeLabel'
  },
  CURRENCY: {
    dataField: 'currencyCode',
    selector: item => get(item, 'currency.code'),
    label: 'currencyLabel'
  },
  MARKET: {
    dataField: 'marketName',
    selector: item => get(item, 'market.translation.value'),
    label: 'marketLabel'
  },
  MARKET_STATUS: {
    dataField: 'marketStatus',
    selector: item => get(item, 'market.status.isOpened') ? 'Open' : 'Close',
    label: 'marketStatusLabel'
  },
  ISIN: {
    dataField: 'iSIN',
    selector: item => item.isin,
    label: 'isinLabel'
  },
  SYMBOL: {
    dataField: 'ticker',
    selector: item => item.symbol,
    label: 'symbolLabel'
  },
  BID: {
    dataField: 'bid',
    selector: item => get(item, 'currentPrice.bid'),
    label: 'bidLabel'
  },
  BID_SIZE: {
    dataField: 'bidSize',
    selector: item => get(item, 'currentPrice.bidSize'),
    label: 'bidSizeLabel'
  },
  ASK: {
    dataField: 'ask',
    selector: item => get(item, 'currentPrice.ask'),
    label: 'askLabel'
  },
  ASK_SIZE: {
    dataField: 'askSize',
    selector: item => get(item, 'currentPrice.askSize'),
    label: 'askSizeLabel'
  },
  OPEN: {
    dataField: 'open',
    selector: item => get(item, 'currentPrice.open'),
    label: 'openLabel'
  },
  LAST: {
    dataField: 'last',
    selector: item => get(item, 'currentPrice.last'),
    label: 'lastLabel'
  },
  CHANGE: {
    dataField: 'change',
    selector: item => get(item, 'currentPrice.change'),
    label: 'changeShareDetailLabel'
  },
  CHANGE_PERCENT: {
    dataField: 'changePercentage',
    selector: item => get(item, 'currentPrice.changePercentage'),
    label: 'changePercentLabel'
  },
  HIGH: {
    dataField: 'high',
    selector: item => get(item, 'currentPrice.high'),
    label: 'highLabel'
  },
  LOW: {
    dataField: 'low',
    selector: item => get(item, 'currentPrice.low'),
    label: 'lowLabel'
  },
  VOLUME: {
    dataField: 'volume',
    selector: item => get(item, 'currentPrice.volume'),
    label: 'volumeLabel'
  },
  TOTAL_TRADES: {
    dataField: 'totalTrades',
    selector: item => get(item, 'lastDayIntraday.totalCount'),
    label: 'totalTradesLabel'
  },
  PREVIOUS_CLOSE: {
    dataField: 'prevClose',
    selector: item => get(item, 'currentPrice.prevClose'),
    label: 'previousCloseLabel'
  },
  YTD_HIGH: {
    dataField: 'highYTD',
    selector: item => {
      return {
        value: get(item, 'ytd.highest'),
        date: get(item, 'ytd.highestDate')
      };
    },
    label: 'ytdHighLabel'
  },
  YTD_LOW: {
    dataField: 'lowYTD',
    selector: item => {
      return {
        value: get(item, 'ytd.lowest'),
        date: get(item, 'ytd.lowestDate')
      };
    },
    label: 'ytdLowLabel'
  },
  YTD_HIGH_DATE: {
    dataField: 'highYtdDate',
    selector: item => get(item, 'ytd.highestDate'),
    label: 'highYtdDateLabel'
  },
  YTD_LOW_DATE: {
    dataField: 'lowYtdDate',
    selector: item => get(item, 'ytd.lowestDate'),
    label: 'lowYtdDateLabel'
  },
  WEEKS_52_HIGH: {
    dataField: 'highest52w',
    selector: item => {
      return {
        value: get(item, 'fifty_two_weeks.highest'),
        date: get(item, 'fifty_two_weeks.highestDate')
      };
    },
    label: 'weeks52HighLabel'
  },
  WEEKS_52_LOW: {
    dataField: 'lowest52w',
    selector: item => {
      return {
        value: get(item, 'fifty_two_weeks.lowest'),
        date: get(item, 'fifty_two_weeks.lowestDate')
      };
    },
    label: 'weeks52LowLabel'

  },
  WEEKS_52_HIGH_DATE: {
    dataField: 'highest52wDate',
    selector: item => get(item, 'fifty_two_weeks.highestDate'),
    label: 'highest52wDateLabel'
  },
  WEEKS_52_LOW_DATE: {
    dataField: 'lowest52wDate',
    selector: item => get(item, 'fifty_two_weeks.lowestDate'),
    label: 'lowest52wDateLabel'
  },
  ALL_TIME_HIGH: {
    dataField: 'allTimeHigh',
    selector: item => {
      return {
        value: get(item, 'all.highest'),
        date: get(item, 'all.highestDate')
      };
    },
    label: 'allTimeHighLabel'

  },
  ALL_TIME_LOW: {
    dataField: 'allTimeLow',
    selector: item =>{
      return {
        value: get(item, 'all.lowest'),
        date: get(item, 'all.lowestDate')
      };
    },
    label: 'allTimeLowLabel'

  },
  ALL_TIME_HIGH_DATE: {
    dataField: 'allTimeHighDate',
    selector: item => get(item, 'all.highestDate'),
    label: 'allTimeHighDateLabel'
  },
  ALL_TIME_LOW_DATE: {
    dataField: 'allTimeLowDate',
    selector: item => get(item, 'all.lowestDate'),
    label: 'allTimeLowDateLabel'
  },
  YTD_PERCENT: {
    dataField: 'yTD',
    selector: item => get(item, 'ytd.changePercentage'),
    label: 'ytdPercentLabel'

  },
  WEEKS_52_PERCENT: {
    dataField: 'percent52W',
    selector: item => get(item, 'fifty_two_weeks.changePercentage'),
    label: 'weeks52PercentLabel'

  },
  LIST: {
    dataField: 'listName',
    selector: item => get(item, 'list.listName'),
    label: 'listLabel'

  },
  INDUSTRY: {
    dataField: 'industry',
    selector: item => get(item, 'subSector.marCat'),
    label: 'industryLabel'
  },
  NUMBER_OF_SHARES: {
    dataField: 'noShares',
    selector: item => get(item, 'noShares'),
    label: 'numberOfSharesLabel'

  },
  USE_PREV_CLOSE_DAY_FOR_MCAP: {
    dataField: 'usePrevCloseDayForMcap',
    selector: item => item.usePrevCloseDayForMcap,
    label: 'usePrevCloseDayForMcapLabel'
  },
  MARKET_CAP: {
    dataField: 'marketCap',
    dependencies: ['PREVIOUS_CLOSE', 'LAST', 'NUMBER_OF_SHARES', 'USE_PREV_CLOSE_DAY_FOR_MCAP'],
    selector: (prevClose, last, noShares, usePrevCloseDayForMcap) => usePrevCloseDayForMcap ? prevClose * noShares : last * noShares,
    label: 'marketCapLabel'

  },
  LOT_SIZE: {
    dataField: 'lotSize',
    selector: item => item.lotSize,
    label: 'lotSizeLabel'

  },
  EPS: {
    dataField: 'eps',
    selector: item => item.eps
  },
  P_E: {
    dataField: 'pE',
    dependencies: ['EPS', 'LAST'],
    selector: (eps, last) => eps === 0 ? 0 : last / eps,
    label: 'peLabel'
  },
  AVERAGE_PRICE: {
    dataField: 'vWAP',
    selector: item => get(item, 'currentPrice.vwap'),
    label: 'averagePriceLabel'

  },
  TURNOVER: {
    dataField: 'todayTurnover',
    selector: item => get(item, 'currentPrice.todayTurnover'),
    label: 'turnoverLabel'

  }
};

/**
 *
 * @param {string} fieldName
 * @param {Set<string>} [stack]
 */
function fieldResolver(fieldName, stack) {
  if(stack) {
    if(stack.has(fieldName)) throw new Error(`Field: ${fieldName} circle dependency detected`);
  } else {
    stack = new Set([fieldName]);
  }

  if(!(fieldName in shareDetailMapping)) throw new Error(`Field: ${fieldName} do not exist!`);

  const field = shareDetailMapping[fieldName];

  if(field.dependencies && field.dependencies.length > 0) {
    return field.dependencies.map(item => fieldResolver(item, stack)).flat();
  }

  return [field];
}

const markAllToFalse = Array.from(new Set(Object.keys(shareDetailMapping)
.map(item => fieldResolver(item))))
.flat()
.map(item => item.dataField + 'Inc')
.reduce((acc, item) => Object.assign(acc, {[item]: false}), {});

/**
 *
 * @param {Array<keyof shareDetailMapping>} fieldNames
 */
export function getVariableRequire(fieldNames) {
  const requireFields = new Set();

  for(const fieldName of fieldNames) {
    fieldResolver(fieldName).forEach(item => requireFields.add(item.dataField));
  }

  return Array.from(requireFields)
  .map(item => item + 'Inc')
  .reduce((acc, item) => Object.assign(acc, {[item]: true}), {...markAllToFalse});
}

/**
 *
 * @param {Array<keyof shareDetailMapping>} fieldNames
 * @param {any} source
 */
export function normalizeGraphqlData(fieldNames, source) {
  const { usePrevCloseDayForMcap = false } = appConfig.get();
  const result = {};
  for(const fieldName of fieldNames) {

    const field = shareDetailMapping[fieldName];

    if(field.dependencies) {
      result[field.dataField] = field.selector.apply(undefined, fieldResolver(fieldName).map(item => item.selector({ ...source, usePrevCloseDayForMcap })));
    } else {
      result[field.dataField] = field.selector(source);
    }
  }

  return result;
}
