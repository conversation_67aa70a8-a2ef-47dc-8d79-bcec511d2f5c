@ECHO OFF

:: usage:
:: publish.bat <[options]>
::
:: options:
::  --sln <value>     -> path to project or solution file. Default empty.
::  --profile <value> -> The publish profile which is referent to *.pubxml.
::  --env <value>     -> The deploy environment. Which will set as a name of <EnvironmentName/> to web.config.
::  --usr <value>     -> The user-name who has previlege to deploy app to remote server.
::  --pwd <value>     -> The password of user who has previlege to deploy app to remote server.
::  --url <value>     -> The absolute url of MSDeployService of remote server.
::  --build           -> Indicates building app before publishing. Default not build.
::  --build-client    -> build client app along with server side. Default  not build.
::  --restore         -> Indicates storing dependency packages before building and publishing. Default not restore.
SET ROOT=%CD%
ECHO Current working DIR: %ROOT%

:: publish.bat --sln soltion_or_project_file --profile Production --env Production --usr UserName --pwd P#ssw@rd --url "https://ee-v-webcat161.euroland.com:8172/msdeploy.axd"

setlocal

:parse
    if "%~1"=="" GOTO endparse
    if "%~1"=="--sln" ( set "_ProjectOrSolution=%~2" )
    if "%~1"=="--profile" ( set "_PublishProfile=-p:PublishProfile=%~2" )
    if "%~1"=="--env" ( set "_EnvironmentName=-p:EnvironmentName=%~2" )
    if "%~1"=="--usr" ( set "_PublishUserName=%~2" )
    if "%~1"=="--pwd" ( set "_PublishUserPassword=%~2" )
    if "%~1"=="--url" ( set "_DeployServiceUrl=-p:MSDeployServiceURL=%~2" )
    if "%~1"=="--build" ( set "_HasBuild=true" )
    if "%~1"=="--restore" ( set "_HasRestore=true" )
    if "%~1"=="--build-client" ( set "_BuildClient=%~2" & set "_HasBuildClient=true" )
    shift
    GOTO parse
:endparse

if "%_HasRestore%"=="" set ( set "_HasRestore=false" )
if "%_HasBuild%"=="" set ( set "_HasBuild=false" )
if "%_HasBuildClient%"=="" ( set "_BuildClient=false" )
if "%_HasBuildClient%"=="true" if "%_BuildClient%"=="" ( set "_BuildClient=true" )

if not "%_HasBuild%"=="true" ( set "_NoBuildArg=--no-build" )
if not "%_HasRestore%"=="true" ( set "_NoRestoreArg=--no-restore" )
if not "%_HasBuild%"=="true" ( set "_HasRestore=false" )
if not "%_HasBuild%"=="true" ( set "_BuildClient=false" )

:: Default _BuildClient=false
if "%_BuildClient%"=="" ( set "_BuildClient=false" )

:: Restore packages
if "%_HasRestore%"=="true" ( echo "Restoring..." )

if "%_HasRestore%"=="true" ( dotnet restore %_ProjectOrSolution% )

:: Build solution
if "%_HasBuild%"=="true" ( echo "Building..." )

if "%_HasBuild%"=="true" ( dotnet build %_ProjectOrSolution% -c Release --no-restore -p:GitlabBuild=%_BuildClient% %_NoRestoreArg% )

:: Publish solution
echo "Publishing..."
dotnet publish %_ProjectOrSolution% -c Release --no-build --no-restore -p:UserName=%_PublishUserName% -p:Password=%_PublishUserPassword% %_NoBuildArg% %_NoRestoreArg% %_PublishProfile% %_EnvironmentName% %_DeployServiceUrl%

:: debug
@REM echo --sln %_ProjectOrSolution%
@REM echo --profile %_PublishProfile%
@REM echo --env %_EnvironmentName%
@REM echo --usr %_PublishUserName%
@REM echo --pwd %_PublishUserPassword%
@REM echo --url %_DeployServiceUrl%
@REM echo --build-client %_BuildClient%

@REM if "%_HasRestore%"=="true" ( echo dotnet restore %_ProjectOrSolution% )
@REM if "%_HasBuild%"=="true" ( echo dotnet build %_ProjectOrSolution% -c Release --no-restore -p:GitlabBuild=%_BuildClient% %_NoRestoreArg% )
@REM echo dotnet publish %_ProjectOrSolution% -c Release --no-build --no-restore -p:UserName=%_PublishUserName% -p:Password=%_PublishUserPassword% %_NoRestoreArg% %_PublishProfile% %_EnvironmentName% %_DeployServiceUrl%


endlocal
