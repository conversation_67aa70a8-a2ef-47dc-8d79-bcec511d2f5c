import {gql} from './utils';

export const CHART_HISTORY_QUERY = gql(/* GraphQL */`query ChartHistory(
  $id: Int!
  $first: Int
  $last: Int
  $toCurrency: String
  $fromDate: DateTime
  $toDate: DateTime
  $fromDateHistory: DateTime
  $toDateHistory: DateTime
  $timeIntervalGrouping: Int
  $adjClose: Boolean
  $isIntraday: Boolean!
) {
  instrumentById(id: $id, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    historicals(
      where: { dateTime: { gte: $fromDateHistory, lte: $toDateHistory } }
      first: 9999999
      order: { dateTime: ASC }
    ) {
      nodes {
        close
        dateTime
        high
        low
        open
        volume
        instrumentId
      }
    }
    intraday: intradayGroupBySecond(
      where: { dateTime: { gte: $fromDate, lte: $toDate } }
      first: $first
      last: $last
      timeIntervalGrouping: $timeIntervalGrouping
      order: { dateTime: ASC }
    ) @include(if: $isIntraday) {
      nodes {
        close
        dateTime
        high
        low
        open
        volume
        instrumentId
      }
    }
  }
}
`);