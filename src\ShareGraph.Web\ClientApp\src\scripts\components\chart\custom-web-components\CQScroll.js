import { CIQ } from '../chartiq-import';

const CQScroll = CIQ.UI.components('cq-scroll')[0].classDefinition;
CQScroll.prototype.resize = function () {
  var node = this.node;
  var context = CIQ.UI.getMyContext(this);
  if (node.parents('.sharing').length) return; /*share.js appends this class to the body.
			Do not attempt unnecessary resize of scroll
			for a chart about to become a shared image.*/
  if (this.hasAttribute('cq-no-resize')) return;
  if (this.hasAttribute('cq-no-maximize')) this.noMaximize = true;
  var position = this.getBoundingClientRect();
  var reduceMenuHeight = this.reduceMenuHeight || 45; // defaulted to 45 to take into account 15px of padding on menus and then an extra 5px for aesthetics
  var contextHeight, contextTop;
  if (context && context.topNode) {
    var contextRect = context.topNode.getBoundingClientRect();
    contextHeight = contextRect.height;
    contextTop = contextRect.top;
  } else {
    // Fallback to the window height if context element cannot be found
    contextHeight = window.innerHeight;
    contextTop = 0;
  }
  if (!contextHeight) return;
  var height = contextHeight - (position.top - contextTop) - reduceMenuHeight;
  var holders = node.parents('.stx-holder,.stx-subholder,.chartContainer');
  if (holders.length) {
    holders.each(function () {
      var holderBottom = this.getBoundingClientRect().top + CIQ.elementDimensions(this).height;
      height = Math.min(height, holderBottom - position.top - 5); // inside a holder we ignore reduceMenuHeight, but take off 5 pixels just for aesthetics
    });
  }

  // If there are subsequent siblings that have a fixed height then make room for them
  var nextAll = node.nextAll();
  for (var i = 0; i < nextAll.length; i++) {
    var sibling = nextAll[i];
    if (sibling && !CIQ.trulyVisible(sibling)) continue; // skip hidden siblings
    height -= CIQ.elementDimensions(sibling, {
      border: 1,
      padding: 1,
      margin: 1
    }).height;
  }
  if (!this.noMaximize) node.css({ height: height + 'px' });

  //

  // only apply for scroll in popup setting study
  var parentTag = node.parents('cq-study-dialog');
  if (parentTag.length) {
    var chart =
      context && Object.prototype.hasOwnProperty.call(context, 'stx') ? context.stx.container.closest('.graph') : null;
    if (chart) {
      var maxHeight = chart.offsetHeight - 200;
      node.css({ 'max-height': maxHeight + 'px' });
    }
  } else {
    node.css({ 'max-height': height + 'px' });
  }

  this.refresh();
};


/**
 * swipe for scroll
 */
class CustomScroll extends CQScroll {
  constructor() {
    super();
    this.isTouchdown = false;
    this.pagePosition = {};

    this.handleTouchStart = this.handleTouchStart.bind(this);
    this.handleTouchEnd = this.handleTouchEnd.bind(this);
    this.handleTouchMove = this.handleTouchMove.bind(this);
    this.direction = -1;
  }

  adoptedCallback() {
		super.adoptedCallback();
		CIQ.UI.flattenInheritance(this, CustomScroll);
		this.constructor = CustomScroll;
	}

  connectedCallback() {
    super.connectedCallback();
    let direction = this.getAttribute('direction');
    if(direction) {
      this.direction = parseInt(direction);
    }
    this.addEventListener('touchstart', this.handleTouchStart);
    this.addEventListener('touchmove', this.handleTouchMove);
    document.addEventListener('touchend', this.handleTouchEnd);
    document.addEventListener('touchcancel', this.handleTouchEnd);
  }

  handleTouchStart (e) {
    this.isTouchdown = true;
    this.pagePosition = {
      pageX: e.touches[0].pageX,
      scrollLeft: this.scrollLeft,
      pageY: e.touches[0].pageY,
      scrollTop: this.scrollTop
    };
  }

  handleTouchEnd () {
    this.isTouchdown = false;
    this.pagePosition = {};
  }

  handleTouchMove (e) {
    e.stopPropagation();
    if(!this.isTouchdown) return;
    const { pageX, pageY, scrollLeft, scrollTop } = this.pagePosition;
    if(!pageX || !pageY) return;
    const moveToLeft = this.direction * (e.touches[0].pageX - pageX);
    const moveToTop = this.direction * (e.touches[0].pageY - pageY);
    this.scrollTo(scrollLeft + moveToLeft, scrollTop + moveToTop);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    document.removeEventListener('touchend', this.handleTouchEnd);
    document.removeEventListener('touchcancel', this.handleTouchEnd);
  }
}

CIQ.UI.addComponentDefinition('custom-scroll', CustomScroll);
