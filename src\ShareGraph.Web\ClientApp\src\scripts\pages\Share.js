import appConfig  from '../services/app-config';
import { useEffect, useState, useRef } from 'react';
import i18n from '../services/i18n';
import { appSettings } from '../../appSettings';
import SocialButton from '../components/SocialButton';
import ToolTip from '../components/commons/ToolTip';
import { copy } from '../helper';
import useAutoFocusToPopup from '../customHooks/useAutoFocusToPopup';
import useFocusTrap from '../customHooks/useFocusTrap';

export default function ShareComponent() {
  const [copied, setCopied] = useState(false);

  const inputRef = useRef();
  const copiedButtonRef = useRef();
  const dialogRef = useRef();

  const shareGraphSettings = appConfig.get();
  const [uploadedImgUrl, setUploadedImgUrl] = useState(null);
  const enabledSocialMedias = shareGraphSettings.chart.enabledSocialMedias;
  const {origin, pathname} = window.location;
  //use default https://dev.vn.euroland.com/tools/sharegraph3 use default share link. Because share social only working in hosting enviroment
  const hostname = origin.includes('localhost') ? 'https://dev.vn.euroland.com/tools/sharegraph3' : origin + pathname.replace(/\/social/, '');
  const sDownloadLink = `${hostname}/share?companyCode=${appSettings.companyCode}&lang=${
    appSettings.language
  }&v=${appSettings.companyCodeVersion || ''}`;

  useAutoFocusToPopup();
  useFocusTrap(dialogRef);

  useEffect(() => {
    //shareUrl.current.value = window.xprops.thumbnailUrl;
    setUploadedImgUrl(window.xprops.thumbnailUrl);
    document.querySelector('html').classList.add('share-dialog-pop-out');

  });

  const handleCopyLink = () => {
    if(uploadedImgUrl) {
      copy(uploadedImgUrl).then(() => {
        setCopied(true);
        removeCopiedStatusAfterMoment();
        openTooltip();
      });
    }
  };

  const openTooltip = () => {
    copiedButtonRef.current?.classList.add('showPopup');
    hideTooltipAfterMoment();
  };

  const hideTooltipAfterMoment = () => {
    const REMOVED_TIME = 2; // seconds
    let timer;
    if (timer) clearTimeout(timer);

    timer = setTimeout(() => {
      copiedButtonRef.current?.classList.remove('showPopup');
    }, REMOVED_TIME * 1000);
  };

  const removeCopiedStatusAfterMoment = () => {
    const REMOVED_TIME = 3; // seconds
    let timer;

    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      setCopied(false);
    }, REMOVED_TIME * 1000);
  };

  return (
    <div className='dialog--social-sharing dialog--iframe dialog__wrapper' ref={dialogRef}>
      <header className="dialog__header">
        <h2 className="dialog__title">{i18n.translate('shareYourChartLabel')}</h2>
      </header>
      <div className="dialog__body">
        <div className="dialog__form-group">
          <input aria-label="Shareable link" ref={inputRef} defaultValue={uploadedImgUrl} type="text" className='dialog__form-control' readOnly />
          <button ref={copiedButtonRef} className='tooltip-wrapper tooltip-right' onClick={handleCopyLink}>
              <ToolTip>{copied ? i18n.translate('linkCopied') : i18n.translate('copy')}</ToolTip>
            <span className="fs-copy"></span>
          </button>
        </div>
        <div className='dialog__aws-btns'>
          {
            enabledSocialMedias.map((type, index) => {
              const url = sDownloadLink + '&type=' + type.toLowerCase() + '&urlImg=' + uploadedImgUrl;
              return <SocialButton message=' ' key={index} type={type.toLowerCase()} url={type.toLowerCase() === 'facebook' ? url : encodeURIComponent(url)} />;
            })
          }
        </div>
      </div>
      <CloseDialogButton />
    </div>
  );
}

export function CloseDialogButton() {

  const handleCloseDialog = () => {
    window.xprops?.close();
  };

  return <button
    aria-label={i18n.translate('close')}
    className='dialog__close-icon'
    onClick={handleCloseDialog}
  />;
}


