import produce from 'immer';

import {
  FETCH_52WEEKS_BEGIN,
  FETCH_52WEEKS_SUCCESS,
  FETCH_52WEEKS_FAILURE
//   REFRESH_52WEEKS_BEGIN,
//   REFRESH_52WEEKS_SUCCESS,
//   REFRESH_52WEEKS_FAILURE,
} from '../actions/52WeeksAction';

const initialState = {
  instruments: [],
  loading: true,
//   refreshing: false,
  fetchError: null
//   refreshError: null
};

export default function create52WeeksReducer(instrumentIds = []) {  
  initialState.instruments = instrumentIds.map((i) => {
    return {
      instrumentId: i
    };
  });
  
  return function weeks52Reducer(state = initialState, action) {
    switch (action.type) {
      case FETCH_52WEEKS_BEGIN:
        return produce(state, draft => {
          draft.loading = true;
          draft.fetchError = null;
        });
      case FETCH_52WEEKS_SUCCESS:
        return produce(state, draft => {
          draft.loading = false;
          draft.instruments = action.payload.instruments;
        });
      case FETCH_52WEEKS_FAILURE:
        return produce(state, draft => {
          draft.loading = false;
          draft.fetchError = action.payload.error;
        });
    //   case REFRESH_52WEEKS_BEGIN:
    //     return produce(state, draft => {
    //       draft.refreshing = true;
    //       draft.fetchError = null;
    //     });
    //   case REFRESH_52WEEKS_SUCCESS:
    //     return produce(state, draft => {
    //       draft.refreshing = false;
    //       draft.instruments = action.payload.instruments;
    //     });
    //   case REFRESH_52WEEKS_FAILURE:
    //     return produce(state, draft => {
    //       draft.refreshing = false;
    //       draft.refreshError = action.payload.error;
    //     });
      default:
        return state;
    }
  };
}
