﻿<?xml version="1.0" encoding="UTF-8"?>
<settings>
   <Layout>FIXED</Layout>
   <TimeZone>Europe/Brussels</TimeZone>
   <UseLatinNumber>false</UseLatinNumber>
   <StyleURI>dkcbg.css</StyleURI>
   <Instruments>
      <Instrument>
         <Id>32865</Id>
         <Color>#7DCA48</Color>
         <Order>2</Order>
         <Default>true</Default>
         <TickerName>
				<en-GB>CARL B (COP)</en-GB>
            <fr-fr>Price</fr-fr>
				<ar-AE>CARL B</ar-AE>
			</TickerName>

          <Ticker>
				<en-GB>CARL B</en-GB>
            <fr-fr>Price</fr-fr>
				<ar-AE>CARL B</ar-AE>
			</Ticker>
				
      </Instrument>

    
      <Instrument>
         <Id>12590</Id>
         <Color>#04A851</Color>
         <Order>5</Order>
         <Default>true</Default>
         <TickerName>
				<en-GB><PERSON><PERSON></en-GB>
				<ar-AE>CARL B</ar-AE>
			</TickerName>
			
      </Instrument>
      <Instrument>
         <Id>96359</Id>
         <Color>#2AABE4</Color>
         <Order>6</Order>
         <Default>true</Default>
         
      </Instrument>
      <Instrument>
         <Id>102649</Id>
         <Color>#0071BD</Color>
         <Order>7</Order>
         <Default>true</Default>
        
         <!-- <Ticker>
				<en-GB>Share 7</en-GB>
				<ar-AE>Share 1 in arabic </ar-AE>
			</Ticker> -->
      </Instrument>
   </Instruments>
   <Indices>
      <Enabled>true</Enabled>
      <Index>
         <Id>19107</Id>
         <Color>#111E6C</Color>
         <Order>4</Order>
         <TickerName>
            <en-GB>CARL A (COP) 1.</en-GB>
            <ar-AE>CARL B (COP).</ar-AE>
         </TickerName>
      </Index>
      <Index>
         <Id>102650</Id>
         <Color>#F90703</Color>
         <Order>1</Order>
         <TickerName>
            <en-GB>CARL A (COP) 2.</en-GB>
            <ar-AE>CARL B (COP).</ar-AE>
         </TickerName>
      </Index>
      <Index>
         <Id>96360</Id>
         <Color>#005A73</Color>
         <Order>1</Order>
         <TickerName>
            <en-GB>CARL A (COP) 3.</en-GB>
            <ar-AE>CARL B (COP).</ar-AE>
         </TickerName>
      </Index>
      <Index>
         <Id>12581</Id>
         <Color>#005A73</Color>
         <Order>1</Order>
         <TickerName>
            <en-GB>CARL A (COP) 4.</en-GB>
            <ar-AE>CARL B (COP).</ar-AE>
         </TickerName>
      </Index>

      <Index>
         <Id>3562</Id>
         <Color>#8A0303</Color>
         <Order>5</Order>
         <TickerName>
            <en-GB>CARL A (COP) 5.</en-GB>
            <ar-AE>CARL B (COP).</ar-AE>
         </TickerName>
      </Index>
   </Indices>
   <Ticker>
      <!-- Posible values: GRAPH, TABLE -->
      <EnabledFormat>GRAPH, TABLE</EnabledFormat>
      <!-- Posible values: SINGLE, MULTIPLE -->
      <TickerType>MULTIPLE</TickerType>
      <!-- Posible values: SINGLE_TICKER_1, SINGLE_TICKER_2, MULTIPLE_TICKER_1, MULTIPLE_TICKER_2, {CustomTemplate}  -->
      <!-- <GraphTickerTemplate>SINGLE_TICKER_1_WITH_DANGEROUS_HTML_TAGS</GraphTickerTemplate> -->
      <GraphTickerTemplate>MULTIPLE_TICKER_1</GraphTickerTemplate>
      <!-- <TableColumns>TABLE_TICKER_SINGLE, TABLE_TICKER_MULTIPLE</TableColumns> -->
      <TableTickerTemplate>TABLE_TICKER_MULTIPLE</TableTickerTemplate>
      <SlidesPerView>4</SlidesPerView>
      <!-- Possible values: FADE, TRANSFORM -->
      <TableAnimation>FADE</TableAnimation>
      <!-- Possible values: FADE, BLINK_PRICE, BLINK_MARKET -->
      <GraphAnimation>BLINK_PRICE</GraphAnimation>
   </Ticker>
   <Peers>
      <Enabled>true</Enabled>
      <Peer>
         <Id>9287</Id>
         <Color>#0097A7</Color>
         <Order>1</Order>
         <TickerName>
            <en-GB>Heineken</en-GB>
         </TickerName>
      </Peer>
      <Peer>
         <Id>10000</Id>
         <Color>#FF0000</Color>
         <Order>2</Order>
         <TickerName>
            <en-GB>In Bev</en-GB>
         </TickerName>
         <CurrencyCode>
            <en-GB>DKK</en-GB>
            <ar-AE>DKK</ar-AE>
         </CurrencyCode>
      </Peer>
      <Peer>
         <Id>32904</Id>
         <Color>#00FF00</Color>
         <Order>3</Order>
         <TickerName>
            <en-GB>Diageo</en-GB>
         </TickerName>
         <CurrencyCode>
            <en-GB>DKK</en-GB>
            <ar-AE>DKK</ar-AE>
         </CurrencyCode>
      </Peer>
      <Peer>
         <Id>3541</Id>
         <Color>#0000FF</Color>
         <Order>4</Order>
         <TickerName>
            <en-GB>Efes</en-GB>
         </TickerName>
         <CurrencyCode>
            <en-GB>DKK</en-GB>
            <ar-AE>DKK</ar-AE>
         </CurrencyCode>
      </Peer>
      <Peer>
         <Id>3562</Id>
         <Color>#126548</Color>
         <Order>5</Order>
         <CurrencyCode>
            <en-GB>DKK</en-GB>
            <ar-AE>DKK</ar-AE>
         </CurrencyCode>
      </Peer>
   </Peers>
   <ShareDetails>
      <!-- Options: True|False -->
      <Enabled>True</Enabled>
      <!-- Fields available: TIME,CURRENCY,MARKET,MARKET_STATUS ...-->
      <ShareDataItems>TIME,CURRENCY,MARKET_CAP,LOT_SIZE,P_E,AVERAGE_PRICE,TURNOVER
</ShareDataItems>
      <!-- Options: True|False -->
      <DisplayOnSelectedTicker>false</DisplayOnSelectedTicker>
      <!-- Options: Grid|Flow -->
      <DisplayType>Flow</DisplayType>
      <!-- Options: True|False -->
      <PartialDisplay>True</PartialDisplay>
      <NumberItemsInCollapsedMode>2</NumberItemsInCollapsedMode>
   </ShareDetails>
   <Performance>
      <!-- Posible values: GRAPH, TABLE
		Empty for disable -->
      <EnabledFormat>GRAPH,TABLE</EnabledFormat>
      <!-- Posible values: SHARE_PRICE_DEVELOPMENT, SHARE_PRICE_DEVELOPMENT_BY_YEARS, 52_WEEKS_HIGH_LOW  
		Empty for disable -->
      <PerformanceType>SHARE_PRICE_DEVELOPMENT,SHARE_PRICE_DEVELOPMENT_BY_YEARS,52_WEEKS_HIGH_LOW</PerformanceType>
       <!-- Option values: 2,3,4,5,6 -->
      <NumberOfYearSPByYear>5</NumberOfYearSPByYear>
      <!--Option values: true, false-->
      <ShowEarliestYearFirstSPByYear>false</ShowEarliestYearFirstSPByYear>
   </Performance>
   <Format>
      <en-GB>
         <TickerDateTimeFormat>DD MMM, YYYY hh:mm A [(GMT] Z[)]</TickerDateTimeFormat>
         <ShortDate xml:space="preserve">MM/DD/YYYY</ShortDate>
         <DecimalDigits>2</DecimalDigits>
         <PercentDigits>2</PercentDigits>
         <!--DecimalSeparator: comma or dot-->
         <DecimalSeparator xml:space="preserve">.</DecimalSeparator>
         <!--ThousandsSeparator: comma, dot, space-->
         <ThousandsSeparator xml:space="preserve">.</ThousandsSeparator>
         <!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
         <NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
      </en-GB>
      <ar-AE>
         <TickerDateTimeFormat>MMM d, HH:mm:ss 'ARABICCEST'</TickerDateTimeFormat>
         <ShortDate xml:space="preserve">dd/MM/YYYY</ShortDate>
          <LongDate xml:space="preserve">MM MMM/DD/YYYY</LongDate>
         <DecimalDigits>7</DecimalDigits>
         <PercentDigits>2</PercentDigits>
         <!--DecimalSeparator: comma or dot-->
         <DecimalSeparator xml:space="preserve">,</DecimalSeparator>
         <!--ThousandsSeparator: comma, dot, space-->
         <ThousandsSeparator xml:space="preserve">.</ThousandsSeparator>
         <!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
         <NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
      </ar-AE>
   </Format>
   <CustomPhrases>
      <rangeLabel>
         <en-GB>range label custom</en-GB>
         <fi-FI>Lataa tämä tapahtuma Outlookiin</fi-FI>
         <sv-SE>Ladda ner denna aktiviteter till Outlook</sv-SE>
      </rangeLabel>
      <!-- <shareDataTab>
			<en-GB>
				Share Data CustomPhrases
			</en-GB>
		</shareDataTab>
		<shareDetailsTitle>
			<en-GB>
				Share Details CustomPhrases
			</en-GB>
		</shareDetailsTitle> -->
   </CustomPhrases>

</settings>
