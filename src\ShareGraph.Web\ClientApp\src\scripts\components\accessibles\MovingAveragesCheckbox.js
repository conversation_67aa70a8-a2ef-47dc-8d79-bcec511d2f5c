import { MOVING_AVERAGES } from '../../common';
import i18n from '../../services/i18n';
import AccessibleCheckbox from './AccessibleCheckbox';

const MovingAveragesCheckbox = ({ value = [], onChange }) => {
  const options = [
    {
      id: MOVING_AVERAGES.MA_10.key,
      name: MOVING_AVERAGES.MA_10.key,
      value: MOVING_AVERAGES.MA_10.value,
      title: `10 ${i18n.translate('days')}`
    },
    {
      id: MOVING_AVERAGES.MA_20.key,
      name: MOVING_AVERAGES.MA_20.key,
      value: MOVING_AVERAGES.MA_20.value,
      title: `20 ${i18n.translate('days')}`
    },
    {
      id: MOVING_AVERAGES.MA_50.key,
      name: MOVING_AVERAGES.MA_50.key,
      value: MOVING_AVERAGES.MA_50.value,
      title: `50 ${i18n.translate('days')}`
    }
  ];

  return (
    <>
      <h4 id="selectma">{i18n.translate('selectMovingAverages')}</h4>
      <AccessibleCheckbox id="cklMA" options={options} value={value} onChange={onChange} />
    </>
  );
};

export default MovingAveragesCheckbox;
