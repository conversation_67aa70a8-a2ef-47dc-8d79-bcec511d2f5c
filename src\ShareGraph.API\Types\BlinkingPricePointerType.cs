using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.ShareGraph.API.Types
{
  public class BlinkingPricePointerType : ObjectType<BlinkingPricePointer>
  {
    protected override void Configure(IObjectTypeDescriptor<BlinkingPricePointer> descriptor)
    {
      descriptor
            .Field(t => t.Enabled)
            .ResolveWith<BlinkingPricePointerResolver>(t => t.GetEnabled(default!));
    }
  }
}
