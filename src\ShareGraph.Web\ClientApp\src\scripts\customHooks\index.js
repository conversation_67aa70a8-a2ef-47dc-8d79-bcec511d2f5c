import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectTicker } from '../actions/tickerActions';

export function usePrevious(value) {
  const ref = useRef();
  useEffect(() => {
    return () => {
      ref.current = value;
    };
  });
  return ref.current;
}

export function useOnClickOutside(ref, handler) {
  useEffect(
    () => {
      const listener = event => {
        // Do nothing if clicking ref's element or descendent elements
        if (!ref.current || ref.current.contains(event.target)) {
          return;
        }
        handler(event);
      };
      document.addEventListener('mousedown', listener);
      document.addEventListener('touchstart', listener);
      return () => {
        document.removeEventListener('mousedown', listener);
        document.removeEventListener('touchstart', listener);
      };
    },
    // Add ref and handler to effect dependencies
    // It's worth noting that because passed in handler is a new ...
    // ... function on every render that will cause this effect ...
    // ... callback/cleanup to run every render. It's not a big deal ...
    // ... but to optimize you can wrap handler in useCallback before ...
    // ... passing it into this hook.
    [ref, handler]
  );
}

/**
 * @returns {[{current: boolean}, setIsFirstRenderChart: () => void]}
 */
export const useIsFirstRenderChart = () => {
  const isFirstRenderChartRef = useRef(true);
  const setIsFirstRenderChart = isFirstRender => {
    isFirstRenderChartRef.current = isFirstRender;
  };
  return [isFirstRenderChartRef, setIsFirstRenderChart];
};

function getRealEventName(eventName) {
  eventName = eventName.replace('on', '');
  eventName = eventName.toLowerCase().trim();
  return eventName;
}

export function useTreatLikeDom() {
  const property = useRef({});
  const events = useRef({});
  const dataIndex = useRef(Math.random().toString(32).slice(2, 7));
  const initEvent = elements => {
    const eventsList = {
      ...events.current
    };
    for (const [eventName, callback] of Object.entries(eventsList)) {
      const realEventName = getRealEventName(eventName);
      elements.forEach(element => {
        element.addEventListener(realEventName, callback);
      });
    }

    return () => {
      for (const [eventName, callback] of Object.entries(eventsList)) {
        const realEventName = getRealEventName(eventName);
        elements.forEach(element => {
          element.removeEventListener(realEventName, callback);
        });
      }
    };
  };
  useLayoutEffect(() => {
    const elements = Array.from(document.querySelectorAll(`[data-id-index="${dataIndex.current}"]`));
    Object.keys(property.current).map(attr => {
      elements.forEach(element => {
        element.setAttribute(attr, property.current[attr]);
      });
    });

    const cancelEvent = initEvent(elements);

    return () => {
      cancelEvent();
    };
  });
  return {
    className: value => {
      property.current = {
        ...property.current,
        class: value
      };

      return {
        className: value
      };
    },
    events: eventList => {
      events.current = eventList;
      return {};
    },
    attr: attrs => {
      property.current = {
        ...property.current,
        ...attrs
      };
      return {
        ...attrs
      };
    },
    'data-id-index': dataIndex.current
  };
}


export const useScrollable = () => {
  const ref = useRef();
  const isMousedown = useRef(false);
  const pagePosition = useRef({});
  useEffect(() => {
    const element = ref.current;
    if(!element) return;
    const handleMousemove = e => {
      if(!isMousedown.current) return;
      const { pageX, pageY, scrollLeft, scrollTop } = pagePosition.current;
      if(!pageX || !pageY) return;
      element.scrollTo(scrollLeft + (e.pageX - pageX), scrollTop + (e.pageY - pageY));
    };
    const handleMouseup = () => {
      isMousedown.current = false;
      pagePosition.current = {};
    };


    document.addEventListener('mousemove', handleMousemove);
    document.addEventListener('mouseup', handleMouseup);
    return () => {
      document.removeEventListener('mousemove', handleMousemove);
      document.removeEventListener('mouseup', handleMouseup);
    };
  }, [ ref.current ]);
  
  return {
    onMouseDown: e => {
      isMousedown.current = true;
      const element = ref.current;
      pagePosition.current = {
        pageX: e.pageX,
        scrollLeft: element.scrollLeft,
        pageY: e.pageY,
        scrollTop: element.scrollTop
      };
    },
    ref
  };
};

export const useLazyActiveInstrument = () => {
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const [lazySelectedInstrument, setLazySelectedInstrument] = useState(selectedInstrumentId);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(selectTicker(lazySelectedInstrument));
  }, [lazySelectedInstrument, dispatch]);

  useEffect(() => {
    setLazySelectedInstrument(selectedInstrumentId);
  }, [selectedInstrumentId]);
  
  return {
    selectedInstrumentId,
    lazySelectedInstrument,
    setLazySelectedInstrument
  };
};
