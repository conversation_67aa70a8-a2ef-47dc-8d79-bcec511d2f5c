import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import i18n from '../../../services/i18n';
import ToolTip from '../../commons/ToolTip';
import { useOnClickOutside } from '../../../customHooks/index';
import appConfig from '../../../services/app-config';
import { TEMPLATE_TICKER } from '../../../common';
import { toImage } from '../../../lib/htmlToImage';
import { chartLoading } from '../ChartHelper';
import { getTickerDom, showSelectedMultipleTickerTable } from './DownloadImageButton';
import {uploadFile} from '../../../services/commonService';

export function ShareButton() {
  const isIFrame = window.self !== window.top;
  const integration = window['euroland'];

  //const appContext = useContext(AppContext);
  const refDialog = useRef(null);
  const [showDialogSoicialSharing, setDialogSoicialSharing] = useState(false);
  useOnClickOutside(refDialog, () => setDialogSoicialSharing(false));
  const exportBtnRef = useRef(null);
  const shareGraphSettings = appConfig.get();
  const tickerType = shareGraphSettings.ticker.tickerType;
  const loadingChart = useMemo(() => {
    return chartLoading.self();
  }, []);

  const handleClickShareDialog = (event) => {
    event.preventDefault();
    if(loadingChart.isLoading()) {
      return;
    } else {
      loadingChart.show();
    }
    ensureIntegrationComponent()
      .then(extractScreenshot)
      .then(uploadFile)
      .then((res) => {
        if (!res) {
          return false;
        }
        return res;
      })
      .then(openShareDialog)
      .catch((e) => {
        throw e;
      }).finally(() => loadingChart.hide());
  };


  const extractScreenshot = () => {
    const graphDom = document.querySelector('.graph');
    const shareGraphDom = document.querySelector('.share-graph');

    const undoShowSelectedMultipleTickerTable = showSelectedMultipleTickerTable(shareGraphDom);
    const { tickerDom, tickerTableDom, isMultipleTicker} = getTickerDom({ shareGraphDom, tickerType });

    let screenShotWrapper = document.createElement('div');
    screenShotWrapper.classList.add('screenShot');

    // export chart
    const canvas1 = toImage(graphDom, {backgroundColor:'#ffffff', filter: filter, avoidBlockPage: true})
    .then(canvas=> ({canvas: canvas, key: 'graph'}));
    const canvas2 = toImage(tickerDom, {
      backgroundColor: '#ffffff',
      width: Math.ceil(tickerDom.getBoundingClientRect().width) // The library is not correctly retrieving the width of the element. For instance, if the width is 100.44, the library will round it to 100. Sometimes, this causes a break in the UI as the image does not display correctly.
    })
      .then((canvas) => ({ canvas: canvas, key: 'ticker' }))
      .finally(() => {
        typeof undoShowSelectedMultipleTickerTable === 'function' &&
          undoShowSelectedMultipleTickerTable();
      });

    return Promise.all([canvas1, canvas2])
      .then((values) => {
        const canvasWrapper = generateCanvasExport(values, tickerTableDom);
        return new Promise((resovle) => {
          canvasWrapper.toBlob(function (blob) {
            var file = new File([blob], 'share-graph.jpg', { type: 'image/jpeg' });
             resovle(file);
          });
        });
      }).catch((err => {
        console.log('Error extractScreenshot: '+ err);
        //cb.call(this, null);
        return null;
      }));
  };

  function generateCanvasExport (values, tickerTableDom) {
    const isRtl = window.appSettings?.isRtl;
    const tickerCanvas = values.find(x => x.key === 'ticker').canvas;
    const graphCanvas = values.find(x => x.key === 'graph').canvas;
    const canvasWrapper = document.createElement('canvas');
    let offsetLeft = 0;
    const paddingTop = 30;
    const paddingLeft = 30;
    const gap = 30;
    const preivewImageDimension = 1200/628; // preview social image
    canvasWrapper.width =  graphCanvas.width + paddingLeft;
    canvasWrapper.height = tickerCanvas.height + graphCanvas.height + gap + paddingTop;
    if (canvasWrapper.width/canvasWrapper.height < preivewImageDimension) {
      const widthExtended = canvasWrapper.height*preivewImageDimension;
      offsetLeft = (widthExtended - canvasWrapper.width)/2;
      canvasWrapper.width = widthExtended;
    }
    var canvasWrapperContext = canvasWrapper.getContext('2d');
    let tickerCanvasX = isRtl ? canvasWrapper.width - paddingLeft/2 - offsetLeft - tickerCanvas.width : paddingLeft/2 + offsetLeft;
    let tickerCanvasWidth = tickerCanvas.width;

    if(tickerTableDom || tickerCanvas.width > graphCanvas.width){
      tickerCanvasWidth = graphCanvas.width;
      tickerCanvasX = paddingLeft/2 + offsetLeft;
    }

    canvasWrapperContext.fillStyle = '#fff';
    canvasWrapperContext.fillRect(0, 0 , canvasWrapper.width, canvasWrapper.height);
    canvasWrapperContext.drawImage(tickerCanvas, tickerCanvasX, paddingTop/2, tickerCanvasWidth, tickerCanvas.height);
    canvasWrapperContext.drawImage(graphCanvas, offsetLeft + paddingLeft/2, tickerCanvas.height + gap + paddingTop/2);
    return canvasWrapper;
  }


  const filter = (node)=>{
    const exclusionClasses = ['switcher', 'additional-options', 'spinner', 'spin-loader'];
    if(node instanceof HTMLElement) {
      const styleList = getComputedStyle(node);

      if(node.nodeName === 'CQ-LOADER') return false;

      if(styleList.display === 'none') return false;
      if(styleList.visibility === 'hidden') return false;
      if(styleList.opacity === '0') return false;

    }

    return !exclusionClasses.some(classname=> {
      if (node.classList) {
        return node.classList.contains(classname);
      }
    });
  };

  /**
   * Creates the integration component that represent for social share dialog.
   * @param {{ shareUrl, thumbnailUrl }} props List of prop values to pass to dialog/popup window
   * @returns {window.euroland.components.ShareDialogComponent}
   */
  function createShareComponent(props) {
    return integration.components.ShareDialogComponent(props);
  }

  function openShareDialog(thumbnailUrl) {
    return new Promise((resolve, reject) => {
      try {
        const integrationLayoutPosition = window.xprops ? window.xprops.layout.middle : '#middleLayout';
        const component = createShareComponent({
          shareUrl: 'https://myshare.com',
          thumbnailUrl: thumbnailUrl,
          onClose: () => {
            exportBtnRef.current?.focus();
            if (!isIFrame && document.getElementById('middleLayout')) {
              document.getElementById('middleLayout').remove();
            }
          },
          onRendered: () => {
            console.log('Share Dialog has completed its full render');
          }
        });

        if (isIFrame) {
          component.renderTo(window.parent, integrationLayoutPosition);
        } else {

          let middle = document.getElementById('middleLayout');
          if (!middle) {
            middle = document.createElement('div');
            middle.id = 'middleLayout';
            document.body.appendChild(middle);
          }
          component.renderTo(window.parent, integrationLayoutPosition);
        }

        resolve(component);
      } catch (e) {
        reject(e);
      }
    });

  }

  function ensureIntegrationComponent() {
    return new Promise((resolve, reject) => {
      if (!integration) {
        console.warn('The integration object \'window.euroland\' or \'window.xprops\' is \'null\' or \'undefined\'. Check whether the "integration.js" is loaded properly.');
        reject('The integration object \'window.euroland\' or \'window.xprops\' is \'null\' or \'undefined\'. Check whether the "integration.js" is loaded properly.');
      } else {
        resolve();
      }
    });

  }

  useEffect(() => {
    if (!refDialog.current) return;
    const chart = refDialog.current.closest('.graph');
    const dialog = refDialog.current.querySelector('.dialog');
    const bottom = (chart.offsetHeight - dialog.offsetHeight) / 2;
    const left = (chart.offsetWidth - dialog.offsetWidth) / 2;
    var elementStyle = {
      bottom: bottom + 'px',
      left: left + 'px'
    };
    Object.assign(dialog.style, elementStyle);
  }, [showDialogSoicialSharing]);

  return (
    <>
      <button
        ref={exportBtnRef}
        className="tooltip-wrapper option-button option-button--share"
        onClick={handleClickShareDialog}
        aria-labelledby="shareChartBtn"
      >
        <i aria-hidden="true" className="fs fs-icon-share"></i>
        <ToolTip aria-hidden="true" id="shareChartBtn">
          {i18n.translate('shareOption')}
        </ToolTip>
      </button>
    </>
  );
}
