import axios from 'axios';
import { getBearerToken } from '../utils/auth';
import type { Instrument, Watchlist } from './watchlistTypes';

class WatchlistService {
  private baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:5111';

  private getHeaders() {
    return {
      'Authorization': `Bearer ${getBearerToken()}`,
      'Content-Type': 'application/json'
    };
  }

  private handleError(error: unknown, defaultMessage: string): never {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || `${defaultMessage}: ${error.message}`;
      throw new Error(errorMessage);
    }
    throw error;
  }

  async fetchWatchlists() {
    try {
      const response = await axios.get(`${this.baseUrl}/api/watchlist/find-all`, {
        headers: this.getHeaders()
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, 'Failed to fetch watchlists');
    }
  }

  async addInstrument(instrumentId: number, watchlistId: string): Promise<Instrument> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/WatchList/add-detail`,
        { instrumentIds: [instrumentId], watchlistId },
        { headers: this.getHeaders() }
      );
      
      return response.data;
    } catch (error) {
      this.handleError(error, 'Failed to add instrument');
    }
  }

  async removeInstrument(watchlistId: string, instrumentId: number): Promise<void> {
    try {
      await axios.delete(`${this.baseUrl}/api/Watchlist/remove-detail`, {
        params: {
          WatchListId: watchlistId,
          Id: instrumentId.toString()
        },
        headers: {
          'Authorization': `Bearer ${getBearerToken()}`
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to remove instrument');
    }
  }

  async createWatchlist(name: string): Promise<Watchlist> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/WatchList/add`,
        { name },
        { headers: this.getHeaders() }
      );
      
      return response.data;
    } catch (error) {
      this.handleError(error, 'Failed to create watchlist');
    }
  }

  async updateWatchlist(id: string, name: string): Promise<Watchlist> {
    try {
      const response = await axios.put(
        `${this.baseUrl}/api/WatchList/update`,
        { name, id },
        { headers: this.getHeaders() }
      );
      
      return response.data;
    } catch (error) {
      this.handleError(error, 'Failed to update watchlist');
    }
  }

  async deleteWatchlist(id: string): Promise<void> {
    try {
      await axios.delete(`${this.baseUrl}/api/WatchList/delete/${id}`, {
        headers: {
          'Authorization': `Bearer ${getBearerToken()}`
        }
      });
    } catch (error) {
      this.handleError(error, 'Failed to delete watchlist');
    }
  }
}

export const watchlistService = new WatchlistService(); 