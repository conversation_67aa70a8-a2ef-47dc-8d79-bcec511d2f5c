import dayjs from 'dayjs';
import { appSettings } from '../../appSettings';
import appConfig from './app-config';
import fetchApi from './fetch-api';
import { formatWithDateTime } from '../helper';
import { CIQ } from '../../scripts/components/chart/chartiq-import';
import {client, clientRT} from './graphql-client';
import {DIVIDEND_EVENT_QUERY} from '../graphql-queries/dividendEventsQuery';
import {PRESS_RELEASE_PAGINATION_QUERY, PRESS_RELEASE_QUERY} from '../graphql-queries/pressReleasesQuery';
import {EARNING_EVENTS_QUERY} from '../graphql-queries/earningEventsQuery';
import {COMPANY_QUERY} from '../graphql-queries/companyQuery';
import { CURRENCIES_BY_CODE_QUERY} from '../graphql-queries/currencyQuery';
import {STOCK_OVERVIEW_QUERY} from '../graphql-queries/stockOverviewQuery';
import { WEBCASTS_QUERY } from '../graphql-queries/webcastsQuery';
import { systemLanguagesPromise} from './systemLanguages';
function getTickerMock() {
  const API_URL = '../assets/data/ticker.json';

  return fetch(API_URL, {
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => response.json());
}

function getSettingMock() {
  const API_URL = '../assets/data/setting.json';

  return fetch(API_URL, {
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => response.json());
}

export function getAppData(instrumentIds) {
  if (appSettings.useMockData) {
    return getTickerMock();
  }
  const versionParam = appSettings.companyCodeVersion ? `&v=${appSettings.companyCodeVersion}` : '';
  const SHARE_DATA_API_URI =
    appSettings.sDataApiUrl +
    `?companyCode=${appSettings.companyCode}&lang=${appSettings.language}${versionParam}`;
  return fetch(SHARE_DATA_API_URI, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
          instruments(instrumentIds: [${instrumentIds.join(',')}]){
            instrumentId
            marketName
            volumeChange
            ticker
            tickerName
            low52W
            high52W
            currencyCode
            last
            open
            high
            low
            high52W
            change
            changePercentage
            bid
            ask
            volume
            lastUpdatedDate
            percent52W
            marketAbbreviation
            marketStatus
            symbol
          }
        }`
    })
  }).then(res => res.json());
}

export function getSetting() {
  if (appSettings.useMockData) {
    return getSettingMock();
  }
  const versionParam = appSettings.companyCodeVersion ? `&v=${appSettings.companyCodeVersion}` : '';
  const isPreviewParam = appSettings.isPreview ? '&isPreview' : '';
  const GRAPH_API_URL =
    appSettings.shareGraphApiUrl +
    `?companyCode=${appSettings.companyCode}&lang=${appSettings.language}${versionParam}${isPreviewParam}`;
  return fetch(GRAPH_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: /* GraphQL */`query{
          configuration{
            setting{
              useLatinNumber
              usePrevCloseDayForMcap
              tickerRefreshSeconds
              googleTagEnabled
              currencies{
                enabled
                currencies{
                  code
                  text
                  fullName
                  decimalDigits
                }
              }
              colorBlindMode {
                enabled
                defaultSelected
              }
              videoSetting{
                videoType
              }
              accessibilities{
                enabled
                timeZone
                formats{
                  calendarDate
                  tickerDateTimeFormat
                }
              }
              styleURI
              timeZone
              layout
              customPhrases
              companyLogo{
                path
              }
              companyName{
               cultureCode
               value
              }
              instruments{
                enabledAdjustPrice
                id
                default
                color
                order
                tickerName
                ticker
                shareName
                currencyCode
                marketName
                marketAbbreviation
                customTimeZone
                pressRelease{
                  sourceFilter
                }
                isRT
                symbol
                decimalDigits
              }
              pressReleases{
                languageForwarding
                typeFilter
                excludedTypeFilter
                openAs
                newPageUrlPattern
              }
              ticker{
                enabledFormat
                graphTickerTemplate
                graphTickerType
                tableTickerTemplate
                tableTickerType
                tickerType
                slidesPerView
                tableAnimation
                graphAnimation
              }
              peers{
                enabled
                animation
                peers{
                  id
                  color
                  order
                  tickerName
                  shareName
                  currencyCode
                  ticker
                  isRT
                  symbol
                  decimalDigits
                  marketAbbreviation
                  enabledAdjustPrice
                }
              }
              performance{
                enabledFormats
                performanceTypes
                enable52WTableColumns
                numberOfYearSPByYear
                showEarliestYearFirstSPByYear
                enableSharePriceDevelopmentColumns
                excludeIds
              }
              chart {
                defaultTooltipType
                defaultChartType
                enabledChartTypes
                excludeStudies
                enabledEvents
                defaultEvents
                enabledChartPreferences
                enabledYAxisPreferences
                enabledPeriods
                defaultPeriod
                hideChartTitle
                enabledHoverEvent
                enabledAdditionalOptions
                enabledSocialMedias
                highPriceIndicatorColor
                lowPriceIndicatorColor
                isStripedBackground
                defaultVolumeChart
                blinkingPricePointer {
                  enabled
                }
                excelDownloadOption {
                  includedTotalReturn
                  includedSelectedPeersAndIndicies
                }
                studies{
                  rSI{
                    rSI
                  }
                  mACD{
                    mACD
                    signal
                    increasingBar
                    decreasingBar
                  }
                  bollingerBands{
                    bollingerBandsTop
                    bollingerBandsMedian
                    bollingerBandsBottom
                  }
                  stochastics {
                    k
                    d
                    overBought
                    overSold
                  }
                  ichimokuClouds {
                    conversionLine
                    baseLine
                    leadingSpanA
                    leadingSpanB
                    leadingSpan
                  }
                  totalReturn{
                    tTRT
                  }
                  aDX{
                    diPlus
                    diMinus
                    aDX
                    positiveBar
                    negativeBar
                  },
                  volume{
                    downVolumeColor
                    upVolumeColor
                  }
                  volumeUnderlay{
                    downVolumeColor
                    upVolumeColor
                    yAxisHeightFactor
                  }
                }
              }
              shareDetails {
                enabled
                shareDataItems
                displayOnSelectedTicker
                displayType
                partialDisplay
                numberItemsInCollapsedMode
                numberItemsInPrinting
                print
                numberOfShareDisplay
                marketCapDisplay
                turnOverDisplay
              }
              indices{
                enabled
                animation
                indices{
                  id
                  color
                  order
                  shareName
                  tickerName
                  ticker
                  currencyCode
                  isRT
                  symbol
                  decimalDigits
                  marketAbbreviation
                  enabledAdjustPrice
                }
              }

              format{
                tickerDateTimeFormat
                shortDate
                timeFormat
                longDate
                decimalDigits
                percentDigits
                decimalSeparator
                thousandsSeparator
                negativeNumberFormat
                positiveNumberFormat
                positiveChangeFormat
              }

              trade{
                enabled
                numberOfTrades
                timeFormat
                tradeRefreshSeconds
              }

              orderDepth {
                enabled
                calculateBy
                refreshSeconds
              }

            }
          }
        }`
    })
  }).then(res => res.json());
}

export function getShareDetailFieldsCustom() {
  const API_URL = '../assets/data/shareDetailFieldCustom.json';
  return fetch(API_URL, {
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => response.json());
}

/**
 *
 * @param {string} strFilter
 * @returns {Array<Array<string>>}
 */
const parsePressReleaseSetting = (strFilter) => strFilter
.toLowerCase()
.split(';')
.map(item => item.trim())
.filter(Boolean)
.map(item => item.split('|'));

export async function fetchPressReleaseDataPaging({ins, companyCode, from, to, isRT, cursor, signal}) {
  companyCode = companyCode || appSettings.companyCode;
  const settingsXML = appConfig.get();
  const lang = appSettings.language || 'en-gb';

  const includeMessages = parsePressReleaseSetting(
    settingsXML.pressReleases.typeFilter || ''
  ).map((item) => ({
    messageTypeId: { eq: parseInt(item[1]) },
    sourceId: { eq: parseInt(item[0]) }
  }));
  const includeMessagesCondition = includeMessages?.length > 0 ? [{
    or: includeMessages
  }] : [];
  const excludeMessages = parsePressReleaseSetting(
    settingsXML.pressReleases.excludedTypeFilter || ''
  ).map((item) => ({
    or: [
      { sourceId: { neq: parseInt(item[0]) } },
      { messageType: { id: { neq: parseInt(item[1]) } } }
    ]
  }));

  const instrumentSetting = settingsXML.instruments.find((x) => x.id === ins);
  const prSources = Object.prototype.hasOwnProperty.call(
    instrumentSetting,
    'pressRelease'
  )
    ? instrumentSetting.pressRelease.sourceFilter
    : '';

  const systemLanguages = await systemLanguagesPromise;

  const handleOrCondition = (item) => {
    const [sourceId, sourceLang, uiLang] = item;
    return [
      {languageId: { eq: systemLanguages.getCultureByCode(uiLang).id}},
      sourceId && sourceLang &&{languageId: { eq: systemLanguages.getCultureByCode(sourceLang).id}, sourceId: { eq: parseInt(sourceId) }}
    ].filter(Boolean);
  };

  const defaultOrCondition = parsePressReleaseSetting(`||${lang}`).map(handleOrCondition).flat();
  const orCondition = parsePressReleaseSetting(
    settingsXML.pressReleases.languageForwarding || ''
  )
    .filter((item) => item[item.length - 1] === lang)
    .map(handleOrCondition).flat();

  return await (isRT ? clientRT : client).query(PRESS_RELEASE_PAGINATION_QUERY, {
    companyCode,
    fromDate: formatWithDateTime(from),
    toDate: formatWithDateTime(to),
    orCondition: orCondition.length > 0 ? orCondition : defaultOrCondition,
    cursor,
    andCondition: includeMessagesCondition
    .concat(excludeMessages)
    .concat(prSources ? [{sourceId: { in: prSources.split(',').map(item => parseInt(item)) }}] : [])
  }, { fetchOptions: { signal }});
}

//earning
async function fetchEarningData(companyCode, from, to, isRT) {
  companyCode = companyCode || appSettings.companyCode;
  const url = new URL(appSettings.sDataApiUrl);

  const  result = await (isRT ? clientRT : client).query(EARNING_EVENTS_QUERY, {companyCode, fromDate: formatWithDateTime(from), toDate: formatWithDateTime(to) });

  return {
    data: {
      earningsEvents: result.data.company.fcEventsByTypes.nodes.map(item => ({
        date: item.dateTime,
        heading: item.eventName
      }))
    }
  };
}

//video
function fetchVideoData(companyCode, from, to) {
  companyCode = companyCode || appSettings.companyCode;
  return client.query(WEBCASTS_QUERY, { companyCode, from, to});
}

export function setEarningData(companyCode, stx, suggestedStartDate, suggestedEndDate) {
  return fetchEarningData(companyCode, suggestedStartDate, suggestedEndDate, stx.chart.query.isRTSelectedInstrumentId).then(res => {
    var oldEarnings = stx.chart.earningData || [];
    var newEarningFormat = res.data.earningsEvents.map(x => {
      return { Date: new Date(x.date), ...x };
    });
    var newEarning = newEarningFormat.filter(
      x => oldEarnings.find(y => y.Date.getTime() === x.Date.getTime() && x.heading === x.heading) === undefined
    );
    const setEarning = [...oldEarnings, ...newEarning];
    stx.chart.earningData = setEarning;
    return setEarning;
  });
}

export function setVideoData(companyCode, stx, suggestedStartDate, suggestedEndDate) {
  return fetchVideoData(companyCode, suggestedStartDate, suggestedEndDate).then(res => {
    const oldVideos = stx.chart.videoData || [];
    const newVideoFormat = res.data.webcasts.filter(x => {
      const date = dayjs(x.date);
      return date.isValid() && !date.isBefore(suggestedStartDate) && !date.isAfter(suggestedEndDate);
    }).map(x => {
      return {  ...x, Date: new Date(x.date) };
    });
    const newVideo = newVideoFormat.filter(
      x => oldVideos.find(y => y.Date.getTime() === x.Date.getTime() && x.heading === x.heading) === undefined
    );
    const setVideo = [...oldVideos, ...newVideo];
    stx.chart.videoData = setVideo;
    return setVideo;
  });
}

// dividend
export async function fetchDividendData(instrumentId, from, to, isRT) {

  const data = await (isRT ? clientRT : client).query(DIVIDEND_EVENT_QUERY, { id: instrumentId,  fromDate: formatWithDateTime(from), toDate: formatWithDateTime(to) });

  return {
    data: {
      dividendEvents: data.data.instrumentById?.dividends.nodes.map(item => ({
        dividend: item.grossDivAdj,
        date: item.exDate,
        currency: item.currency
      })) ?? []
    }
  };
}

export function setDividendData(ins, stx, suggestedStartDate, suggestedEndDate) {
  const promise = new Promise(function (resolve, reject) {
    resolve([]);
  });
  //const selectedInstrumentId = !stx.chart.masterData ? ins : stx.chart.masterData[0].instrumentId;
  //only get dividend for main chart
  // if(ins !== selectedInstrumentId) return promise;
  const isRT = stx.chart.query.isRTSelectedInstrumentId;
  return fetchDividendData(ins, suggestedStartDate, suggestedEndDate, isRT).then(res => {
    var oldDividends = stx.chart.dividendData || [];
    if (oldDividends.length > 0 && oldDividends[0].instrumentId !== ins) {
      oldDividends = [];
    }
    var newDividendFormat = res.data.dividendEvents.map(x => {
      return { Date: new Date(x.date), instrumentId: ins, ...x };
    });
    var newDividend = newDividendFormat.filter(
      x => oldDividends.find(y => y.Date.getTime() === x.Date.getTime() && x.dividend === x.dividend) === undefined
    );
    const setDividend = [...oldDividends, ...newDividend];
    stx.chart.dividendData = setDividend;
    return setDividend;
  });
}

export function uploadFile(file) {
  if (!file) {
    new Promise(resolve => {
      resolve(null);
    });
  }
  const getForgeryToken =() => {
    const xsrfStorage = document.querySelector('[data-xsrf-token]');
    if(xsrfStorage) {
      return xsrfStorage.dataset.xsrfToken;
    }

    return null;
  };

  const companyCode = appSettings.companyCode;
  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;
  const url = `${baseUrl}share/make-screenshot?companycode=${companyCode}`;
  const forgeryToken = getForgeryToken();
  const data = new FormData();
  data.append('file', file);

  if(!forgeryToken) {
    //console.warn('Not found cookie named XSRF-TOKEN. This could lead to screenshot upload feature may not work');
    return new Promise((resolve, reject) => {
      reject('Not found XSRF-TOKEN. This token is used to prevent Cross-site Request Forgery attacks and missing it could lead to social sharing feature may not work');
    });
  }

  return fetchApi(url, { method: 'POST', headers: { 'XSRF-TOKEN': (forgeryToken || '') }, body: data })
    .then(res => res.json())
    .then(res => {
      if (!res) return null;
      return res.url;
    })
    .catch((e) => {
      console.log('Error occurs while uploading screenshot', e);
      return null;
    });
}

export function fetchStockOverview (instrumentId, {
  period,
  from,
  to,
  toCurrency
}) {
  return client.query(STOCK_OVERVIEW_QUERY, {instrumentId, period, from, to, toCurrency});
}

export function getCompany(companyCode) {
  return client.query(COMPANY_QUERY, { code: companyCode});
}

export function getCurrencies(companyCodes = []) {
  return client.query(CURRENCIES_BY_CODE_QUERY, { companyCodes });
}
