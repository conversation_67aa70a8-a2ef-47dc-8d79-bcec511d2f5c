import {useMemo} from 'react';
import usePeers from '../../customHooks/usePeers';
import i18n from '../../services/i18n';
import AccessibleCheckbox from './AccessibleCheckbox';
import TickerName from '../TickerName';

const PeerCheckbox = ({ value = [], onChange }) => {
  const { peers } = usePeers();
  const options = useMemo(
    () =>
      peers.map((item) => ({
        ...item,
        title: (
          <TickerName
            marketAbbreviation={item.marketAbbreviation}
            shareName={item.shareName}
            instrumentId={item.instrumentId}
          />
        )
      })),
    [peers]
  );

  if(!peers?.length) return null;

  return (
    <>
      <h4 id="selectpeers">{i18n.translate('selectPeers')}</h4>
      <AccessibleCheckbox id="cklPeers" options={options} value={value} onChange={onChange} />
    </>
  );
};

export default PeerCheckbox;
