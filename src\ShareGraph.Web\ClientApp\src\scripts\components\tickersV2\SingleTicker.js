import { useRef, useContext, useEffect, useLayoutEffect, useCallback, useState } from 'react';
import { AppContext } from '../../AppContext';
import { default as htmlGraphTemplate } from '../../../markup/ticker/SINGLE_TICKER_1.html'; // eslint-disable-line
import { default as htmlTableTemplate } from '../../../markup/ticker/TABLE_TICKER_SINGLE.html'; // eslint-disable-line
import {
  replaceKey, formatDateTime, convertNumber, convertNumberDecimal,
  getCustomPhraseTicker, convertPercentDecimal, convertMinutesToString, classByValue,
  marketStatusByValue, resetClasslist, getOpenDateTimeByMinute, getTimeZoneDisplay, getCountDownMinutesOpenMarket, translateStringFormat, formatChangeNumber, convertChangePercentDecimal,
  formatNumberDisplay,
  formatShortDateAsUTC
} from '../../helper';
import i18n from '../../services/i18n';
import { isArray } from '../../utils';
import { TICKER_SWITCH_TYPE, TICKER_ANIMATION } from '../../common';
import { usePrevious } from '../../customHooks/index';
import {useDelay} from '../../customHooks/useDelayUpdate';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import useWatchChange from './useWatchChange';
import {classNames} from '@euroland/libs';
import useDelayLoading from '../../customHooks/useDelayLoading';
import useRefreshing from './useRefreshing';
import useIntervalForceRender from '../../customHooks/useIntervalForceRender';
import useResize from '../../customHooks/useResize';
import useTickerLabels from './useTickerLabels';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import { useSelectedTicker } from '../../customHooks/useTickers';

export const SingleTicker = ({ data: _data, tickerFormat }) => {

  const selectedTicker = useSelectedTicker();
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER );
  const containerRef = useRef(null);
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const prevData = usePrevious(data);
  const settings = useContext(AppContext);
  const { width } = useResize();
  const { getLabelsByTemplate } = useTickerLabels();
  const { formatNumberByInstrument, formatNumberChangeByInstrument } = useFormatNumberByInstrument();

  const measureContainerTableRef = useCallback(node => {
    // Check table has scroll or not
    if (node !== null) {
      const hasScroll = node.scrollWidth > node.clientWidth;

      setHasHorizontalScroll(hasScroll);
    }
  }, [width]);

  const isTickerTableFormat = tickerFormat.toUpperCase() === TICKER_SWITCH_TYPE.TABLE_TYPE;
  const tickerData = data[0];
  const refreshing = useRefreshing(tickerData);
  const [delayLoading] = useDelayLoading(refreshing);
  const isRtl = window.appSettings.isRtl;
  const fieldChanged = useWatchChange(data[0], ['bid', 'ask', 'last', 'open', 'high', 'low', 'volume']);
  const tickerAnimation = tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE ? (settings.ticker.graphAnimation || 'fade').toLowerCase() :
                (settings.ticker.tableAnimation || 'fade').toLowerCase();

  const {turnOverDisplay,marketCapDisplay,numberOfShareDisplay} = settings?.shareDetails;


  useIntervalForceRender('minus', tickerData.isRT);
  useLayoutEffect(() => {
    if (isArray(data) && data.length) {
      changeStyle();
    }
  });

  useEffect(() => {
    if (prevData) {
          const instrumentId = tickerData.instrumentId;
          let prevItem = prevData
            ? prevData.find((e) => e.instrumentId == instrumentId)
            : null;
          let currentItem = data
            ? data.find((e) => e.instrumentId == instrumentId)
            : null;
          isTickerTableFormat ? applyAnimationTable() : applyAnimationGraph(prevItem, currentItem);
    }
  });

  const addAnimation = (type, fieldChange, item) => {
        if (prevData) {
          let prevCurrentData = prevData?.find(e => e.instrumentId == tickerData.instrumentId);
          if (!tickerData || !prevCurrentData) return;
          let prevItem = _normalizeData(prevCurrentData);
          let currentItem = _normalizeData(tickerData);
          //add animation for change
          let changeValue = tickerData[fieldChange] - prevCurrentData[fieldChange];
          const queryType = `.ticker__change--${type}`;
          const queryChangeValue = queryType + ' .ticker__change-value';
          const queryAnimation = queryType + ' .ticker__animation-inner';
          const queryTransform = `transforming-top-${type}`;
          item.querySelector(queryChangeValue).innerText = prevItem[fieldChange];
          const cloneChangeNode = item.querySelector(queryChangeValue).cloneNode();
          cloneChangeNode.innerText = currentItem[fieldChange];
          if (item.querySelectorAll(`${queryChangeValue}`).length < 2) {
            item.querySelector(`${queryAnimation}`).append(cloneChangeNode);
          }
          changeValue >= 0 ? item.classList.add(`${queryTransform}`, `${queryTransform}--increase`) : item.classList.add('transforming-top', `${queryTransform}--decrease`);
          setTimeout(() => {
            item.classList.remove(`${queryTransform}`, `${queryTransform}--increase`, `${queryTransform}--decrease`);
            item.querySelectorAll(`${queryChangeValue}`).length >= 2 && item.querySelector(`${queryChangeValue}:first-child`).remove();
          }, 1000);

      }
  };

  const applyAnimationTable = () => {
      const item = containerRef.current.querySelector('.table__body-tr');
      if (!item) return;
      if (tickerAnimation.toUpperCase() === TICKER_ANIMATION.TRANSFORM) {
        addAnimation('number', 'change', item);
        addAnimation('percent', 'changePercentage', item);
      }
    };


  const applyAnimationGraph = (prevItem, currentItem) => {

      const item = containerRef.current;
      if (!item) return;
      let lastValue = currentItem.last - prevItem.last;
      item.classList.remove('animate--increase', 'animate--decrease', 'animate--neutral' );
      if (lastValue === 0){
        item.classList.add('animate--neutral');
        return;
      }
      lastValue > 0 ? item.classList.add('animate--increase') : item.classList.add('animate--decrease');
  };

  function changeStyle() {
    const { percent52W, low52W, high52W, volumeChange, change, last, high, low, changePercentage, marketStatus } = tickerData;
    resetClasslist(containerRef.current, ['indicator-', 'market-']);
    if (isTickerTableFormat) {
      const element = containerRef.current.querySelector('.table__body-tr');
      if (!element) return;
      element.classList.add(`day-range--${classByValue(changePercentage)}`, marketStatusByValue(marketStatus));
      if(classByValue(change)) element.classList.add(classByValue(change));
    }
    else {
      containerRef.current.classList.add( `day-range--${classByValue(changePercentage)}`,
        `w52-range--${classByValue(percent52W)}`, `volume--${classByValue(volumeChange)}`, marketStatusByValue(marketStatus));
      if(classByValue(change)) containerRef.current.classList.add(classByValue(change));
      let percentDayRange = _calCulatePercent(last, low, high);
      let percent52WRange = _calCulatePercent(last, low52W, high52W);
      let dayRangeElement = containerRef.current.querySelector('.day-range .slider__rectangle .fs-triangle-down');
      let dayRangeBarElement = containerRef.current.querySelector('.day-range .slider__rectangle .slider__rectangle-bar');
      if (dayRangeElement)
        dayRangeElement.style[`${isRtl ? 'right' : 'left'}`] = percentDayRange + '%';
      if (dayRangeBarElement){
        dayRangeBarElement.style['width'] = percentDayRange + '%';
      }
      let w52RangeElement = containerRef.current.querySelector('.w52-range .slider__rectangle .fs-triangle-down');
      let w52RangeBarElement = containerRef.current.querySelector('.w52-range .slider__rectangle .slider__rectangle-bar');
      if (w52RangeElement)
        w52RangeElement.style[`${isRtl ? 'right' : 'left'}`] = percent52WRange + '%';
      if (w52RangeBarElement){
        w52RangeBarElement.style['width'] = percent52WRange + '%';
      }
    }

  }

  function _getTemplateHtml() {
    return isTickerTableFormat ? settings.ticker.tableTickerTemplate || htmlTableTemplate : settings.ticker.graphTickerTemplate || htmlGraphTemplate;
  }

  function _renderTemplate(item) {
    if (isArray(item) && item.length) {
      item = item[0];
      const tickerHTML = _getTemplateHtml();
      if (isTickerTableFormat) {
        const tableHTMLDOM = new DOMParser().parseFromString(tickerHTML, 'text/xml');
        const tableRowHTML = tableHTMLDOM.querySelector('.table__body-tr');
        if (!tableRowHTML) return '<h2>Table HTML template not recognized</h2>';
      }
      const labels = getLabelsByTemplate(tickerHTML);
      const dataTicker = _normalizeData(item);
      const dataTemplate = { ...dataTicker, ...labels };
      return replaceKey(tickerHTML, dataTemplate);
    }

    return '<h2> No data</h2>';

  }

  function _normalizeData(item) {
    const data = { ...item };
    const countDownMinutes = getCountDownMinutesOpenMarket(item);
    data.bid = formatNumberByInstrument(item.bid, item.instrumentId);
    data.ask = formatNumberByInstrument(item.ask, item.instrumentId);
    data.change = formatNumberChangeByInstrument(item.change);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = formatNumberByInstrument(Math.max(item.high, item.last), item.instrumentId);
    data.high52W = formatNumberByInstrument(Math.max(item.high52W, item.last), item.instrumentId);
    data.last = formatNumberByInstrument(item.last, item.instrumentId);
    data.low = formatNumberByInstrument(Math.min(item.low, item.last), item.instrumentId);
    data.low52W = formatNumberByInstrument(Math.min(item.low52W, item.last), item.instrumentId);
    data.open = formatNumberByInstrument(item.open, item.instrumentId);
    data.percent52W  = convertPercentDecimal(item.percent52W );
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatNumberChangeByInstrument(item.volumeChange);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.timeToCloseMarket = `${convertMinutesToString(countDownMinutes, i18n.translate('hrs'), i18n.translate('mins'))} ${getTimeZoneDisplay(item.id, selectedTicker?.timezoneIANA)} `;
    data.dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, item.id, selectedTicker?.timezoneIANA);
    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';
    data.todayTurnover = formatNumberByInstrument(
      formatNumberDisplay(item.todayTurnover, turnOverDisplay), item.instrumentId
    );
    data.marketCap = formatNumberByInstrument(
      formatNumberDisplay(item.marketCap, marketCapDisplay), item.instrumentId
    );
    data.noShares = convertNumber(
      formatNumberDisplay(item.noShares, numberOfShareDisplay),
      { decimalDigits: 0 }
    );
    data.lowYtdDate = formatShortDateAsUTC(item.lowYtdDate);
    data.highYtdDate = formatShortDateAsUTC(item.highYtdDate);
    data.highest52wDate = formatShortDateAsUTC(item.highest52wDate);
    data.lowest52wDate = formatShortDateAsUTC(item.lowest52wDate);
    data.allTimeHighDate = formatShortDateAsUTC(item.allTimeHighDate);
    data.allTimeLowDate = formatShortDateAsUTC(item.allTimeLowDate);

    data.allTimeHigh = formatNumberByInstrument(item.allTimeHigh, item.instrumentId);
    data.allTimeLow = formatNumberByInstrument(item.allTimeLow, item.instrumentId);
    data.lowYtd = formatNumberByInstrument(item.lowYtd, item.instrumentId);
    data.highYtd = formatNumberByInstrument(item.highYtd, item.instrumentId);
    data.percentYtd = convertChangePercentDecimal(item.percentYtd);
    data.totalTrades = convertNumber(item.totalTrades);
    data.prevClose = formatNumberByInstrument(item.prevClose, item.instrumentId);
    data.lotSize = convertNumber(item.lotSize);
    data.pE = formatNumberByInstrument(item.eps === 0 ? 0 : item.last / item.eps, item.instrumentId);
    data.averagePrice = formatNumberByInstrument(item.averagePrice, item.instrumentId);
    data.askSize = convertNumber(item.askSize);
    data.bidSize = convertNumber(item.bidSize);



    //getTimeZoneDisplay();
    return getCustomPhraseTicker(data);
  }

  function _calCulatePercent(currValue, min, max) {
    let percent = 0;
    if (currValue < min) {
      percent = 0;
    }
    else if (currValue > max) {
      percent = 100;
    } else {
      percent = ((currValue - min) / (max - min)) * 100;
    }
    return percent;
  }

  return (
    <>
    {
      tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE &&
      <>
      <div
        data-change={fieldChanged.join(',')}
        ref={containerRef}
        className={classNames(`ticker__inner ticker__inner--${tickerAnimation}`, {
          streaming: data?.[0].isRT,
          loading: delayLoading
        })}
        dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}
      ></div>
      </>
    }

    {
      tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE &&
       <>
        <div className={classNames(`ticker-table ticker-table-v2 table-responsive table-responsive--${tickerAnimation}`,
        { 'scrolled-table': hasHorizontalScroll }
        )}
          ref={measureContainerTableRef}
        >
          <div ref={containerRef} className={`ticker__inner ticker__inner--${tickerAnimation}`} dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}>
          </div>
        </div>
      </>
     }
    </>
  );
};
