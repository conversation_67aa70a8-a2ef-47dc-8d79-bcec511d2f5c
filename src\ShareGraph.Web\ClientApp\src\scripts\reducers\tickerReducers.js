import produce from 'immer';

import {
  <PERSON><PERSON><PERSON>_TICKERS_BEGIN,
  <PERSON><PERSON>CH_TICKERS_SUCCESS,
  <PERSON>ET<PERSON>_TICKERS_FAILURE,
  REFRESH_TICKER_BEGIN,
  REFRESH_TICKER_SUCCESS,
  REFRESH_TICKER_FAILURE,
  TICKER_SELECTED_CHANGE_SUCCESS
} from '../actions/tickerActions';
import {calcPriceChange} from '../utils';
import {UPDATE_STOCK_DATA, UPDATE_TICKER_OFFICIAL_CLOSE} from '../actions';
import dayjs from '../utils/dayjs';
import {getLoadStrategy} from '../helper';

const initialState = {
  instruments: [],
  selectedInstrumentId: null,
  updateStrategy: /** @type {import('../../../types/configuration-app').IUpdateStrategy} */({}),
  loading: true,
  refreshing: false,
  fetchError: null,
  refreshError: null,
  refreshStarted: false
};

export default function createTickerReducer(instrumentIds = []) {
  initialState.instruments = instrumentIds.map((i) => {
    return {
      instrumentId: i
    };
  });

  initialState.updateStrategy = instrumentIds.reduce((acc, item) => {
    acc[item] = 'fetch';
    return acc;
  }, {});
  return function tickerReducer(state = initialState, action) {
    switch (action.type) {
      case FETCH_TICKERS_BEGIN:
        return produce(state, draft => {
          draft.loading = true;
          draft.fetchError = null;
        });
      case FETCH_TICKERS_SUCCESS:
        return produce(state, draft => {
          draft.loading = false;
          const instruments = action.payload.instruments;
          draft.instruments = instruments;
          instruments.forEach(item => {
            draft.updateStrategy[item.id] = getLoadStrategy(item);
          });
        });
      case FETCH_TICKERS_FAILURE:
        return produce(state, draft => {
          draft.loading = false;
          draft.fetchError = action.payload.error;
        });
      case TICKER_SELECTED_CHANGE_SUCCESS:
        return produce(state, draft => {
          draft.selectedInstrumentId = action.payload.instrumentId;
        });
      case REFRESH_TICKER_BEGIN:
        return produce(state, draft => {
          draft.refreshing = true;
          draft.fetchError = null;
        });
      case UPDATE_TICKER_OFFICIAL_CLOSE:
        return produce(state, draft => {
          const payload = action.payload;
          draft.instruments.forEach((item) => {
            if(item.id !== payload.instrumentId) return;
            item.officialClose = payload.officialClose;
            item.officialDate = payload.officialDate;
            draft.updateStrategy[payload.instrumentId] = getLoadStrategy(item);
          });
        });
      case REFRESH_TICKER_SUCCESS:
        return produce(state, draft => {
          draft.refreshing = false;
          draft.refreshStarted = true;
          for (const intrument of action.payload.instruments) {
            const index = draft.instruments.findIndex(i => i.id === intrument.id);
            const strategy = getLoadStrategy(intrument);
            draft.updateStrategy[intrument.id] = strategy;
            if (index !== -1) {
              draft.instruments[index] = intrument;
            } else {
              draft.instruments.push(intrument);
            }
          }
        });
      case REFRESH_TICKER_FAILURE:
        return produce(state, draft => {
          draft.refreshing = false;
          draft.refreshError = action.payload.error;
        });
      case UPDATE_STOCK_DATA:
        return produce(state, draft => {
          const payload = /** @type { import('../actions').UpdateStockPayload } */(action.payload);
          const fields = /** @type { const } */(['date', 'bid', 'ask', 'volume', 'high', 'low', 'close' ]);
          draft.instruments.forEach(item => {
            if(item.instrumentId !== payload.instrumentId) return;
            if(dayjs(payload.data.date).isBefore(item.lastUpdatedDate)) {
              console.warn('new ticker data before current data');
              return;
            }

            fields
              .filter((item) => payload.data[item] !== undefined)
              .forEach((field) => {
                switch (field) {
                  case 'close': {
                    const newData = calcPriceChange(
                      item.prevClose,
                      payload.data.close
                    );
                    item.last = newData.last;
                    item.change = newData.change;
                    item.changePercentage = newData.changePercentage;
                    break;
                  }
                  case 'date': {
                    item['lastUpdatedDate'] = new Date(payload.data['date']).toISOString();
                    break;
                  }
                  default: {
                    if (item[field] !== payload.data[field]) {
                      item[field] = payload.data[field];
                    }
                  }
                }
              });

            draft.updateStrategy[item.instrumentId] = getLoadStrategy(item);
          });
        });
      default:
        return state;
    }
  };
}
