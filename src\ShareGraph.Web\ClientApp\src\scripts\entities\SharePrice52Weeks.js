import { convertChangePercentDecimal, getCustomShareNameByInstrumentId } from '../helper';

export default class SharePrice52Weeks {
  constructor(params = {}, formatNumberByInstrument) {
    Object.assign(this, params);
    this.shareName = getCustomShareNameByInstrumentId(params.instrumentId) || params.shareName || '';
    this.id = params.id || params.instrumentId;
    this.last = params.last ? formatNumberByInstrument(params.last, this.id) : params.last;
    this.low52W = params.low52W ? formatNumberByInstrument(params.low52W, this.id) : params.low52W;
    this.high52W = params.high52W ? formatNumberByInstrument(params.high52W, this.id) : params.high52W;

    const valueLast = parseFloat(params.last || 0);
    const valueLow = parseFloat(params.low52W || 0);
    const valueHigh = parseFloat(params.high52W || 0);
    const percent52W_Low = (valueLast / valueLow - 1) * 100;
    const percent52W_High = -(1 - valueLast / valueHigh) * 100;
    this.percent52WLow = convertChangePercentDecimal(percent52W_Low || 0);
    this.percent52WHigh = convertChangePercentDecimal(percent52W_High || 0);
  }
}
