import produce from 'immer';

import {
    FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_BEGIN,
    FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_SUCCESS,
    FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_FAILURE
 } from '../actions/sharePriceDevelopmentByYearsAction';

 const initialState = {
     instruments: [],
     loading: true,
    fetchError: null
 };


export default function createSharePriceDevelopmentByYearsReducer(instrumentIds = []) {
    initialState.instruments = instrumentIds.map((i) => {
      return {
        instrumentId: i
      };
    });

    return function sharePriceDevelopmentByYearsReducer(state = initialState, action) {
      switch (action.type) {
        case FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_BEGIN:
          return produce(state, draft => {
            draft.loading = true;
            draft.fetchError = null;
          });
        case FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_SUCCESS:
          return produce(state, draft => {
            draft.loading = false;
            draft.instruments = action.payload.instruments;
          });
        case FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_FAILURE:
          return produce(state, draft => {
            draft.loading = false;
            draft.fetchError = action.payload.error;
          });
        default:
          return state;
      }
    };
  }
