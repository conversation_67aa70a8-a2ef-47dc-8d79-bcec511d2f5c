/* eslint-disable require-yield */
import { cloneNode } from './cloneNode';
import { embedImages } from './embedImages';
import { applyStyleWithOptions } from './applyStyleWithOptions';
import { embedWebFonts, getWebFontCSS } from './embedWebFonts';
import { getNodeWidth, getNodeHeight, getPixelRatio, createImage, canvasToBlob, nodeToDataURL } from './util';
function getImageSize(node, options = {}) {
  const width = options.width || getNodeWidth(node);
  const height = options.height || getNodeHeight(node);
  return { width, height };
}
export function toSvg(node, options = {}) {
    const { width, height } = getImageSize(node, options);
    return Promise.resolve(node)
      .then(nativeNode => cloneNode(nativeNode, options, true))
      .then(clonedNode => embedWebFonts(clonedNode, options))
      .then(clonedNode => embedImages(clonedNode, options))
      .then(clonedNode => applyStyleWithOptions(clonedNode, options))
      .then(clonedNode => nodeToDataURL(clonedNode, width, height));
}
const dimensionCanvasLimit = 16384; // as per https://developer.mozilla.org/en-US/docs/Web/HTML/Element/canvas#maximum_canvas_size
function checkCanvasDimensions(canvas) {
  if (canvas.width > dimensionCanvasLimit || canvas.height > dimensionCanvasLimit) {
    if (canvas.width > dimensionCanvasLimit && canvas.height > dimensionCanvasLimit) {
      if (canvas.width > canvas.height) {
        canvas.height *= dimensionCanvasLimit / canvas.width;
        canvas.width = dimensionCanvasLimit;
      } else {
        canvas.width *= dimensionCanvasLimit / canvas.height;
        canvas.height = dimensionCanvasLimit;
      }
    } else if (canvas.width > dimensionCanvasLimit) {
      canvas.height *= dimensionCanvasLimit / canvas.width;
      canvas.width = dimensionCanvasLimit;
    } else {
      canvas.width *= dimensionCanvasLimit / canvas.height;
      canvas.height = dimensionCanvasLimit;
    }
  }
}
export function toCanvas(node, options = {}) {
    return toSvg(node, options)
      .then(createImage)
      .then(img => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        const ratio = options.pixelRatio || getPixelRatio();
        const { width, height } = getImageSize(node, options);
        const canvasWidth = options.canvasWidth || width;
        const canvasHeight = options.canvasHeight || height;
        canvas.width = canvasWidth * ratio;
        canvas.height = canvasHeight * ratio;
        if (!options.skipAutoScale) {
          checkCanvasDimensions(canvas);
        }
        canvas.style.width = `${canvasWidth}`;
        canvas.style.height = `${canvasHeight}`;
        if (options.backgroundColor) {
          context.fillStyle = options.backgroundColor;
          context.fillRect(0, 0, canvas.width, canvas.height);
        }
        context.drawImage(img, 0, 0, canvas.width, canvas.height);
        return canvas;
      });
}
export function toPixelData(node, options = {}) {
    const { width, height } = getImageSize(node, options);
    return toCanvas(node, options).then(canvas => {
      const ctx = canvas.getContext('2d');
      return ctx.getImageData(0, 0, width, height).data;
    });
}
export function toPng(node, options = {}) {
    return toCanvas(node, options).then(canvas => canvas.toDataURL());
}
export function toJpeg(node, options = {}) {
    return toCanvas(node, options).then(canvas => canvas.toDataURL('image/jpeg', options.quality || 1));
}
export function toBlob(node, options = {}) {
    return toCanvas(node, options).then(canvasToBlob);
}
export function getFontEmbedCSS(node, options = {}) {
    return getWebFontCSS(node, options);
}

export function toImage (node, options) {
  return toSvg(node, options).then(url => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        resolve(img);
      };
      img.onerror = reject;
      img.decoding = 'sync';
      img.height = node.offsetHeight;
      img.width = node.offsetWidth;
      img.src = url;
    });
  });
}
//# sourceMappingURL=index.js.map
