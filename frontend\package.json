{"name": "watchlist-widget", "private": false, "version": "1.0.0", "type": "module", "main": "dist/watchlist-widget.umd.js", "module": "dist/watchlist-widget.es.js", "exports": {".": {"import": "./dist/watchlist-widget.es.js", "require": "./dist/watchlist-widget.umd.js"}}, "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:widget": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@preact-signals/query": "^2.1.1", "@preact/signals": "^2.2.1", "axios": "^1.10.0", "es-toolkit": "^1.39.6", "fuzzysort": "^3.1.0", "lucide-preact": "^0.525.0", "lucide-react": "^0.525.0", "preact": "^10.24.0", "rc-virtual-list": "^3.19.1", "react-toastify": "^11.0.5", "urql": "^4.2.2"}, "devDependencies": {"@babel/core": "^7.27.7", "@eslint/js": "^9.29.0", "@preact/preset-vite": "^2.9.0", "@types/node": "^22.0.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "terser": "^5.28.1", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vite-plugin-css-injected-by-js": "^3.5.2"}, "peerDependencies": {"preact": ">=10.0.0"}}