import { useState, useRef, useContext } from 'react';

import { useMouseOutChart } from '../../../customHooks/common';
import useHeadsUpHR from '../../../customHooks/useHeadsUpHR';
import ChartContext from '../../../context/ChartContext';
import i18n from '../../../services/i18n';

const ChartHeadsUpHRTooltip = () => {
  const [show, setShow] = useState(false);
  const forceHideRef = useRef(show);
  const {
    chart: [chartEngine]
  } = useContext(ChartContext);

  const { close, high, low, open } = useHeadsUpHR(() => {
    setShow(true);
  });
  useMouseOutChart(() => {
    setShow(false);
  });

  if (!show || !open || !chartEngine?.layout?.headsUp?.static) return null;

  return (
    <div className="chart-hu-tooltip">
      <div
        aria-hidden
        className="close-btn"
        onClick={() => {
          setShow(false);
          forceHideRef.current = false;
        }}
      >
        ✕
      </div>
      <div className="chart-hu-tooltip__field">
        <div className="chart-hu-tooltip__field-name">{i18n.translate('openLabel')}</div>
        <div className="chart-hu-tooltip__field-value">{open}</div>
      </div>
      <div className="chart-hu-tooltip__field">
        <div className="chart-hu-tooltip__field-name">{i18n.translate('highLabel')}</div>
        <div className="chart-hu-tooltip__field-value">{high}</div>
      </div>
      <div className="chart-hu-tooltip__field">
        <div className="chart-hu-tooltip__field-name">{i18n.translate('lowLabel')}</div>
        <div className="chart-hu-tooltip__field-value">{low}</div>
      </div>
      <div className="chart-hu-tooltip__field">
        <div className="chart-hu-tooltip__field-name">{i18n.translate('close')}</div>
        <div className="chart-hu-tooltip__field-value">{close}</div>
      </div>
    </div>
  );
};

export default ChartHeadsUpHRTooltip;
