import dayjs from 'dayjs';
import EventCollection from '../../../utils/eventCollection';
import {identity, maxBy, minBy} from 'es-toolkit';
import { CIQ } from '../chartiq-import';
import {formatShortDate} from '../../../helper';

export const MARKER_CLASS = /** @type {const} */ ({
  ACTIVE: 'active--marker',
  OPEN: 'event-marker--open',
  CONTENT: 'event-marker__content',
  HOVER_MODE: 'event-marker--hover',
  CLICK_MODE: 'event-marker--click',
  TOOLTIP: 'event-marker__tooltip',
  TOOLTIP_INNER: 'event-marker__tooltip--inner',
  TOOLTIP_ITEM: 'event-marker__item'
});

/**
 * Gets the week number for a given date.
 * @param {Date} date - The input date.
 * @returns {number} The week number of the year.
 */
function getWeekNumber(date) {
  const $date = dayjs(date);
  const dayOfYear = $date.diff($date.startOf('year'), 'day') + 1; // Day of the year
  const weekNumber = Math.ceil(dayOfYear / 7); // Calculate week number
  return weekNumber;
}

export class EventMarker {
  /**
   *
   * @param {{
   * stx: import('chartiq').CIQ.ChartEngine,
   * eventCollection: EventCollection
   * }} param0
   */
  constructor({ setting, stx, template, date, eventCollection, records, label }) {
    this.setting = setting;
    this.records = records ?? [];
    this.stx = stx;
    this.template = template;
    this.eventCollection = eventCollection;
    this.label = label;

    this.mainNode = /**@type {HTMLElement}*/(this.template.cloneNode(true));
    this.mainNode.removeAttribute('id');
    this.mainNode.classList.add(MARKER_CLASS.ACTIVE);

    this.initializeTooltipContent();
    this.initializeTooltip();
    this.initEvents();

    this.marker = new CIQ.Marker({
      stx: stx,
      label: this.label,
      xPositioner: 'date',
      x: date,
      node: this.mainNode
    });

    this.mainNode.__instance = this;
  }


  /**
   * depend on headsUp and setting we decision switch hover or click to show tooltip
   * @returns {Boolean}
   */
  get isHovering() {
    const { toggleInfo, dynamic, floating } = this.stx.layout.headsUp ?? {};
    if(!this.setting.chart.enabledHoverEvent) return false;

    if(toggleInfo === false) return true;

    return !dynamic && !floating;
  }

  /**
   * Gets the CSS class for hover or click mode.
   * @returns {string} The appropriate CSS class for interaction mode.
   */
  get hoverOrClickClass() {
    return this.isHovering ? MARKER_CLASS.HOVER_MODE : MARKER_CLASS.CLICK_MODE;
  }

  get isOpened() {
    return this.mainNode.classList.contains(MARKER_CLASS.OPEN);
  }

  /**
   * Adds a new record to the marker and updates the tooltip.
   * @param {any} record - The record to add to the marker.
   */
  addRecord(record) {
    this.records.push(record);
    this.populateTooltip();
  }

  initializeTooltipContent() {
    this.contentEl = this.mainNode.querySelector('.' + MARKER_CLASS.CONTENT);
    this.tooltipEl = this.mainNode.querySelector('.' + MARKER_CLASS.TOOLTIP);
    this.tooltipInnerEl = this.mainNode.querySelector('.' + MARKER_CLASS.TOOLTIP_INNER);
    this.tooltipItemEl = this.mainNode.querySelector('.' + MARKER_CLASS.TOOLTIP_ITEM);
  }

  /**
   * Initializes the tooltip by resetting and populating it.
   */
  initializeTooltip() {
    this.resetTooltip();
    this.populateTooltip();
  }

  populateTooltip() {
    if(this.records.length === 0) return;
    this.resetTooltip();
    const prepareEl = (el, marker) => {
      el.setAttribute('data-press-id', marker.id);
      el.querySelector('time').innerHTML = formatShortDate(marker.date, {isTimezone: false, isUtc: true});
      el.querySelector('strong').innerHTML = marker.title;
    };

    this.records.forEach((marker, index) => {
      const clone = this.tooltipItemEl.cloneNode(true);
      prepareEl(clone, marker);
      this.tooltipInnerEl.appendChild(clone);
    });
  }

  /**
   * Resets the tooltip by clearing its inner content.
   */
  resetTooltip() {
    this.tooltipInnerEl.innerHTML = '';
  }

  /**
   * Initializes event listeners for the marker.
   */
  initEvents() {
    this.eventCollection.listen(this.mainNode, 'click', this.onClick.bind(this));
    this.eventCollection.listen(this.mainNode, 'mouseover', this.onMouseOver.bind(this));
    this.eventCollection.listen(this.mainNode, 'mouseleave', this.onMouseLeave.bind(this));
    this.eventCollection.listen(this.mainNode, 'touchstart', this.onClick.bind(this));
  }

  onMouseOver(){
    this.forceHiddenChartTooltip();
    if(!this.isHovering) return;
    this.showTooltip();
  }

  /**
   *
   * @param {MouseEvent} e
   */
  onMouseLeave(e) {
    this.resetChartTooltip();
    if(!this.isHovering) return;
    this.hiddenTooltip();
  }

  /**
   *
   * @param {MouseEvent} e
   */
  onClick(e) {
    if(e.target && e.target instanceof HTMLElement) {
      const tooltip = e.target.closest('.' + MARKER_CLASS.TOOLTIP);
      if(tooltip) return; // prevent close when clicked on tooltip;
    }
    if(this.isOpened) {
      this.hiddenAllTooltip();
    } else {
      this.showTooltip();
    }
  }

  showTooltip() {
    this.hiddenAllTooltip();
    this.mainNode.classList.add(MARKER_CLASS.OPEN, this.hoverOrClickClass);
    this.adjustTooltipPosition();
  }


  /**
   * Adjusts the tooltip position based on marker and chart dimensions.
   */
  adjustTooltipPosition() {
    const bottom = parseInt(this.mainNode.style.getPropertyValue('bottom'));
    const tooltipHeight = this.tooltipEl.clientHeight;
    const tooltipWidth = this.tooltipEl.clientWidth;
    const leftFromPanel = parseInt(this.mainNode.style.getPropertyValue('left'));
    const rightFromPanel = this.stx.chart.panel.width - leftFromPanel;

    if (bottom < tooltipHeight) {
      this.tooltipEl.classList.add('top');
    } else {
      this.tooltipEl.classList.remove('top');
    }

    if (rightFromPanel < tooltipWidth) {
      this.tooltipEl.classList.add('right');
    } else {
      this.tooltipEl.classList.remove('right');
    }
  }

  hiddenTooltip() {
    this.mainNode.classList.remove(MARKER_CLASS.OPEN, this.hoverOrClickClass);
  }

  /**
   * Retrieves the chart tooltip elements.
   * @returns {{dynamic: HTMLElement | null, tooltip: HTMLElement | null}} Chart tooltip elements.
   */
  getChartTooltip() {
    const chartContainer = this.stx.container;

    /** @type {{dynamic: HTMLElement | null, tooltip: HTMLElement | null}} */
    const result = {dynamic: null,tooltip: null};
    if (this.stx.layout.headsUp.dynamic) {
      result.dynamic = chartContainer.querySelector('cq-hu-dynamic');
		}
		if (this.stx.layout.headsUp.floating) {
      result.tooltip = chartContainer.querySelector('stx-hu-tooltip');
		}

    return result;
  }

  /**
   * Forces hiding of chart tooltips.
   */
  forceHiddenChartTooltip() {
    const {dynamic, tooltip} = this.getChartTooltip();
    if (dynamic) {
      dynamic.style.display = 'none';
		}
		if (tooltip) {
      tooltip.style.display = 'none';
		}
  }

  /**
   * Resets the chart tooltip to its default state.
   */
  resetChartTooltip() {
    const {dynamic, tooltip} = this.getChartTooltip();
    if (dynamic) {
      dynamic.style.display = '';
		}
		if (tooltip) {
      tooltip.style.display = '';
		}
  }

  /**
   * Hides all open tooltips in the chart context.
   */
  hiddenAllTooltip() {
    const cqContext = this.stx.uiContext.node.length ? this.stx.uiContext.node[0] : document.body;
    cqContext.querySelectorAll(`.${MARKER_CLASS.OPEN}`).forEach(element => {
      element.classList.remove(MARKER_CLASS.OPEN, MARKER_CLASS.HOVER_MODE, MARKER_CLASS.CLICK_MODE);
    });
  }

  /**
   * Destroys the marker, removing it from the chart.
   */
  destroy() {
    this.marker.remove();
  }
}

/**
 * @abstract
 */
export class EventLoader extends EventTarget {
  /**
   *
   * @param {Date} from
   * @param {Date} to
   * @param {any} options
   */
  constructor(from, to, options) {
    super();
    this.options = options;
    this.from = from;
    this.to = to;
    this.initLoad = false;
    this.loading = false;
    this.forceStop = false;
    this.data = [];
    this.abortController = new AbortController();
  }

  /**
  /**
   * Fetches events with optional cursor-based pagination.
   * @abstract
   * @param {string} [cursor] - Pagination cursor.
   * @returns {Promise<{endCursor: string | undefined, data: Array<any>}>} Fetched events and next cursor.
   */
  async fetch(cursor) {
    throw new Error('not implement yet');
  }


  /**
   * Loads events and calls the provided callback for each batch.
   * @param {function(Array<any>):void} onLoad - Callback function to process loaded events.
   * @returns {Promise<void>}
   */
  async load(onLoad) {
    try {
      if (this.initLoad) return;
      this.initLoad = true;
      this.loading = true;
      let cursor;

      do {
        this.abortController.signal.throwIfAborted();
        const {data, endCursor} = await this.fetch(cursor);
        if(!data) break;
        onLoad(data);
        this.data.push(...data);
        cursor = endCursor;
      } while(cursor);

      this.loading = false;
    } catch(e) {
      this.forceStop = true;
      if(!(e instanceof AbortSignal)) {
        console.error(e);
      }
    } finally {
      this.loading = false;
      this.dispatchEvent(new Event('loaded'));
    }
  }

  /**
   * Destroys the loader and stops any ongoing loading.
   */
  destroy() {
    this.cancel();
  }

  /**
   * Cancels the ongoing loading process.
   */
  cancel() {
    this.abortController.abort();
    if(this.loading) {
      this.forceStop = true;
    }
  }

  /**
   * Static method to create and start loading events.
   * @param {Date} from - Start date for loading events.
   * @param {Date} to - End date for loading events.
   * @param {Object} options - Additional loading options.
   * @param {function(Array<any>):void} onLoad - Callback function to process loaded events.
   * @returns {EventLoader} The created and started event loader.
   */
  static create(from, to, options, onLoad) {
    const instance = new this(from, to, options);
    instance.load(onLoad);
    return instance;
  }
}

/**
 * @abstract
 */
export class EventNode {
  /**
   *
   * @param {any} stx
   * @param {HTMLElement} template
   * @param {Record<any>} setting
   */
  constructor(stx, template, setting) {
    this.stx = stx;
    this.template = template;
    this.setting = setting;
    this.eventCollection = new EventCollection();
    this.markers = /** @type {Record<string, EventMarker>} */ ({});
    this.isDestroyed = false;
    this.interval = this.stx.layout.interval;
    this._records = [];

    this.eventCollection.listenerFactor(() => {
      const handle = ({target}) => {
        if(!(target && target instanceof HTMLElement)) return;

        const markerActive = document.querySelector('.' + MARKER_CLASS.OPEN);
        const isClickOutside = !target.closest('.' + MARKER_CLASS.OPEN);
        if(isClickOutside && markerActive && markerActive?.__instance) {
          markerActive.__instance.hiddenTooltip();
        }
      };

      document.addEventListener('click', handle);
      return () => document.removeEventListener('click', handle);
    });

    this.eventCollection.listenerFactor(() => {
      let timer = null;
      const handle = ({value}) => {
        if(value !== this.interval) {
          this.removeAllMarker();
          this.interval = value;
          clearTimeout(timer);
          // reInitMarker is a large process that moves tasks off the current call stack;
          timer = setTimeout(() => {
            this.reInitMarker();
          });
        }
      };

      CIQ.UI.observeProperty('interval', stx.layout, handle);
      return () =>  CIQ.UI.unobserveProperty('interval', stx.layout, handle);
    });

    this.utcOffset = 0;

    if(this.stx.timeZoneOffset) {
      this.utcOffset = this.stx.timeZoneOffset - new Date().getTimezoneOffset();
    }
  }

  /**
   * Creates a marker for a specific date and records.
   * @abstract
   * @param {Date} date - Date for the marker.
   * @param {Array<any>} records - Records associated with the marker.
   * @returns {EventMarker} The created event marker.
   */
  createMarker(date, records) {
    throw new Error('not implement yet');
  }

  /**
   *
   * @param {Date} date
   */
  startOfDate(date) {
    const displayDate = dayjs.utc(date).startOf('date');
    return new Date(displayDate.get('year'), displayDate.get('month'), displayDate.get('date'));
  }

  /**
   * Generates a marker key based on the current interval.
   * @param {Date} date - Date to generate key for.
   * @returns {string} The generated marker key.
   */
  getMarkerKey(date) {
    switch(this.interval) {
      case 'minute':
      case 'day':
        return dayjs(date).format('YYYY-MM-DD');
      case 'week':
      case 'month':
        return `${new Date(date).getFullYear()}-${getWeekNumber(date)}`;
      default:
        return dayjs(date).format('YYYY-MM-DD');
    }
  }

  /**
   * Handles loading of event records.
   * @param {Array<{Date: Date}>} records - Records to load.
   */
  onLoad(records) {
    if(this.isDestroyed) return;
    records.map(item => {
      const dateObj = this.startOfDate(item.Date);
      const date = this.getMarkerKey(dateObj);
      if(this.markers[date]) {
        this.markers[date].addRecord(item);
      } else {
        this.markers[date] = this.createMarker(dateObj, [item]);
      }
    });
    this._records.push(...records);
    this.stx.draw();
  }

  /**
   * Reinitializes markers with existing records.
   */
  reInitMarker() {
    if(Object.keys(this.markers).length > 0) throw new Error('call removeAllMarker before run');
    const records = this._records;
    this._records = []; // onLoad method will merge new and old records together, we delete all existing records to make sure there are no double values
    this.onLoad(records);
  }

  /**
   * Removes all existing markers.
   */
  removeAllMarker() {
    Object.values(this.markers).forEach(marker => marker.destroy());
    this.markers = {};
  }

  /**
   * Destroys the event node and cleans up resources.
   */
  destroy() {
    this.removeAllMarker();
    this.eventCollection.removeAll();
    this.isDestroyed = true;
  }
}

/**
 * @abstract
 */
export class EventHandler extends EventTarget {

  /**
   * Creates an instance of EventHandler.
   * @param {Object} stx - Chart engine instance.
   * @param {Object} options - Additional handler options.
   */
  constructor(stx, options) {
    super();
    this.stx = stx;
    this.loaders = /** @type {Array<EventLoader>} */([]);
    this.markers = {};
    this.eventNode = /** @type {EventNode | null}  */ (null);
    this.options = options;
    this.isSleep = false;
    this.eventCollection = new EventCollection();
  }

  /**
   * Creates an event node (abstract method to be implemented by subclasses).
   * @abstract
   * @returns {EventNode} The created event node.
   */
  eventNodeCreator() { throw new Error('not implement yet'); }

  /**
   * Creates an event loader (abstract method to be implemented by subclasses).
   * @abstract
   * @param {Date} from - Start date for loading events.
   * @param {Date} to - End date for loading events.
   * @returns {EventLoader} The created event loader.
   */
  eventLoaderCreator(from, to) { throw new Error('not implement yet'); }

  initializeEventNode() {
    if(this.eventNode) return;
    this.eventNode = this.eventNodeCreator();
    this.isSleep = false;
    const oldData = this.loaders.filter(item => item.data.length > 0).map(item => item.data).flat();

    if(oldData.length === 0) return;
    this.eventNode.onLoad(oldData);
  }

  /**
   * Loads events for a given date range.
   * @param {Date} from
   * @param {Date} to
   */
  initializeLoader(from, to) {
    const loader = this.eventLoaderCreator(from, to);
    this.eventCollection.listen(loader, 'loaded', () => this.isLoaderLoaded());
    this.loaders.push(loader);
  }

  get isPending() {
    return this.loaders.some(item => item.loading);
  }

  isLoaderLoaded() {
    if(!this.isPending) {
      this.dispatchEvent(new Event('loaded'));
    }
  }

  addEventListenerOnce(eventName, cb) {
    const clear = () => this.removeEventListener(eventName, cb);
    this.eventCollection.listenerFactor(() => {
      this.addEventListener(eventName, cb);
      return clear;
    });

    return clear;
  }

  /**
   * Loads events for a given date range.
   * @param {Date} from - Start date for loading events.
   * @param {Date} to - End date for loading events.
   */
  load(from, to) {
    if(!this.isIntersectionTime([from, to])) this.reset();

    this.initializeEventNode();
    if (!this.range) {
      this.range = [from, to];
      this.initializeLoader(from, to);
      return;
    }

    const { leftJoin, rightJoin } = this.intersectionTime([from, to]);

    if (leftJoin) this.initializeLoader(leftJoin[0], leftJoin[1]);
    if (rightJoin) this.initializeLoader(rightJoin[0], rightJoin[1]);

    if(this.range[0] > from) this.range[0] = from;
    if(this.range[1] < to) this.range[1] = to;
  }

  /**
   * Calculates the intersection of time ranges.
   * @param {[Date, Date]} compareRange - Range to compare against existing range.
   * @returns {leftJoin: [Date, Date], rightJoin: [Date, Date]} Object containing left and right join ranges.
   */
  intersectionTime(compareRange) {
    const [leftRange, rightRange] = this.range;
    const [leftCompare, rightCompare] = compareRange;
    const result = { leftJoin: undefined, rightJoin: undefined };

    if (leftCompare < leftRange) {
      result.leftJoin = [leftCompare, leftRange];
    }

    if (rightCompare > rightRange) {
      result.rightJoin = [rightRange, rightCompare];
    }

    return result;
  }

  /**
   * Checks if the given range intersects with the existing range.
   * @param {[Date, Date]} compareRange - Range to compare against existing range.
   * @returns {boolean} True if the ranges intersect, false otherwise.
   */
  isIntersectionTime(compareRange) {
    if (!this.range) return false;
    const [leftRange, rightRange] = this.range;
    const [leftCompare, rightCompare] = compareRange;
    return leftCompare <= rightRange && leftRange <= rightCompare;
  }

  reset() {
    this.loaders.forEach(loader => loader.destroy());
    this.loaders = [];
    this.eventNode?.destroy();
    this.eventNode = null;

    this.eventCollection.removeAll();
    this.range = null;
  }

  /**
   * Puts the event handler to sleep, stopping processing and destroying resources.
   */
  sleep() {
    this.loaders.forEach(loader => loader.destroy());
    this.eventNode.destroy();
    this.eventNode = null;
    this.isSleep = true;

    this.eventCollection.removeAll();
    if(this.loaders === 0) return;

    if(this.loaders.length === 1) {
      if(this.loaders[0].forceStop) this.range = null;
      return;
    }

    const result = this.loaders.reduce((acc, item) => {
      if(acc.length === 0) {
        acc.push({forceStop: item.forceStop, loaders: [item]});
        return acc;
      }

      const last = acc[acc.length - 1];
      if(last.forceStop === item.forceStop) {
        last.loaders.push(item);
      } else {
        acc.push({forceStop: item.forceStop, loaders: [item]});
      }

      return acc;
    }, /** @type {Array<{forceStop: boolean, loaders: Array<EventLoader>}>} */ ([]));


    const data = maxBy(result.reverse().filter(item => item.forceStop === false), item => item.loaders.length);

    if(data) {
      const from = minBy(data.loaders.map(item => item.from), identity);
      const to = maxBy(data.loaders.map(item => item.to), identity);
      this.loaders = data.loaders;
      this.range = [from, to];
    }
  }
}
