import { mergeObjectWithDefinedProps } from '../utils/common';
import { getIndicesSetting } from '../configs/configuration-app';
import {PEER_INDICES_QUERY} from '../graphql-queries/peerIndicesQuery';
import {client, clientRT} from '../services/graphql-client';
import {trimAbbreviation} from '../utils';
import { get } from 'es-toolkit/compat';
/**
 *
 * @param {Array<string | number>} notRealTimeIds
 * @param {Array<string | number>} realtimeIds
 * @param {Array<string | number>} additionalRealtimeIds
 * @param {import('../reducers/currencyReducers').CurrencyOption['code']} toCurrency
 */
async function getIndicesDataNewData(notRealTimeIds = [],realtimeIds = [], additionalRealtimeIds = [], toCurrency) {

  if(!notRealTimeIds.length && !realtimeIds.length && !additionalRealtimeIds.length) {
    return new Promise((_, reject) => {
      reject('Not any instrument id provided.');
    });
  }

  const mapping = item => {
    const shareName = trimAbbreviation(item.shareName);
    const marketStatus = get(item, 'market.status.isOpened') ? 'Open' : 'Close';

    return {
      instrumentId: item.id,
      shareName,
      currencyCode: get(item, 'currency.code'),
      last: get(item, 'currentPrice.last'),
      change: get(item, 'currentPrice.change'),
      changePercentage: get(item, 'currentPrice.changePercentage'),
      marketStatus,
      ticker: item.symbol,
      open: get(item, 'currentPrice.open'),
      lastUpdatedDate: item.date,
      symbol: get(item, 'currency.symbol'),
      normalDailyOpen: get(item, 'market.openTimeLocal'),
      normalDailyClose: get(item, 'market.closeTimeLocal'),
      marketTimeZone: get(item, 'market.timezoneName'),
      marketName: get(item, 'market.translation.value'),
      marketAbbreviation: get(item, 'market.abbreviation'),
      officialClose: get(item, 'currentPrice.officialClose'),
      officialCloseDate: get(item, 'currentPrice.officialCloseDate')
    };
  };

  const indicesSetting =  getIndicesSetting();
  
  const realTime = {
    ids: realtimeIds.filter(id => !indicesSetting[id].enabledAdjustPrice), 
    adjIds: realtimeIds.filter(id => indicesSetting[id].enabledAdjustPrice)
  };

  const notRealTime = {
    ids: notRealTimeIds.filter(id => !indicesSetting[id].enabledAdjustPrice), 
    adjIds: notRealTimeIds.filter(id => indicesSetting[id].enabledAdjustPrice)
  };

  const promiseAll = [];

  if(realTime.ids.length > 0 || additionalRealtimeIds.length > 0) promiseAll.push(
    clientRT.query(PEER_INDICES_QUERY, {
      ids: realTime.ids,
      adjClose: false,
      additionalRealtimeIds,
      toCurrency
    })
  );

  if(realTime.adjIds.length > 0) promiseAll.push(
    clientRT.query(PEER_INDICES_QUERY, {
      ids: realTime.adjIds,
      adjClose: true,
      additionalRealtimeIds: [],
      toCurrency
    })
  );

  if(notRealTime.ids.length > 0) promiseAll.push(
    client.query(PEER_INDICES_QUERY, {
      ids: notRealTime.ids,
      adjClose: false,
      additionalRealtimeIds: [],
      toCurrency
    })
  );

  if(notRealTime.adjIds.length > 0) promiseAll.push(
    client.query(PEER_INDICES_QUERY, {
      ids: notRealTime.adjIds,
      adjClose: true,
      additionalRealtimeIds: [],
      toCurrency
    })
  );

  const results = await Promise.all(promiseAll);

  const instruments = results.map(result => result.data.instrumentByIds?.map(mapping) ?? []).flat();

  const additionalRealtime = results?.[0].data?.additionalRealtime?.map(item => ({
    instrumentId: item.id,
    officialClose: item.currentPrice.officialClose,
    officialCloseDate: item.currentPrice.officialCloseDate
  })) ?? [];

  return {
    instruments,
    additionalRealtime
  };
}


// Peers Actions
export const FETCH_INDICES_BEGIN = 'FETCH_INDICES_BEGIN';
export const FETCH_INDICES_SUCCESS = 'FETCH_INDICES_SUCCESS';
export const FETCH_INDICES_FAILURE = 'FETCH_INDICES_FAILURE';
export const REFRESH_INDICES_BEGIN = 'REFRESH_INDICES_BEGIN';
export const REFRESH_INDICES_SUCCESS = 'REFRESH_INDICES_SUCCESS';
export const REFRESH_INDICES_FAILURE = 'REFRESH_INDICES_FAILURE';
export const INDICES_SELECTED_CHANGE_SUCCESS =
  'INDICES_SELECTED_CHANGE_SUCCESS';
export const INDICES_DESELECTED_CHANGE = 'INDICES_DESELECTED_CHANGE';
export const UPDATE_INDICES_OFFICIAL_CLOSE = 'UPDATE_INDICES_OFFICIAL_CLOSE';

export const fetchIndicesBegin = () => ({
  type: FETCH_INDICES_BEGIN
});

export const fetchIndicesSuccess = (instruments = []) => {
  const indicesSetting = getIndicesSetting();
  return {
    type: FETCH_INDICES_SUCCESS,
    payload: {
      instruments: instruments.map((ins) =>
        mergeObjectWithDefinedProps(ins, indicesSetting[ins.instrumentId])
      )
    }
  };
};

export const fetchIndicesFailure = (error) => ({
  type: FETCH_INDICES_FAILURE,
  payload: { error }
});

export const selectIndicesSuccess = (instrumentId) => ({
  type: INDICES_SELECTED_CHANGE_SUCCESS,
  payload: { instrumentId }
});

export const refreshIndicesBegin = () => ({
  type: REFRESH_INDICES_BEGIN
});

export const refreshIndicesSucess = (instruments = []) => {
  const indicesSetting = getIndicesSetting();
  return {
    type: REFRESH_INDICES_SUCCESS,
    payload: {
      instruments: instruments.map((ins) =>
        mergeObjectWithDefinedProps(ins, indicesSetting[ins.instrumentId])
      )
    }
  };
};

export const refreshIndicesFailure = (error) => ({
  type: REFRESH_INDICES_FAILURE,
  payload: { error }
});

export const removeSelectedIndice = (instrumentId) => ({
  type: INDICES_DESELECTED_CHANGE,
  payload: { instrumentId }
});

export function fetchIndices() {
  return async (dispatch, getState) => {
    const instrumentIds = getState().indices.instruments.map((ins) => ins.instrumentId);
    const indicesSetting =  getIndicesSetting();
    const notRealTimeIds = instrumentIds.filter(id => !indicesSetting[id].isRT);
    const realtimeIds = instrumentIds.filter(id => indicesSetting[id].isRT);
    dispatch(fetchIndicesBegin());

    const { currency } = getState().currency;
    const toCurrency = currency?.code;
    try {
      if(instrumentIds.length === 0) {
        dispatch(fetchIndicesSuccess([]));
        return [];
      } else {
        const { instruments } = await getIndicesDataNewData(notRealTimeIds, realtimeIds, [], toCurrency);
        dispatch(fetchIndicesSuccess(instruments));
        return instruments;
      }
    } catch (err) {
      dispatch(fetchIndicesFailure(err));
      throw err;
    }
  };
}

export function refreshIndices() {
  return async (dispatch, getState) => {
    const state = getState();
    const instrumentIds = state.indices.instruments.map((ins) => ins.instrumentId);
    const indicesSetting =  getIndicesSetting();
    const updateStrategy = state.indices.updateStrategy;
    const notRealTimeIds = instrumentIds.filter(id => updateStrategy[id] !== 'socket' && !indicesSetting[id].isRT);
    const realtimeIds = instrumentIds.filter(id => updateStrategy[id] !== 'socket' && indicesSetting[id].isRT);
    dispatch(refreshIndicesBegin());
    const additionalRealtimeIds = instrumentIds.filter(id => updateStrategy[id] === 'socket');

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    try {
      if(instrumentIds.length === 0) {
        dispatch(fetchIndicesSuccess([]));
        return [];
      } else {
        const {instruments, additionalRealtime} = await getIndicesDataNewData(notRealTimeIds, realtimeIds, additionalRealtimeIds, toCurrency);
        dispatch(refreshIndicesSucess(instruments));
        additionalRealtime.forEach((item) => {
          const { instrumentId, officialClose, officialCloseDate } = item;
          if (officialClose == null || officialCloseDate == null) return;
          dispatch(
            updateOfficialClose(instrumentId, officialClose, officialCloseDate)
          );
        });
        return instruments;
      }
    } catch (err) {
      dispatch(refreshIndicesFailure(err));
      throw err;
    }
  };
}

export function selectIndices(instrumentId) {
  return (dispatch, getState) => {
    const state = getState().indices;
    const instruments = state.instruments;
    // const previousSelected = state.selectedInstrumentId;

    // if (previousSelected === instrumentId) {
    //   return;
    // }
    if (instruments.filter((ins) => ins.instrumentId === instrumentId).length) {
      dispatch(selectIndicesSuccess(instrumentId));
    } else {
      // TODO: Notify out that no Peers matches with provided `${instrumentId}`
    }
  };
}

export function deSelectIndice(instrumentId) {
  return (dispatch, getState) => {
    const state = getState().indices;

    if (instrumentId <= 0 || !state.selectedIndicesIds.includes(instrumentId)) {
      return;
    }

    dispatch(removeSelectedIndice(instrumentId));
  };
}

/**
 *
 * @param {number} officialClose
 * @param {string} officialCloseDate
 * @param {number} instrumentId
 * @returns
 */
export function updateOfficialClose(instrumentId, officialClose, officialCloseDate) {
  return {
    type: UPDATE_INDICES_OFFICIAL_CLOSE,
    payload: { officialClose, officialCloseDate, instrumentId }
  };
}
