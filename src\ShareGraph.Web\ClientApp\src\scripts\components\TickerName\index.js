import {getAllInstrumentsSetting} from '../../configs/configuration-app';
import i18n from '../../services/i18n';



export function getTickerNameCustom ({ shareName, instrumentId, marketAbbreviation }) {
  const all = getAllInstrumentsSetting();
  const item = all[instrumentId];

  if(!item) {
    console.error('instrumentId ${instrumentId} do not exist in setting');
    return {shareName, marketAbbreviation};
  }

  return {
    shareName: item?.shareName || shareName, 
    marketAbbreviation: item?.marketAbbreviation || marketAbbreviation
  };
}

export function getRawHTMLTickerName ({ shareName,instrumentId, marketAbbreviation }) {
  const data = getTickerNameCustom({ shareName, instrumentId, marketAbbreviation });
  return `<span class="ticker-name"><span class="ticker-name__share-name">${data.shareName}</span>
  ${data.marketAbbreviation ? `&nbsp;<span class="ticker-name__market-abbreviation">
    ${i18n.translate('marketAbbreviationFormat', data.marketAbbreviation)}
  </span>` : ''}</span>`;
}

export function getTickerName ({ shareName,instrumentId, marketAbbreviation }) {
  const data = getTickerNameCustom({ shareName, instrumentId, marketAbbreviation });
  return `${data.shareName} ${data.marketAbbreviation ? i18n.translate('marketAbbreviationFormat', data.marketAbbreviation) : ''}`.trim();
}

const TickerName = ({ shareName, instrumentId, marketAbbreviation }) => {
  const data = getTickerNameCustom({ shareName, instrumentId, marketAbbreviation });
  return (
    <span className='ticker-name'>
      <span className="ticker-name__share-name">{data.shareName}</span>&nbsp;
      {data.marketAbbreviation ? <span className="ticker-name__market-abbreviation">
        {i18n.translate('marketAbbreviationFormat', data.marketAbbreviation)}
      </span> : null}
    </span>
  );
};

export default TickerName;
