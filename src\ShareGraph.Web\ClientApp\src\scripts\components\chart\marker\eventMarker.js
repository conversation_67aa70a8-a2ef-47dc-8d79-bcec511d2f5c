import appConfig from '../../../services/app-config';
import {debounce} from '../../../utils';
import EventCollection from '../../../utils/eventCollection';

export default function EventMarker(stx, type) {
  const eventCollection = new EventCollection();
	const setting = appConfig.get();
  const eventMarkerOpenCssClass = 'event-marker--open';
  const prototypeNode = document.querySelector('#chartEventPrototype');
  function isHovering() {
    return setting.chart.enabledHoverEvent && !stx.layout.headsUp.dynamic && !stx.layout.headsUp.floating;
  }

  function createMarkerNode(abbreviationType) {
		let mainNode = prototypeNode.cloneNode(true),
			eventTypeSpan = mainNode.querySelector('.event-marker__content'),
			tooltipSpan = mainNode.querySelector('.event-marker__tooltip'),
			titleSpan = tooltipSpan.querySelector('strong'),
			formatedDateStrSpan = tooltipSpan.querySelector('time');

      mainNode.removeAttribute('id');
			mainNode.classList.add(type);
      eventCollection.listen(mainNode, 'click', handleShowMarker);
      eventCollection.listen(mainNode, 'mouseover', handleMarkerMouseOver);
      eventCollection.listen(mainNode, 'mouseleave', debounce(handleMarkerMouseLeave, 100));
      eventCollection.listen(mainNode, 'touchstart', handleTouchMarker);
  
			eventTypeSpan.innerHTML = abbreviationType;

    return {
      $node: mainNode,
      setId: (id) => {
        mainNode.id = id;
      },
      setLabels: (title, formatedDate) => {
        titleSpan.innerHTML = title;
        formatedDateStrSpan.innerHTML = formatedDate;
      }
    };
	}

  function handleTouchMarker(e){
    e.preventDefault();
    const el = e.target.closest('.event-marker');
    const isTooltip = e.target.closest('.event-marker__tooltip');
    const isOpen = el.classList.contains('event-marker--open');
    // close all
    Array.from(document.querySelectorAll('.event-marker--open.event-marker')).forEach(element => {
      if(el === element) return;
      hideMarker(element);
    });

    if (!isOpen) {
      showMarker(el, false);
    } else if(!isTooltip) {
      hideMarker(el);
    }
  }

  function handleMarkerMouseOver(e) {
		const cqContext = this.closest('cq-context');
		if (stx.layout.headsUp.dynamic) {
			cqContext.querySelector('cq-hu-dynamic').style.display = 'none';
		}
		if (stx.layout.headsUp.floating) {
			cqContext.querySelector('stx-hu-tooltip').style.display = 'none';
		}

    if(!isHovering()) return;


    handleMarkerHover.call(e.target.closest('.event-marker'), e);
	}

	function handleMarkerMouseLeave() {
		const cqContext = this.closest('cq-context');
    if(isHovering()) {
      this.classList.remove('event-marker--hover');
    }
    if (stx.layout.headsUp.dynamic) {
			cqContext.querySelector('cq-hu-dynamic').style.display = '';
		}
		if (stx.layout.headsUp.floating) {
			cqContext.querySelector('stx-hu-tooltip').style.display = '';
		}
	}

	function handleMarkerHover() {
    Array.from(document.querySelectorAll('.event-marker--open.event-marker')).forEach(element => {
      if(this === element) return;
      hideMarker(element);
    });
    showMarker(this, isHovering());
	}

  function hideMarker (element) {
    return ['event-marker--hover', 'event-marker--click', eventMarkerOpenCssClass].filter(item => {
      if(element.classList.contains(item)) {
        element.classList.remove(item);
        return true;
      }
    }).length > 0;
  }

  function showMarker (element, isHovering) {
    const actionEvent = isHovering ? 'hover': 'click';
    const activeClass = 'event-marker--' + actionEvent;
    const hasActive = element.classList.contains(activeClass);

		//close all detail Event Marker
		[...element.parentNode.querySelectorAll('.event-marker')].forEach(item => {
			item.classList.remove(activeClass);
		});
    if(hasActive) return;

		element.classList.add(activeClass, eventMarkerOpenCssClass);
		const bottom = parseInt(element.style.getPropertyValue('bottom'));
		const tooltip = element.querySelector('.event-marker__tooltip');
		if (!tooltip) return;
		const tooltipHeight = tooltip.clientHeight;
		const tooltipWidth = tooltip.clientWidth;
		const rightFromPanel = stx.chart.panel.width - parseInt(element.style.getPropertyValue('left'));
		//check bottom position
		bottom < tooltipHeight ? tooltip.classList.add('top') : tooltip.classList.remove('top');
		//check right position
		rightFromPanel < tooltipWidth ? tooltip.classList.add('right') : tooltip.classList.remove('right');
  }

	function handleShowMarker() {
    showMarker(this, isHovering());
	}

  function handleTouchOutside() {
    const handle = (e) => {
      if(e.target.closest('.event-marker')) return;

      // close all
      Array.from(document.querySelectorAll('.event-marker--open.event-marker')).forEach(element => {
        hideMarker(element);
      });
    };

    return {
      register: () => {
        eventCollection.listen(document, 'touchstart', handle);
      },
      unregister: () => {
        eventCollection.removeAll();
      }
    };
  }

  return {
    createMarkerNode,
    handleTouchOutside: handleTouchOutside()
  };
}