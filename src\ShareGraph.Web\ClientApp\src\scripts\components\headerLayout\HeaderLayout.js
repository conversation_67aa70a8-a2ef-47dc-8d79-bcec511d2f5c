import { useContext } from 'react';
import LinkAccessible from './LinkAccessible';
import { AppContext } from '../../AppContext';
import { Ticker } from '../tickers/Ticker';

function HeaderLayout() {
  const settings = useContext(AppContext);

  return (
    <>
      <div id="tickerLayout" className="tickerLayout">
        <LinkAccessible />
        {settings.ticker.enabledFormat.length > 0 && (
          <Ticker tickerType={settings.ticker.tickerType} />
        )}
      </div>
    </>
  );
}

export default HeaderLayout;
