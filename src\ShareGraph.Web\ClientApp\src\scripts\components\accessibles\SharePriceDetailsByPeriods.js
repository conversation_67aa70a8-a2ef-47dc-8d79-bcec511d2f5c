import { useEffect, useLayoutEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';

import useHistoricalDatas from '../../customHooks/useHistoricalDatas';
import useQuery from '../../customHooks/useQuery';
import useSharePricePerformance from '../../customHooks/useSharePricePerformance';
import {
  classNames,
  convertToIntArray,
  formatDate,
  getDateTimeParam,
  pickBy,
  stringifyQuery,
  translateStringFormat
} from '../../helper';
import appConfig from '../../services/app-config';
import i18n from '../../services/i18n';
import Table from '../commons/accessibilities/Table';
import { MAP_TYPE_PERIOD_QUERY, PERIOD_TYPES, TIME_UNITS, VIEW_TYPES } from '../../common';
import { useChangeQuoteCurrency, useSelectedCurrency } from '../../customHooks/useSelectedCurrency';

const SharePriceDetailsByPeriods = () => {
  const location = useLocation();
  const setting = appConfig.get();
  // [p: peerIds, i: indices, mas: movingAverages]
  const search = useQuery({ arrays: ['p', 'i', 'mas'] });
  const selectedCurrency = useSelectedCurrency();

  const [{ instruments, compareColumns, additionalColumns }, { getHistoricalDatas }] = useHistoricalDatas();
  const [{ selectedInstrumentId, selectedSharePrice }] = useSharePricePerformance();
  const typePage = search.typePage || VIEW_TYPES.MONTHLY;
  const period = MAP_TYPE_PERIOD_QUERY[search.period] || PERIOD_TYPES.CUSTOM_RANGE;

  let fromDate = search.startDate || '';
  let toDate = search.endDate || '';

  if (typePage === VIEW_TYPES.DAILY) {
    fromDate = search.dailyFrom;
    toDate = search.dailyTo;
  }

  const getPeriod = typePage => {
    switch (typePage) {
      case VIEW_TYPES.DAILY:
        return i18n.translate('daily');
      case VIEW_TYPES.WEEKLY:
        return i18n.translate('weekly');
      default:
        return i18n.translate('monthly');
    }
  };

  useLayoutEffect(() => {
    const appInnerDOM = document.querySelector('.accessibility-app__inner');
    window.xprops?.scrollTop(0);
  }, [instruments.length, location]);

  const handleGetHistoricalDatas = (instrumentId) => {
    if (!instrumentId) return;
    const { p: peerIds, i: indexIds } = search;
    getHistoricalDatas(
      pickBy({
        instrumentId,
        type: typePage,
        from:
          fromDate && (period === PERIOD_TYPES.CUSTOM_RANGE || period === PERIOD_TYPES.ALL)
            ? getDateTimeParam(dayjs(fromDate).toDate())
            : null,
        to:
          toDate && (period === PERIOD_TYPES.CUSTOM_RANGE || period === PERIOD_TYPES.ALL)
            ? getDateTimeParam(dayjs(toDate).toDate())
            : null,
        period,
        peers: peerIds && peerIds.length ? convertToIntArray(peerIds) : [],
        indices: indexIds && indexIds.length ? convertToIntArray(indexIds) : [],
        mas: search.mas && search.mas.length ? convertToIntArray(search.mas) : [],
        toCurrency: selectedCurrency?.code ? `"${selectedCurrency?.code}"` : ''
      })
    );
  };

  useEffect(() => {
    handleGetHistoricalDatas(selectedInstrumentId);
  }, [location, selectedInstrumentId]);

  const getDailyEvent = ({ dividendEvent, splitEvent, radio } = {}) => {
    if (dividendEvent && splitEvent) {
      return `${i18n.translate('cEventDividend')} ,Split ${radio}`;
    }
    if (dividendEvent) {
      return `${i18n.translate('cEventDividend')}`;
    }
    if (splitEvent) {
      return `Split ${radio}`;
    }
    return i18n.translate('notAvailable');
  };

  const columnsAvailable = [
    {
      columnDisplay:
        typePage === VIEW_TYPES.DAILY ? i18n.translate('date') : i18n.translate('periodStartingFromLabel'),
      fieldName: 'dateTimeFormat'
    },
    ...additionalColumns,
    ...compareColumns,
    {
      columnDisplay:
        typePage === VIEW_TYPES.DAILY ? i18n.translate('event') : i18n.translate('linkToDailyDataLabel'),
      fieldName: 'linkToDailyData',
      render: row =>
        typePage === VIEW_TYPES.DAILY ? (
          getDailyEvent(row)
        ) : (
          <Link
            className="link-to-data"
            to={{
              ...location,
              search: stringifyQuery({
                ...search,
                dailyFrom: formatDate(row.dateTime, {isISO: true}),
                dailyTo: formatDate(row.dateTime, {
                  isISO: true,
                  add: { type: TIME_UNITS[typePage], amount: 1 }
                }),
                period: PERIOD_TYPES.CUSTOM_RANGE,
                typePage: VIEW_TYPES.DAILY
              })
            }}
          >
            {i18n.translate('dailyPrices')}
          </Link>
        )
    }
  ];

  useChangeQuoteCurrency(() => {
    handleGetHistoricalDatas(selectedInstrumentId);
  });

  return (
    <>
      <h1 id="titlePage">
        {i18n.translate('accessibleShareGraph')} –{' '}
        {classNames({
          [i18n.translate('monthlyBreakdown')]: typePage === VIEW_TYPES.MONTHLY,
          [i18n.translate('weeklyBreakdown')]: typePage === VIEW_TYPES.WEEKLY,
          [i18n.translate('dailyBreakdown')]: typePage === VIEW_TYPES.DAILY
        })}
      </h1>
      <Table
        className="share-performance-table__periods"
        caption={translateStringFormat('sharePriceDetailsCaptionByPeriod', [
          getPeriod(typePage),
          selectedSharePrice.currency,
          formatDate(fromDate, { format: setting.accessibilities.formats?.date || 'DD MMMM, YYYY' }),
          formatDate(toDate, { format: setting.accessibilities.formats?.date || 'DD MMMM, YYYY' })
        ])}
        columns={columnsAvailable}
        data={instruments}
      />
      <div id="viewmonthly">
        <nav id="navviewmonthly" className="link-to-data__monthly-and-weekly">
          {(typePage === VIEW_TYPES.WEEKLY || typePage === VIEW_TYPES.DAILY) && (
            <Link
              id="lbtMonthlyView"
              className="link-to-data"
              to={{
                ...location,
                search: stringifyQuery(
                  pickBy({
                    ...search,
                    typePage: VIEW_TYPES.MONTHLY
                  })
                )
              }}
            >
              {i18n.translate('sharePriceDetailsMonthlyLink')}
            </Link>
          )}
          {typePage === VIEW_TYPES.DAILY && (
            <span id="splitMonthlyWeekly" role="presentation" aria-hidden="true">
              |
            </span>
          )}
          {(typePage === VIEW_TYPES.MONTHLY || typePage === VIEW_TYPES.DAILY) && (
            <Link
              className="link-to-data"
              id="lbtWeeklyView"
              to={{
                ...location,
                search: stringifyQuery(
                  pickBy({
                    ...search,
                    typePage: VIEW_TYPES.WEEKLY
                  })
                )
              }}
            >
              {i18n.translate('sharePriceDetailsWeeklyLink')}
            </Link>
          )}
        </nav>
      </div>
    </>
  );
};

export default SharePriceDetailsByPeriods;
