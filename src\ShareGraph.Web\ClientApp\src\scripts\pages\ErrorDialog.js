export default function ErrorDialog() {
  async function handleConfirm() {
    window.xprops.onConfirm();
    window.xprops.close();
  }

  return (
    <div className="error-dialog-confirm" id="printDialogContainer">
      <h4 className="error-dialog-confirm__title">Oops!</h4>
      <div className="error-dialog-confirm__content">{window.xprops.message}</div>
      <button className="error-dialog-confirm__close-btn" onClick={handleConfirm}>
        Go back
      </button>
    </div>
  );
}
