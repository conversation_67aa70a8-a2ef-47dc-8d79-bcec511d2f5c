const webpack = require('webpack');
const Dotenv = require('dotenv-webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const paths = require('./paths');
const { resolve } = require('path');
const ESLintPlugin = require('eslint-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');


const eslintOptions = {
  extensions: ['js'],
  exclude: [
    '/node_modules/'
  ],
  cache: false,
  overrideConfigFile: paths.eslintConfigPath,
  emitWarning: true,
  // Fail only on errors
  failOnWarning: false,
  failOnError: false,
  // Toggle autofix
  fix: false,
  formatter: require('eslint/lib/cli-engine/formatters/stylish')
};

module.exports = {
  mode: 'development',
  devtool: 'cheap-module-source-map',
  devServer: {
    hot: true,
    static: {
      directory: paths.outputPath
    },
    // Configured by NetCore Middleware
    /*port: 3000,
    proxy: {
        '/api': 'http://localhost:3001',
    },
    open: true,
    */
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    compress: true,
    historyApiFallback: true
  },
  module: {
  },
  output: {
    path: paths.outputPath,
    filename: 'js/[name].js',
    chunkFilename: 'js/[name].js',
    assetModuleFilename: 'assets/[name]-[contenthash:8][ext]'
    //library: 'euroland',
    //libraryTarget: 'umd'
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: "css/[name].css",
      chunkFilename: "css/[id].css",
    }),
    new HtmlWebpackPlugin({
      template: resolve(__dirname, '../', '../', 'index.html'),
      filename: resolve(__dirname, '../', '../', 'dist', 'index.html'),
      inject: 'body',
      chunks: ['main'],
      cache: false
    }),
    new ESLintPlugin(eslintOptions),
    new Dotenv({
      path: paths.envDevPath, // Path to .env.development file
      expand: true
    }),
    new Dotenv({
      path: paths.envPath, // Path to .env file
      expand: true
    })
    //new webpack.HotModuleReplacementPlugin()
  ],
  optimization: {
    runtimeChunk: 'single',
    moduleIds: 'hashed',
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendor: {
          priority: -10,
          test: /[\\/]node_modules[\\/]/,
          name: 'vendor'
        },
        "chartiq": {
          priority: -8,
          test: /[\\/]node_modules[\\/]chartiq[\\/]/,
          name: 'chartiq'
        },
        realtimelibe: {
          priority: -8,
          test: /[\\/]node_modules[\\/](?:[@]azure[\\/])/,
          name: 'realtimelib'
        }
      },
    }
  }
};
