﻿<div class="ticker--single-ticker-2">
  <div class="ticker__col">
    <h3 class="ticker__name">
      {tickerName} ({marketAbbreviation})
    </h3>
    <h2 class="ticker__price">{last} <span class="ticker__code">{currencyCodeStr}</span></h2>
    <p class="ticker__change">
      <span class="ticker__change-value">
        <span>
          <i class="fs fs-triangle-up"></i>
          <i class="fs fs-triangle-down"></i>
        </span>
        <span>{change}</span>&nbsp;
        <span class="ticker__change-percentage">{changePercentage}%</span>
      </span>
    </p>
    <p class="ticker__relative-volume">
      <span class="volume__label">{volumeLabel}:</span>
      <span class="volume__value">{volume}</span>
      <span class="tooltip-wrapper">
        <span class="tooltip">
          <span class="tooltip__content">{relativeVolumelabel}</span>
        </span>
        <i class="fs fs-arrow-increase"></i>
      </span>
      <span class="relative-volume__value">{volumeChange} X </span>
    </p>
    <!-- <p class="status-closed">
      <i class="fs fs-close-radio"></i>
      <span class="market-name">{marketName}</span>
      <span class="market-status">{marketCloseLabel}. </span>
      <span class="market-date">{marketWillOpenLabel} {dateTimeToMarketOpen}</span>
    </p> -->
    <!-- <p class="status-opened">
      <i class="fs fs-checked-radio"></i>
      <span class="market-name">{marketName} {marketOpenedLabel}. </span>
      <br>
      <span class="market-date">{marketWillCloseLabel} {timeToCloseMarket}</span>
    </p> -->
    <!-- <p class="status-openIn">
      <i class="fs fs-checked-radio"></i>
      <span class="market-openTime">{marketName} {marketOpenInLabel} {dateTimeToMarketOpen}</span>
      <br>
      <span class="market-date">{lastUpdatedDate}</span>
    </p> -->
  </div>
  <div class="ticker__col">
      <ul class="ticker__list">
        <li class="ticker__list-item ticker__list-item--open"><span>{openLabel}:</span><strong class="ticker__list-item--open-value">{open}</strong></li>
        <li class="ticker__list-item ticker__list-item--high"><span>{highLabel}:</span><strong class="ticker__list-item--high-value">{high}</strong></li>
        <li class="ticker__list-item ticker__list-item--low"><span>{lowLabel}:</span><strong class="ticker__list-item--low-value">{low}</strong></li>
        <!-- <li class="ticker__list-item ticker__list-item--volume"><span>{volumeLabel}:</span><strong class="ticker__list-item--volume-value">{volume}</strong></li> -->
        <li class="ticker__list-item ticker__list-item--bid-ask"><span>{bidAskLabel}:</span><strong><span class="ticker__list-item--bid-value">{bid}</span>/<span class="ticker__list-item--ask-value">{ask}</span></strong></li>
      </ul>
  </div>
</div>
