import { useRef, useContext, useEffect, useLayoutEffect } from 'react';
import { AppContext } from '../../AppContext';
import { default as htmlGraphTemplate } from '../../../markup/ticker/SINGLE_TICKER_1.html'; // eslint-disable-line
import { default as htmlTableTemplate } from '../../../markup/ticker/TABLE_TICKER_SINGLE.html'; // eslint-disable-line
import {
  replaceKey, formatDateTime, convertNumber, convertNumberDecimal,
  getCustomPhraseTicker, convertPercentDecimal, convertMinutesToString, i18nTranslate, classByValue,
  marketStatusByValue, resetClasslist, getOpenDateTimeByMinute, getTimeZoneDisplay, getCountDownMinutesOpenMarket, translateStringFormat, formatChangeNumber, convertChangePercentDecimal
} from '../../helper';
import i18n from '../../services/i18n';
import { isArray } from '../../utils';
import { TICKER_SWITCH_TYPE, TICKER_ANIMATION } from '../../common';
import { usePrevious } from '../../customHooks/index';
import TimeStamp from '../TimeStamp';
import {useDelay} from '../../customHooks/useDelayUpdate';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import useWatchChange from './useWatchChange';
import {classNames} from '@euroland/libs';
import useDelayLoading from '../../customHooks/useDelayLoading';
import useRefreshing from './useRefreshing';

export const SingleTicker = ({ data: _data, tickerFormat }) => {
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER );
  const containerRef = useRef(null);
  const prevData = usePrevious(data);
  const settings = useContext(AppContext);
  const isTickerTableFormat = tickerFormat.toUpperCase() === TICKER_SWITCH_TYPE.TABLE_TYPE;
  const tickerData = data[0];
  const refreshing = useRefreshing(tickerData);
  const [delayLoading] = useDelayLoading(refreshing);
  const { lastUpdatedDate } = tickerData;
  const isRtl = window.appSettings.isRtl;
  const fieldChanged = useWatchChange(data[0], ['bid', 'ask', 'last', 'open', 'high', 'low', 'volume']);
  const tickerAnimation = tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE ? (settings.ticker.graphAnimation || 'fade').toLowerCase() :
                (settings.ticker.tableAnimation || 'fade').toLowerCase();

  useLayoutEffect(() => {
    if (isArray(data) && data.length) {
      changeStyle();
    }
  });

  const turnOverDisplay = settings?.shareDetails?.turnOverDisplay;


  useEffect(() => {
    if (prevData) {
          const instrumentId = tickerData.instrumentId;
          let prevItem = prevData
            ? prevData.find((e) => e.instrumentId == instrumentId)
            : null;
          let currentItem = data
            ? data.find((e) => e.instrumentId == instrumentId)
            : null;
          isTickerTableFormat ? applyAnimationTable() : applyAnimationGraph(prevItem, currentItem);
    }
  });

  const addAnimation = (type, fieldChange, item) => {
        if (prevData) {
          let currentItem = tickerData;
          let prevItem = prevData
            ? prevData.find((e) => e.instrumentId == currentItem.instrumentId)
            : null;

          //add animation for change
          let changeValue = currentItem[fieldChange] - prevItem[fieldChange];
          const queryType = `.ticker__change--${type}`;
          const queryChangeValue = queryType + ' .ticker__change-value';
          const queryAnimation = queryType + ' .ticker__animation-inner';
          const queryTransform = `transforming-top-${type}`;
          item.querySelector(queryChangeValue).innerText = prevItem[fieldChange];
          const cloneChangeNode = item.querySelector(queryChangeValue).cloneNode();
          cloneChangeNode.innerText = currentItem[fieldChange];
          if (item.querySelectorAll(`${queryChangeValue}`).length < 2) {
            item.querySelector(`${queryAnimation}`).append(cloneChangeNode);
          }
          changeValue >= 0 ? item.classList.add(`${queryTransform}`, `${queryTransform}--increase`) : item.classList.add('transforming-top', `${queryTransform}--decrease`);
          setTimeout(() => {
            item.classList.remove(`${queryTransform}`, `${queryTransform}--increase`, `${queryTransform}--decrease`);
            item.querySelectorAll(`${queryChangeValue}`).length >= 2 && item.querySelector(`${queryChangeValue}:first-child`).remove();
          }, 1000);

      }
  };

  const applyAnimationTable = () => {
      const item = containerRef.current.querySelector('.table__body-tr');
      if (!item) return;
      if (tickerAnimation.toUpperCase() === TICKER_ANIMATION.TRANSFORM) {
        addAnimation('number', 'change', item);
        addAnimation('percent', 'changePercentage', item);
      }
    };


  const applyAnimationGraph = (prevItem, currentItem) => {

      const item = containerRef.current;
      if (!item) return;
      let lastValue = currentItem.last - prevItem.last;
      item.classList.remove('animate--increase', 'animate--decrease', 'animate--neutral' );
      if (lastValue === 0){
        item.classList.add('animate--neutral');
        return;
      }
      lastValue > 0 ? item.classList.add('animate--increase') : item.classList.add('animate--decrease');
  };

  function changeStyle() {
    const { percent52W, low52W, high52W, volumeChange, change, last, high, low, changePercentage, marketStatus } = tickerData;
    resetClasslist(containerRef.current, ['indicator-', 'market-']);
    if (isTickerTableFormat) {
      if (!containerRef.current.querySelector('.table__body-tr')) return;
      containerRef.current.querySelector('.table__body-tr').classList.add(classByValue(change), `day-range--${classByValue(changePercentage)}`, marketStatusByValue(marketStatus));
    }
    else {
      containerRef.current.classList.add(classByValue(change), `day-range--${classByValue(changePercentage)}`,
        `w52-range--${classByValue(percent52W)}`, `volume--${classByValue(volumeChange)}`, marketStatusByValue(marketStatus));
      let percentDayRange = _calCulatePercent(last, low, high);
      let percent52WRange = _calCulatePercent(last, low52W, high52W);
      let dayRangeElement = containerRef.current.querySelector('.day-range .slider__rectangle .fs-triangle-down');
      if (dayRangeElement)
        dayRangeElement.style[`${isRtl ? 'right' : 'left'}`] = percentDayRange + '%';
      let w52RangeElement = containerRef.current.querySelector('.w52-range .slider__rectangle .fs-triangle-down');
      if (w52RangeElement)
        w52RangeElement.style[`${isRtl ? 'right' : 'left'}`] = percent52WRange + '%';
    }

  }

  function _getTemplateHtml() {
    return isTickerTableFormat ? settings.ticker.tableTickerTemplate || htmlTableTemplate : settings.ticker.graphTickerTemplate || htmlGraphTemplate;
  }

  function _renderTemplate(item) {
    if (isArray(item) && item.length && item[0]) {
      item = item[0];
      const tickerHTML = _getTemplateHtml();
      if (isTickerTableFormat) {
        const tableHTMLDOM = new DOMParser().parseFromString(tickerHTML, 'text/xml');
        const tableRowHTML = tableHTMLDOM.querySelector('.table__body-tr');
        if (!tableRowHTML) return '<h2>Table HTML template not recognized</h2>';
      }

      const labels = _getLabel();
      const dataTicker = _normalizeData(item);
      const dataTemplate = { ...dataTicker, ...labels };
      return replaceKey(tickerHTML, dataTemplate);
    }

    return '<h2> No data</h2>';

  }

  function _normalizeData(item) {
    const data = { ...item };
    const countDownMinutes = getCountDownMinutesOpenMarket(item);
    data.bid = convertNumberDecimal(item.bid);
    data.ask = convertNumberDecimal(item.ask);
    data.change = formatChangeNumber(item.change);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = convertNumberDecimal(Math.max(item.high, item.last));
    data.high52W = convertNumberDecimal(Math.max(item.high52W, item.last));
    data.last = convertNumberDecimal(item.last);
    data.low = convertNumberDecimal(Math.min(item.low, item.last));
    data.low52W = convertNumberDecimal(Math.min(item.low52W, item.last));
    data.open = convertNumberDecimal(item.open);
    data.percent52W  = convertPercentDecimal(item.percent52W );
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatChangeNumber(item.volumeChange);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.timeToCloseMarket = `${convertMinutesToString(countDownMinutes, i18n.translate('hrs'), i18n.translate('mins'))} ${getTimeZoneDisplay(item.id)} `;
    data.dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, item.id);
    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';
    data.turnoverDisplay = i18n.translate(turnOverDisplay);
    //getTimeZoneDisplay();
    return getCustomPhraseTicker(data);
  }

  function _calCulatePercent(currValue, min, max) {
    let percent = 0;
    if (currValue < min) {
      percent = 0;
    }
    else if (currValue > max) {
      percent = 100;
    } else {
      percent = ((currValue - min) / (max - min)) * 100;
    }
    return percent;
  }

  function _getLabel() {
    const singleLabels = [
      'w52RangeLabel',
      'volumeLabel',
      'bidAskLabel',
      'marketCloseLabel',
      'marketOpenedLabel',
      'openLabel',
      'highLabel',
      'lowLabel',
      'marketOpenInLabel',
      'sharesLabel',
      'lastLabel',
      'changePercentageLabel',
      'changeLabel',
      'relativeVolumelabel',
      'marketWillOpenLabel',
      'marketWillCloseLabel',
      'weeks52HighLabel'
    ];

    return {
      dayRangeLabel: i18n.translate('rangeLabel'),
      ...i18nTranslate(singleLabels)
    };
  }

  return (
    <>
    {
      tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE &&
      <>
      <time className="ticker__time">{formatDateTime(lastUpdatedDate)}</time>
      <div
        data-change={fieldChanged.join(',')}
        ref={containerRef}
        className={classNames(`ticker__inner ticker__inner--${tickerAnimation}`, {
          streaming: data?.[0].isRT,
          loading: delayLoading
        })}
        dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}
      ></div>
      </>
    }

    {
      tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE &&
       <>
        <TimeStamp tickerData={tickerData} />
        <div className={`ticker-table table-responsive table-responsive--${tickerAnimation}`}>
          <div ref={containerRef} className={`ticker__inner ticker__inner--${tickerAnimation}`} dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}>
          </div>
        </div>
      </>
     }
    </>
  );
};
