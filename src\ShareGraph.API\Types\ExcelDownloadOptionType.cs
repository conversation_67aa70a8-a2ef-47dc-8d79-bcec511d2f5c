using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.ShareGraph.API.Types
{
  public class ExcelDownloadOptionType : ObjectType<ExcelDownloadOption>
  {
    protected override void Configure(IObjectTypeDescriptor<ExcelDownloadOption> descriptor)
    {
      descriptor
            .Field(t => t.IncludedTotalReturn)
            .ResolveWith<ExcelDownloadOptionResolver>(t => t.GetIncludedTotalReturn(default!));

      descriptor
            .Field(t => t.IncludedSelectedPeersAndIndicies)
            .ResolveWith<ExcelDownloadOptionResolver>(t => t.GetIncludedSelectedPeersAndIndicies(default!));
    }
  }
}
