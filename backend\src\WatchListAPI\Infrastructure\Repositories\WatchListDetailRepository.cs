﻿using Microsoft.EntityFrameworkCore;
using WatchListAPI.Entities;

namespace WatchListAPI.Infrastructure.Repositories
{
    public interface IWatchListDetailRepository : IRepositoryBase<EurolandIDProfileDbContext, WatchListDetail, Guid>
    {
        Task<WatchListDetail?> FindInstrumentAsync(int instrumentId, Guid watchListId);
        IQueryable<WatchListDetail> FindAllInstruments (Guid watchListId);
        Task<List<WatchListDetail>> FindByWatchListIdsAsync(IEnumerable<Guid> watchListIds);
    }
    public class WatchListDetailRepository : RepositoryBase<EurolandIDProfileDbContext, WatchListDetail, Guid>, IWatchListDetailRepository
    {
        public WatchListDetailRepository(EurolandIDProfileDbContext context) : base(context)
        {
        }

        public IQueryable<WatchListDetail> FindAllInstruments(Guid watchListId)
        {
            return _dbContext.Set<WatchListDetail>().Where(e => e.WatchListId == watchListId);
        }

        public async Task<WatchListDetail?> FindInstrumentAsync(int instrumentId, Guid watchListId)
        {
            return await _dbContext.Set<WatchListDetail>().FirstOrDefaultAsync(e => e.InstrumentId == instrumentId && e.WatchListId == watchListId);
        }

        public async Task<List<WatchListDetail>> FindByWatchListIdsAsync(IEnumerable<Guid> watchListIds)
        {
            return await _dbContext.Set<WatchListDetail>()
                .Where(d => watchListIds.Contains(d.WatchListId))
                .ToListAsync();
        }
    }
}
