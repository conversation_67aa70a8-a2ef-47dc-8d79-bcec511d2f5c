﻿using System.Diagnostics.Metrics;
using Microsoft.EntityFrameworkCore;
using WatchListAPI.Common;
using WatchListAPI.DTOs;
using WatchListAPI.Entities;

namespace WatchListAPI.Infrastructure.Repositories
{
    public interface IInstrumentRepository : IRepositoryBase<EurolandIDProfileDbContext, Instrument, Guid>
    {
        Task<PagingResult<InstrumentDto>> GetInstrumentsAsync(PagingRequestBaseDto input);
    }

    public class InstrumentRepository : RepositoryBase<SharkDbContext, Instrument, Guid>, IInstrumentRepository
    {
        public InstrumentRepository(SharkDbContext context) : base(context)
        {
        }

        public async Task<PagingResult<InstrumentDto>> GetInstrumentsAsync(PagingRequestBaseDto input)
        {
            var keyword = input.Keyword?.Trim().ToLower();
            if (string.IsNullOrEmpty(keyword))
            {
                return new();
            }
            var query = from i in _dbContext.Instrument
                        join m in _dbContext.Market on i.MarketId equals m.MarketNumber
                        join ip in _dbContext.InstrumentPrice on i.Id equals ip.InstrumentId into ips
                        from ip in ips.DefaultIfEmpty()
                        where (i.Ticker != null && i.Ticker.ToLower().Contains(keyword))
                                || (m.Abbreviation != null && m.Abbreviation.ToLower().Contains(keyword))
                        select new InstrumentDto
                        {
                            InstrumentId = i.Id,
                            Ticker = i.Ticker,
                            Abbreviation = m.Abbreviation,
                            LastPrice = ip.Last,
                            Change = ip.Change,
                            ChangePercentage = ip.ChangePercentage
                        };

            return new PagingResult<InstrumentDto>()
            {
                Items = await query
                    .OrderByDescending(e => e.LastPrice)
                    .Skip(input.GetSkip())
                    .Take(input.PageSize)
                    .ToListAsync(),
                TotalItems = await query.CountAsync(),
            };
        }
    }
}
