import { useRef, useContext, useEffect, useCallback } from 'react';
import { useSelector, useDispatch, useStore } from 'react-redux';

import { AppContext } from '../../AppContext';
import { IndicesItem } from './IndicesItem';
import { selectIndices } from '../../actions/indicesActions';
import useLayoutCarousel from '../../customHooks/useLayoutCarousel';
import TinySlider from '../CustomeCarousel/TinySlider';
import { throttle } from '../../helper';
import useStocksRealtime from '../../real-time/useStockRealtime';
import {updateStock} from '../../actions';
import isSameDayWithLastTick from '../../real-time/isSameDayWithLastTick';

const appSettings = window.appSettings;
export const Indices = () => {
  const settings = useContext(AppContext);
  const store = useStore();
  // get selected Indices Ids from state
  const selectedIndicesIds = useSelector(state => state.indices.selectedIndicesIds);
  // sort list indices by order attribute
  const indicesContainerRef = useRef();
  const tickerAnimation = settings.indices.animation || 'fade';
  const dispatch = useDispatch();
  // get data of indices
  const data = useSelector(state => state.indices.instruments);

  const refreshing = useSelector(state => state.indices.refreshing);
  const [{ dataOrderedCarousel }] = useLayoutCarousel(indicesContainerRef, {
    data,
    settings: settings.indices.indices
  });

  // check if refreshing or not, will add class loading
  useEffect(() => {
    if (!refreshing) return;

    if (indicesContainerRef.current) {
      const indicesContainer = indicesContainerRef.current;
      setTimeout(() => {
        indicesContainer.classList.add('loading');
        setTimeout(() => {
          indicesContainer.classList.remove('loading');
        }, 1000);
      }, 50);
    }
  }, [indicesContainerRef, refreshing]);

  useStocksRealtime(function(data) {
    if(!isSameDayWithLastTick({stockId: data.id, date: data.date, store })) return;
    dispatch(updateStock(data.id, { close: data.price, date: data.date }));
  }, settings.indices.indices.map(item => item.id));

  // event when indices select
  const onIndicesSelectedChange = instrumentId => {
    if (instrumentId) {
      dispatch(selectIndices(instrumentId));
    }
  };

  return (
    <div
      ref={indicesContainerRef}
      className={`indices__inner comparison__inner  comparison__animation--${tickerAnimation.toLowerCase()}`}
    >
      <TinySlider isRtl={appSettings?.isRtl}>
        {dataOrderedCarousel.map((itemWrap, indexWrap) => {
          return (
            <div key={indexWrap} className="carousel__item-wrap">
              {itemWrap.map((item, index) => (
                <div className="tiny-slider__item" key={index}>
                  <div>
                    <IndicesItem
                      isSelected={selectedIndicesIds.includes(item.instrumentId)}
                      onItemClick={onIndicesSelectedChange}
                      key={item.instrumentId}
                      data={item}
                    />
                  </div>
                </div>
              ))}
            </div>
          );
        })}
      </TinySlider>
    </div>
  );
};
