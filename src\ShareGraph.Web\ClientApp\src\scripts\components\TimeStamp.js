import { formatDateTime } from '../helper';
import i18n from '../services/i18n';

export default function TimeStamp({ tickerData }) {
  const { marketName, lastUpdatedDate } = tickerData;
  const isClose = tickerData.marketStatus === 'Close';
  return (
    <div className="time-stamp">
      <time className="time-stamp__time">
        {formatDateTime(lastUpdatedDate)} <span className="time-stamp__time-separate">|</span>
      </time>
      {isClose ? (
        <p className="status-closed">
          <i className="fs fs-close-radio"></i>
          <span className="market-name">
            {marketName} {i18n.translate('marketCloseLabel')}.
          </span>
        </p>
      ) : (
        <p className="status-opened">
          <i className="fs fs-checked-radio"></i>
          <span className="market-name">
            {marketName} {i18n.translate('marketOpenedLabel')}.
          </span>
        </p>
      )}
    </div>
  );
}
