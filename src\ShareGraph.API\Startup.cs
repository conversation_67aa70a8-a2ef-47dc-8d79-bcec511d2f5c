using Euroland.FlipIT.ShareGraph.API.Extensions;
using Euroland.FlipIT.ShareGraph.API.Infrastructure;
using Euroland.FlipIT.ShareGraph.API.Queries;
using Euroland.FlipIT.ShareGraph.API.Services;
using Euroland.FlipIT.ShareGraph.API.Types;
using Euroland.NetCore.ToolsFramework.AzureSetting;
using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog.Core;
using System;
using static System.Net.Mime.MediaTypeNames;

namespace Euroland.FlipIT.ShareGraph.API
{
  public class Startup
  {
    public Startup(IConfiguration configuration, IHostEnvironment environment)
    {
      Configuration = configuration;
      Environment = environment;
    }

    public IConfiguration Configuration { get; }
    public IHostEnvironment Environment { get; }

    public void ConfigureServices(IServiceCollection services)
    {
      services.AddHttpContextAccessor();

      // config euroland tool settings
      services.AddToolSetting(Environment, Configuration);

      // config cors for dev
      // In reality, the API always should be behind a reverse proxy, API management, etc.
      // Therefore it is better to offload CORS from that infrastructure.
      if (Environment.IsDevelopment())
      {
        services.AddCors(o => o.AddPolicy("MyCorsPolicy", builder =>
        {
          builder.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
        }));
      }

      // config GraphQL Server
      services
          .AddGraphQLServer()
          .AddQueryType(d => d.Name("Query"))
              .AddTypeExtension<ConfigurationQueries>()
          .AddType<SettingType>()
          .AddType<ConfigurationType>()
          .AddType<StudyType>()
          .AddType<ExcelDownloadOptionType>()
          .AddType<BlinkingPricePointerType>()
          .InitializeOnStartup();

      // services.AddOptions<StorageConfiguration>()
      //         .Bind(Configuration.GetSection(nameof(StorageConfiguration)));

      //services.AddAzureBlobClients(Configuration);

      //services.AddToolSettingStorage(Configuration);
      services.AddScoped<IGraphQLClientFactory, DefaultGraphQLClientFactory>();
      services.AddScoped<ICurrencyService, CurrencyService>();
      services.AddTickerTemplate(Environment, Configuration);
      services.AddTranslationService(Configuration);
      //services.AddControllers();
      services.AddHealthChecks();
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, Logger logger)
    {
      if (env.IsDevelopment())
      {
        app.UseDeveloperExceptionPage();
      }
      else
      {
        app.UseExceptionHandler(exceptionHandlerApp =>
        {
          exceptionHandlerApp.Run(async context =>
          {
            context.Response.ContentType = Text.Plain;

            var exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
            var exceptionType = exceptionHandlerFeature?.Error;
            if (IsSettingNotFoundException(exceptionType))
            {
              context.Response.StatusCode = StatusCodes.Status404NotFound;
              await context.Response.WriteAsync("Not found company setting.");
            }
            else
            {
              await context.Response.WriteAsync("An exception was thrown.");
              context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            }
          });
        });
      }

      app.UseCompanyStyleSheet(Configuration, Environment);

      if (Configuration.GetValue<bool>("UseAzureFileSettingStorage", false))
      {
        var azureFileOptions = new AzureFileSettingOptions()
        {
          ConnectionString = Configuration.GetConnectionString("ToolsConfig"),
          ShareName = Configuration["AzureFileShareName"]
        };
        app.UseAppSetting(azureFileOptions);
      }
      else
      {
        if (Configuration.GetValue<bool>("OpifexPreviewEnabled", false))
        {
          app.UseWhen(context => context.IsPreviewMode(), app =>
             app.AddPreviewAppSetting(logger));
        }

        app.UseWhen(context => !context.IsPreviewMode(), app =>
          app.UseAppSetting());
      }

      if (Environment.IsDevelopment())
      {
        app.UseCors("MyCorsPolicy");
      }

      app.UseRouting();

      //app.UseAuthorization();

      app.UseEurolandRequestLocalization();

      app.UseEndpoints(endpoints =>
      {
        endpoints.MapHealthChecks("/health");
        endpoints.MapGraphQL();
        //endpoints.MapControllers();
      });
    }

    private static bool IsSettingNotFoundException(Exception ex)
    {
      return ex is Euroland.NetCore.ToolsFramework.Setting.SettingFileNotFoundException
        || ex.InnerException is Euroland.NetCore.ToolsFramework.Setting.SettingFileNotFoundException;
    }
  }
}
