import { getCustomPhrasePeer } from '../helper';

export default class Peer {
  constructor(params = {}) {
    Object.assign(this, params);
    this.peerData = getCustomPhrasePeer(params);
    this.title = this.peerData?.tickerName || params.shareName || '';
    this.id = params.instrumentId ? `${params.instrumentId}_p` : '';
    this.value = params.instrumentId ? `${params.instrumentId}_p` : '';
  }
}
