import i18n from '../../services/i18n';

/**
 * chartLoading provider method to control loading behavior
 * loading only hidden when processLoading empty
 * @example
 * - we are have fetch data. we want to show loading, set chartLoading.show('any_name')
 * - when fetch done we set chartLoading.hide('any_name') to hidden loading
 */
export const chartLoading = {
  processLoading: {},
  get: function () { return document.querySelector('cq-loader'); },

  setStatus: function (text) { 
    if(!document.querySelector('#loading-status')) return;
    const id = setTimeout(() => {
      document.querySelector('#loading-status').innerHTML = text;
      clearTimeout(id);
    }, 100);
  },

  show: function (label) {
    if(!label) return ;
    if(!(label in this.processLoading)) {
      this.processLoading[label] = undefined;
    }

    if(!this.get()) return;
    this.setStatus(i18n.translate('loading'));
    this.get().show();
  },
  hide: function (label) {
    if(!label) return ;

    if(label in this.processLoading) {
      delete this.processLoading[label];
    }

    if(!this.get() || Object.keys(this.processLoading).length > 0) return;
    this.setStatus(i18n.translate('loadingComplete'));
    this.get().hide();
  },
  hideAll: function () {
    Object.keys(this.processLoading).forEach(key => {
      this.hide(key);
    });
  },
  isLoading: function (label) {
    return label in this.processLoading;
  },
  self: function() {
    const labelRandom = Math.random().toString(32).slice(2,7);
    return {
      show: () => this.show(labelRandom),
      isLoading: () => this.isLoading(labelRandom),
      hide: () => this.hide(labelRandom)
    };
  }
};
