import { hexToHSL, hslToHex } from '../../utils/color';

function lighterThan (color, alpha) {
  const hsl = hexToHSL(color);
  const more = ((100 - hsl.l) * (1 - alpha));

  return hslToHex(hsl.h, hsl.s, hsl.l + more);
}


/**
 *
 * @param {string} num
 */
function numberCssToNumberJs(num) {
  if (typeof num !== 'string') return;

  let result = parseFloat(num);

  if (!Number.isNaN(result)) {
    return result;
  }

  result = parseFloat('0' + num);

  if (!Number.isNaN(result)) {
    return result;
  }
}

/**
 *
 * @param {string} url
 * @returns { Promise<HTMLImageElement> }
 */
const loadImage = (url) => new Promise((resolve, reject) => {
  const img = new Image();
  img.crossOrigin = 'anonymous';
  img.onload = () => {
    img.width = img.naturalWidth;
    img.height = img.naturalHeight;
    resolve(img);
  };
  img.onerror = reject;
  img.decoding = 'sync';

  img.src = url;
});

/**
 *
 * @param {import('../../../lib/chartiq/chartiq').CIQ.ChartEngine} chartEngine
 * @param {Record<string, string>} style
 * @param {string} colorMainChart
 */
async function addBackgroundMountain (chartEngine, style, colorMainChart) {
  const lineStyle = chartEngine.chart.lineStyle;
  if(!lineStyle) return;
  const background = style.background;
  const isTransparentBackground = background.startsWith('rgba(0, 0, 0, 0)') || background.toLowerCase() === 'none';
  const backgroundImage = getUrlCss(style.backgroundImage ?? '');

  if(backgroundImage) {
    try {
      const image = await loadImage(backgroundImage);
      const canvas = /** @type {HTMLCanvasElement} */(chartEngine.chart.canvas);
      const canvasStx = canvas.getContext('2d');
      const canvasPattern = canvasStx?.createPattern(image, 'repeat');
      if(!canvasPattern) {
        console.warn(`Failed to create the chart mountain background image '${backgroundImage}`);
        return;
      }
      lineStyle.fillStyle = canvasPattern;
    } catch (e) {
      console.warn(e);
    }
  } else if(!isTransparentBackground) {
    const opacity = numberCssToNumberJs(style.opacity) ?? 0.5;
    const newBackgroundColor = lighterThan(colorMainChart, opacity) ?? colorMainChart;
    lineStyle.fillStyle = newBackgroundColor;
  }

  chartEngine.draw();
}


const getUrlCss = (cssUrl) => {
  const regex = /url\((.*)\)/;
  if(!regex.test(cssUrl)) return;
  return cssUrl.replace(/url\((.*)\)/, (_, group1) => group1.replace(/^["']/, '').replace(/["']$/, ''));
};


/**
 *
 * @param {import('../../../lib/chartiq/chartiq').CIQ.ChartEngine} chartEngine
 * @param { string } colorMainChart
 */
export default function mountainChart(chartEngine, colorMainChart) {
  const style = /** @type {Record<string, any>} */ ({
    ...chartEngine.canvasStyle('stx_mountain_chart')
  });

  const lineStyle = {
    color: colorMainChart
    // width: 2
  };

  chartEngine.setStyle(
    'stx_mountain_chart',
    'background-color',
    colorMainChart
  );
  chartEngine.setStyle(
    'stx_mountain_chart',
    'border-top-color',
    colorMainChart
  );
  // chartEngine.setStyle('stx_mountain_chart', 'width', 2);
  chartEngine.setLineStyle(lineStyle);
  chartEngine.setStyle('stx_scatter_chart', 'color', colorMainChart);

  addBackgroundMountain(chartEngine, style, colorMainChart).catch(e => console.error(e));
}
