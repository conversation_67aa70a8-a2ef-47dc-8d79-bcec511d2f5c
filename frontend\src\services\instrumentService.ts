import axios from 'axios';
import { getBearerToken } from '../utils/auth';

class InstrumentService {
  private baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:5111';

  async searchInstruments(page: number, pageSize: number, keyword: string) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/api/Instrument/find-all`,
        {
          params: {
            pageIndex: page,
            pageSize: pageSize,
            keyword: keyword
          },
          headers: {
            'Authorization': `Bearer ${getBearerToken()}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || `Failed to search instruments: ${error.message}`;
        throw new Error(errorMessage);
      }
      throw error;
    }
  }
}

export const instrumentService = new InstrumentService();