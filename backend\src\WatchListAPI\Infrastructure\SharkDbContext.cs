﻿using Microsoft.EntityFrameworkCore;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using System.Diagnostics.Metrics;
using WatchListAPI.Entities.SharkDb;

namespace WatchListAPI.Infrastructure
{
    public class SharkDbContext : DbContext
    {
        public SharkDbContext()
        {
        }

        public SharkDbContext(DbContextOptions<SharkDbContext> options) : base(options)
        {
        }

        public virtual DbSet<Entities.SharkDb.Instrument> Instrument { get; set; }
        public virtual DbSet<Market> Market { get; set; }
        public virtual DbSet<InstrumentPrice> InstrumentPrice { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
        }
    }
}
