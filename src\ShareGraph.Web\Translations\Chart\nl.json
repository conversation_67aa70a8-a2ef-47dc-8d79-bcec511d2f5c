{"(UTC-06:00) Central Time (US and Canada)": {"key": "UTC0600CentralTimeUSandCanada", "value": "(UTC-06: 00) Centrale tijd (VS en Canada)"}, "(UTC-06:00) Guadalajara, Mexico City, Monterrey": {"key": "UTC0600GuadalajaraMexicoCityMonterrey", "value": "(UTC-06: 00) Guadalajara, Mexico City, Monterrey"}, "(UTC-06:00) Saskatchewan": {"key": "UTC0600Saskatchewan", "value": "(UTC-06: 00) Saskatchewan"}, "(UTC-07:00) Arizona": {"key": "UTC0700Arizona", "value": "(UTC-07: 00) Arizona"}, "(UTC-07:00) Chihuahua, Mazatlan": {"key": "UTC0700ChihuahuaMazatlan", "value": "(UTC-07: 00) Chihuahua, Mazatlan"}, "(UTC-07:00) Mountain Time (US and Canada)": {"key": "UTC0700MountainTimeUSandCanada", "value": "(UTC-07: 00) Mountain Time (VS en Canada)"}, "(UTC-08:00) Pacific Time (US and Canada)": {"key": "UTC0800PacificTimeUSandCanada", "value": "(UTC-08: 00) Pacific Time (VS en Canada)"}, "(UTC-08:00) Tijuana": {"key": "UTC0800Tijuana", "value": "(UTC-08: 00) Tijuana"}, "(UTC-09:00) Alaska": {"key": "UTC0900Alaska", "value": "(UTC-09: 00) Alaska"}, "(UTC-10:00) Hawaii": {"key": "UTC1000Hawaii", "value": "(UTC-10: 00) Hawaii"}, "ATR Trailing Stop": {"key": "ATRTrailingStop", "value": "ATR Trailing Stop"}, "ATR Trailing Stops": {"key": "ATRTrailingStops", "value": "ATR STRAILT STOPPEN"}, "Auto Select": {"key": "AutoSelect", "value": "Auto selecteren"}, "auto": {"key": "auto", "value": "Auto"}, "Average": {"key": "Average", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Average Line": {"key": "AverageLine", "value": "Gemiddelde lijn"}, "Average True Range": {"key": "AverageTrueRange", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> waar bereik"}, "Average Type": {"key": "AverageType", "value": "Gemiddelde type"}, "Awesome": {"key": "Awesome", "value": "Geweldig"}, "Awesome Oscillator": {"key": "AwesomeOscillator", "value": "Geweldige oscillator"}, "(UTC-03:30) Newfoundland and Labrador": {"key": "UTC0330NewfoundlandandLabrador", "value": "(UTC-03: 30) Newfoundland en Labrador"}, "(UTC-04:00) Asuncion": {"key": "UTC0400Asuncion", "value": "(UTC-04: 00) Asuncion"}, "(UTC-04:00) Atlantic Time (Canada)": {"key": "UTC0400AtlanticTimeCanada", "value": "(UTC-04: 00) Atlantic Time (Canada)"}, "(UTC-04:00) Caracas": {"key": "UTC0400Caracas", "value": "(UTC-04: 00) Caracas"}, "(UTC-04:00) Georgetown, La Paz, Manaus, San Juan": {"key": "UTC0400GeorgetownLaPazManausSanJuan", "value": "(UTC-04: 00) Georgetown, La Paz, Manaus, San Juan"}, "(UTC-04:00) Santiago": {"key": "UTC0400Santiago", "value": "(UTC-04: 00) Santiago"}, "(UTC-05:00) Bogota, Lima, Quito, Rio Branco": {"key": "UTC0500BogotaLimaQuitoRioBranco", "value": "(UTC-05: 00) Bogota, Lima, Quito, Rio Branco"}, "(UTC-05:00) Eastern Time (US and Canada)": {"key": "UTC0500EasternTimeUSandCanada", "value": "(UTC-05: 00) Eastern Time (VS en Canada)"}, "(UTC-05:00) Indiana (East)": {"key": "UTC0500IndianaEast", "value": "(UTC-05: 00) Indiana (oost)"}, "(UTC-06:00) Central America": {"key": "UTC0600CentralAmerica", "value": "(UTC-06: 00) Midden-Amerika"}, "(UTC+12:00) Anadyr, Kamchatka": {"key": "UTC1200AnadyrKamchatka", "value": "(UTC+12: 00) <PERSON><PERSON>r, Kamchatka"}, "(UTC+12:00) Auckland, Wellington": {"key": "UTC1200AucklandWellington", "value": "(UTC+12: 00) Auckland, Wellington"}, "(UTC+12:45) Chatham": {"key": "UTC1245Chatham", "value": "(UTC+12: 45) Chatham"}, "(UTC+13:00) Samoa": {"key": "UTC1300Samoa", "value": "(UTC+13: 00) Samoa"}, "(UTC+13:00) Tonga": {"key": "UTC1300Tonga", "value": "(UTC+13: 00) Tonga"}, "(UTC+14:00) Kiritimati": {"key": "UTC1400Kiritimati", "value": "(UTC+14: 00) <PERSON><PERSON><PERSON><PERSON>"}, "%b": {"key": "b", "value": "%B"}, "%D": {"key": "D", "value": "%D"}, "%D Moving Average Type": {"key": "DMovingAverageType", "value": "%D voortschrijdend gemiddelde type"}, "%D Periods": {"key": "DPeriods", "value": "%D perioden"}, "Abstract": {"key": "Abstract", "value": "Abstract"}, "Acc Swing": {"key": "AccSwing", "value": "ACC Swing"}, "Accumulation/Distribution": {"key": "AccumulationDistribution", "value": "Accumulatie/distributie"}, "Accumulative Swing Index": {"key": "AccumulativeSwingIndex", "value": "Accumulatieve swingindex"}, "Add": {"key": "Add", "value": "Toevoegen"}, "ADD": {"key": "ADD", "value": "TOEVOEGEN"}, "Add/Remove Favorite": {"key": "AddRemoveFavorite", "value": "Favoriet toevoegen/verwijderen"}, "Add Stop Loss": {"key": "AddStopLoss", "value": "<PERSON><PERSON><PERSON> stopverlies toe"}, "Add Take Profit": {"key": "AddTakeProfit", "value": "<PERSON><PERSON><PERSON> winst toe"}, "ADX": {"key": "ADX", "value": "ADX"}, "ADX/DMS": {"key": "ADXDMS", "value": "Adx_dms"}, "ALL": {"key": "ALLCAPITAL", "value": "ALLE"}, "All": {"key": "All_chart", "value": "Alle"}, "All-Time High Lookback Period": {"key": "AllTimeHighLookbackPeriod", "value": "High-lookback-periode aller tijden"}, "Alligator": {"key": "Alligator", "value": "Alligator"}, "Annotation": {"key": "Annotation", "value": "<PERSON><PERSON><PERSON>"}, "Arc": {"key": "Arc", "value": "<PERSON><PERSON>"}, "Aroon": {"key": "Aroon", "value": "Aloon"}, "Aroon Down": {"key": "AroonDown", "value": "<PERSON><PERSON>"}, "Aroon Osc": {"key": "AroonOsc", "value": "<PERSON>roon O<PERSON>c"}, "(UTC+09:00) Seoul": {"key": "UTC0900Seoul", "value": "(UTC+09: 00) Seoul"}, "(UTC+09:30) Adelaide": {"key": "UTC0930Adelaide", "value": "(UTC+09: 30) Adelaide"}, "(UTC+09:30) Darwin": {"key": "UTC0930<PERSON><PERSON><PERSON>", "value": "(UTC+09: 30) Darwin"}, "(UTC+10:00) Brisbane": {"key": "UTC1000Brisbane", "value": "(UTC+10: 00) Brisbane"}, "(UTC+10:00) Canberra, Melbourne, Sydney": {"key": "UTC1000CanberraMelbourneSydney", "value": "(UTC+10: 00) Canberra, Melbourne, Sydney"}, "(UTC+10:00) Guam, Port Moresby": {"key": "UTC1000GuamPortMoresby", "value": "(UTC+10: 00) Guam, Port Moresby"}, "(UTC+10:00) Ust-Nera, Vladivostok": {"key": "UTC1000UstNeraVladivostok", "value": "(UTC+10: 00) UST-<PERSON><PERSON>, Vladivostok"}, "(UTC+11:00) Magadan": {"key": "UTC1100Magadan", "value": "(UTC+11: 00) Magadan"}, "(UTC+11:00) Noumea, Solomon Islands": {"key": "UTC1100NoumeaSolomonIslands", "value": "(UTC+11: 00) Noumea, Solomon Islands"}, "(UTC+11:00) Sakhalin, Srednekolymsk": {"key": "UTC1100SakhalinSrednekolymsk", "value": "(UTC+11: 00) Sakhalin, Srednekolymsk"}, "(UTC-11:00) American Samoa, Midway Island": {"key": "UTC1100AmericanSamoaMidwayIsland", "value": "(UTC-11: 00) Amerikaan Samoa, Midway Island"}, "(UTC) Casablanca": {"key": "UTCCasablanca", "value": "(UTC) Casablanca"}, "(UTC) Dublin": {"key": "UTCDublin", "value": "(UTC) Dublin"}, "(UTC) Lisbon, London": {"key": "UTCLisbonLondon", "value": "(UTC) Lissabon, Londen"}, "(UTC) Greenwich Mean Time, Reykjavik": {"key": "UTCGreenwichMeanTimeReykjavik", "value": "(UTC) Greenwich Mean Time, Reykjavik"}, "(UTC+01:00) Algiers, Tunis": {"key": "UTC0100AlgiersTunis", "value": "(UTC+01: 00) Algiers, Tunis"}, "(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna": {"key": "UTC0100AmsterdamBerlinBernRomeStockholmVienna", "value": "(UTC+01: 00) Amsterdam, Berlijn, Bern, Rome, Stockholm, Wenen"}, "(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague": {"key": "UTC0100BelgradeBratislavaBudapestLjubljanaPrague", "value": "(UTC+01: 00) Belgrado, Bratislava, Boedapest, Ljubljana, Praag"}, "(UTC+01:00) Brussels, Copenhagen, Madrid, Paris": {"key": "UTC0100BrusselsCopenhagenMadridParis", "value": "(UTC+01: 00) Brussel, Kopenhagen, Madrid, Parijs"}, "(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb": {"key": "UTC0100SarajevoSkopjeWarsawZagreb", "value": "(UTC+01: 00) Sarajevo, Skopje, Warschau, Zagreb"}, "30m": {"key": "_30m", "value": "30m"}, "3M": {"key": "_3M", "value": "3m"}, "3Y": {"key": "_3Y", "value": "3y"}, "4 Hour": {"key": "_4Hour", "value": "4 uur"}, "4H": {"key": "_4H", "value": "4h"}, "5 Min": {"key": "_5Min", "value": "5 minuten min van 5 min minie 5 min minie 5 minuten minpem 5 minuten minpem 5 min"}, "5D": {"key": "_5D", "value": "5d"}, "5m": {"key": "_5m", "value": "5m"}, "5Y": {"key": "_5Y", "value": "5y"}, "6M": {"key": "_6M", "value": "6m"}, "-DI": {"key": "DI", "value": "-Di"}, "-VI": {"key": "VI", "value": "-Vi"}, "(Scroll for more options)": {"key": "Scrollformoreoptions", "value": "(<PERSON><PERSON> voor meer opties)"}, "(UTC-01:00) Azores": {"key": "UTC0100Azores", "value": "(UTC-01: 00) Azoren"}, "(UTC-01:00) Cape Verde Islands": {"key": "UTC0100CapeVerdeIslands", "value": "(UTC-01: 00) Kaapse eilanden"}, "(UTC-02:00) Mid-Atlantic": {"key": "UTC0200MidAtlantic", "value": "(UTC-02: 00) Mid-Atlantisch"}, "(UTC-03:00) Buenos Aires": {"key": "UTC0300BuenosAires", "value": "(UTC-03: 00) Buenos Aires"}, "(UTC-03:00) Montevideo": {"key": "UTC0300Montevideo", "value": "(UTC-03: 00) Montevideo"}, "(UTC-03:00) Punta Arenas": {"key": "UTC0300PuntaArenas", "value": "(UTC-03: 00) Punta Arenas"}, "(UTC-03:00) Sao Paulo": {"key": "UTC0300SaoPaulo", "value": "(UTC-03: 00) Sao Paulo"}, "Aroon Oscillator": {"key": "AroonOscillator", "value": "Aroon -oscillator"}, "Aroon Up": {"key": "AroonUp", "value": "<PERSON><PERSON> o<PERSON>"}, "Arrow": {"key": "Arrow", "value": "Pijl"}, "Attach": {"key": "Attach", "value": "Bijvoegen"}, "ATH Lookback Period": {"key": "ATHLookbackPeriod", "value": "Ath -lookback -periode"}, "ATR": {"key": "ATR", "value": "Huichelen"}, "ATR Bands": {"key": "ATRBands", "value": "ATR -bands"}, "ATR Bands Bottom": {"key": "ATRBandsBottom", "value": "ATR Bands Bottom"}, "ATR Bands Channel": {"key": "ATRBandsChannel", "value": "ATR Bands -kanaal"}, "ATR Bands Top": {"key": "ATRBandsTop", "value": "ATR Bands Top"}, "(UTC+08:00) Beijing, Chongqing, Hong Kong SAR": {"key": "UTC0800BeijingChongqingHongKongSAR", "value": "(UTC+08: 00) Beijing, Chongqing, Hong Kong SAR"}, "(UTC+08:00) Brunei, Kuala Lumpur, Singapore": {"key": "UTC0800BruneiKualaLumpurSingapore", "value": "(UTC+08: 00) Brunei, Kuala Lumpur, Singapore"}, "(UTC+08:00) Choibalsan, Ulaanbaatar": {"key": "UTC0800ChoibalsanUlaanbaatar", "value": "(UTC+08: 00) <PERSON><PERSON><PERSON>, Ulaanbaatar"}, "(UTC+08:00) Irkutsk": {"key": "UTC0800Irkutsk", "value": "(UTC+08: 00) Irkutsk"}, "(UTC+08:00) Manila, Taipei": {"key": "UTC0800ManilaTaipei", "value": "(UTC+08: 00) Manila, Taipei"}, "(UTC+08:00) Perth": {"key": "UTC0800Perth", "value": "(UTC+08: 00) Perth"}, "(UTC+08:45) Eucla": {"key": "UTC0845Eucla", "value": "(UTC+08: 45) Eucla"}, "(UTC+09:00) Chita, Khandyga, Yakutsk": {"key": "UTC0900ChitaKhandygaYakutsk", "value": "(UTC+09: 00) Chita, Khandyga, Yakutsk"}, "(UTC+09:00) Osaka, Sapporo, Tokyo": {"key": "UTC0900OsakaSapporoTokyo", "value": "(UTC+09: 00) Osaka, Sapporo, Tokyo"}, "(UTC+09:00) Pyongyang": {"key": "UTC0900Pyongyang", "value": "(UTC+09: 00) Pyongyang"}, "Axis Label": {"key": "AxisLabel", "value": "Aslabel"}, "Axis Label:": {"key": "AxisLabel", "value": "Axis -label:"}, "Axis Text": {"key": "AxisText", "value": "As tekst"}, "B": {"key": "B", "value": "B"}, "Background": {"key": "Background", "value": "Achtergrond"}, "Background Color": {"key": "BackgroundColor", "value": "<PERSON>chtergrondkleur"}, "Balance of Power": {"key": "BalanceofPower", "value": "<PERSON><PERSON><PERSON> van <PERSON>"}, "Bandwidth": {"key": "Bandwidth", "value": "Bandbreedte"}, "Bar": {"key": "Bar", "value": "Bar"}, "Bars": {"key": "Bars", "value": "Staven"}, "%K": {"key": "K", "value": "%K"}, "%K Double Smoothing Periods": {"key": "KDoubleSmoothingPeriods", "value": "%K dubbele gladde periodes"}, "%K Periods": {"key": "KPeriods", "value": "%K -periodes"}, "%K Smoothing Periods": {"key": "KSmoothingPeriods", "value": "%K glading perioden"}, "% View": {"key": "View", "value": "% Weergave"}, "% Change": {"key": "Change", "value": "% Wijziging"}, "+DI": {"key": "DI", "value": "+Di"}, "+VI": {"key": "VI", "value": "+VI"}, "1 D": {"key": "_1D", "value": "1 D"}, "1 Hour": {"key": "_1Hour", "value": "1 uur"}, "1 Mo": {"key": "_1Mo", "value": "1 mnd"}, "1 Standard Deviation (1Ïƒ)": {"key": "_1StandardDeviation1", "value": "1 standaardafwijking (1ïƒ)"}, "1 W": {"key": "_1W", "value": "1 W"}, "10 Min": {"key": "_10Min", "value": "10 min"}, "10m": {"key": "_10m", "value": "10m"}, "13px": {"key": "_13px", "value": "13px"}, "15 Min": {"key": "_15Min", "value": "15 minuten tot 15 minuten tot 15 minuten min"}, "15m": {"key": "_15m", "value": "15m"}, "1D": {"key": "_1D", "value": "1D"}, "1H": {"key": "_1H", "value": "1 uur"}, "1m": {"key": "_1m", "value": "1m"}, "1M": {"key": "_1M", "value": "1m"}, "12M": {"key": "_12M", "value": "12m"}, "1W": {"key": "_1W", "value": "1W"}, "1Y": {"key": "_1Y", "value": "1y"}, "2 Standard Deviation (1Ïƒ)": {"key": "_2StandardDeviation1", "value": "2 Standaardafwijking (1ïƒ)"}, "2 Standard Deviation (2Ïƒ)": {"key": "_2StandardDeviation2", "value": "2 Standaardafwijking (2ïƒ)"}, "3 Standard Deviation (1Ïƒ)": {"key": "_3StandardDeviation1", "value": "3 Standaardafwijking (1ïƒ)"}, "3 Standard Deviation (3Ïƒ)": {"key": "_3StandardDeviation3", "value": "3 Standaardafwijking (3ïƒ)"}, "30 Min": {"key": "_30Min", "value": "30 min"}, "1 Min": {"key": "_1Min", "value": "1 min min minie minuut 1 min min minie duur 1 minuut"}, "2 Mins": {"key": "_2_Mins", "value": "2 minuten"}, "5 Mins": {"key": "_5_Mins", "value": "5 minuten"}, "15 Mins": {"key": "_15_Mins", "value": "15 minuten"}, "30 Mins": {"key": "_30_Mins", "value": "30 minuten"}, "2Mins": {"key": "_2Mins", "value": "2 minuten"}, "5Mins": {"key": "_5Mins", "value": "5 minuten"}, "15Mins": {"key": "_15Mins", "value": "15 minuten"}, "30Mins": {"key": "_30Mins", "value": "30 minuten"}, "4 Hours": {"key": "_4Hours", "value": "4 uur"}, "(UTC+05:45) Kathmandu": {"key": "UTC0545Kathmandu", "value": "(UTC+05: 45) Kathmandu"}, "(UTC+06:00) Almaty": {"key": "UTC0600Almaty", "value": "(UTC+06: 00) Almaty"}, "(UTC+06:00) Astana, Dhaka": {"key": "UTC0600AstanaDhaka", "value": "(UTC+06: 00) Astana, Dhaka"}, "(UTC+06:00) Omsk": {"key": "UTC0600Omsk", "value": "(UTC+06: 00) OMSK"}, "(UTC+06:30) Yangon": {"key": "UTC0630Yangon", "value": "(UTC+06: 30) Yangon"}, "(UTC+07:00) Bangkok, Jakarta, Vietnam": {"key": "UTC0700BangkokJakartaVietnam", "value": "(UTC+07: 00) Bangkok, Jakarta, Vietnam"}, "(UTC+07:00) Barnaul, Novosibirsk, Tomsk": {"key": "UTC0700BarnaulNovosibirskTomsk", "value": "(UTC+07: 00) Barnaul, Novosibirsk, Tomsk"}, "(UTC+07:00) Hovd": {"key": "UTC0700Hovd", "value": "(UTC+07: 00) HOVD"}, "(UTC+07:00) Krasnoyarsk": {"key": "UTC0700Krasnoyarsk", "value": "(UTC+07: 00) Krasnoyarsk"}, "(UTC+07:00) Novokuznetsk": {"key": "UTC0700Novokuznetsk", "value": "(UTC+07: 00) Novokuznetsk"}, "(UTC+03:00) Nairobi": {"key": "UTC0300Nairobi", "value": "(UTC+03: 00) Nairobi"}, "(UTC+03:00) Simferopol": {"key": "UTC0300Simferopol", "value": "(UTC+03: 00) Simferopol"}, "(UTC+03:30) Tehran": {"key": "UTC0330Tehran", "value": "(UTC+03: 30) Teheran"}, "(UTC+04:00) Astrakhan, Samara, Saratov, Ulyanovsk": {"key": "UTC0400AstrakhanSamaraSaratovUlyanovsk", "value": "(UTC+04: 00) Astrakhan, Samara, Saratov, Ulyanovsk"}, "(UTC+04:00) Baku": {"key": "UTC0400Baku", "value": "(UTC+04: 00) Baku"}, "(UTC+04:00) Dubai, Muscat": {"key": "UTC0400DubaiMuscat", "value": "(UTC+04: 00) Dubai, Muscat"}, "(UTC+04:30) Kabul": {"key": "UTC0430Kabul", "value": "(UTC+04: 30) Kabul"}, "(UTC+05:00) Karachi, Tashkent": {"key": "UTC0500KarachiTashkent", "value": "(UTC+05: 00) Karachi, Tasjkent"}, "(UTC+05:00) Yekaterinburg": {"key": "UTC0500Yekaterinburg", "value": "(UTC+05: 00) Yekaterinburg"}, "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi": {"key": "UTC0530ChennaiKolkataMumbaiNewDelhi", "value": "(UTC+05: 30) Chennai, Kolkata, Mumbai, New Delhi"}, "(UTC+02:00) Athens, Bucharest": {"key": "UTC0200AthensBucharest", "value": "(UTC+02: 00) At<PERSON>e, Boekarest"}, "(UTC+02:00) Cairo": {"key": "UTC0200Cairo", "value": "(UTC+02: 00) Cairo"}, "(UTC+02:00) Cyprus": {"key": "UTC0200Cyprus", "value": "(UTC+02: 00) Cyprus"}, "(UTC+02:00) Harare, Johannesburg": {"key": "UTC0200HarareJohannesburg", "value": "(UTC+02: 00) Harare, Johannesburg"}, "(UTC+02:00) Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius": {"key": "UTC0200HelsinkiKievRigaSofiaTallinnVilnius", "value": "(UTC+02: 00) Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius"}, "(UTC+02:00) Jerusalem": {"key": "UTC0200Jerusalem", "value": "(UTC+02: 00) Jeruzalem"}, "(UTC+02:00) Kaliningrad": {"key": "UTC0200Kaliningrad", "value": "(UTC+02: 00) Kaliningrad"}, "(UTC+03:00) Baghdad, Kuwait, Qatar, Riyadh": {"key": "UTC0300BaghdadKuwaitQatarRiyadh", "value": "(UTC+03: 00) Bagdad, Koeweit, Qatar, Riyadh"}, "(UTC+03:00) Istanbul": {"key": "UTC0300Istanbul", "value": "(UTC+03: 00) Istanbul"}, "(UTC+03:00) Minsk, Moscow, Kirov, Volgograd": {"key": "UTC0300MinskMoscowKirovVolgograd", "value": "(UTC+03: 00) Minsk, Moskou, Kirov, Volgograd"}, "Bars Color": {"key": "BarsColor", "value": "<PERSON>s kleur"}, "Base Line": {"key": "BaseLine", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Base Line Period": {"key": "BaseLinePeriod", "value": "Basislijnperiode"}, "Baseline": {"key": "Baseline", "value": "Uitsteeksel"}, "Baseline Delta": {"key": "BaselineDelta", "value": "Baseline delta"}, "BATS BZX real-time.": {"key": "BATSBZXrealtime", "value": "BATS BZX Real-time."}, "Bearish": {"key": "Bearish", "value": "Bearish"}, "Beta": {"key": "Beta", "value": "<PERSON><PERSON><PERSON>"}, "Beta Callouts Candle Border": {"key": "BetaCalloutsCandleBorder", "value": "Beta callouts kaarsen grens"}, "Black": {"key": "Black", "value": "<PERSON><PERSON>"}, "Chande Mtm": {"key": "ChandeMtm", "value": "<PERSON><PERSON>"}, "Change Timezone": {"key": "ChangeTimezone", "value": "<PERSON><PERSON>i<PERSON>"}, "Channel": {"key": "Channel", "value": "<PERSON><PERSON><PERSON>"}, "Channel Fill": {"key": "ChannelFill", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Chart": {"key": "Chart", "value": "<PERSON><PERSON>"}, "Chart Preferences": {"key": "ChartPreferences", "value": "Grafiekvoorkeuren"}, "Chart Scale": {"key": "ChartScale", "value": "Grafiekschaal"}, "Chart Shared Successfully!": {"key": "ChartSharedSuccessfully", "value": "<PERSON><PERSON> met succes gedeeld!"}, "Chart Style": {"key": "ChartStyle", "value": "Grafiekstijl"}, "Chart Type": {"key": "ChartType", "value": "Grafiektype"}, "Boll %b": {"key": "<PERSON><PERSON><PERSON>", "value": "Boll %b"}, "Boll BW": {"key": "BollBW", "value": "Boll BW"}, "Bollinger %b": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Bollinger %B"}, "Bollinger Bands": {"key": "BollingerBands_chart", "value": "Bollinger -bands"}, "Bollinger Bands Bottom": {"key": "BollingerBandsBottom", "value": "Bollinger Bands Bottom"}, "Bollinger Bands Median": {"key": "BollingerBandsMedian", "value": "Bollinger Bands Mediaan"}, "Bollinger Bands Top": {"key": "BollingerBandsTop", "value": "Bollinger Bands Top"}, "Bollinger Bandwidth": {"key": "BollingerBandwidth", "value": "Bollinger bandbreedte"}, "Border": {"key": "Border", "value": "<PERSON><PERSON><PERSON>"}, "Bulge Threshold": {"key": "BulgeThreshold", "value": "Uitpuilende drempel"}, "Check": {"key": "Check", "value": "Rekening"}, "Choose language": {"key": "Chooselanguage", "value": "<PERSON><PERSON> taal"}, "Choose Timezone": {"key": "ChooseTimezone", "value": "<PERSON><PERSON>"}, "Choppiness Index": {"key": "ChoppinessIndex", "value": "Choppiness index"}, "Clear": {"key": "Clear", "value": "Duidelijk"}, "Clear All": {"key": "ClearAll", "value": "Duidelijk alles duidelijk"}, "Clear All Drawings": {"key": "ClearAllDrawings", "value": "Wis alle tekeningen"}, "Close": {"key": "Close_chart", "value": "Dichtbij"}, "Color": {"key": "Color", "value": "<PERSON><PERSON><PERSON>"}, "Colored Bar": {"key": "ColoredBar", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> balk"}, "Display Average": {"key": "DisplayAverage", "value": "G<PERSON>id<PERSON>d weergeven"}, "Distance(%)": {"key": "Distance", "value": "Afstand(%)"}, "Divergence": {"key": "Divergence", "value": "Afwijking"}, "Dividends": {"key": "Dividends", "value": "Dividenden"}, "Don''t see your study below? Type in your search here.": {"key": "DontseeyourstudybelowTypeinyoursearchhere", "value": "Zie je je studie hieronder niet?Typ hier uw zoekopdracht in."}, "Donchian Channel": {"key": "DonchianChannel", "value": "Donchiaans<PERSON> kanaal"}, "Donchian High": {"key": "DonchianHigh", "value": "Donchian High"}, "Donchian Low": {"key": "DonchianLow", "value": "Donchian Low"}, "Donchian Median": {"key": "DonchianMedian", "value": "Donchische mediaan"}, "Donchian Width": {"key": "DonchianWidth", "value": "Donchische breedte"}, "Candle Wick": {"key": "CandleWick", "value": "Candle Wick"}, "Candles": {"key": "Candles", "value": "<PERSON><PERSON><PERSON>"}, "Center Of Gravity": {"key": "CenterOfGravity", "value": "Zwaartepunt"}, "Chaikin MF": {"key": "ChaikinMF", "value": "Chaikin MF"}, "Chaikin Money Flow": {"key": "ChaikinMoneyFlow", "value": "<PERSON><PERSON><PERSON> gelds<PERSON>om"}, "Chaikin Vol": {"key": "ChaikinVol", "value": "Chaikin Vol"}, "Chaikin Volatility": {"key": "ChaikinVolatility", "value": "Chaikin -volatiliteit"}, "Chande Fcst": {"key": "ChandeFcst", "value": "Chande FCST"}, "Chande Forecast Oscillator": {"key": "ChandeForecastOscillator", "value": "<PERSON><PERSON> voorspelde oscillator"}, "Chande Momentum Oscillator": {"key": "ChandeMomentumOscillator", "value": "Chande Momentum Oscillator"}, "Colored Line": {"key": "ColoredLine", "value": "Gekleurde lijn"}, "COMMODITIES": {"key": "COMMODITIES", "value": "<PERSON><PERSON><PERSON>"}, "Commodity Channel Index": {"key": "CommodityChannelIndex", "value": "Commodity Channel Index"}, "Compare": {"key": "Compare", "value": "Vergelijken"}, "Comparison Symbol": {"key": "ComparisonSymbol", "value": "Vergelijkingssymbool"}, "Composite": {"key": "Composite", "value": "Samengesteld"}, "Composite RSI": {"key": "CompositeRSI", "value": "Composiet RSI"}, "Continuous": {"key": "Continuous", "value": "<PERSON><PERSON><PERSON>"}, "Conversion Line": {"key": "ConversionLine", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Conversion Line Period": {"key": "ConversionLinePeriod", "value": "Conversie lijnperiode"}, "Coppock": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Coppock Curve": {"key": "CoppockCurve", "value": "Coppock Curve"}, "Correl": {"key": "Correl", "value": "<PERSON><PERSON><PERSON>"}, "Correlation": {"key": "Correlation", "value": "Correlatie"}, "Correlation Coefficient": {"key": "CorrelationCoefficient", "value": "Correlatiecoëfficiënt"}, "Courier": {"key": "Courier", "value": "<PERSON><PERSON><PERSON>"}, "Create": {"key": "Create", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Create a New Custom Theme": {"key": "CreateaNewCustomTheme", "value": "Maak een nieuw aangepast thema"}, "Create Custom Theme": {"key": "CreateCustomTheme", "value": "Maak een aangepast thema"}, "Create Image": {"key": "CreateImage", "value": "Maak een af<PERSON>"}, "Bullish": {"key": "Bullish", "value": "Bullish"}, "Buy Stops": {"key": "BuyStops", "value": "Ko<PERSON> stops"}, "by Xignite.": {"key": "byXignite", "value": "door xignite."}, "Callout": {"key": "Callout", "value": "Callout"}, "Callouts": {"key": "Callouts", "value": "Uit<PERSON>ep<PERSON>"}, "cancel": {"key": "cancel", "value": "annuleren"}, "Candle": {"key": "Candle", "value": "<PERSON><PERSON>"}, "Candle Border": {"key": "CandleBorder", "value": "<PERSON><PERSON><PERSON> grens"}, "Candle Borders": {"key": "CandleBorders", "value": "Kaarsengrenzen"}, "Candle Color": {"key": "CandleColor", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Cross": {"key": "Cross", "value": "<PERSON><PERSON><PERSON>"}, "Crosshair": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Crosshairs": {"key": "Crosshairs", "value": "Vizier"}, "Crosshair (Alt + \\)": {"key": "CrosshairAlt", "value": "Crosshair (alt + \\)"}, "Crossline": {"key": "Crossline", "value": "Crossline"}, "CURRENCIES": {"key": "CURRENCIES", "value": "Valuta"}, "Current Studies": {"key": "CurrentStudies", "value": "Huidige studies"}, "Current Symbols": {"key": "CurrentSymbols", "value": "Huidige symbolen"}, "Current Timezone is": {"key": "CurrentTimezoneis", "value": "De huidige tijdzone is"}, "Custom Themes": {"key": "CustomThemes", "value": "Aangepaste thema's"}, "Done": {"key": "Done_chart", "value": "<PERSON><PERSON><PERSON>"}, "Doodle": {"key": "Doodle", "value": "Doodle"}, "Double Exponential": {"key": "DoubleExponential", "value": "Dubbel exponentieel"}, "Double Smoothing Period": {"key": "DoubleSmoothingPeriod", "value": "<PERSON><PERSON><PERSON> gladde periode"}, "Down Volume": {"key": "DownVolume", "value": "Volume"}, "Downtrend": {"key": "Downtrend", "value": "<PERSON><PERSON>"}, "Draw": {"key": "Draw", "value": "Tekenen"}, "Ease of Movement": {"key": "EaseofMovement", "value": "<PERSON><PERSON><PERSON> van bewe<PERSON>"}, "Edit": {"key": "Edit", "value": "Bewerking"}, "Earnings": {"key": "Earnings", "value": "Winst"}, "Demo data.": {"key": "Demodata", "value": "De<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "Detach": {"key": "<PERSON><PERSON>", "value": "Losmaken"}, "Detrended": {"key": "Detrended", "value": "Uitgesproken"}, "Detrended Price Oscillator": {"key": "DetrendedPriceOscillator", "value": "Distrended Price Oscillator"}, "Directional": {"key": "Directional", "value": "Directioneel"}, "Disparity Index": {"key": "DisparityIndex", "value": "Dispariteitsindex"}, "Display": {"key": "Display", "value": "Weergave"}, "Display 1 Standard Deviation (1Ïƒ)": {"key": "Display1StandardDeviation1", "value": "Display 1 standaardafwijking (1ïƒ)"}, "Display 2 Standard Deviation (2Ïƒ)": {"key": "Display2StandardDeviation2", "value": "Display 2 standaardafwijking (2ïƒ)"}, "Display 3 Standard Deviation (3Ïƒ)": {"key": "Display3StandardDeviation3", "value": "Display 3 standaardafwijking (3ïƒ)"}, "Cycle 1": {"key": "Cycle1", "value": "Cyclus 1"}, "Cycle 2": {"key": "Cycle2", "value": "Cyclus 2"}, "Cycle 3": {"key": "Cycle3", "value": "Cyclus 3"}, "D": {"key": "D", "value": "D"}, "daily": {"key": "daily_chart", "value": "dageli<PERSON>s"}, "Darvas": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "Darvas Box": {"key": "DarvasBox", "value": "Darvas Box"}, "Data delayed 15 min.": {"key": "Datadelayed<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>d 15 minuten."}, "Data is randomized.": {"key": "Dataisrandomized", "value": "G<PERSON>vens worden gerandomiseerd."}, "Data is real-time.": {"key": "Dataisrealtime", "value": "Gegevens zijn realtime."}, "Exit Field": {"key": "ExitField", "value": "Uitgangsveld"}, "Exponential": {"key": "Exponential", "value": "Exponentieel"}, "Extended Hours": {"key": "ExtendedHours", "value": "Verlengde uren"}, "Fade": {"key": "Fade", "value": "Vervagen"}, "Fake": {"key": "Fake", "value": "Nep"}, "Fan": {"key": "Fan", "value": "Fan"}, "Fast": {"key": "Fast", "value": "Snel"}, "Fast MA Period": {"key": "FastMAPeriod", "value": "Snelle MA -periode"}, "Favorites": {"key": "Favorites", "value": "Favorieten"}, "Fib Arc": {"key": "FibArc", "value": "FIB -boog"}, "Date Dividers": {"key": "DateDividers", "value": "Datum Dividers"}, "Date/Time": {"key": "DateTime", "value": "Datum/tijd"}, "Date/Time:": {"key": "DateTime", "value": "Datum/tijd:"}, "Day": {"key": "Day", "value": "<PERSON><PERSON>"}, "Days Per Year": {"key": "DaysPerYear", "value": "Dagen per jaar"}, "Decreasing Bar": {"key": "DecreasingBar", "value": "Afnemende balk"}, "Default": {"key": "<PERSON><PERSON><PERSON>", "value": "Standaard"}, "Default Themes": {"key": "Default<PERSON><PERSON><PERSON>", "value": "Standaardthema's"}, "Delete": {"key": "Delete", "value": "Verwijderen"}, "Delete Study": {"key": "DeleteStudy", "value": "Studie verwijderen"}, "Fib Fan": {"key": "FibFan", "value": "Fan"}, "Fib Time Zone": {"key": "FibTimeZone", "value": "FIB -tijdzone"}, "Fibonacci": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "fibonacci": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Field": {"key": "Field", "value": "Veld"}, "field": {"key": "field", "value": "veld"}, "Fill": {"key": "Fill", "value": "<PERSON><PERSON><PERSON>"}, "Focus Arrow": {"key": "FocusArrow", "value": "Focus Arrow"}, "Forecast": {"key": "Forecast", "value": "Voorspelling"}, "FOREX": {"key": "FOREX", "value": "Forex"}, "Elder Ray Index": {"key": "ElderRayIndex", "value": "Ouderling Ray Index"}, "Ellipse": {"key": "Ellipse", "value": "Ellips"}, "ema": {"key": "ema", "value": "EMA"}, "End of day data.": {"key": "Endofdaydata", "value": "<PERSON><PERSON>."}, "Enter box size and hit \"Enter\"": {"key": "EnterboxsizeandhitEnter", "value": "<PERSON><PERSON><PERSON> de <PERSON> in en druk op \"Enter\""}, "Enter name of view:": {"key": "Enternameofview", "value": "<PERSON><PERSON><PERSON> <PERSON> van de weergave in:"}, "Enter reversal and hit Enter": {"key": "EnterreversalandhitEnter", "value": "<PERSON><PERSON><PERSON> omkering in en druk op \"Enter\""}, "Enter Symbol": {"key": "EnterSymbol", "value": "<PERSON><PERSON><PERSON> symbool in"}, "Enter value and hit Enter": {"key": "EntervalueandhitEnter", "value": "<PERSON><PERSON><PERSON> de waarde in en druk op enter"}, "Events": {"key": "Events", "value": "Evenementen"}, "Edit Settings...": {"key": "EditSettings", "value": "Instellingen bewerken ..."}, "EF": {"key": "EF", "value": "EF"}, "EF Trigger": {"key": "EFTrigger", "value": "EF -trigger"}, "Ehler Fisher": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON> -visse<PERSON>"}, "Ehler Fisher Transform": {"key": "EhlerFisherTransform", "value": "<PERSON>hler Fisher Transform"}, "Elder Bear Power": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Ouderling Bear Power"}, "Elder Bull Power": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Oudere bull power"}, "Elder Force": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON> kracht"}, "Elder Force Index": {"key": "ElderForceIndex", "value": "<PERSON><PERSON>re krachtindex"}, "Elder Impulse System": {"key": "ElderImpulseSystem", "value": "<PERSON><PERSON>rling impulssysteem"}, "Formula courtesy": {"key": "Formulacourtesy", "value": "Formule hoffelijkheid"}, "Fractal Channel": {"key": "FractalChannel", "value": "Fract<PERSON> kanaal"}, "Fractal Chaos": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Fractale chaos"}, "Fractal Chaos Bands": {"key": "FractalChaosBands", "value": "Fractal Chaos Bands"}, "Fractal Chaos Oscillator": {"key": "FractalChaosOscillator", "value": "Fractale chaos oscillator"}, "Fractal High": {"key": "FractalHigh", "value": "Fractal High"}, "Fractal Low": {"key": "FractalLow", "value": "Fractaal laag"}, "FUNDS": {"key": "FUNDS", "value": "FONDSEN"}, "FUTURES": {"key": "FUTURES", "value": "Futures"}, "FX": {"key": "FX", "value": "Fx"}, "Gain": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Gann Fan": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "Garamond": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Gartley": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Gator": {"key": "Gator", "value": "Gator"}, "Gator Oscillator": {"key": "GatorOscillator", "value": "Gator Oscillator"}, "Generating Image": {"key": "GeneratingImage", "value": "Afbeelding genereren"}, "Ghost": {"key": "Ghost", "value": "Spook"}, "Ghost Boxes": {"key": "GhostBoxes", "value": "Spookboxen"}, "Gopala": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Heavy Rate of Change Period": {"key": "HeavyRateofChangePeriod", "value": "Zware snelheid van veranderingsperiode"}, "Heavy SMA Period": {"key": "HeavySMAPeriod", "value": "Zware SMA -periode"}, "Heikin Ashi": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Heikin-Ashi": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>n-ashi"}, "Helvetica": {"key": "Helvetica", "value": "Helvetica"}, "HHV/LLV Lookback": {"key": "HHVLLVLookback", "value": "HHV/LLV -lookback"}, "High": {"key": "High", "value": "<PERSON><PERSON>"}, "High Low": {"key": "HighLow", "value": "Hoog laag"}, "High Low Bands": {"key": "HighLowBands", "value": "Hoge lage banden"}, "High Low Bottom": {"key": "HighLowBottom", "value": "Hoge lage bodem"}, "High Low Median": {"key": "HighLowMedian", "value": "Hoog lage mediaan"}, "High Low Top": {"key": "HighLowTop", "value": "High Low Top"}, "High Minus Low": {"key": "HighMinusLow", "value": "Hoog min laag"}, "High Period": {"key": "HighPeriod", "value": "Hoge periode"}, "High-Low": {"key": "HighLow", "value": "High-low"}, "high/low": {"key": "highlow", "value": "hoog/laag"}, "High/Low Values": {"key": "HighLowValues", "value": "Hoge/lage waarden"}, "Highest High Value": {"key": "HighestHighValue", "value": "Ho<PERSON>ste hoge waarde"}, "HighLow": {"key": "HighLow", "value": "<PERSON><PERSON>"}, "Hist Vol": {"key": "HistVol", "value": "Hist vol"}, "Gopalakrishnan Range Index": {"key": "GopalakrishnanRangeIndex", "value": "Gopalakrishnan Range Index"}, "Gradient": {"key": "Gradient", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Green": {"key": "Green", "value": "<PERSON><PERSON><PERSON>"}, "Grid Lines": {"key": "GridLines", "value": "Roosterlijnen"}, "Grid View": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Rasteraanzicht"}, "H": {"key": "H_chart", "value": "H"}, "harmonic": {"key": "harmonic", "value": "harmonisch"}, "Heart": {"key": "Heart", "value": "<PERSON>"}, "Heaviest Rate of Change Period": {"key": "HeaviestRateofChangePeriod", "value": "De z<PERSON>te snelheid van veranderingsperiode"}, "Heaviest SMA Period": {"key": "HeaviestSMAPeriod", "value": "Zwaarste SMA -periode"}, "Histogram": {"key": "Histogram", "value": "Histogram"}, "Historical Volatility": {"key": "HistoricalVolatility", "value": "Historische volatiliteit"}, "Hollow Candle": {"key": "HollowCandle", "value": "<PERSON><PERSON> kaars"}, "Horizontal": {"key": "Horizontal", "value": "Horizontaal"}, "Hull": {"key": "Hull", "value": "Romp"}, "I": {"key": "I", "value": "I"}, "Ichimoku Clouds": {"key": "IchimokuClouds_chart", "value": "Ichimoku -wolken"}, "Increasing Bar": {"key": "IncreasingBar", "value": "Toenemende bar"}, "Index": {"key": "Index", "value": "Index"}, "INDEXES": {"key": "INDEXES", "value": "Indexen"}, "INDICES": {"key": "INDICES_chart", "value": "Indices"}, "Info": {"key": "Info", "value": "Info"}, "Intercept": {"key": "Intercept", "value": "Onderscheppen"}, "Interval": {"key": "Interval", "value": "Interval"}, "Intraday Momentum Index": {"key": "IntradayMomentumIndex", "value": "Intraday momentumindex"}, "Intraday Mtm": {"key": "IntradayMtm", "value": "Intraday MTM"}, "Jaw": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "Jaw Offset": {"key": "JawOffset", "value": "Ka<PERSON> offset"}, "Jaw Period": {"key": "JawPeriod", "value": "Kaakperiode"}, "Kagi": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "Long Cycle": {"key": "LongCycle", "value": "Lange cyclus"}, "Long RoC": {"key": "LongRoC", "value": "Lang <PERSON>"}, "Loss": {"key": "Loss", "value": "Verlies"}, "Low": {"key": "Low", "value": "Laag"}, "Low Period": {"key": "LowPeriod", "value": "Lage periode"}, "Lowest Low Value": {"key": "LowestLowValue", "value": "Laagste lage waarde"}, "LR Slope": {"key": "LRSlope", "value": "LR helling"}, "M": {"key": "M", "value": "M"}, "M Flow": {"key": "MFlow", "value": "<PERSON> stroom"}, "MA": {"key": "MA", "value": "Ma"}, "Moving Average Envelope": {"key": "MovingAverageEnvelope", "value": "Verhuisgemiddelde envelop"}, "Moving Average Type": {"key": "MovingAverageType", "value": "Type voortschrijdend gemiddelde"}, "ms": {"key": "ms", "value": "mevrouw"}, "Multiplier": {"key": "Multiplier", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Name": {"key": "Name", "value": "<PERSON><PERSON>"}, "Neg Vol": {"key": "NegVol", "value": "Neg Vol"}, "Negative Bar": {"key": "NegativeBar", "value": "Negatieve balk"}, "Negative Volume Index": {"key": "NegativeVolumeIndex", "value": "Negatieve volume -index"}, "Neutral": {"key": "Neutral", "value": "Neutrale"}, "New Custom Theme": {"key": "NewCustomTheme", "value": "<PERSON><PERSON>w a<PERSON>a"}, "New Theme": {"key": "NewTheme", "value": "<PERSON><PERSON><PERSON> thema"}, "New Theme Name": {"key": "NewThemeName", "value": "<PERSON><PERSON><PERSON>"}, "New Theme Name:": {"key": "NewThemeName", "value": "<PERSON><PERSON><PERSON>:"}, "Night": {"key": "Night", "value": "<PERSON><PERSON>"}, "No Tool": {"key": "NoTool", "value": "<PERSON><PERSON>"}, "None": {"key": "None_chart", "value": "<PERSON><PERSON>"}, "None available": {"key": "Noneavailable", "value": "<PERSON><PERSON>"}, "Not enough data to compute": {"key": "Notenoughdatatocompute", "value": "<PERSON><PERSON> geno<PERSON> gegevens om te berekenen"}, "Offset": {"key": "Offset", "value": "Verbijstering"}, "On Bal Vol": {"key": "OnBalVol", "value": "Per saldo volume"}, "Lagging Span": {"key": "LaggingSpan", "value": "Overspanning"}, "Lagging Span Period": {"key": "LaggingSpanPeriod", "value": "Achterblijvende spanperiode"}, "Leading Span A": {"key": "LeadingSpanA", "value": "Toonaangevende span a"}, "Leading Span B": {"key": "LeadingSpanB", "value": "Toonaangevende Span B"}, "Leading Span B Period": {"key": "LeadingSpanBPeriod", "value": "Toonaangevende Span B -periode"}, "Level Offset": {"key": "LevelOffset", "value": "Niveau offset"}, "Levels": {"key": "Levels", "value": "Niveaus"}, "Light Rate of Change Period": {"key": "LightRateofChangePeriod", "value": "Lichtsnelheid van veranderingsperiode"}, "Light SMA Period": {"key": "LightSMAPeriod", "value": "Lichte SMA -periode"}, "Lightest Rate of Change Period": {"key": "LightestRateofChangePeriod", "value": "Lichtste snel<PERSON><PERSON> van veranderingsperiode"}, "Lightest SMA Period": {"key": "LightestSMAPeriod", "value": "Lichtste SMA -periode"}, "Like all ChartIQ markers, the object itself is managed by the chart, so when you scroll the chart the object moves with you. It is also destroyed automatically for you when the symbol is changed.": {"key": "LikeallChartIQmarkerstheobjectitselfismanagedbythechartsowhenyouscrollthecharttheobjectmoveswithyouItisalsodestroyedautomaticallyforyouwhenthesymbolischanged", "value": "Zoals alle chartiq -markeringen, wordt het object zelf beheerd door de grafiek, dus wanneer u over de grafiek bladert, beweegt het object met u.Het wordt ook automatisch voor u vernietigd wanneer het symbool wordt gewijzigd."}, "Limit Move Value": {"key": "LimitMoveValue", "value": "<PERSON><PERSON><PERSON> de waarde van de beweging"}, "Lin Fcst": {"key": "LinFcst", "value": "Lin fcst"}, "Lin Incpt": {"key": "LinIncpt", "value": "Lin Incpt"}, "Lin R2": {"key": "LinR2", "value": "Lin R2"}, "Line": {"key": "Line", "value": "<PERSON><PERSON>"}, "Lines": {"key": "Lines", "value": "Lijnen"}, "Line Break": {"key": "LineBreak", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Line/Bar Chart": {"key": "LineBarChart", "value": "Lijn/staafdiagram"}, "On Balance Volume": {"key": "OnBalanceVolume", "value": "Per saldo volume"}, "Open": {"key": "Open", "value": "Open"}, "Open shared chart in new window": {"key": "Opensharedchartinnewwindow", "value": "Open de gedeelde grafiek in een nieuw venster"}, "or": {"key": "or", "value": "of"}, "OverBought": {"key": "OverBought", "value": "Overbought"}, "Overlay": {"key": "Overlay", "value": "Overlay"}, "OverSold": {"key": "OverSold", "value": "Oververkiezing"}, "P Rel": {"key": "PRel", "value": "P REL"}, "Palatino": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Parabolic SAR": {"key": "ParabolicSAR", "value": "Parabolische SAR"}, "Linear Reg Forecast": {"key": "LinearRegForecast", "value": "Lineaire regprognose"}, "Linear Reg Intercept": {"key": "LinearRegIntercept", "value": "Lineair regcept"}, "Linear Reg R2": {"key": "LinearRegR2", "value": "Lineaire reg R2"}, "Linear Reg Slope": {"key": "LinearRegSlope", "value": "Lineaire reghelling"}, "Lips": {"key": "Lips", "value": "Lippen"}, "Lips Offset": {"key": "LipsOffset", "value": "Lippen offset"}, "Lips Period": {"key": "LipsPeriod", "value": "Lippenperiode"}, "List View": {"key": "ListView", "value": "Lijstweergave"}, "Locale": {"key": "Locale", "value": "Landinstelling"}, "Log Scale": {"key": "LogScale", "value": "Logschaal"}, "Keltner": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Keltner Bottom": {"key": "KeltnerBottom", "value": "<PERSON><PERSON>ner Bottom"}, "Keltner Channel": {"key": "KeltnerChannel", "value": "<PERSON><PERSON><PERSON> -kana<PERSON>"}, "Keltner Median": {"key": "KeltnerMedian", "value": "Ke<PERSON>ner mediaan"}, "Keltner Top": {"key": "KeltnerTop", "value": "<PERSON><PERSON><PERSON> top"}, "Klinger": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "Klinger Volume Oscillator": {"key": "KlingerVolumeOscillator", "value": "<PERSON>linger Volume Oscillator"}, "KlingerSignal": {"key": "<PERSON>lingerSignal", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "KST": {"key": "KST", "value": "KST"}, "KSTSignal": {"key": "KSTSignal", "value": "Kstsignal"}, "ma": {"key": "ma", "value": "ma"}, "MA Env": {"key": "MAEnv", "value": "<PERSON>v"}, "MA Env Bottom": {"key": "MAEnvBottom", "value": "MA Env Bottom"}, "MA Env Median": {"key": "MAEnvMedian", "value": "MA Env Mediaan"}, "MA Env Top": {"key": "MAEnvTop", "value": "MA Env Top"}, "MA Period": {"key": "MAPeriod", "value": "MA -periode"}, "MACD": {"key": "MACD_chart", "value": "<PERSON>d"}, "Market Data": {"key": "MarketData", "value": "Marktgegevens"}, "Market Facilitation Index": {"key": "MarketFacilitationIndex", "value": "Marktfacilitatie -index"}, "Markings": {"key": "Markings", "value": "<PERSON><PERSON><PERSON>"}, "Momentum": {"key": "Momentum", "value": "Momentum"}, "Momentum Indicator": {"key": "MomentumIndicator", "value": "Momentumindicator"}, "Money Flow Index": {"key": "MoneyFlowIndex", "value": "Geldstroomindex"}, "More": {"key": "More", "value": "<PERSON><PERSON>"}, "More studies": {"key": "Morestudies", "value": "Meer studies"}, "Mountain": {"key": "Mountain", "value": "Berg"}, "Mountain Charts": {"key": "MountainCharts", "value": "Bergkaarten"}, "Mountain Color": {"key": "MountainColor", "value": "Bergkleur"}, "Moving Average": {"key": "MovingAverage", "value": "Voortschrijdend gemiddelde"}, "Moving Average Deviation": {"key": "MovingAverageDeviation", "value": "VOORWEGENDE VERKOPICATIE"}, "Percent": {"key": "Percent", "value": "Percentage"}, "percent": {"key": "percent", "value": "percentage"}, "Perf Idx": {"key": "PerfIdx", "value": "Perf idx"}, "Performance Index": {"key": "PerformanceIndex", "value": "Performance Index"}, "Period": {"key": "Period_chart", "value": "Periode"}, "pips": {"key": "pips", "value": "pips"}, "Pitchfork": {"key": "Pitchfork", "value": "Hooivork"}, "Pivot": {"key": "Pivot", "value": "<PERSON><PERSON><PERSON>"}, "Pivot Points": {"key": "PivotPoints", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Plot Type": {"key": "PlotType", "value": "Plottype"}, "Mass Idx": {"key": "MassIdx", "value": "Massa IDX"}, "Mass Index": {"key": "MassIndex", "value": "Massa -index"}, "Maximum AF": {"key": "MaximumAF", "value": "Maximale AF"}, "mean": {"key": "mean", "value": "gemeen"}, "Measure": {"key": "Measure", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Med Price": {"key": "MedPrice", "value": "Medeprijs"}, "median": {"key": "median", "value": "mediaan"}, "Median Price": {"key": "MedianPrice", "value": "Mediane pri<PERSON>s"}, "Min Tick Value": {"key": "MinTickValue", "value": "<PERSON>"}, "Minimum AF": {"key": "MinimumAF", "value": "Minimale AF"}, "Plots": {"key": "Plots", "value": "Plots"}, "PMO": {"key": "PMO", "value": "PMO"}, "PMOSignal": {"key": "PMOSignal", "value": "Pmosignaal"}, "Point & Figure": {"key": "PointFigure", "value": "Punt en figuur"}, "Points": {"key": "Points", "value": "<PERSON><PERSON><PERSON>"}, "points": {"key": "points", "value": "punten"}, "Points Or Percent": {"key": "PointsOrPercent", "value": "Punten of percentage"}, "Popular Studies": {"key": "PopularStudies", "value": "Populaire studies"}, "Pos Vol": {"key": "PosVol", "value": "POS Vol"}, "Positive Bar": {"key": "PositiveBar", "value": "Positieve bar"}, "Positive Volume Index": {"key": "PositiveVolumeIndex", "value": "Positieve volume -index"}, "Press this button to generate a shareable image:": {"key": "Pressthisbuttontogenerateashareableimage", "value": "Druk op deze knop om een ​​deelbare afbeelding te genereren:"}, "Pretty Good": {"key": "<PERSON><PERSON><PERSON>", "value": "Best goed"}, "Pretty Good Oscillator": {"key": "PrettyGoodOscillator", "value": "<PERSON><PERSON><PERSON> goede oscillator"}, "Press Releases": {"key": "PressReleases", "value": "Persberichten"}, "Price": {"key": "Price", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Price Minimum": {"key": "PriceMinimum", "value": "Prijsminimum"}, "Price Momentum Oscillator": {"key": "PriceMomentumOscillator", "value": "Prijsmomentum oscillator"}, "Price Osc": {"key": "PriceOsc", "value": "Prijs OSC"}, "Price Oscillator": {"key": "PriceOscillator", "value": "Prijs oscillator"}, "Price Rate of Change": {"key": "PriceRateofChange", "value": "Prijspercentage van verandering"}, "Price Relative": {"key": "PriceRelative", "value": "Prijs familie<PERSON>"}, "Price ROC": {"key": "PriceROC", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Price Vol": {"key": "PriceVol", "value": "Prijsvol"}, "Price Volume Trend": {"key": "PriceVolumeTrend", "value": "Prijsvolumetrend"}, "Prime Bands Bottom": {"key": "PrimeBandsBottom", "value": "Prime Bands Bottom"}, "Prime Bands Channel": {"key": "PrimeBandsChannel", "value": "Prime Bands -kanaal"}, "Prime Bands Top": {"key": "PrimeBandsTop", "value": "Prime Bands Top"}, "Prime Number": {"key": "PrimeNumber", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Prime Number Bands": {"key": "PrimeNumberBands", "value": "Priemgetalbanden"}, "Rel Vig": {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Relevant"}, "Rel Vol": {"key": "RelVol", "value": "Rel Vol"}, "Relative Vigor Index": {"key": "RelativeVigorIndex", "value": "Relatieve krachtindex"}, "Relative Volatility": {"key": "RelativeVolatility", "value": "Relatieve volatiliteit"}, "RelVigSignal": {"key": "RelVigSignal", "value": "Hervigsignaal"}, "Renko": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "Resistance 1": {"key": "Resistance1", "value": "Weerstand 1"}, "Resistance 2": {"key": "Resistance2", "value": "Weerstand 2"}, "Resistance 3": {"key": "Resistance3", "value": "Weerstand 3"}, "Result": {"key": "Result", "value": "Resultaat"}, "Random Walk Index": {"key": "RandomWalkIndex", "value": "Random Walk Index"}, "Random Walk Low": {"key": "RandomWalkLow", "value": "Willekeurige wandeling laag"}, "Range Bars": {"key": "RangeBars", "value": "Bereikstaven"}, "Range Selector": {"key": "RangeSelector", "value": "Range selector"}, "Rate Of Change": {"key": "RateOfChange", "value": "<PERSON><PERSON>"}, "RAVI": {"key": "RAVI", "value": "<PERSON>"}, "Ray": {"key": "<PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Rectangle": {"key": "Rectangle", "value": "Rechthoek"}, "Redo": {"key": "Redo", "value": "<PERSON><PERSON>u<PERSON> doen"}, "Regression Line": {"key": "RegressionLine", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Restore Config": {"key": "RestoreConfig", "value": "[Fr] Resteer configuratie"}, "Retracement": {"key": "Retracement", "value": "Terugtrekken"}, "right-click to delete": {"key": "rightclicktodelete", "value": "<PERSON><PERSON> met de rechtermuisknop om te verwijderen"}, "right-click to manage": {"key": "rightclicktomanage", "value": "<PERSON><PERSON> met de rechtermuisknop om te beheren"}, "Risk/Reward": {"key": "RiskReward", "value": "Risico/beloning"}, "RSI": {"key": "RSI_chart", "value": "RSI"}, "RSquared": {"key": "RSquared", "value": "Rsquared"}, "s": {"key": "s", "value": "S"}, "save": {"key": "save", "value": "redden"}, "Save": {"key": "Save", "value": "<PERSON><PERSON>"}, "Prime Number Oscillator": {"key": "PrimeNumberOscillator", "value": "Priemgetal oscillator"}, "Pring''s Know Sure Thing": {"key": "PringsKnowSureThing", "value": "<PERSON><PERSON> 'weet zeker"}, "Pring''s Special K": {"key": "PringsSpecialK", "value": "Pring 'Special K"}, "Psychological Line": {"key": "PsychologicalLine", "value": "Psychologische lijn"}, "QStick": {"key": "QStick", "value": "Qstick"}, "Quadrant Lines": {"key": "QuadrantLines", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Rainbow Moving Average": {"key": "RainbowMovingAverage", "value": "Regenboog voortschrijdend gemiddelde"}, "Rainbow Oscillator": {"key": "RainbowOscillator", "value": "Rainbow Oscillator"}, "Random Walk": {"key": "RandomWalk", "value": "<PERSON><PERSON><PERSON><PERSON> wandeling"}, "Random Walk High": {"key": "RandomWalkHigh", "value": "<PERSON><PERSON><PERSON><PERSON> wandeling hoog"}, "Save Config": {"key": "SaveConfig", "value": "<PERSON><PERSON><PERSON> configuratie"}, "Save Theme": {"key": "SaveTheme", "value": "<PERSON><PERSON><PERSON> thema"}, "Save View": {"key": "SaveView", "value": "Be<PERSON><PERSON> weergave"}, "Saved View": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Opgeslagen weergave"}, "Saved Views": {"key": "SavedViews", "value": "Opgeslagen uitzichten"}, "Scale Factor": {"key": "ScaleFactor", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Schaff": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "Schaff Trend Cycle": {"key": "SchaffTrendCycle", "value": "<PERSON><PERSON><PERSON> trendcyclus"}, "Search": {"key": "Search", "value": "Zoe<PERSON><PERSON>dracht"}, "Segment": {"key": "Segment", "value": "Segment"}, "Select Tool": {"key": "SelectTool", "value": "Selecteer tool"}, "Sell Stops": {"key": "SellStops", "value": "Verkoop stops"}, "Series": {"key": "Series", "value": "Serie"}, "Set Point & Figure Parameters": {"key": "SetPointFigureParameters", "value": "Stel punt- en figuurparameters in"}, "Set Price Lines": {"key": "SetPriceLines", "value": "Stel prijslijnen in"}, "Set Range": {"key": "SetRange", "value": "Instellen"}, "Set Reversal Percentage": {"key": "SetReversalPercentage", "value": "Stel omkeerpercentage in"}, "Settings": {"key": "Settings", "value": "Instellingen"}, "Shading": {"key": "Shading", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Shape": {"key": "<PERSON><PERSON><PERSON>", "value": "Vorm"}, "Supertrend": {"key": "Supertrend", "value": "Supertrend"}, "Support 1": {"key": "Support1", "value": "Ondersteuning 1"}, "Support 2": {"key": "Support2", "value": "Ondersteuning 2"}, "Support 3": {"key": "Support3", "value": "Ondersteuning 3"}, "Swing": {"key": "Swing", "value": "<PERSON><PERSON><PERSON>"}, "Swing Index": {"key": "SwingIndex", "value": "Swing Index"}, "Symbol": {"key": "Symbol", "value": "Symbool"}, "T": {"key": "T", "value": "T"}, "Take Profit": {"key": "TakeProfit", "value": "Winst maken"}, "Teeth": {"key": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "standard": {"key": "standard", "value": "standaard"}, "Standard Deviation": {"key": "StandardDeviation", "value": "Standaardafwijking"}, "Standard Deviations": {"key": "StandardDeviations", "value": "Standaardafwijkingen"}, "Star": {"key": "Star", "value": "<PERSON><PERSON>"}, "STARC Bands": {"key": "STARCBands", "value": "Starc Bands"}, "STARC Bands Bottom": {"key": "STARCBandsBottom", "value": "Starc Bands Bottom"}, "STARC Bands Median": {"key": "STARCBandsMedian", "value": "Starc Bands mediaan"}, "STARC Bands Top": {"key": "STARCBandsTop", "value": "Starc Bands Top"}, "Stch Mtm": {"key": "StchMtm", "value": "STCH MTM"}, "STD Dev": {"key": "STDDev", "value": "Std Dev"}, "Shape - Arrow": {"key": "ShapeArrow", "value": "Vorm - pijl"}, "Shape - Check": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Vorm - controle"}, "Shape - Cross": {"key": "ShapeCross", "value": "Vorm - K<PERSON><PERSON>"}, "Shape - Focus": {"key": "ShapeFocus", "value": "Vorm - Focus"}, "Shape - Heart": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Vorm - hart"}, "Shape - Star": {"key": "ShapeStar", "value": "Vorm - ster"}, "Share": {"key": "Share_chart", "value": "<PERSON><PERSON>"}, "Share This Chart": {"key": "ShareThisChart", "value": "Deel deze grafiek"}, "Share Your Chart": {"key": "ShareYourChart", "value": "<PERSON>l uw grafiek"}, "Shift": {"key": "Shift", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Show Tooltip": {"key": "ShowTooltip", "value": "Toon tooltip"}, "Signal": {"key": "Signal", "value": "Signaal"}, "Signal Period": {"key": "SignalPeriod", "value": "Signaalperiode"}, "Signal Periods": {"key": "SignalPeriods", "value": "Signaalperioden"}, "Simple": {"key": "Simple", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Simple Circle": {"key": "SimpleCircle", "value": "<PERSON>en<PERSON><PERSON>ge cirkel"}, "Simple Square": {"key": "SimpleSquare", "value": "Eenvoudig vierkant"}, "Simulated data.": {"key": "Simulateddata", "value": "Gesimuleerde gegevens."}, "Slope": {"key": "Slope", "value": "Helling"}, "Slow": {"key": "Slow", "value": "<PERSON><PERSON><PERSON>"}, "Trade Volume Index": {"key": "TradeVolumeIndex", "value": "Handelsvolume -index"}, "Trend Intensity Index": {"key": "TrendIntensityIndex", "value": "Trendintensiteitsindex"}, "Triangular": {"key": "Triangular", "value": "Driehoekig"}, "Triple Exponential": {"key": "TripleExponential", "value": "Drievoudig exponentieel"}, "TRIX": {"key": "TRIX", "value": "Trix"}, "True Range": {"key": "TrueRange", "value": "<PERSON>cht bereik"}, "Twiggs": {"key": "Twiggs", "value": "Twijgen"}, "Twiggs Money Flow": {"key": "TwiggsMoneyFlow", "value": "Twiggs geldstroom"}, "Type": {"key": "Type", "value": "Type"}, "Typical Price": {"key": "TypicalPrice_chart", "value": "Typische prijs"}, "SMA9": {"key": "SMA9", "value": "SMA9"}, "Smooth": {"key": "Smooth", "value": "<PERSON><PERSON>"}, "Smoothing Period": {"key": "SmoothingPeriod", "value": "Afvlakkingsperiode"}, "Speed Resistance Arc": {"key": "SpeedResistanceArc", "value": "Speedweerstand boog"}, "Speed Resistance Line": {"key": "SpeedResistanceLine", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "SPY": {"key": "SPY", "value": "SPION"}, "Squarewave": {"key": "Squarewave", "value": "Squarewave"}, "squarewave": {"key": "squarewave", "value": "squarewave"}, "Squat": {"key": "Squat", "value": "<PERSON><PERSON><PERSON>"}, "Step": {"key": "Step", "value": "Stap"}, "STD Period": {"key": "STDPeriod", "value": "STD -periode"}, "Stochastic Momentum Index": {"key": "StochasticMomentumIndex", "value": "Stochastische momentumindex"}, "Stochastics": {"key": "Stochastics_chart", "value": "Stochastiek"}, "STOCKS": {"key": "STOCKS", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "Stop Levels": {"key": "StopLevels", "value": "Stopniveaus"}, "Stop Loss": {"key": "StopLoss", "value": "Stop verlies"}, "Stops": {"key": "Stops", "value": "Stopt"}, "Strong Ratio": {"key": "StrongRatio", "value": "<PERSON><PERSON><PERSON> verhouding"}, "Studies": {"key": "Studies", "value": "<PERSON><PERSON><PERSON>"}, "Study": {"key": "Study", "value": "<PERSON><PERSON><PERSON>"}, "Shift Percentage": {"key": "ShiftPercentage", "value": "Verschuivingspercentage"}, "Shift Type": {"key": "ShiftType", "value": "Verschuivingstype"}, "Shinohara Intensity Ratio": {"key": "ShinoharaIntensityRatio", "value": "De verhouding tussen de intensiteit van <PERSON>"}, "Short Cycle": {"key": "ShortCycle", "value": "<PERSON>rte cyclus"}, "Short RoC": {"key": "ShortRoC", "value": "Kort ROC"}, "Show All": {"key": "ShowAll", "value": "Toon alles"}, "Show Dynamic Callout": {"key": "ShowDynamicCallout", "value": "Toon dynamische callout"}, "Show Fractals": {"key": "ShowFractals", "value": "Toon fractals"}, "Show Zones": {"key": "ShowZones", "value": "Toon zones"}, "Show Heads-Up Static": {"key": "ShowHeadsUpStatic", "value": "Toon heads-up statisch"}, "Slow MA Period": {"key": "SlowMAPeriod", "value": "Langzame MA -periode"}, "SMA1": {"key": "SMA1", "value": "SMA1"}, "SMA10": {"key": "SMA10", "value": "SMA10"}, "SMA2": {"key": "SMA2", "value": "SMA2"}, "SMA3": {"key": "SMA3", "value": "SMA3"}, "SMA4": {"key": "SMA4", "value": "SMA4"}, "SMA5": {"key": "SMA5", "value": "SMA5"}, "SMA6": {"key": "SMA6", "value": "SMA6"}, "SMA7": {"key": "SMA7", "value": "SMA7"}, "SMA8": {"key": "SMA8", "value": "SMA8"}, "Time Series Forecast": {"key": "TimeSeriesForecast", "value": "Tijdreeksvoorspelling"}, "Time Zone": {"key": "TimeZone", "value": "Tijdzone"}, "Times New Roman": {"key": "TimesNewRoman", "value": "Times New Roman"}, "Timezone": {"key": "Timezone", "value": "Tijdzone"}, "Tirone Levels": {"key": "TironeLevels", "value": "<PERSON><PERSON><PERSON> niveaus"}, "To set your timezone use the location button below or scroll through the following list": {"key": "Tosetyourtimezoneusethelocationbuttonbeloworscrollthroughthefollowinglist", "value": "Om uw tijdzone in te stellen, gebruikt u de onderstaande locatieknop of bladert u door de volgende lijst"}, "To set your timezone use the location button below, or scroll through the following list...": {"key": "Tosetyourtimezoneusethelocationbuttonbeloworscrollthroughthefollowinglist", "value": "Om uw tijdzone in te stellen, gebruikt u de onderstaande locatieknop of bladert u door de volgende lijst ..."}, "Tolerance Percentage": {"key": "TolerancePercentage", "value": "Tolerantiepercentage"}, "Total Return": {"key": "TotalReturn", "value": "Totaal rendement"}, "Trade Vol": {"key": "TradeVol", "value": "Handelsvol"}, "Teeth Offset": {"key": "TeethOffset", "value": "Tanden offset"}, "Teeth Period": {"key": "TeethPeriod", "value": "Tandenperiode"}, "Themes": {"key": "Themes", "value": "<PERSON><PERSON>'s"}, "Text": {"key": "Text", "value": "Tekst"}, "This is a callout marker": {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "Dit is een callout -marker"}, "This is an example of a complex marker which can contain html, video, images, css, and animations.": {"key": "Thisisanexampleofacomplexmarkerwhichcancontainhtmlvideoimagescssandanimations", "value": "<PERSON><PERSON> is een voorbeeld van een complexe marker die HTML, video, afbeeldingen, CSS en animaties kan bevatten."}, "TII": {"key": "TII", "value": "Tii"}, "Time Cycle": {"key": "TimeCycle", "value": "Tijdcyclus"}, "Time Fcst": {"key": "TimeFcst", "value": "Tijd fcst"}, "Time Series": {"key": "TimeSeries", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Ulcer Index": {"key": "UlcerIndex", "value": "<PERSON> van de zweer"}, "Ultimate": {"key": "Ultimate", "value": "Ultiem"}, "Ultimate Oscillator": {"key": "UltimateOscillator", "value": "Ultieme oscillator"}, "Underlay": {"key": "Underlay", "value": "Te ondernemen"}, "Undo": {"key": "Undo", "value": "Ongedaan maken"}, "Units": {"key": "Units", "value": "Eenheden"}, "Unrealized Gain/Loss": {"key": "UnrealizedGainLoss", "value": "<PERSON><PERSON> -gere<PERSON><PERSON><PERSON> winst/verlies"}, "Up Volume": {"key": "UpVolume", "value": "Up volume"}, "Uploading Image": {"key": "UploadingImage", "value": "Afbeelding uploaden"}, "Uptrend": {"key": "Uptrend", "value": "Trend"}, "vidya": {"key": "vidya_lower", "value": "<PERSON><PERSON><PERSON>"}, "Adj_Close": {"key": "<PERSON>j_<PERSON>", "value": "Adj_close"}, "hl/2": {"key": "hl_2", "value": "HL/2"}, "hlc/3": {"key": "hlc_3", "value": "HLC/3"}, "hlcc/4": {"key": "hlcc_4", "value": "HLCC/4"}, "ohlc/4": {"key": "ohlc_4", "value": "OHLC/4"}, "Videos": {"key": "Videos", "value": "Video's"}, "W": {"key": "W", "value": "W"}, "W Acc Dist": {"key": "WAccDist", "value": "W acc dist"}, "Weak Ratio": {"key": "WeakRatio", "value": "Zwakke verhouding"}, "weekly": {"key": "weekly_chart", "value": "wekelijks"}, "Weighted": {"key": "Weighted", "value": "<PERSON><PERSON><PERSON>"}, "Weighted Close": {"key": "WeightedClose", "value": "<PERSON><PERSON><PERSON>"}, "Welles Wilder": {"key": "WellesWilder", "value": "<PERSON>"}, "White": {"key": "White", "value": "Wit"}, "Williams %R": {"key": "WilliamsR", "value": "Williams %r"}, "Your timezone is your current location": {"key": "Yourtimezoneisyourcurrentlocation", "value": "Uw tijdzone is uw huidige locatie"}, "Use My Current Location": {"key": "UseMyCurrentLocation", "value": "Gebruik mijn huidige locatie"}, "Use the following link to share your chart:": {"key": "Usethefollowinglinktoshareyourchart", "value": "Gebruik de volgende link om uw grafiek te delen:"}, "Use Volume": {"key": "UseVolume", "value": "Gebruik volume"}, "Valuation Lines": {"key": "ValuationLines", "value": "Waarderingsleidingen"}, "Variable": {"key": "Variable", "value": "Variabel"}, "Vchart": {"key": "Vchart", "value": "Vchart"}, "vdma": {"key": "vdma", "value": "VDMA"}, "Vertical": {"key": "Vertical", "value": "Verticaal"}, "Vertical Horizontal Filter": {"key": "VerticalHorizontalFilter", "value": "Verticaal horizontaal filter"}, "Vertex Line": {"key": "VertexLine", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VIDYA": {"key": "VIDYA", "value": "<PERSON><PERSON><PERSON>"}, "Views": {"key": "Views", "value": "Uitzichten"}, "Vol": {"key": "Vol", "value": "Vol"}, "Vol Osc": {"key": "VolOsc", "value": "Vol Osc"}, "vol profile": {"key": "volprofile", "value": "Vol profiel"}, "Vol ROC": {"key": "VolROC", "value": "Vol roc"}, "vol undr": {"key": "volundr", "value": "Volume te weinig"}, "Volume": {"key": "Volume", "value": "Volume"}, "Volume % of Avg": {"key": "VolumeofAvg", "value": "Volume % van AVG"}, "Volume Candle": {"key": "VolumeCandle", "value": "<PERSON><PERSON><PERSON>"}, "YTD": {"key": "YTD_chart", "value": "YTD"}, "Y-Axis Preferences": {"key": "YAxisPreferences", "value": "Y-asvoorkeuren"}, "ZigZag": {"key": "ZigZag", "value": "Zigzag"}, "volume": {"key": "volume", "value": "Volume"}, "Moving Average Cross": {"key": "MovingAverageCross", "value": "Voortschrijdend gemiddelde k<PERSON>is"}, "Technical indicators": {"key": "Technicalindicators", "value": "Technische indicatoren"}, "Others": {"key": "Others", "value": "<PERSON><PERSON><PERSON>"}, "Invert": {"key": "Invert", "value": "Omkeren"}, "Linear": {"key": "Linear", "value": "Lineair"}, "Pop out": {"key": "Popout", "value": "Volledig scherm"}, "Pop in": {"key": "Popin", "value": "Verlaat het volledige scherm"}, "Info On": {"key": "InfoOn", "value": "Info over"}, "Info Off": {"key": "InfoOff", "value": "Info Off"}, "Indicator": {"key": "Indicator", "value": "Indicator"}, "Current Indicator": {"key": "CurrentIndicator", "value": "Huidige indicator"}, "Stripe Background": {"key": "StripeBackground", "value": "Stripe -achtergrond"}, "1 Day": {"key": "_1Day", "value": "1 dag"}, "1 Week": {"key": "_1Week", "value": "1 week"}, "1 Month": {"key": "_1Month", "value": "1 maand"}, "1 Year": {"key": "_1Year", "value": "1 jaar"}, "Volume Chart": {"key": "VolumeChart_chart", "value": "<PERSON><PERSON><PERSON>"}, "Volume Not Available": {"key": "VolumeNotAvailable", "value": "Volume niet besch<PERSON>"}, "Volume Oscillator": {"key": "VolumeOscillator", "value": "Volume -oscillator"}, "Volume Profile": {"key": "VolumeProfile", "value": "Volumeprofiel"}, "Volume Rate of Change": {"key": "VolumeRateofChange", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Volume Spike": {"key": "VolumeSpike", "value": "Volume spike"}, "Volume Underlay": {"key": "VolumeUnderlay", "value": "Volume te weinig"}, "Vortex Indicator": {"key": "VortexIndicator", "value": "Vortex -indicator"}, "VT HZ Filter": {"key": "VTHZFilter", "value": "VT Hz -filter"}, "VWAP": {"key": "VWAP", "value": "Vwap"}}