import { Client, fetchExchange } from '@urql/core';
import fetchApi from './fetch-api';

export const client = new Client({
  url: window.appSettings.sDataApiUrl,
  exchanges: [fetchExchange],
  fetch: (input, init) => {
    let name;

    const body = JSON.parse(init.body);
    if(body.operationName) {
      name = body.operationName;
    }
    return fetchApi(input, {...init, name});
  }
});

export const clientRT = new Client({
  url: window.appSettings.sDataApiUrl,
  exchanges: [fetchExchange],
  fetch: (input, init) => {
    let name;

    const body = JSON.parse(init.body);
    if(body.operationName) {
      name = body.operationName;
    }

    if(init.headers && typeof init.headers === 'object') {
      init.headers['x-db-rtdata'] = true;
    }
    return fetchApi(input, {...init, name});
  }
});