/* eslint-disable jsx-a11y/no-noninteractive-tabindex */
import { useEffect, useContext, useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation,Link } from 'react-router-dom';

import { LAYOUT } from '../common';
import { AppContext } from '../AppContext';
import { selectTicker, fetchTickers } from '../actions/tickerActions';
import { Comparison } from '../components/comparisonV2/Comparison';
import ShareDetails from '../components/ShareDetails';
import { classNames, dynamicSort } from '../helper';
import Switcher from '../components/Switcher';
import i18n from '../services/i18n';
import { Performance } from '../components/performance/Performance';
import Chart from '../components/chart/Chart';
import { Skeleton } from '../components/Skeleton';
import ChartContext from '../context/ChartContext';
import { useIsFirstRenderChart } from '../customHooks';
import Footer from '../components/Footer';
import ErrorBoundary from '../components/ErrorBoundary';
import HeaderLayout from '../components/headerLayout/HeaderLayoutV2';
import Trades from '../components/trades';
import { OrderDepth } from '../components/OrderDepth';

export default function Home() {
  const settings = useContext(AppContext);
  const layout = settings.layout || LAYOUT.FULL;
  const location = useLocation();
  const dispatch = useDispatch();
  const isTickerInfoLoading = useSelector(state => state.tickers.loading);
  const [isSwitchTab, setIsSwitchTab] = useState(false);
  const chart = useState(null);
  const chartContext = useState(null);
  const firstRenderChart = useIsFirstRenderChart();
  const enableTrades = settings.trade.enabled;
  const enableOrderDepth = settings.orderDepth?.enabled;
  const performanceEnabled = () => {
    return settings.performance.enabledFormats.length > 0 && settings.performance.enabledFormats[0] && settings.performance.performanceTypes.length > 0 && settings.performance.performanceTypes[0];
  };

  const shareTypes = [
    {
      type: 'SHARE_GRAPH',
      text: i18n.translate('shareGraphTab'),
      isDefaultSelected: true,
      id: 'shareGraph'
    },
    {
      type: 'SHARE_DATA',
      text: i18n.translate('shareDataTab'),
      isDefaultSelected: false,
      id: 'shareDetail'
    },
    enableTrades
      ? {
        type: 'TRADES',
        text: i18n.translate('trades'),
        isDefaultSelected: false,
        id: 'trades'
      } : {},
    enableOrderDepth
      ? {
        type: 'ORDER_DEPTH',
        text: i18n.translate('orderDepth'),
        isDefaultSelected: false,
        id: 'orderDepth'
      } : {},
    performanceEnabled()
      ? {
        type: 'PERFORMANCE',
        text: i18n.translate('performanceTab'),
        isDefaultSelected: false,
        id: 'performance'
    } : {}

  ].filter(shareType => !!shareType.type);

  const [shareType, setShareType] = useState(shareTypes.find(c => c.isDefaultSelected).type);

  const getDefaultSelectedTickerId = useCallback(() => {
    const tickerDefault = settings.instruments.filter(x => x.default).sort(dynamicSort('order')).find(x => x.default);
    return tickerDefault ? tickerDefault.id : settings.instruments[0].id;
  }, [settings.instruments]);

  const LinkAccessible = () => settings.accessibilities.enabled && (
    <Link
      className={classNames('accessibility-link', {
        'no-switcher': settings.ticker.enabledFormat.length < 2
      })}
      to={{
        ...location,
        pathname: '/accessibility'
      }}
    >
      {i18n.translate('accessibleShareGraph')}
    </Link>
  );

  useEffect(() => {
    // Load ticker's information as soon as possible
    dispatch(fetchTickers());
    dispatch(selectTicker(getDefaultSelectedTickerId()));
  }, [dispatch, getDefaultSelectedTickerId, settings.shareDetails.shareDataItems, settings.shareDetails.enabled]);

  const handleSwitchFormat = (type) => {
    setShareType(type);
    if(!isSwitchTab) {
      setIsSwitchTab(true);
    }
  };

  const [colorBlindMode, setColorBlindMode] = useState(false);

  const isComparisonHide = settings.peers.peers.length == 0 && settings.indices.indices.length == 0;

  return (
    <ErrorBoundary>
      <div className="app__inner">
        <ChartContext.Provider value={{ chart, chartContext, firstRenderChart, shareType }}>
          {layout.toLowerCase() === LAYOUT.FULL.toLocaleLowerCase() && ( // Full Layout
            <main role='main' tabIndex="-1" className="share-graph share-graph--full">
              <HeaderLayout />
              <FullChart
                settings={settings}
                colorBlindMode={colorBlindMode}
                isTickerInfoLoading={isTickerInfoLoading}
              />
                {!isComparisonHide && (
                  <div id="comparison" className="comparison-wrapper">
                    <Comparison></Comparison>
                  </div>
                )}
              <div id="shareDetail" className="shareDetail" role='region' aria-label={i18n.translate('shareDataTab')}>
                {settings.shareDetails.enabled && settings.shareDetails.shareDataItems && (
                  <ShareDetails></ShareDetails>
                )}
              </div>
              { enableTrades &&
                <div id='trades' className="trades" role='region' tabIndex={0} aria-label={i18n.translate('trades')}>
                  <Trades />
                </div>
              }
              { enableOrderDepth && <div id='orderDepth' role='region' tabIndex={0} aria-label={i18n.translate('orderDepth')}><OrderDepth /></div> }
              <div className="performance" id="performance" role='region' tabIndex={0} aria-label={i18n.translate('performanceTab')}>
                {performanceEnabled() && <Performance />}
              </div>
              <div className="barier" style={{ height: '1px' }}></div>
            </main>
          )}

          {layout.toLowerCase() === LAYOUT.FIXED.toLocaleLowerCase() && ( // Fixed Layout | share-graph--fixed
            <main role='main' tabIndex="-1" className="share-graph share-graph--fixed share-graph--fixed-v2">
              <HeaderLayout />
              {/* <ColorBlindModeButton onChange={changeBlindMode}></ColorBlindModeButton> */}
              <div id="tabs" className='main__inner'>
                <Switcher
                  className="switcher--tab"
                  onClick={handleSwitchFormat}
                  type={'SHARE'}
                  tabs={shareTypes}
                  tabActive={shareType}
                  isButton={false}
                  ariaLabel={i18n.translate('shareTabs')}
                />

                <div id='shareGraph' aria-labelledby='tab-shareGraph' hidden={shareType !== 'SHARE_GRAPH'} className={classNames('share-graph__peer-indices',{
                  'share-graph--hide': shareType !== 'SHARE_GRAPH',
                  'comparison--hide': isComparisonHide
                   })}
                  role='tabpanel' tabIndex="0">
                  <FixedChart
                    settings={settings}
                    isSwitchTab={isSwitchTab}
                    colorBlindMode={colorBlindMode}
                    isTickerInfoLoading={isTickerInfoLoading}
                  />
                    {!isComparisonHide && (
                      <div id="comparison" className="comparison-wrapper">
                        <Comparison></Comparison>
                      </div>
                    )}
                </div>
                {shareType === 'SHARE_DATA' && (
                  <div id="shareDetail" aria-labelledby="tab-shareDetail" className="shareDetail" role='tabpanel' hidden={shareType !== 'SHARE_DATA'}>
                    {settings.shareDetails.enabled && <ShareDetails></ShareDetails>}
                  </div>
                )}
                {shareType === 'TRADES' && (
                  <div id='trades' aria-labelledby='tab-trades' className="trades" role='tabpanel' tabIndex="0" hidden={shareType !== 'TRADES'}>
                    <Trades />
                  </div>
                )}
                {shareType === 'ORDER_DEPTH' && <div id='orderDepth' aria-labelledby='tab-orderDepth' role='tabpanel' tabIndex="0" hidden={shareType !== 'ORDER_DEPTH'}><OrderDepth /></div>}
                {shareType === 'PERFORMANCE' && <div className="performance" id="performance" aria-labelledby='tab-performance' role='tabpanel'>
                    {performanceEnabled() && <Performance />}
                </div>}
              </div>
              <div className="barier" style={{ height: '1px' }}></div>
            </main>
          )}

          <Footer />
          <div id="loading-status" role='status' aria-live='polite' style={{position: 'absolute', width: 0, height: 0, overflow: 'hidden'}}></div>
        </ChartContext.Provider>

        {/* fix hidden footer in iframe */}
        <div style={{ height: '5px' }}></div>
      </div>
    </ErrorBoundary>
  );
}

const FixedChart = ({ isTickerInfoLoading, settings, colorBlindMode, isSwitchTab }) => {
  return (
    <div id="graph" className="graph">
      {isTickerInfoLoading && <Skeleton />}
      {!isTickerInfoLoading && (
        <Chart settings={settings} colorBlindMode={colorBlindMode} isSwitchTab={isSwitchTab} />
      )}
    </div>
  );
};

const FullChart = ({ isTickerInfoLoading, settings, colorBlindMode }) => (
  <div id="graph" className={classNames({
    'graph-no-comparison': settings.peers.peers.length == 0 && settings.indices.indices.length == 0
    }, 'graph')}>
    {isTickerInfoLoading && <Skeleton />}
    {!isTickerInfoLoading && <Chart settings={settings} colorBlindMode={colorBlindMode} />}
  </div>
);
