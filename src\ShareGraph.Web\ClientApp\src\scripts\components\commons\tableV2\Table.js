import { DEVICES } from '../../../common';
import { classNames, getDeviceType, isValidatedNumber } from '../../../helper';
import i18n from '../../../services/i18n';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow
} from './TableComponent';
import useHorizontalScroll from '../../../customHooks/useHorizontalScroll';

export const TableV2 = ({
  headings,
  datas,
  summary,
  caption,
  className,
  indicatorColumns = [],
  footer,
  footerElement,
  loading
}) => {
  const classByValue = (value) => {
    if (!isValidatedNumber(value)) return '';
    return value > 0
      ? 'indicator-up'
      : value < 0
      ? 'indicator-down'
      : 'indicator-neutral';
  };

  const {tableRef, outerRef, hasHorizontalScroll} = useHorizontalScroll({loading});

  return (
    <div ref={outerRef}>
      <Table summary={summary || undefined} ref={tableRef} className={classNames({[className]: !!className})}>
        {caption && (
          <caption className='tableV2__caption'>{caption}</caption>
        )}
      <TableHeader>
        <TableRow>
        {headings.map((heading, index) => {
        const columnLabel = heading.displayShortLabel &&
            (getDeviceType() === DEVICES.SM || getDeviceType() === DEVICES.XS)
            ? heading.displayShortLabel
            : heading.columnDisplay;

        return (
            <TableHead
            className={classNames({'tableV2__sm-head': hasHorizontalScroll})}
            scope='col'
            data-column={heading.fieldName}
            key={index}
            >
            {columnLabel}
            </TableHead>
        );
        })}
        </TableRow>
      </TableHeader>
      <TableBody>
        {datas.map((data, index) => {
            const dataKeysLength = Object.keys(data).length;
            if (dataKeysLength !== 0) {
            const tableCells = headings.map((heading, ind) => {
                const { fieldName } = heading;
                const valueFields = data[fieldName];
                const isIndicatorColumn = indicatorColumns.includes(fieldName);
                const numValueFields = valueFields.length;

                let renderedCell;
                if (numValueFields === 1) {
                const { value, display } = valueFields[0];
                const classIndicator = isIndicatorColumn ? classByValue(value) : '';
                renderedCell = (
                    <TableCell
                    key={ind}
                    className={classNames(classIndicator, { 'tableV2__sm-col': hasHorizontalScroll })}
                    data-column={fieldName}
                    >
                    {display}
                    </TableCell>
                );
                } else if (numValueFields === 2) {
                const classIndicator = isIndicatorColumn ? classByValue(valueFields[0].value) : '';
                const dataDisplay = valueFields.map((d, i) => (
                    <div key={i} className={`field${d.lable.replace(':', '')}_value`}>
                    <span className={`field${d.lable.replace(':', '')}-label`}>
                        {d.lable}
                    </span>{' '}
                    <span className={`field${d.lable.replace(':', '')}-data`}>
                        {d.display}
                    </span>
                    </div>
                ));
                renderedCell = (
                    <TableCell
                    key={ind}
                    className={classNames(classIndicator, { 'tableV2__sm-col': hasHorizontalScroll })}
                    data-column={fieldName}
                    >
                    {dataDisplay}
                    </TableCell>
                );
                } else {
                const { value, display } = valueFields;
                const classIndicator = isIndicatorColumn ? classByValue(value) : '';
                renderedCell = (
                    <TableCell
                    key={ind}
                    className={classNames(classIndicator, { 'tableV2__sm-col': hasHorizontalScroll })}
                    data-column={fieldName}
                    >
                    {display}
                    </TableCell>
                );
                }
                return renderedCell;
            });

            return <TableRow className={classNames({'tableV2__sm-row': hasHorizontalScroll})} key={index}>{tableCells}</TableRow>;
            } else {
            return (
                <TableRow key={index}>
                <TableCell colSpan={headings.length} className='text-center'>
                    {i18n.translate('notFoundData')}
                </TableCell>
                </TableRow>
            );
            }
        })}
        </TableBody>
        {footerElement && (
          <TableFooter>
            <TableRow>
              {Object.keys(footerElement).map(fieldName => {
                const { display, colSpan } = footerElement[fieldName];
                return (
                  <TableCell colSpan={colSpan} data-column={fieldName} key={fieldName}>
                    {display}
                  </TableCell>
                );
              })}
            </TableRow>
          </TableFooter>
        )}
    </Table>
      {footer &&
          <TableFooter>
            <TableRow><TableCell colSpan={headings.length}>{footer}</TableCell></TableRow>
          </TableFooter>
      }
    </div>
  );
};
