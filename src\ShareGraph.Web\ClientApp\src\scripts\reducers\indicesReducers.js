import produce from 'immer';

/**
 * @typedef { import('../actions').UpdateStockPayload } UpdateStockPayload
 */

import {
  FETCH_INDICES_BEGIN,
  FETCH_INDICES_SUCCESS,
  FETCH_INDICES_FAILURE,
  REFRESH_INDICES_BEGIN,
  REFRESH_INDICES_SUCCESS,
  REFRESH_INDICES_FAILURE,
  INDICES_SELECTED_CHANGE_SUCCESS,
  INDICES_DESELECTED_CHANGE,
  UPDATE_INDICES_OFFICIAL_CLOSE
} from '../actions/indicesActions';
import {calcPriceChange} from '../utils';
import {UPDATE_STOCK_DATA} from '../actions';
import dayjs from '../utils/dayjs';
import {MarketTime} from '../services/market';
import {getLoadStrategy} from '../helper';

const initialState = {
  instruments: /** @type {Array<Record<string, any>>} */([]),
  updateStrategy: /** @type {import('../../../types/configuration-app').IUpdateStrategy} */({}),
  selectedIndicesIds: /** @type {Array<number>} */([]),
  loading: true,
  refreshing: false,
  fetchError: null,
  refreshError: null
};

export default function createIndicesReducer(instrumentIds = []) {  
  initialState.instruments = instrumentIds.map((i) => {
    return {
      instrumentId: i
    };
  });

  initialState.updateStrategy = instrumentIds.reduce((acc, i) => {
    acc[i] = 'fetch';
    return acc;
  }, {});
  
  return function indicesReducer(state = initialState, action) {
    switch (action.type) {
      case FETCH_INDICES_BEGIN:
        return produce(state, draft => {
          draft.loading = true;
          draft.fetchError = null;
        });
      case FETCH_INDICES_SUCCESS:
        return produce(state, draft => {
          draft.loading = false;
          const instruments = action.payload.instruments;
          draft.instruments = instruments;
          instruments.forEach(item => {
            draft.updateStrategy[item.id] = getLoadStrategy(item);
          });
        });
      case FETCH_INDICES_FAILURE:
        return produce(state, draft => {
          draft.loading = false;
          draft.fetchError = action.payload.error;
        });
      case INDICES_SELECTED_CHANGE_SUCCESS:
        return produce(state, draft => { 
          if(Array.isArray(action.payload.instrumentId)) {
            draft.selectedIndicesIds = [...draft.selectedIndicesIds, ...action.payload.instrumentId];
            return;
          }
          if(draft.selectedIndicesIds.includes(action.payload.instrumentId)){
            draft.selectedIndicesIds.splice(draft.selectedIndicesIds.indexOf(action.payload.instrumentId), 1);
          }else{
            draft.selectedIndicesIds = [...draft.selectedIndicesIds, action.payload.instrumentId];
          }
        });
      case REFRESH_INDICES_BEGIN:
        return produce(state, draft => {
          draft.refreshing = true;
          draft.fetchError = null;
        });
      case UPDATE_INDICES_OFFICIAL_CLOSE:
        return produce(state, draft => {
          const { instrumentId, officialClose, officialCloseDate } = action.payload;
          draft.instruments.forEach(instrument => {
            if(instrument.instrumentId !== instrumentId) return;
            instrument.officialClose = officialClose;
            instrument.officialCloseDate = officialCloseDate;

            draft.updateStrategy[instrumentId] = getLoadStrategy(/** @type {any} */(instrument));
          });
        });
      case REFRESH_INDICES_SUCCESS:
        return produce(state, draft => {
          draft.refreshing = false;
          for (const intrument of action.payload.instruments) {
            draft.updateStrategy[intrument.id] = getLoadStrategy(intrument);
            const index = draft.instruments.findIndex(i => i.id === intrument.id);
            if (index !== -1) {
              draft.instruments[index] = intrument;
            } else {
              draft.instruments.push(intrument);
            }
          }
        });
      case REFRESH_INDICES_FAILURE:
        return produce(state, draft => {
          draft.refreshing = false;
          draft.refreshError = action.payload.error;
        });
      case INDICES_DESELECTED_CHANGE:
        return produce(state, draft => {
          draft.selectedIndicesIds = draft.selectedIndicesIds.filter(ins => ins !== action.payload.instrumentId);
        });
      case UPDATE_STOCK_DATA:
        return produce(state, draft => {
          const payload = /** @type { UpdateStockPayload } */(action.payload);
          const close = payload.data?.close;
          const date = payload.data.date;
          if(!date) throw new Error('date not found');
          if(!dayjs(draft.lastUpdatedDate).isSame(date, 'day')) {
            console.log('because same date then we dont update close price');
            return;
          }
          if(close === undefined) return;
          
          draft.instruments.forEach(item => {
            if(item.instrumentId !== action.payload.instrumentId) return;
            const newData = calcPriceChange(item.open, close);

            item.last = newData.last;
            item.change = newData.change;
            item.changePercentage = newData.changePercentage;
          });
        });
      default:
        return state;
    }
  };
}

export const indicesSelector = state => state.indices.instruments.reduce((s, ins) => {
  s[ins.instrumentId] = ins;
  return s;
}, {});