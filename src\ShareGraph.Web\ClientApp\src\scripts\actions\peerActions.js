import fetchApi from '../services/fetch-api';
import { appSettings } from '../../appSettings';
import { getPeersSetting } from '../configs/configuration-app';
import { mergeObjectWithDefinedProps } from '../utils/common';
import { getCurrencyText } from '../helper';
import {client, clientRT} from '../services/graphql-client';
import {PEER_INDICES_QUERY} from '../graphql-queries/peerIndicesQuery';
import { get } from 'es-toolkit/compat';

/**
 * @typedef {import('../reducers/currencyReducers').CurrencyOption} CurrencyOption
 * @param {Array<string | number>} notRealTimeIds
 * @param {Array<string | number>} realtimeIds
 * @param {Array<string | number>} additionalRealtimeIds
 * @param {CurrencyOption['code']} toCurrency
 */
async function getPeersDataNewQuery(notRealTimeIds = [],realtimeIds = [], additionalRealtimeIds = [], toCurrency) {
  if(!notRealTimeIds.length && !realtimeIds.length && !additionalRealtimeIds.length) {
    return new Promise((_, reject) => {
      reject('Not any instrument id provided.');
    });
  }

  const mapping = item => {
    const shareName = item.shareName.replace((/\([a-zA-Z]+\)$/), '').trim();
    const marketStatus = get(item, 'market.status.isOpened') ? 'Open' : 'Close';

    return {
      instrumentId: item.id,
      shareName,
      currencyCode: get(item, 'currency.code'),
      last: get(item, 'currentPrice.last'),
      change: get(item, 'currentPrice.change'),
      changePercentage: get(item, 'currentPrice.changePercentage'),
      marketStatus,
      ticker: item.symbol,
      open: get(item, 'currentPrice.open'),
      lastUpdatedDate: item.date,
      symbol: get(item, 'currency.symbol'),
      normalDailyOpen: get(item, 'market.openTimeLocal'),
      normalDailyClose: get(item, 'market.closeTimeLocal'),
      marketTimeZone: get(item, 'market.timezoneName'),
      marketName: get(item, 'market.translation.value'),
      marketAbbreviation: get(item, 'market.abbreviation'),
      officialClose: get(item, 'currentPrice.officialClose'),
      officialCloseDate: get(item, 'currentPrice.officialCloseDate')
    };
  };

  const peersSetting = getPeersSetting();

  const realTime = {
    ids: realtimeIds.filter(id => !peersSetting[id].enabledAdjustPrice), 
    adjIds: realtimeIds.filter(id => peersSetting[id].enabledAdjustPrice)
  };

  const notRealTime = {
    ids: notRealTimeIds.filter(id => !peersSetting[id].enabledAdjustPrice), 
    adjIds: notRealTimeIds.filter(id => peersSetting[id].enabledAdjustPrice)
  };

  const promiseAll = [];

  if(realTime.ids.length > 0 || additionalRealtimeIds.length > 0) promiseAll.push(
    clientRT.query(PEER_INDICES_QUERY, {
      ids: realTime.ids,
      additionalRealtimeIds,
      toCurrency,
      adjClose: false
    })
  );

  if(realTime.adjIds.length > 0) promiseAll.push(
    clientRT.query(PEER_INDICES_QUERY, {
      ids: realTime.adjIds,
      additionalRealtimeIds: [],
      toCurrency,
      adjClose: false
    })
  );

  if(notRealTime.ids.length > 0) promiseAll.push(
    client.query(PEER_INDICES_QUERY, {
      ids: notRealTime.ids,
      additionalRealtimeIds: [],
      toCurrency,
      adjClose: false
    })
  );

  if(notRealTime.adjIds.length > 0) promiseAll.push(
    client.query(PEER_INDICES_QUERY, {
      ids: notRealTime.adjIds,
      additionalRealtimeIds: [],
      toCurrency,
      adjClose: false
    })
  );

  const results = (await Promise.all(promiseAll));

  const instruments = results.map(result => result.data.instrumentByIds?.map(mapping) ?? []).flat();

  const additionalRealtime = results?.[0].data?.additionalRealtime?.map(item => ({
    instrumentId: item.id,
    officialClose: get(item, 'currentPrice.officialClose'),
    officialCloseDate: get(item, 'currentPrice.officialCloseDate')
  })) ?? [];

  return {
    instruments,
    additionalRealtime
  };

  // const [realTimeData, notRealTimeData] = await Promise.all([
  //   (realtimeIds.length || additionalRealtimeIds.length) ? clientRT.query(PEER_INDICES_QUERY, {
  //     ids: realtimeIds.filter(id => !peersSetting[id].enabledAdjustPrice), 
  //     adjIds: realtimeIds.filter(id => peersSetting[id].enabledAdjustPrice),
  //     additionalRealtimeIds, 
  //     toCurrency
  //   }) : Promise.resolve(),
  //   notRealTimeIds.length ? client.query(PEER_INDICES_QUERY, {
  //     ids: notRealTimeIds.filter(id => !peersSetting[id].enabledAdjustPrice), 
  //     adjIds: notRealTimeIds.filter(id => peersSetting[id].enabledAdjustPrice),
  //     additionalRealtimeIds: [],
  //     toCurrency
  //   }) : Promise.resolve()
  // ]);

  
  // const realtime = realTimeData?.data.instrumentByIds.map(mapping) ?? [];
  // const notRealTime = notRealTimeData?.data.instrumentByIds.map(mapping) ?? [];
  
  // return {
  //   data: {
  //     notRealTime,
  //     realtime,
  //     additionalRealtime: realTimeData?.data.additionalRealtime?.map(item => ({
  //       instrumentId: item.id,
  //       officialClose: item.currentPrice.officialClose,
  //       officialCloseDate: item.currentPrice.officialCloseDate
  //     })) ?? []
  //   }
  // };
}


// Peers Actions
export const FETCH_PEERS_BEGIN = 'FETCH_PEERS_BEGIN';
export const FETCH_PEERS_SUCCESS = 'FETCH_PEERS_SUCCESS';
export const FETCH_PEERS_FAILURE = 'FETCH_PEERS_FAILURE';
export const REFRESH_PEERS_BEGIN = 'REFRESH_PEERS_BEGIN';
export const REFRESH_PEERS_SUCCESS = 'REFRESH_PEERS_SUCCESS';
export const REFRESH_PEERS_FAILURE = 'REFRESH_PEERS_FAILURE';
export const PEERS_SELECTED_CHANGE_SUCCESS = 'PEERS_SELECTED_CHANGE_SUCCESS';
export const PEERS_SELECTED_CHANGE = 'PEERS_SELECTED_CHANGE';
export const PEERS_DESELECTED_CHANGE = 'PEERS_DESELECTED_CHANGE';
export const UPDATE_PEERS_OFFICIAL_CLOSE = 'UPDATE_PEERS_OFFICIAL_CLOSE';
export const fetchPeersBegin = () => ({
  type: FETCH_PEERS_BEGIN
});

export const fetchPeersSuccess = (instruments = []) => {
  const peersSetting = getPeersSetting();
  return (dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);

    return dispatch({
      type: FETCH_PEERS_SUCCESS,
      payload: {
        instruments: instruments.map(ins =>
          mergeObjectWithDefinedProps(
            {
              ...ins,
              originalCurrency: ins.currencyCode
            },
            {
              ...peersSetting[ins.instrumentId], 
              currencyCode: selectedCurrencyText || peersSetting[ins.instrumentId].currencyCode
            }
          )
        )
      }
    });

  };
  
};

export const fetchPeersFailure = (error) => ({
  type: FETCH_PEERS_FAILURE,
  payload: { error }
});

export const selectPeersuccess = (instrumentId) => ({
  type: PEERS_SELECTED_CHANGE_SUCCESS,
  payload: { instrumentId }
});

export const refreshPeersBigin = () => ({
  type: REFRESH_PEERS_BEGIN
});

export const refreshPeersucess = (instruments = []) => {
  const peersSetting = getPeersSetting();

  return(dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);
    
    return dispatch({
      type: REFRESH_PEERS_SUCCESS,
      payload: {
        instruments: instruments.map(ins =>
          mergeObjectWithDefinedProps(
            {
              ...ins,
              originalCurrency: ins.currencyCode
            },
            {
              ...peersSetting[ins.instrumentId], 
              currencyCode: selectedCurrencyText || peersSetting[ins.instrumentId].currencyCode
            }
          )
        )
      }
    });
  };
};

export const refreshPeersFailure = (error) => ({
  type: REFRESH_PEERS_FAILURE,
  payload: { error }
});

export const addSelectedPeer = (instrumentId) => ({
  type: PEERS_SELECTED_CHANGE,
  payload: { instrumentId }
});

export const removeSelectedPeer = (instrumentId) => ({
  type: PEERS_DESELECTED_CHANGE,
  payload: { instrumentId }
});

export function fetchPeers() {
  return async (dispatch, getState) => {
    const peersSetting = getPeersSetting();
    const instrumentIds = getState().peers.instruments.map((ins) => ins.instrumentId);
    const notRealTimeIds = instrumentIds.filter(id => !peersSetting[id].isRT);
    const realtimeIds = instrumentIds.filter(id => peersSetting[id].isRT);
    dispatch(fetchPeersBegin());

    const { currency } = getState().currency;
    const toCurrency = currency?.code;
    try {
      if(instrumentIds.length === 0) {
        dispatch(fetchPeersSuccess([]));
        return [];
      } else {
        try{
          const { instruments } = await getPeersDataNewQuery(notRealTimeIds, realtimeIds, [], toCurrency);

          dispatch(fetchPeersSuccess(instruments));
          return instruments;

        }
        catch (error) {
          console.warn(error);
          return [];
        }
      }
    } catch (err) {
      dispatch(fetchPeersFailure(err));
      throw err;
    }
  };
}

export function refreshPeers() {
  return async (dispatch, getState) => {
    const state = getState();
    const peersSetting = getPeersSetting();
    const instrumentIds = state.peers.instruments.map((ins) => ins.instrumentId);
    const updateStrategy = state.peers.updateStrategy;
    const notRealTimeIds = instrumentIds.filter(id => updateStrategy[id] !== 'socket' && !peersSetting[id].isRT);
    const realtimeIds = instrumentIds.filter(id => updateStrategy[id] !== 'socket' && peersSetting[id].isRT);
    const additionalRealtimeIds = instrumentIds.filter(id => updateStrategy[id] === 'socket');
    dispatch(refreshPeersBigin());

    const { currency } = state.currency;
    const toCurrency = currency?.code;
    try {
      if(instrumentIds.length === 0) {
        dispatch(fetchPeersSuccess([]));
        return [];
      } else {
        try{
        const { additionalRealtime, instruments } = await getPeersDataNewQuery(notRealTimeIds, realtimeIds, additionalRealtimeIds, toCurrency);
        dispatch(refreshPeersucess(instruments));
        additionalRealtime.forEach((item) => {
          const { instrumentId, officialClose, officialCloseDate } = item;
          if (officialClose == null || officialCloseDate == null) return;
          dispatch(
            updateOfficialClose(instrumentId, officialClose, officialCloseDate)
          );
        });
        return instruments;
      }
      catch (error) {
        console.warn(error);
        return [];
      }
      }
    } catch (err) {
      dispatch(refreshPeersFailure(err));
      throw err;
    }
  };
}

export function selectPeers(instrumentId) {
  return (dispatch, getState) => {
    const state = getState().peers;
    const instruments = state.instruments;
    // const previousSelected = state.selectedInstrumentId;

    // if (previousSelected === instrumentId) {
    //   return;
    // }
    if (instruments.filter((ins) => ins.instrumentId === instrumentId).length) {
      dispatch(selectPeersuccess(instrumentId));
    } else {
      // TODO: Notify out that no Peers matches with provided `${instrumentId}`
    }
  };
}

export function deSelectPeer(instrumentId) {
  return (dispatch, getState) => {
    const state = getState().peers;

    if (instrumentId <= 0 || !state.selectedPeerIds.includes(instrumentId)) {
      return;
    }

    dispatch(removeSelectedPeer(instrumentId));
  };
}

/**
 *
 * @param {number} officialClose
 * @param {string} officialCloseDate
 * @param {number} instrumentId
 * @returns
 */
export function updateOfficialClose(instrumentId, officialClose, officialCloseDate) {
  return {
    type: UPDATE_PEERS_OFFICIAL_CLOSE,
    payload: { officialClose, officialCloseDate, instrumentId }
  };
}
