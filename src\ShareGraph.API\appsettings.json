{"AllowedHosts": "*", "ConnectionStrings": {"TranslationDbContext": "Server=************;Database=shark;User=uShark;PWD=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;"}, "Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "*************", "port": "514", "appName": "FlipIT.ShareGraph.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.ShareGraph.API"}}, "GeneralSettingsPath": "\\\\sofs\\Shared_storage\\tools-settings\\config", "ToolSettingsPath": "\\\\sofs\\Shared_storage\\tools-settings\\tools\\sharegraph3\\config", "UseAzureFileSettingStorage": false, "AzureFileShareName": "tools-settings", "InternalSDataApiUrl": "http://localhost:88/tools/flipit-sdata-api/graphql"}