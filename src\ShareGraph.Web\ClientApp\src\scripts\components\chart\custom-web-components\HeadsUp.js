import { CIQ } from '../chartiq-import';
import { convertNumber, convertNumberInString, translateStringFormat, formatShortDate, formatLongDate, convertChangePercentDecimal, formatDateNoConvertTZ } from '../../../helper';
import { getPriceAndChangeFromQuote } from './common';
import dayjs from 'dayjs';

/**
 * Determines information inside of the HeadsUp display based on position.
 * @memberof CIQ.UI.HeadsUp
 * @private
 */
CIQ.UI.HeadsUp.prototype.position = function () {
  var stx = this.context.stx;
  var bar = stx.barFromPixel(stx.cx);
  this.tick = stx.tickFromPixel(stx.cx);
  var prices = stx.chart.xaxis[bar];
  //var currentQuote=stx.chart.currentQuote;
  var plotField = stx.chart.defaultPlotField;
  var highLowBars = stx.chart.highLowBars;
  if (!plotField || highLowBars) plotField = 'Close';
  var node = this.node;
  var self = this;
  function formatPrice(value) {
    var numStr = '';
    var chartScale = stx.layout.chartScale,
      panel = stx.chart.panel,
      yAxis = stx.chart.yAxis;
    if (yAxis.originalPriceFormatter && yAxis.originalPriceFormatter.func) {
      numStr = yAxis.originalPriceFormatter.func(stx, panel, value);
    } else if (yAxis.priceFormatter && chartScale != 'percent' && chartScale != 'relative') {
      numStr = yAxis.priceFormatter(stx, panel, value);
    } else {
      numStr = stx.formatYAxisPrice(value, panel);
    }
    // return numStr.replace(/(\.[0-9]*[1-9])0+$|\.0*$/, '$1');
    return numStr;
  }
  function printValues() {
    var $node = CIQ.UI.$(node);
    self.timeout = null;
    function valOrNA(text) {
      return CIQ.isValidNumber(parseFloat(text)) ? text : 'N/A';
    }
    if($node?.[0]?.nodeName === 'CQ-HU-STATIC') {
      $node[0].style && $node[0].style.setProperty('display', 'none');
    }
    if (prices) {
      $node.find('cq-hu-price').text('N/A');
      $node.find('cq-hu-open').text('N/A');
      $node.find('cq-hu-close').text('N/A');
      $node.find('cq-hu-high').text('N/A');
      $node.find('cq-hu-low').text('N/A');
      $node.find('cq-hu-date').text('N/A');
      $node.find('cq-hu-volume').text('N/A');
      $node.find('cq-hu-change').text('N/A');
      $node.find('cq-volume-rollup').text('');
      if (prices.data) {
        var quote = CIQ.clone(prices.data);
        if (quote.Open === undefined) quote.Open = quote.Close;
        if (quote.High === undefined) quote.High = Math.max(quote.Open, quote.Close);
        if (quote.Low === undefined) quote.Low = Math.min(quote.Open, quote.Close);
        if($node[0].nodeName === 'CQ-HU-STATIC' && quote.Close !== undefined) {
            $node[0].style && $node[0].style.setProperty('display', '');
          }
        $node.find('cq-currency').text(translateStringFormat('currency',[stx.chart.symbolObject.currencyCode]));
        $node.find('cq-hu-open').text(formatPrice(valOrNA(quote.Open)));
        $node.find('cq-hu-price').text(formatPrice(valOrNA(quote[plotField])));
        $node.find('cq-hu-close').text(formatPrice(valOrNA(quote.Close)));
        $node.find('cq-hu-high').text(formatPrice(valOrNA(quote.High)));
        $node.find('cq-hu-low').text(formatPrice(valOrNA(quote.Low)));
        var changeData = getPriceAndChangeFromQuote(quote, stx);
        $node.find('cq-hu-change').text(convertChangePercentDecimal(Number(changeData.changePct)) + '%');
        var volume = CIQ.condenseInt(quote.Volume);
        volume = convertNumber(quote.Volume);
        var rollup = volume.charAt(volume.length - 1);
        if (rollup > '9') {
          volume = volume.substring(0, volume.length - 1);
          $node.find('cq-volume-rollup').text(rollup.toUpperCase());
        }
        $node.find('cq-hu-volume').text(volume);
        var tickDate = quote.displayDate;
        if (!tickDate) tickDate = quote.DT;
        // if (stx.internationalizer) {
        //   if (CIQ.ChartEngine.isDailyInterval(stx.layout.interval)) {
        //     $node.find('cq-hu-date').text(stx.internationalizer.yearMonthDay.format(tickDate));
        //   } else {
        //     $node
        //       .find('cq-hu-date')
        //       .text(
        //         stx.internationalizer.yearMonthDay.format(tickDate) +
        //           ' ' +
        //           stx.internationalizer.hourMinute.format(tickDate)
        //       );
        //   }
        // } else {
        //   if (CIQ.ChartEngine.isDailyInterval(stx.layout.interval)) {
        //     $node.find('cq-hu-date').text(CIQ.mmddyyyy(CIQ.yyyymmddhhmm(tickDate)));
        //   } else {
        //     $node.find('cq-hu-date').text(CIQ.mmddhhmm(CIQ.yyyymmddhhmmssmmm(tickDate)));
        //   }
        // }
        if (CIQ.ChartEngine.isDailyInterval(stx.layout.interval)) {
          $node.find('cq-hu-date').text(convertNumberInString(formatShortDate(tickDate, {isTimezone: false})));
        } else {
          let { displayZone } = stx; 
          let dateLocal = dayjs(tickDate).format('YYYY-MM-DD HH:mm:ss');
          let utcDate = dayjs.tz(dateLocal, displayZone).utc();
          $node.find('cq-hu-date').text(convertNumberInString(formatLongDate(utcDate, {isTimezone: true})));
        }
        var visuals = $node.find('cq-volume-visual');
        if (visuals.length) {
          var relativeCandleSize = self.maxVolume.value ? quote.Volume / self.maxVolume.value : 0;
          if(relativeCandleSize < 0.1 && relativeCandleSize !== 0) {
            relativeCandleSize += 0.01;
          }
          visuals.css({ width: Math.round(relativeCandleSize * 100) + '%' });
        }
      }
      // not sure why we'd need this, commenting out for now
      /*if(currentQuote && currentQuote[plotField] && self.tick==stx.chart.dataSet.length-1){
					node.find("cq-hu-price").text(valOrNA(stx.formatPrice(currentQuote[plotField])));
				}*/
    }
  }
  if (this.tick != this.prevTick || (prices && +prices.DT == +stx.chart.endPoints.end)) {
    if (this.timeout) clearTimeout(this.timeout);
    var ms = this.params.followMouse ? 0 : 0; // IE and FF struggle to keep up with the dynamic heads up.
    this.timeout = setTimeout(printValues, ms);
  }
  this.prevTick = this.tick; // We don't want to update the dom every pixel, just when we cross into a new candle
  if (this.params.followMouse) {
    if (stx.openDialog) this.tick = -1; // Turn off the headsup when a modal is on
    this.followMouse(this.tick);
  }
};

CIQ.UI.components('cq-hu-static')[0].classDefinition.markup = `
  <cq-hu-static-inner>
    <!-- <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">Price</div>
        <cq-hu-price></cq-hu-price>
        <cq-currency></cq-currency>
    </div> -->
    <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">Open</div>
        <cq-hu-open></cq-hu-open>
        <!-- <cq-currency></cq-currency> -->
    </div>
    <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">High</div>
        <cq-hu-high></cq-hu-high>
        <!-- <cq-currency></cq-currency> -->
    </div>
    <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">Low</div>
        <cq-hu-low></cq-hu-low>
        <!-- <cq-currency></cq-currency> -->
    </div>
    <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">Close</div>
        <cq-hu-close></cq-hu-close>
        <!-- <cq-currency></cq-currency> -->
    </div>
    <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">Volume</div>
        <cq-volume-section>
            <cq-hu-volume></cq-hu-volume>
            <cq-volume-rollup></cq-volume-rollup>
        </cq-volume-section>
    </div>
    <div class="cq-hu-static-inner__item">
        <div class="cq-hu-static-inner__item-label">% Change</div>
        <cq-hu-change></cq-hu-change>
    </div>
  </cq-hu-static-inner>
`;

CIQ.UI.components('cq-hu-dynamic')[0].classDefinition.markup = `
<div>
  <cq-hu-col1>
    <cq-hu-date></cq-hu-date>
    <div class='cq-hu-price__wrapper'>
      <cq-hu-price></cq-hu-price>
      <cq-currency></cq-currency>
    </div>
    <cq-volume-grouping>
      <div>Volume</div>
      <div>
        <cq-volume-visual></cq-volume-visual>
      </div>
      <div>
        <cq-hu-volume></cq-hu-volume>
        <cq-volume-rollup></cq-volume-rollup>
      </div>
    </cq-volume-grouping>
  </cq-hu-col1>
  <cq-hu-col2>
    <div class='dynamic-tooltip-detail'>
      <div>Open</div>
      <cq-hu-open></cq-hu-open>
    </div>
    <div class='dynamic-tooltip-detail'>
      <div>Close</div>
      <cq-hu-close></cq-hu-close>
    </div>
    <div class='dynamic-tooltip-detail'>
      <div>High</div>
      <cq-hu-high></cq-hu-high>
    </div>
    <div class='dynamic-tooltip-detail'>
      <div>Low</div>
      <cq-hu-low></cq-hu-low>
    </div>
  </cq-hu-col2>
</div>
`;

const oldSetHeadsUp = CIQ.UI.Layout.prototype.setHeadsUp;
CIQ.UI.Layout.prototype.setHeadsUp = function () {
  const currentHeadsUp = this.context.stx.layout?.headsUp;
  if(currentHeadsUp) {
    currentHeadsUp.static = false;
    currentHeadsUp.dynamic = false;
    currentHeadsUp.floating = false;
  }
  return oldSetHeadsUp.apply(this, arguments);
};
