{"data": {"configuration": {"setting": {"styleURI": null, "timeZone": "Asia/Bangkok", "layout": "FIXED", "customPhrases": "{}", "instruments": [{"id": 32865, "default": true, "color": "#B2EBF2", "order": 2, "tickerName": null, "ticker": null, "currencyCode": null, "marketName": null, "marketAbbreviation": null}], "ticker": {"enabledFormat": ["GRAPH", " TABLE"], "graphTickerTemplate": "<div class=\"ticker__item-inner ticker__item-inner--multiple-tickers-2 ticker__item-inner--{instrumentId}\">\n  <div class=\"ticker__row ticker__row--header\">\n    <div class=\"ticker__row-item\">\n      <span class=\"\n          ticker__market-abbreviation ticker__market-abbreviation--bg-color\n        \">{marketAbbreviation}</span>\n    </div>\n    <div class=\"ticker__row-item\">\n      <span class=\"ticker__name\">{ticker}</span>\n      <div class=\"ticker__change\">\n        <i class=\"fs fs-triangle-up\"></i>\n        <i class=\"fs fs-triangle-down\"></i>\n        <span class=\"ticker__change-value\">{change} ({changePercentage}%)</span>\n      </div>\n    </div>\n    <div class=\"ticker__row-item ticker__row-item--price\">\n      <h2 class=\"ticker__price\">\n        {last} <span class=\"ticker__currency-code\">{currencyCode}</span>\n      </h2>\n    </div>\n  </div>\n  <div class=\"ticker__row ticker__ranges\">\n    <div class=\"ticker__range ticker__range--day\">\n      <span class=\"ticker__range-label\">{dayRangeLabel}</span>\n      <p class=\"ticker__range-value\">L {low} - {high} H</p>\n    </div>\n    <div class=\"ticker__range ticker__range--52w\">\n      <span class=\"ticker__range-label\">{w52RangeLabel}</span>\n      <p class=\"ticker__range-value\">L {low52W} - {high52W} H</p>\n    </div>\n    <div class=\"ticker__range ticker__range--volume\">\n      <span class=\"ticker__range-label\"><i class=\"fs fs-chart\"></i>{volumeLabel}</span>\n      <p class=\"ticker__range-value\">\n        <span>{volume}</span>\n        <i class=\"fs fs-arrow-increase\"></i>\n        <span class=\"ticker__range-volumn-change\">{volumeChange} X </span>\n      </p>\n    </div>\n  </div>\n  <div class=\"ticker__row ticker__market-status\">\n    <p class=\"status-closed\">\n      <i class=\"fs fs-close-radio\"></i>\n      <span class=\"market-name\">{marketName}</span>\n      <span class=\"market-status\">{marketCloseLabel}</span>\n    </p>\n    <p class=\"status-opened\">\n      <i class=\"fs fs-checked-radio\"></i>\n      <span class=\"market-name\">{marketName} {marketOpenedLabel}</span>\n    </p>\n    <p class=\"status-openIn\">\n      <i class=\"fs fs-checked-radio\"></i>\n      <span class=\"market-openTime\">{marketName} {marketOpenInLabel} {dateTimeToMarketOpen}</span>\n    </p>\n  </div>\n</div>\n", "graphTickerType": "MULTIPLE_TICKER_2", "tableTickerTemplate": "<table class=\"table table-striped\">\n    <thead class=\"table__head\">\n        <tr class=\"table__head-tr\">\n            <th scope=\"col\">{sharesLabel}</th>\n            <th scope=\"col\">{lastLabel}</th>\n            <th scope=\"col\">{openLabel}</th>\n            \n            <th class=\"text-center\" scope=\"col\">{changeLabel}</th>\n            <th class=\"text-center\" scope=\"col\">{changePercentageLabel}</th>\n            \n            <th scope=\"col\">{bidAskLabel}</th>\n        </tr>\n    </thead>\n    <tbody class=\"table__body\">\n        <tr class=\"table__body-tr\">\n            <td class=\"table__body-share\">{shareName}</td>\n            <td><span>{last}</span></td>\n            <td>{open}</td>\n            \n            <td class=\"text-center ticker__change ticker__change--number\">\n                <div class=\"ticker__animation\">\n                    <p class=\"ticker__animation-inner\">\n                        <span class=\"ticker__change-value\">{change}</span>\n                    </p>\n                </div>\n            </td>\n            <td class=\"text-center ticker__change ticker__change--percent\">\n                <div class=\"ticker__animation\">\n                    <p class=\"ticker__animation-inner\">\n                        <span class=\"ticker__change-value\">{changePercentage}</span>\n                    </p>\n                </div>\n            </td>\n            \n            <td>{bid}/{ask}</td>\n        </tr>\n    </tbody>\n</table>\n", "tableTickerType": "TABLE_TICKER_MULTIPLE", "tickerType": "MULTIPLE", "slidesPerView": 4, "tableAnimation": "TRANSFORM", "graphAnimation": "BLINK_PRICE"}, "peers": [{"id": 9287, "color": "#0097A7", "order": 1, "tickerName": "Heineken", "currencyCode": null}, {"id": 3562, "color": "#126548", "order": 5, "tickerName": null, "currencyCode": "DKK"}, {"id": 10000, "color": "#FF0000", "order": 2, "tickerName": "In Bev", "currencyCode": "DKK"}, {"id": 3541, "color": "#0000FF", "order": 4, "tickerName": "<PERSON><PERSON><PERSON>", "currencyCode": "DKK"}, {"id": 32904, "color": "#00FF00", "order": 3, "tickerName": "Diageo", "currencyCode": "DKK"}], "performance": {"enabledFormats": ["TABLE", "GRAPH"], "performanceTypes": ["SHARE_PRICE_DEVELOPMENT", "52_WEEKS_HIGH_LOW", "SHARE_PRICE_DEVELOPMENT_BY_YEARS"], "enable52WTableColumns": ["SHARENAME", "LAST", "LOW52W", "HIGH52W", "PERCENT52WLOW", "PERCENT52WHIGH"], "numberOfYearSPByYear": 5, "showEarliestYearFirstSPByYear": false}, "chart": {"enabledAdditionalOptions": ["EXCEL", "PRINT", "EXPORT", "SHARE"], "enabledPeriods": ["1D", "5D", "1M", "3M", "6M", "YTD", "1Y", "3Y", "5Y", "CUSTOM_RANGE"], "defaultPeriod": "1Y", "defaultTooltipType": "DYNAMIC_CALLOUT", "hideChartTitle": false, "defaultChartType": "MOUNTAIN", "enabledChartTypes": ["CANDLE", "LINE", "VERTEX_LINE", "STEP", "MOUNTAIN", "HISTOGRAM", "BAR"], "excludeStudies": [], "enabledEvents": ["DIVIDEND", "EARNING", "PRESSRELEASES"], "enabledChartPreferences": ["HIGH_LOW_VALUES", "RANGE_SELECTOR", "EXTENDED_HOURS"], "enabledYAxisPreferences": ["PERCENT_VIEW", "LOG_SCALE", "INVERT"], "enabledDefaultVolumeChart": true, "enabledHoverEvent": true}, "shareDetails": {"enabled": true, "shareDataItems": ["TIME", "CURRENCY", "MARKET", "MARKET_STATUS", "ISIN", "SYMBOL", "BID", "BID_SIZE", "ASK", "ASK_SIZE"], "displayOnSelectedTicker": true, "displayType": "Grid", "partialDisplay": true, "numberItemsInCollapsedMode": 8}, "indices": [{"id": 6346004, "color": "#126548", "order": 4, "tickerName": null}, {"id": 2040003, "color": "#126548", "order": 5, "tickerName": null}], "format": {"tickerDateTimeFormat": "DD MMM YYYY HH:mm", "shortDate": "MM/dd/yyyy", "decimalDigits": 2, "percentDigits": 2, "decimalSeparator": ".", "thousandsSeparator": ".", "negativeNumberFormat": "-n"}, "studies": {"macd": {"macd": "#000000", "signal": "#FF0000", "increasingBar": "#00DD00", "decreasingBar": "#FF0000"}, "rsi": {"rsi": "#000000"}}}}}}