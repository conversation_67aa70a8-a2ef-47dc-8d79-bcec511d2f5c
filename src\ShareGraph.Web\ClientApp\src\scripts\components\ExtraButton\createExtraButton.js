import {CHART_EXTRA_BUTTON, DownloadExcelButton, DownloadImageButton, PrintButton, ShareButton} from '../chart/additionalOptions';

export default function createExtraButton(type, key) {
  key = key || type;
  switch (type) {
    case CHART_EXTRA_BUTTON.SHARE_TYPE:
      return <ShareButton key={key} />;
    case CHART_EXTRA_BUTTON.EXCEL_TYPE:
      return <DownloadExcelButton key={key} />;
    case CHART_EXTRA_BUTTON.EXPORT_TYPE:
      return <DownloadImageButton key={key} />;
    case CHART_EXTRA_BUTTON.PRINT_TYPE:
      return <PrintButton key={key} />;
    default:
      return '';
  }
}