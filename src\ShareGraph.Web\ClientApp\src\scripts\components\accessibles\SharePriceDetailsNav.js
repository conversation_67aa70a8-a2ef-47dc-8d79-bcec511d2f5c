import React, { useContext, useEffect } from 'react';
import { Link, useLocation, useSearchParams } from 'react-router-dom';
import { AppContext } from '../../AppContext';
import { useSelectedTicker } from '../../customHooks/useTickers';
import { formatDateISO, formatWithDateTime, translateStringFormat } from '../../helper';
import i18n from '../../services/i18n';

const PERIOD_LINK = {
  '1M': {
    value: 'onemonth',
    label: () => translateStringFormat('monthFormat', [1]),
    time: date => {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() - 1);

      return newDate;
    }
  },
  '3M': {
    value: 'threemonths',
    label: () => translateStringFormat('monthsFormat', [3]),
    time: date => {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() - 3);

      return newDate;
    }
  },
  '6M': {
    value: 'sixmonths',
    label: () => translateStringFormat('monthsFormat', [6]),
    time: date => {
      const newDate = new Date(date);
      newDate.setMonth(newDate.getMonth() - 6);

      return newDate;
    }
  },
  YTD: {
    value: 'ytd',
    label: () => i18n.translate('ytd'),
    time: date => new Date(date.getFullYear(), 0)
  },
  '1Y': {
    value: 'oneyear',
    label: () => translateStringFormat('yearFormat', [1]),
    time: date => {
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() - 1);

      return newDate;
    }
  },
  '3Y': {
    value: 'threeyears',
    label: () => translateStringFormat('yearsFormat', [3]),
    time: date => {
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() - 3);

      return newDate;
    }
  },
  '5Y': {
    value: 'fiveyears',
    label: () => translateStringFormat('yearsFormat', [5]),
    time: date => {
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() - 5);

      return newDate;
    }
  },
  ALL: {
    value: 'all',
    label: () => i18n.translate('all'),
    time: date => {
      const newDate = new Date(date);
      newDate.setFullYear(newDate.getFullYear() - 15);

      return newDate;
    }
  }
};

const SharePriceDetailsNav = () => {
  const settings = useContext(AppContext);
  const [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();
  const selectedInstrument = useSelectedTicker();

  const periodParam = searchParams.get('period') || '';
  const enabledPeriods = settings.chart.enabledPeriods || [];
  const cloneSearchParams = new URLSearchParams(searchParams);
  const currentTime = new Date();
  const buildPeriodLink = period => {
    cloneSearchParams.set('period', period.value);
    switch (period.value) {
      case 'all':
        cloneSearchParams.set('startDate', formatDateISO(selectedInstrument?.startingDate || period.time(currentTime)));
        break;
      default:
        cloneSearchParams.set('startDate', formatDateISO(period.time(currentTime)));
        break;
    }
    cloneSearchParams.set('endDate', formatDateISO(currentTime));
    return location.pathname + '?' + cloneSearchParams.toString();
  };

  // set default period, startDate, endDate
  useEffect(() => {
    if (!periodParam || periodParam === 'all') {
      let periodDefault = settings.chart.defaultPeriod || '1Y';
      const availablePeriods = Object.keys(PERIOD_LINK).filter(period => enabledPeriods.includes(period));
      if(!availablePeriods.includes(periodDefault) && !!availablePeriods.length) {
        periodDefault = availablePeriods[0];
      }
      searchParams.set('period', PERIOD_LINK[periodDefault].value);
      switch (periodDefault) {
        case 'ALL':
          searchParams.set('startDate', formatWithDateTime(selectedInstrument?.startingDate || PERIOD_LINK[periodDefault].time(currentTime)));
          break;
        default:
          searchParams.set('startDate', formatWithDateTime(PERIOD_LINK[periodDefault].time(currentTime)));
          break;
      }
      searchParams.set('endDate', formatWithDateTime(currentTime));
      setSearchParams(searchParams, { replace: true });
    }
  }, [selectedInstrument?.startingDate]);

  return (
    <nav className="time-period">
      {enabledPeriods.map((period, index) => {
        if (!PERIOD_LINK[period]) return null;
        return (
          <React.Fragment key={index}>
            <Link
              role="button"
              aria-pressed="false"
              className={periodParam === PERIOD_LINK[period].value ? 'active' : ''}
              controls="pnlMain"
              to={buildPeriodLink(PERIOD_LINK[period])}
            >
              {PERIOD_LINK[period].label()}
            </Link>
            <span role="presentation" aria-hidden="true">
              &nbsp;|&nbsp;
            </span>
          </React.Fragment>
        );
      })}

      <Link
        role="button"
        aria-pressed="false"
        controls="pnlMain"
        onClick={() => {
          const el = document.getElementById('settingdetail');
          if (el) {
            el.scrollIntoView();
            const pos = el.getBoundingClientRect();
            window.xprops?.scrollTop(pos.top);
          }
        }}
        to={location.pathname + '?' + searchParams.toString() + '#settingdetail'}
      >
        {i18n.translate('skipToDetailedSettings')}
      </Link>
    </nav>
  );
};

export default SharePriceDetailsNav;
