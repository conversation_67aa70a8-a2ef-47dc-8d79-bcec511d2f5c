import { useLayoutEffect, useRef } from 'react';
import { useSearchParams, useLocation, matchPath, Outlet, Link } from 'react-router-dom';
import { PERIOD_TYPES } from '../common';

import useQuery from '../customHooks/useQuery';
import { pickBy, stringifyQuery } from '../helper';
import i18n from '../services/i18n';

export default function Accessibility() {
  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();
  // eslint-disable-next-line no-unused-vars
  const { typePage, dailyFrom, dailyTo, period, ...params } = useQuery();
  const { startDate, endDate, ...restParams} = params;
  const isHomePage = matchPath('/accessibility', pathname);
  const containerOutletRef = useRef();

  useLayoutEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  const scrollToTop = e => {
    e.preventDefault();
    window.xprops?.scrollTop(0);
    containerOutletRef.current?.scrollIntoView({ block: 'start', inline: 'start'});
  };
  return (
    <div ref={containerOutletRef} className="accessibility-app__inner">
      <div>
        {isHomePage && (
          <Link
            to={`/?${stringifyQuery(
              pickBy(restParams)
            )}`}
            className='return-to-main-page'
          >
            {i18n.translate('returnToMainPage')}
          </Link>
        )}
        <Outlet />
      </div>
      <nav className="footer">
        {!isHomePage && (
          <Link
            to={`/accessibility/?${stringifyQuery(
              pickBy({
                ...params,
                period: period !== PERIOD_TYPES.CUSTOM_RANGE ? period : null
              })
            )}`}
          >
            {i18n.translate('returnToMainPage')}
          </Link>
        )}
        {!isHomePage && <span aria-hidden='true'>&nbsp;|&nbsp;</span>}
        <a onClick={scrollToTop} href={`${pathname}?${searchParams}#`}>
          {i18n.translate('backToTop')}
        </a>
      </nav>
    </div>
  );
}
