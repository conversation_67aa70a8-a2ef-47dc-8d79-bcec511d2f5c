import { useRef, useContext, useLayoutEffect, useEffect, useState, useCallback } from 'react';
import { TinySliderNav, SlideItem } from '@euroland/react';

import { AppContext } from '../../AppContext';
import {
  dynamicSort
} from '../../helper';
import { TICKER_SWITCH_TYPE } from '../../common';
import { usePrevious } from '../../customHooks';
import TableTicker from './TableTicker';
import MultipleTickerItem from './MultipleTicker/MultipleTickerItem';
import useResize from '../../customHooks/useResize';
import { classNames } from '@euroland/libs';
import {useDelay} from '../../customHooks/useDelayUpdate';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import i18n from '../../services/i18n';


export const MultipleTicker = ({ data: _data, onTickerSelected = () => {}, tickerFormat, isPrint = false, selectedInstrumentId }) => {
  const settings = useContext(AppContext);
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER);

  const prevData = usePrevious(data);
  const [slidesPerView, setSlidesPerView] = useState(settings.ticker.slidesPerView || 4);
  const { width } = useResize();
  const tinySliderRef = useRef();

  const containerRef = useRef(null);

  useLayoutEffect(() => {
    const carouselElement = document.querySelector('.ticker--multiple');
    if (!carouselElement) return;

    const carouselStyles = getComputedStyle(carouselElement);
    const itemNumber = Number(carouselStyles.getPropertyValue('--slidesPerView'));
    setSlidesPerView(itemNumber);
  }, [tickerFormat, width]);

  const tickerAnimation =
    tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE
      ? settings.ticker.graphAnimation || 'fade'
      : settings.ticker.tableAnimation || 'fade';

  const [dataOrdered, setDataOrdered] = useState();

  let activeOrder = dataOrdered?.findIndex(instrument => instrument.instrumentId === selectedInstrumentId);
  activeOrder = activeOrder === undefined || activeOrder === -1 ? 0 : activeOrder;

  useLayoutEffect(() => {
    setDataOrdered(_sortDataTicker);
  }, [data, _sortDataTicker]);

  // order data by order setting in xml
  const _sortDataTicker = useCallback(() => {
    return data
      .map(x => {
        return {
          ...x,
          order: settings.instruments.find(y => y.id === x.instrumentId).order
        };
      })
      .sort(dynamicSort('order'));
  }, [data, settings.instruments]);

  return (
    <>
      {tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE && (
        <>
          <div
            ref={containerRef}
            className={`ticker__inner ticker__inner--${tickerAnimation.toLowerCase()}`}
          >
            {isPrint ? (
              <div className="ticker__inner--list">
                {(dataOrdered || []).map((item, index) => (
                  <MultipleTickerItem
                    prevData={prevData}
                    tickerFormat={tickerFormat}
                    isSelected={item.instrumentId === selectedInstrumentId}
                    onItemClick={onTickerSelected}
                    key={index}
                    data={item}
                  />
                ))}
              </div>
            ) : (
              <TinySliderNav
                ref={tinySliderRef}
                isRtl={window.appSettings.isRtl}
                settings={{ gutter: 0 }}
                activeOrder={activeOrder}
                role="radiogroup"
                nextSlideLabel={i18n.translate('nextSlideLabel')}
                previousSlideLabel={i18n.translate('previousSlideLabel')}
              >
                {(dataOrdered || []).map((item, index) => {
                  const active = item.instrumentId === selectedInstrumentId;
                  return (
                    <SlideItem
                      key={index}
                      className={classNames('tiny-slider__item', { active })}
                      onClick={() => onTickerSelected(item.instrumentId)}
                      sliderIndex={index}
                      role="radio"
                      aria-checked={active}
                    >
                      <MultipleTickerItem
                        prevData={prevData}
                        tickerFormat={tickerFormat}
                        isSelected={active}
                        // onItemClick={onTickerSelected}
                        key={index}
                        data={item}
                      />
                    </SlideItem>
                  );
                })}
              </TinySliderNav>
            )}
          </div>
        </>
      )}
      {tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE && (
        <>
          <TableTicker
            className="ticker-table ticker-table-v2"
            onItemClick={onTickerSelected}
            data={dataOrdered}
            tickerAnimation={tickerAnimation}
          />
        </>
      )}
    </>
  );
};
