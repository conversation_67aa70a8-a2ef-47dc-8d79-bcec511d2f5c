import React, { useRef } from 'react';
import { useSwipeable } from 'react-swipeable';
import {
  Wrapper,
  CarouselContainer,
  CarouselSlot,
  SlideButton,
  PREV,
  NEXT
} from './CarouselComponents';




const Carousel = props => {
  const carouselRef = useRef(null);
  const getOrder = ({ index, pos, numItems }) => {
    return index - pos < 0 ? numItems - Math.abs(index - pos) : index - pos;
  };

  const getSlidesPerView = () => {
    if(!carouselRef.current) return 2;
    return getComputedStyle(carouselRef.current).getPropertyValue('--slidesPerView');
  };

  const numItems = React.Children.count(props.children);
  const initialState = { pos: numItems - 1, sliding: false, dir: NEXT };
  const [state, dispatch] = React.useReducer(reducer, initialState);
  
  const slide = dir => {
    dispatch({ type: dir, numItems });
    setTimeout(() => {
      dispatch({ type: 'stopSliding' });
    }, 50);
  };
  const handlers = useSwipeable({
    onSwipedLeft: () => slide(NEXT),
    onSwipedRight: () => slide(PREV),
    preventDefaultTouchmoveEvent: true,
    trackMouse: true
  });
  return (
    <div className='carousel' {...handlers} ref={ carouselRef }>
      <Wrapper>
        <CarouselContainer dir={state.dir} sliding={state.sliding} slidesPerView={getSlidesPerView}>
          {React.Children.map(props.children, (child, index) => (
            <CarouselSlot
              slidesPerView={getSlidesPerView()}
              key={index}
              order={getOrder({ index: index, pos: state.pos, numItems })}
            >
              {child}
            </CarouselSlot>
          ))}
        </CarouselContainer>
      </Wrapper>
      <SlideButton onClick={() => slide(PREV)} float="left" className='carousel__btn--prev'>
        <i className="fs fs-caret-left"></i>
      </SlideButton>
      <SlideButton onClick={() => slide(NEXT)} float="right" className='carousel__btn--next'>
        <i className="fs fs-caret-right"></i>
      </SlideButton>
    </div>
  );
};

function reducer(state, { type, numItems }) {
  switch (type) {
    //case 'reset':
      //return initialState;
    case PREV:
      return {
        ...state,
        dir: PREV,
        sliding: true,
        pos: state.pos === 0 ? numItems - 1 : state.pos - 1
      };
    case NEXT:
      return {
        ...state,
        dir: NEXT,
        sliding: true,
        pos: state.pos === numItems - 1 ? 0 : state.pos + 1
      };
    case 'stopSliding':
      return { ...state, sliding: false };
    default:
      return state;
  }
}

export default Carousel;
