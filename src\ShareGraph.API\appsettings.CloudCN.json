{"ConnectionStrings": {"ToolsConfig": "DefaultEndpointsProtocol=https;AccountName=cntoolssettings;AccountKey=****************************************************************************************;EndpointSuffix=core.chinacloudapi.cn", "TranslationDbContext": "Server=tcp:zc7bgrnzx0.database.chinacloudapi.cn,1433;Database=shark;User=uShark;PWD=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;"}, "Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**************", "port": "5141", "appName": "FlipIT.ShareGraph.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.ShareGraph.API"}}, "UseAzureFileSettingStorage": true, "AzureFileShareName": "tools-settings", "GeneralSettingsPath": "config", "ToolSettingsPath": "tools\\sharegraph3\\config", "InternalSDataApiUrl": "http://localhost/tools/sdata-api/graphql"}