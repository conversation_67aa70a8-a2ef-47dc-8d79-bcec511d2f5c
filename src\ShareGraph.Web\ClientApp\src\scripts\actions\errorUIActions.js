export const CLEAR_ERROR_UI = 'CLEAR_ERROR_UI';
export const HAS_ERROR_UI = 'HAS_ERROR_UI';

export const hasErrorUIAct = data => ({ type: HAS_ERROR_UI, payload: data });
export const clearErrorUIAct = () => ({ type: CLEAR_ERROR_UI });

export function hasErrorUI(data) {
  return dispatch => {
    dispatch(hasErrorUIAct(data));
  };
}

export function clearErrorUI() {
  return dispatch => {
    dispatch(clearErrorUIAct());
  };
}
