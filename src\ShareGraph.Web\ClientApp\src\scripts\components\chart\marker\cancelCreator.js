/**
 * @typedef { ((fn: any, ...args: any[]) => { promise: Promise<any>; cancel: () => void; }) & {resolved: boolean, cancelled: boolean, tasks: Array<any>, markers: Array<any>, onCancel: () => void } } Cancellable
 */

export default function cancelCreator() {
  let cancellable = /**@type { Cancellable } */ (/** @type { unknown } */((fn, ...args) => {
    /** @type { () => void} */
    let cancel = () => {
      throw new Error('cancel used before assigned');
    };

    const promise = new Promise((resolve, reject) => {
        const gen = fn(...args);
        cancel = () => {
          if (cancellable.cancelled) return;
          cancellable.onCancel();
          cancellable.cancelled = true;

          if (cancellable.resolved) return;
          reject({ reason: 'cancelled' });
        };

        function onFullfilled(res) {
            if (cancellable.cancelled) return;
            cancellable.resolved = true;
            resolve(res);
        }

        function onRejected(err) {
          if (cancellable.cancelled) return;
          cancellable.cancelled = true;
          cancellable.onCancel();
          
          if (cancellable.resolved) return;
          reject(err);
        }
        gen.then(onFullfilled, onRejected);
    });

    return { promise, cancel };
  }));

  cancellable.tasks = [];
  cancellable.markers = [];
  cancellable.onCancel = function () {};
  return cancellable;
}