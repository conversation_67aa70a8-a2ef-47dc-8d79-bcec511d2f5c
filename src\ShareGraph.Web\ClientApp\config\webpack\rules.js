const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');

const modulesToTranspile = ['bootstrap'];


module.exports = [
  {
    test: /\.html$/i,
    include: [
      path.resolve(__dirname, '../../src/markup')
    ],
    loader: 'html-loader',
    options: {
      minimize: true
    }
  },
  {
    test: /\.js$/,
    exclude: [
      /node_modules/,
      /assets\/eucalendar/
    ],
    //exclude: /node_modules\/(?!(bootstrap)\/).*/,

    /*include: modulesToTranspile.map(moduleName =>
      path.resolve(__dirname, `../../../../node_modules/${moduleName}`)
    ),*/
    //exclude: [new RegExp(`node_modules\/(?!(${modulesToTranspile.join('|')}))`)],
    use: {
      loader: 'babel-loader',
      options: {
        compact: false
      }
    }
  },
  {
    test: /\.s[ac]ss$/i,
    use: [
      // fallback to style-loader in development
      {
          loader: MiniCssExtractPlugin.loader,
          options: {
            esModule: false
          }
      },
      // Translates CSS into CommonJS
      'css-loader',
      // Compiles Sass to CSS
      'sass-loader'
    ]
  },
  {
    test: /\.css$/i,
    use: [
      // fallback to style-loader in development
      {
          loader: MiniCssExtractPlugin.loader,
          options: {
            esModule: false
          }
      },
      // Translates CSS into CommonJS
      'css-loader'
    ]
  },
  {
    test: /\.(ttf|eot|woff|woff2|svg)$/,
    type: 'asset/resource'
  },
  {
    test: /\.(png|jpe?g|gif)$/i,
    use: [
      {
        loader: 'file-loader'
      }
    ]
  }
];
