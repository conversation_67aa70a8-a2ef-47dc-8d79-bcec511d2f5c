import { useEffect, useMemo, useRef } from 'react';
import useAppSelector from './useAppSelector';
import { shallowCompare } from '../helper';


export const useSelectedCurrency = () => {
  const selectedCurrency = useAppSelector(state => state.currency.currency);
  return selectedCurrency;
};

/**
 * @typedef {import('../reducers/currencyReducers').CurrencyOption} CurrencyOption
 * @param {(selectedCurrency: CurrencyOption) => void} callback 
 */
export const useChangeQuoteCurrency = (callback = () => {}) => {
  const selectedCurrency = useSelectedCurrency();
  const isMounted = useRef();

  useEffect(() => {
    if(!isMounted.current) {
      isMounted.current = true;
      return;
    }
    callback(selectedCurrency);
  }, [selectedCurrency]);
};

/**
 * @typedef {import('../reducers/currencyReducers').CurrencyRate} CurrencyRate
 * @param {(currencyRate: CurrencyRate) => void} callback 
 */
export const useTriggerUpdateRateCurrency = (callback = () => {}) => {
  const rateCurrencyState = useAppSelector(state => state.currency.currencyRate);

  const isMounted = useRef();
  const prevRateCurrencyRef = useRef({});

  const rateCurrency = useMemo(() => {
    return Object.keys(rateCurrencyState).reduce((s, code) => {
      s[code] = rateCurrencyState[code].value;
      return s;
    }, {});
  }, [rateCurrencyState]);

  useEffect(() => {
    if(!isMounted.current) {
      isMounted.current = true;
      return;
    }

    if(Object.keys(prevRateCurrencyRef.current).length && !shallowCompare(prevRateCurrencyRef.current, rateCurrency)) {
      callback(rateCurrency);
    }

    prevRateCurrencyRef.current = rateCurrency;
  }, [rateCurrency]);
};