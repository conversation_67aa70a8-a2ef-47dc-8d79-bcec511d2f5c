import {useEffect, useRef} from 'react';
import {useStore} from 'react-redux';
import { shallowCompare } from '../helper';

/**
* @template T
* @param {*} selector 
* @returns 
*/
export function hookReduxCreator(selector) {

 /**
   * The callback will run when one of the list ids has new data on the stream
   * @param {(data: T) => void} observer 
   * @param {Array<number | string>} ids 
  */
 return function (observer, ids) {
   const store = useStore();
   const forwardTo = useRef(({ids, observer}));
   forwardTo.current = {
     ids, observer
   };
   useEffect(() => {
     const isNew =(() => {
       let oldData = {};
       return function (id, data) {
         if(oldData[id] === data) {
           return false;
         } else {
           oldData[id] = data;
           return true;
         }
       };
     })();
     return store.subscribe(function() {
       const state = selector(store.getState());
       if(!isNew('state.selector', state)) return;
       forwardTo.current.ids.forEach(id => {
         const data = state[id];
         if(!data) return;
         if(isNew(id, data)) {
           forwardTo.current.observer(data);
         }
       });
       
     });
   }, [store]);
 };
}


/**
 * @typedef { import('../reducers').RootState } RootState
 * @typedef { import('redux').Store<RootState> } Store
 */

window.useWatchStoreDisabled = false;

/**
 * @example
 * const useWatchRootStore = watchStoreHookCreator(state => state)
 * const useWatchPathStore = watchStoreHookCreator(state => state.path)
 * 
 * @template T
 * @param {(s: RootState) => T} selector
 * @returns 
 */
export function watchStoreHookCreator (selector) {
   /**
  * @param {(s: T) => P} produce 
  * @param {(d: P) => void} callback 
  */
   return function (produce, callback) {
    /**@type { Store } */
    const store = useStore();
    
    const forwardRef = useRef({produce, callback});
    forwardRef.current = { produce, callback };
  
    useEffect(() => {
      /** @type { P | undefined } */
      let prevSelectorVal;

      /** @type { P | undefined } */
      let prevProduceVal;
      function handle () {
        if(window.useWatchStoreDisabled) return;
        const state = store.getState();
        const selectorVal = selector(state);

        if(state !== selectorVal) {
          if(shallowCompare(prevSelectorVal, selectorVal)) return;
          prevSelectorVal = selectorVal;
        }

        const data = forwardRef.current.produce(selectorVal);
        if(shallowCompare(prevProduceVal, data)) return;
        prevProduceVal = data;
        forwardRef.current.callback(data);
      }
  
      return store.subscribe(handle);
    }, [store]);
 };
}