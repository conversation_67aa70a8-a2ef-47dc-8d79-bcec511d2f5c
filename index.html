<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EurolandAppContext Widget Test - Auth & Watchlist</title>
   
</head>
<body>
    <h1>EurolandAppContext Widget Test - Auth & Watchlist</h1>
    
    <div class="container">
        <h2>Setup</h2>
        <p>Testing EurolandAppContext widget with Auth and Watchlist functionality. Open the browser console to see detailed logs.</p>
        <div id="setup-status" class="status">Loading EurolandAppContext...</div>
    </div>

    <!-- Auth Widget Simulation -->
    <div class="widget">
        <h3>🔐 Auth Widget</h3>
        <p>Provides authentication and access token management.</p>
        <button onclick="authWidget.login()">Login</button>
        <button onclick="authWidget.logout()">Logout</button>
        <button onclick="authWidget.refreshToken()">Refresh Token</button>
        <button onclick="authWidget.checkStatus()">Check Auth Status</button>
        <div class="output" id="auth-output">Auth widget ready...</div>
    </div>

    <!-- Watchlist Widget Simulation -->
    <div class="widget">
        <h3>📊 Watchlist Widget</h3>
        <p>Manages instruments and requires authentication for all operations.</p>
        <input type="text" id="instrument-input" placeholder="Enter instrument (e.g., AAPL)" value="AAPL">
        <button onclick="watchlistWidget.addInstrument()">Add Instrument (needs token)</button>
        <button onclick="watchlistWidget.getWatchlist()">Get Watchlist (needs token)</button>
        <button onclick="watchlistWidget.fetchData()">Fetch Data (needs token)</button>
        <button onclick="watchlistWidget.removeInstrument()">Remove Instrument (needs token)</button>
        <div class="output" id="watchlist-output">Watchlist widget ready...</div>
    </div>

    <!-- Load the EurolandAppContext widget -->
    <script type="module" src="./main.ts"></script>
    
    <script>
        // Wait for EurolandAppContext to be available
        function waitForEurolandAppContext() {
            if (window.EurolandAppContext) {
                initializeTest();
            } else {
                setTimeout(waitForEurolandAppContext, 100);
            }
        }

        function updateStatus(message, isError = false) {
            const status = document.getElementById('setup-status');
            status.textContent = message;
            status.className = `status ${isError ? 'error' : 'success'}`;
        }

        function log(widgetId, message) {
            const output = document.getElementById(widgetId);
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        // Helper function to validate token
        function validateToken() {
            const token = window.EurolandAppContext.command('access-token');
            if (!token) {
                return { valid: false, error: 'No access token available - please login first' };
            }
            return { valid: true, token };
        }

        // Auth Widget Simulation
        const authWidget = {
            currentUser: null,
            
            init() {
                // Register auth-related commands
                window.EurolandAppContext.registerCommandHandler('access-token', () => {
                    if (this.currentUser) {
                        return `token_${this.currentUser}_${Date.now()}`;
                    }
                    return null;
                });

                window.EurolandAppContext.registerCommandHandler('current-user', () => {
                    return this.currentUser;
                });

                window.EurolandAppContext.registerCommandHandler('is-authenticated', () => {
                    return !!this.currentUser;
                });

                log('auth-output', 'Auth commands registered');
            },

            login() {
                this.currentUser = 'john_doe';
                window.EurolandAppContext.setState('user', { name: 'John Doe', role: 'admin' });
                window.EurolandAppContext.setState('isAuthenticated', true);
                log('auth-output', `✅ User logged in: ${this.currentUser}`);
            },

            logout() {
                this.currentUser = null;
                window.EurolandAppContext.removeState('user');
                window.EurolandAppContext.setState('isAuthenticated', false);
                log('auth-output', '❌ User logged out');
            },

            refreshToken() {
                if (this.currentUser) {
                    const newToken = window.EurolandAppContext.command('access-token');
                    log('auth-output', `🔄 Token refreshed: ${newToken}`);
                } else {
                    log('auth-output', '⚠️ Cannot refresh token - not logged in');
                }
            },

            checkStatus() {
                const isAuth = window.EurolandAppContext.command('is-authenticated');
                const user = window.EurolandAppContext.command('current-user');
                log('auth-output', `Status: ${isAuth ? '✅ Authenticated' : '❌ Not authenticated'} | User: ${user || 'None'}`);
            }
        };

        // Watchlist Widget Simulation
        const watchlistWidget = {
            instruments: ['MSFT', 'GOOGL'],

            init() {
                // Register watchlist commands - all require tokens
                window.EurolandAppContext.registerCommandHandler('add-instrument', (payload) => {
                    const { symbol, token } = payload;
                    
                    // Validate token
                    if (!token) {
                        return { success: false, error: 'Authentication required - no token provided' };
                    }
                    
                    if (!this.instruments.includes(symbol)) {
                        this.instruments.push(symbol);
                        window.EurolandAppContext.setState('watchlist', [...this.instruments]);
                        return { success: true, symbol, message: `Added ${symbol} with token authentication` };
                    }
                    return { success: false, error: 'Symbol already exists in watchlist' };
                });

                window.EurolandAppContext.registerCommandHandler('get-watchlist', (payload) => {
                    const { token } = payload || {};
                    
                    // Validate token
                    if (!token) {
                        return { success: false, error: 'Authentication required - no token provided', data: [] };
                    }
                    
                    return { success: true, data: [...this.instruments], message: 'Watchlist retrieved with token authentication' };
                });

                window.EurolandAppContext.registerCommandHandler('remove-instrument', (payload) => {
                    const { symbol, token } = payload;
                    
                    // Validate token
                    if (!token) {
                        return { success: false, error: 'Authentication required - no token provided' };
                    }
                    
                    const index = this.instruments.indexOf(symbol);
                    if (index > -1) {
                        this.instruments.splice(index, 1);
                        window.EurolandAppContext.setState('watchlist', [...this.instruments]);
                        return { success: true, symbol, message: `Removed ${symbol} with token authentication` };
                    }
                    return { success: false, error: 'Symbol not found in watchlist' };
                });

                // Listen for authentication changes
                window.EurolandAppContext.on('state-changed', ({ key, value }) => {
                    if (key === 'isAuthenticated') {
                        log('watchlist-output', `🔐 Authentication status changed: ${value ? '✅ Authenticated' : '❌ Not authenticated'}`);
                    }
                    if (key === 'watchlist') {
                        log('watchlist-output', `📊 Watchlist updated: ${JSON.stringify(value)}`);
                    }
                });

                log('watchlist-output', 'Watchlist commands registered (all require authentication)');
                window.EurolandAppContext.setState('watchlist', [...this.instruments]);
            },

            addInstrument() {
                const input = document.getElementById('instrument-input');
                const symbol = input.value.trim().toUpperCase();
                if (!symbol) {
                    log('watchlist-output', '⚠️ Please enter a symbol');
                    return;
                }

                // Validate token first
                const tokenValidation = validateToken();
                if (!tokenValidation.valid) {
                    log('watchlist-output', `❌ Add ${symbol}: ${tokenValidation.error}`);
                    return;
                }

                const result = window.EurolandAppContext.command('add-instrument', { 
                    symbol, 
                    token: tokenValidation.token 
                });
                
                if (result.success) {
                    log('watchlist-output', `✅ ${result.message}`);
                    input.value = '';
                } else {
                    log('watchlist-output', `❌ Add ${symbol}: ${result.error}`);
                }
            },

            getWatchlist() {
                // Validate token first
                const tokenValidation = validateToken();
                if (!tokenValidation.valid) {
                    log('watchlist-output', `❌ Get Watchlist: ${tokenValidation.error}`);
                    return;
                }

                const result = window.EurolandAppContext.command('get-watchlist', { 
                    token: tokenValidation.token 
                });
                
                if (result.success) {
                    log('watchlist-output', `📋 ${result.message}`);
                    log('watchlist-output', `📋 Current watchlist: ${JSON.stringify(result.data)}`);
                } else {
                    log('watchlist-output', `❌ Get Watchlist: ${result.error}`);
                }
            },

            removeInstrument() {
                const input = document.getElementById('instrument-input');
                const symbol = input.value.trim().toUpperCase();
                if (!symbol) {
                    log('watchlist-output', '⚠️ Please enter a symbol');
                    return;
                }

                // Validate token first
                const tokenValidation = validateToken();
                if (!tokenValidation.valid) {
                    log('watchlist-output', `❌ Remove ${symbol}: ${tokenValidation.error}`);
                    return;
                }

                const result = window.EurolandAppContext.command('remove-instrument', { 
                    symbol, 
                    token: tokenValidation.token 
                });
                
                if (result.success) {
                    log('watchlist-output', `✅ ${result.message}`);
                    input.value = '';
                } else {
                    log('watchlist-output', `❌ Remove ${symbol}: ${result.error}`);
                }
            },

            fetchData() {
                // Validate token first
                const tokenValidation = validateToken();
                if (!tokenValidation.valid) {
                    log('watchlist-output', `❌ Fetch Data: ${tokenValidation.error}`);
                    return;
                }

                log('watchlist-output', `🔍 Fetching data with token: ${tokenValidation.token.substring(0, 20)}...`);
                // Simulate API call
                setTimeout(() => {
                    log('watchlist-output', '✅ Data fetched successfully with token authentication!');
                }, 1000);
            }
        };

    
        function initializeTest() {
            updateStatus('EurolandAppContext loaded successfully!');
            
            // Initialize widgets
            authWidget.init();
            watchlistWidget.init();

            // Test command execution
            window.EurolandAppContext.on('command-executed', ({ command, result }) => {
                console.log(`Command executed: ${command}`, result);
            });

            // Test state changes
            window.EurolandAppContext.on('state-changed', ({ key, value, oldValue }) => {
                console.log(`State changed: ${key}`, { oldValue, newValue: value });
            });

            console.log('EurolandAppContext test environment ready!');
            console.log('Available global objects: authWidget, watchlistWidget, debugPanel');
        }

        // Start the test when page loads
        waitForEurolandAppContext();
    </script>
</body>
</html> 