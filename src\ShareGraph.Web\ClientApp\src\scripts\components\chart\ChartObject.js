import React, { useEffect, useState, useRef, useCallback, useContext } from 'react';
import { ChartTemplate, CIQ, getConfig, getCustomConfig, timezoneJS } from './chartiq-import';
import { AppContext } from '../../AppContext';
import { convertNumberDecimal, convertPercentDecimal } from '../../helper';
import {useStore} from 'react-redux';
import ChartHeader  from './components/ChartHeader';
import i18n from '../../services/i18n';
import CreateAlertModal from '../../pages/promode/create-alert/CreateAlertModal';

export { CIQ, getConfig, getCustomConfig, timezoneJS };

function ChartObject({
  isPopout,
  children,
  chartInitialized,
  onSeriesRemoved,
  config,
  additionalOptionsTypes = [],
  onPopUp
}) {
  const containerRef = useRef(null);
  const appContext = useContext(AppContext);
  const [chart] = useState(new CIQ.UI.Chart());
  const [stx, setChartEngine] = useState(null);
  const [UIContext, setUIContext] = useState(null);
  const store = useStore();
  const [isCreateAlertModalOpen, setIsCreateAlertModalOpen] = useState(false);

  function cleanup() {
    console.log('Cleanup ChartObject');
    if(!stx)
      return;
    stx.destroy();
    stx.draw = () => { };
  }

  function createChartAndUI({ container, config }) {
		const uiContext = chart.createChartAndUI({ container, config: {...config, initialData: [], restore: false} });
		return uiContext;
  }

  const removeSeries = useCallback(onSeriesRemoved);

  useEffect(() => {
    const container = containerRef.current;
    const uiContext = createChartAndUI({ container, config });
    const chartEngine = uiContext.stx;
    // chartEngine.setMarketFactory(marketFactory.bind(this, store));
    setTimeout(() => {
      appContext.chartEngine = chartEngine;
      setChartEngine(chartEngine);
      setUIContext(uiContext);

      // Use `symbolChange` callback to track changes of series.
      // A symbol is considered as changed when loadChart(), addSeries(), addStudy(), removeSeries(), removeStudy() is called.
      // Ref: https://documentation.chartiq.com/CIQ.ChartEngine.html#callbacks%5B%60symbolChange%60%5D
      chartEngine.addEventListener('symbolChange', ({ symbol, symbolObject, action }) => {
        switch (action) {
          case 'remove-series': {
            removeSeries({ symbol, symbolObject });
            break;
          }
          // case 'add-series': {
          // }
          // case 'master': {
          // }
        }
      });

      if (chartInitialized) {
        chartInitialized({ chartEngine, uiContext });
      }
    }, 0);

    chartEngine.append('initializeChart', function () {
      const { internationalizer, chart } = chartEngine;

      // overwrite format price of chart
      const priceFormatters = internationalizer.priceFormatters;
      for(let i = 0; i < priceFormatters.length; i++) {
        priceFormatters[i] = {
          format: function (price) {
            return convertNumberDecimal(price);
          }
        };
      }

      // overwrite format percent of chart
      for ( let i = 0; i < 5; i ++) {
        const percentKey = 'percent' + (i === 0 ? '' : i);

        internationalizer[percentKey] = {
          format: function (price) {
            return convertPercentDecimal(price * 100) + '%';
          }
        };
      }

      const sphareCanvas = chart.sphareCanvas = document.createElement('canvas');
      sphareCanvas.setAttribute('data-sphare-canvas', 'true');
      sphareCanvas.style.position = 'absolute';
      sphareCanvas.style.display = 'none';
      sphareCanvas.style.left = '0px';
      sphareCanvas.context = sphareCanvas.getContext('2d');
      chart.container.appendChild(sphareCanvas);
      resizeSphareCanvas.call(this);
    });

    chartEngine.append('resizeChart', resizeSphareCanvas);
    function resizeSphareCanvas() {
      const { chart } = this;

      if (!chart.sphareCanvas) {
        return;
      }
      chart.sphareCanvas.style.width = chart.canvasWidth + 'px';
      chart.sphareCanvas.style.height = chart.canvasHeight + 'px';
      chart.sphareCanvas.width = chart.canvasWidth;
      chart.sphareCanvas.height = chart.canvasHeight;
      chartEngine.adjustBackingStore(chart.sphareCanvas, chart.sphareCanvas.context);
    }
    return cleanup;
  }, []);

  return (
    <>
      <ChartHeader />
      <h3 style={{display: 'none'}} aria-hidden='true'>{i18n.translate('chart')}</h3>
      <div className="chart-context">
        <cq-context ref={containerRef} cq-sticky-dialog>
          {children || (
            <ChartTemplate
              isPopout={isPopout}
              onPopUp={onPopUp}
              additionalOptionsTypes={additionalOptionsTypes}
              onOpenCreateAlertModal={() => setIsCreateAlertModalOpen(true)}
            />
          )}
        </cq-context>
      </div>
      <CreateAlertModal
        isOpen={isCreateAlertModalOpen}
        onClose={() => setIsCreateAlertModalOpen(false)}
      />
    </>
  );
}

export default React.memo(ChartObject);

