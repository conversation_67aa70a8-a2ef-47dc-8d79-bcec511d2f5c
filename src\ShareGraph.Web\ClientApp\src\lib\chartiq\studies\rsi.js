import { CIQ } from 'chartiq/js/components';
import appConfig from '../../../scripts/services/app-config';

let setting;

CIQ.Euroland.Studies.calculateRSI = function (stx, sd) {
  // rsi formula: https://www.investopedia.com/terms/r/rsi.asp

  setting = appConfig.get();

  function toFixed(a, n) {
    return parseFloat(a.toFixed(n));
  }

  var quotes = sd.chart.scrubbed,
    period = sd.days,
    dataLength = quotes.length,
    name = 'RSI ' + sd.name,
    // RSI starts calculations from the second point
    // Cause we need to calculate change between two points
    range = sd.startFrom + 1,
    decimals = 4,
    change,
    gain = 0,
    loss = 0,
    avgLoss,
    i,
    field = sd.inputs.Field,
    avgGain,
    rsiPoint;

  if (!field || field === 'field') {
    field = 'Close';
  }

  if (dataLength < period + 1) {
    if (!sd.overlay) sd.error = true;
    return;
  }

  // Calculate changes for first N points
  while (range < period) {
    if (!range) continue;

    if (!quotes[range][field] && quotes[range][field] !== 0) {
      continue;
    }

    change = toFixed(quotes[range][field] - quotes[range - 1][field], decimals);

    if (change > 0) {
      gain += change;
    } else {
      loss += Math.abs(change);
    }

    range++;
  }

  // Average for first n-1 points:
  avgGain = toFixed(gain / (period - 1), decimals);
  avgLoss = toFixed(loss / (period - 1), decimals);

  for (i = range; i < dataLength; i++) {
    change = toFixed(quotes[i][field] - quotes[i - 1][field], decimals);

    if (change > 0) {
      gain = change;
      loss = 0;
    } else {
      gain = 0;
      loss = Math.abs(change);
    }

    // Calculate smoothed averages, RS, RSI values:
    avgGain = toFixed((avgGain * (period - 1) + gain) / period, decimals);
    avgLoss = toFixed((avgLoss * (period - 1) + loss) / period, decimals);

    // If average-loss is equal zero, then by definition RSI is set
    // to 100:
    if (avgLoss === 0) {
      rsiPoint = 100;
      // If average-gain is equal zero, then by definition RSI is set
      // to 0:
    } else if (avgGain === 0) {
      rsiPoint = 0;
    } else {
      rsiPoint = toFixed(100 - 100 / (1 + avgGain / avgLoss), decimals);
    }

    quotes[i][name] = rsiPoint;
  }

  sd.zoneOutput = 'RSI';
};


const studyName = CIQ.I18N.translate('RSI');


CIQ.Euroland.Studies.createStudy(studyName, {
  name: studyName,
  calculateFN: CIQ.Euroland.Studies.calculateRSI,
  inputs: { Period: 14},
  range: '0 to 100',
  outputs: {
    RSI: setting ? setting.studies.rsi.rsi : '#000000'
  },
  parameters: {}
  // parameters: {
  //   init: {
  //     studyOverZonesEnabled: true,
  //     studyOverBoughtValue: 80,
  //     studyOverBoughtColor: 'auto',
  //     studyOverSoldValue: 20,
  //     studyOverSoldColor: 'auto'
  //   }
  // }
});

export { CIQ };
