﻿<div class="ticker__item-inner ticker__item-inner--multiple-tickers-2 ticker__item-inner--{instrumentId}">
  <div class="ticker__row ticker__row--header">
    <div class="ticker__row-item market-namesymble">
      <div class="ticker__row">
      <div class="ticker__row-item">
        <span
          class="
            ticker__market-abbreviation ticker__market-abbreviation--bg-color
          "
          >{marketAbbreviation}</span
        >
      </div>
      <div class="ticker__row-item">
        <span class="ticker__name">{tickerName}</span>
        <div class=" ticker__row-item--price">
          <h2 class="ticker__price">
            {last} <span class="ticker__currency-code">{currencyCodeStr}</span>
          </h2>
        </div>
      </div>
    </div>
    </div>
    <div class="ticker__row-item">
      <span class="ticker__change ticker__change-value">
        <span>
          <i class="fs fs-triangle-up"></i>
          <i class="fs fs-triangle-down"></i>
        </span>
        <span>{change}</span>
        <span class="ticker__change-percentage">({changePercentage}%)</span>
      </span>
    </div>
  </div>
  <div class="ticker__ranges">
    <div class="ticker__range ticker__range--day">
      <span class="ticker__range-label">{dayRangeLabel}</span>
      <p class="ticker__range-value">L <span class="ticker__range--low-value">{low}</span> - <span class="ticker__range--high-value">{high}</span> H</p>
    </div>
    <div class="ticker__range ticker__range--52w">
      <span class="ticker__range-label">{w52RangeLabel}</span>
      <p class="ticker__range-value">L {low52W} - {high52W} H</p>
    </div>
    <div class="ticker__range ticker__range--volume">
      <span class="ticker__range-label"><i class='fs fs-chart'></i>{volumeLabel}</span>
      <p class="ticker__range-value">
        <span class="ticker__range-volume-value">{volume}</span>
        <span class="tooltip-wrapper">
          <span class="tooltip right">
            <span class="tooltip__content">{relativeVolumelabel}</span>
          </span>
          <i class="fs fs-arrow-increase"></i>
        </span>
        <span class="ticker__range-volumn-change">{volumeChange} X </span>
      </p>
    </div>
    <!-- <div class="ticker__market-open">
      <p class="status-closed">
        <i class="fs fs-close-radio"></i>
        <span class="market-name">{marketName}</span>
        <span class="market-status">{marketCloseLabel}. </span>
      </p>
      <p class="status-opened">
        <i class="fs fs-checked-radio"></i>
        <span class="market-name">{marketName} {marketOpenedLabel}.</span>
      </p>
    </div> -->
  </div>
</div>
