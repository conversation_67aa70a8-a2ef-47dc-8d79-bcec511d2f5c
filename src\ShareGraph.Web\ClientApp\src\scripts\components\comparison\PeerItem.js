import { useRef, useContext, useLayoutEffect, useMemo, useCallback } from 'react';

import { debounceLast } from '../../utils';
import i18n from '../../services/i18n';
import { AppContext } from '../../AppContext';
import {
  replaceKey,
  formatDateTime,
  convertNumber,
  convertNumberDecimal,
  getCustomPhrasePeer,
  convertPercentDecimal,
  i18nTranslate,
  marketStatusByValue,
  classNames,
  clickWithoutMove,
  translateStringFormat,
  formatChangeNumber,
  convertChangePercentDecimal
} from '../../helper';
import { usePrevious } from '../../customHooks';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import {useDelay} from '../../customHooks/useDelayUpdate';

export const PeerItem = ({ data: _data, isSelected, onItemClick = () => {} }) => {
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER);
  const settings = useContext(AppContext);
  const containerRef = useRef(null);
  const { change, marketStatus } = data || {};
  const prevData = usePrevious(data);

  const lastValue = useMemo(() => {
    if (!prevData) return;
    return data.last - prevData.last;
  }, [data]);

  useLayoutEffect(() => {
    _changeStyle();
  });

  function _changeStyle() {
    _setPeerSelectedBorderColor();
  }

  function _setPeerSelectedBorderColor() {
    if (isSelected) {
      const currentIns = settings.peers.peers.find(x => x.id === data.instrumentId);
      const color = currentIns.color || '#E3E3E3';
      containerRef.current.querySelector('.custom-checkbox').style.color = color;
      containerRef.current.style.borderColor = color;
    } else {
      containerRef.current.style.borderColor = '#E3E3E3';
    }
  }

  function _getTemplateHtml() {
    return `<div class="peer__heading comparison__heading">
                  <div class="peer__name-icon comparison__name-icon">
                    <i class="fs fs-checked-checkbox custom-checkbox"></i>
                    <span class="fs-checkbox unchecked-checkbox"><span class="path1"></span><span class="path2"></span></span>
                    <span class="peer__name comparison__name">{tickerName}</span>                    
                  </div>
                  <div class="peer__last-price comparison__last-price-wrapper">
                    <span class="peer__last-price comparison__last-price">{last}</span>
                    <span class="peer__last-price comparison__currency">{currencyCodeStr}</span>
                  </div>
                </div>
                <p class="peer__change comparison__change">       
                    <i class="fs fs-triangle-up"></i>
                    <i class="fs fs-triangle-down"></i>   
                    <span class="peer__change-value comparison__change-value">{change} ({changePercentage}%)</span>
                </p>`;
  }

  function _renderTemplate(item) {
    if (item) {
      const tableRow = _getTemplateHtml();
      const labels = _getLabel();
      const datapeer = _normalizeData(item);
      const dataTemplate = { ...datapeer, ...labels };
      const content = replaceKey(tableRow, dataTemplate);
      return content;
    }

    return '<h2> No data</h2>';
  }

  function _normalizeData(item) {
    const data = { ...item };
    data.bid = convertNumberDecimal(item.bid);
    data.ask = convertNumberDecimal(item.ask);
    data.change = formatChangeNumber(item.change);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = convertNumberDecimal(Math.max(item.high, item.last));
    data.high52W = convertNumberDecimal(Math.max(item.high52W, item.last));
    data.last = convertNumberDecimal(item.last);
    data.low = convertNumberDecimal(Math.min(item.low, item.last));
    data.low52W = convertNumberDecimal(Math.min(item.low52W, item.last));
    data.open = convertNumberDecimal(item.open);
    data.percent52W = convertPercentDecimal(item.percent52W);
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatChangeNumber(item.volumeChange);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';
    return getCustomPhrasePeer(data);
  }

  function _getLabel() {
    const singleLabels = [
      'w52RangeLabel',
      'volumeLabel',
      'bidAskLabel',
      'marketCloseLabel',
      'marketOpenedLabel',
      'openLabel',
      'highLabel',
      'lowLabel',
      'marketOpenInLabel',
      'sharesLabel',
      'lastLabel',
      'changePercentageLabel',
      'changeLabel'
    ];

    return {
      dayRangeLabel: i18n.translate('rangeLabel'),
      ...i18nTranslate(singleLabels)
    };
  }

  const onClick = useCallback(debounceLast(function (event) {
    // Keypresses other than Enter and Space
    if (event.nativeEvent instanceof KeyboardEvent && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }
    event.preventDefault();
    onItemClick(data.instrumentId);
  }, 200), [data.instrumentId]);

  return (
    <div
      {...clickWithoutMove(onClick)}
      onKeyDown={onClick}
      tabIndex="0"
      role="button"
      aria-pressed={isSelected}
      className={classNames(
        {
          'indicator-up': change > 0,
          'indicator-down': change < 0,
          'indicator-neutral': change === 0,
          selected: isSelected,
          'animate--increase': lastValue > 0,
          'animate--decrease': lastValue < 0,
          'animate--neutral': lastValue === 0
        },
        'peer__item comparison__item',
        marketStatusByValue(marketStatus)
      )}
      ref={containerRef}
      // style={{borderColor:borderColor}}
      dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}
    ></div>
  );
};
