import { convertChangePercentDecimal, convertNumberDecimal, CustomLocalStorage, formatChangeNumber } from '../../../helper';
import { CIQ } from '../chartiq-import';
import appConfig from '../../../services/app-config';

// export function onSelectedRangeToLocalStorange(selectedRange, stx) {
//   const { customStorageKey } = stx;
//   var currentStorage = CustomLocalStorage.getItem(customStorageKey) || '{}';
//   var currentStorageObj = { ...JSON.parse(currentStorage), selectedRange: selectedRange };
//   CustomLocalStorage.setItem(customStorageKey, JSON.stringify(currentStorageObj));
// }

export function setRangeActive(stx, selectedRange) {
  var rangeItems = stx.chart.container.closest('cq-context').querySelectorAll('.range-item');
  for (var i = 0; i < rangeItems.length; i++) {
    let currRange = rangeItems[i];
    if (currRange.getAttribute('data-key').toLowerCase() === selectedRange.toLowerCase()) {
      currRange.classList.add('active');
    } else {
      currRange.classList.remove('active');
    }
  }
}

export function drawHighLowLine() {
  if (!this || !this.layout || !this.chart || !this.layout.showHighLowLine) {
    return;
  }

  var parameters = {
    pattern: 'dashed', // options: "solid","dashed","dotted"
    lineWidth: 1 // select any width for the line in pixels
  };

  var chart = this.chart,
    dataSegment = chart.dataSegment,
    ctx = chart.context;

  if (!dataSegment.length) return;

  var arrCloses = dataSegment.filter(d => d && d.Close).map(d => d.Close);
  var high = Math.max(...arrCloses),
    low = Math.min(...arrCloses),
    yHigh = this.pixelFromPrice(high),
    yLow = this.pixelFromPrice(low);

  const { highPriceIndicatorColor, lowPriceIndicatorColor } = appConfig.get().chart;
  this.plotLine(0, 1, yHigh, yHigh, highPriceIndicatorColor, 'horizontal', ctx, true, parameters);
  this.plotLine(0, 1, yLow, yLow, lowPriceIndicatorColor, 'horizontal', ctx, true, parameters);
  //Show high/low label
  const highValueText = CIQ.I18N.translate('High') + ': ' + convertNumberDecimal(high);
  showLabel(this, 'highLabel', 'high-value-line', yHigh, 25, highValueText, highPriceIndicatorColor, 'H');
  const lowValueText = CIQ.I18N.translate('Low') + ': ' + convertNumberDecimal(low);
  showLabel(this, 'lowLabel', 'low-value-line', yLow, 25, lowValueText, lowPriceIndicatorColor, 'L');
  handleOverlapHighValueLineWithLowLine(chart);
}

export const handleOverlapHighValueLineWithLowLine = (chartEngine) => {
  if (!chartEngine) return;
  const highValueLineDOM = chartEngine.container.querySelector('.high-value-line');
  const lowValueLineDOM = chartEngine.container.querySelector('.low-value-line');
  if (!highValueLineDOM || !lowValueLineDOM) return;
  const baseOffsetRight = 20;
  const baseRight = 25;
  const highValueLineRect = highValueLineDOM.getBoundingClientRect();
  const lowValueLineRect = lowValueLineDOM.getBoundingClientRect();
  const deltaTop = Math.abs(highValueLineRect.top - lowValueLineRect.top);
  const maxHeight = Math.max(highValueLineRect.height, lowValueLineRect.height);
  setTimeout(() => {
    const highValueLineDOMStyles = getComputedStyle(highValueLineDOM);
    const highValueLineRight = parseInt(highValueLineDOMStyles.right);
    if (deltaTop < maxHeight) {
      lowValueLineDOM.style.right = highValueLineRight + lowValueLineRect.width + baseOffsetRight  + 'px';
      return;
    }
    lowValueLineDOM.style.right = baseRight + lowValueLineRect.width  + 'px';
  }, 100);
};

export function showLabel(stx, pathNode, className, top, right, text, color, label) {
  const addPxForLow = top > 30 ? -30 : 15;
  const topCal = top + addPxForLow + 'px';
  const rightCal = right + 'px';
  if (!stx[pathNode]) {
    let newNode = document.createElement('div');
    newNode.className = className;
    newNode.innerHTML = text;
    newNode.style.position = 'absolute';
    newNode.style.top = topCal;
    newNode.style.right = rightCal;
    newNode.style.color = color;
    stx[pathNode] = new CIQ.Marker({
      stx: stx,
      label: label,
      xPositioner: 'none',
      yPositioner: 'none',
      node: newNode
    });
  } else {
    stx[pathNode].node.innerText = text;
    stx[pathNode].node.style.top = topCal;
  }
}

export const CLOSE_STATUS_CHART = {
  UP: 'UP',
  DOWN: 'DOWN',
  UNCHANGED: 'UNCHANGED'
};

const getCloseStatusChart = (currentPrice, previousClose) => {
  if (currentPrice > previousClose) {
    return CLOSE_STATUS_CHART.UP;
  }
  if (currentPrice < previousClose) {
    return CLOSE_STATUS_CHART.DOWN;
  }
  return CLOSE_STATUS_CHART.UNCHANGED;
};

/**
 * @typedef {import('../../../../../types/chart-iq').TCloseStatus} TCloseStatus
 * @typedef {import('react').Ref} Ref
 *
 * @returns {{
 *  price: string,
 *  change: number,
 *  changePct: number,
 *  changeString: string,
 *  changePctString: string,
 *  closeStatus: TCloseStatus,
 * }}
 */
export function getPriceAndChangeFromQuote(quote, stx) {
  const data = {};
  const price = stx?.chart?.closePendingAnimation?.Close || quote?.Close;
  data.quote = quote;
  data.price = price ? convertNumberDecimal(price) : '';
  const previousClose = quote?.iqPrevClose;
  data.closeStatus = getCloseStatusChart(price, previousClose);

  const change = CIQ.fixPrice(price - previousClose);
  // toFixed(4) to sync with changePercentage from api (api is approximately 4 decimal) in Ticker.
  const changePct = ((change / previousClose) * 100).toFixed(4);
  data.change = change;
  data.changePct = changePct;
  // data.changePctString = price ? stx.internationalizer.percent2.format(changePct / 100) : '';
  data.changePctString = price ? convertChangePercentDecimal(Number(changePct)) + '%' : '';
  data.changeString = price ? formatChangeNumber(Number(change)) : '';

  return data;
}
