import { number, updateDefault, arabicToNumber, numberToArabic } from '@euroland/format-number';

import { convertFormatNumber, isValidatedNumber } from '../../helper';
import appConfig from '../../services/app-config';
import i18n from '../../services/i18n';

export { number, updateDefault, arabicToNumber, numberToArabic };

export const POSITIVE_NUMBER_FORMAT = 'n';
export const POSITIVE_CHANGE_NUMBER_FORMAT = '+n';

export const getInstrumentSettingById = instrumentId => {
  const config = appConfig.get();

  if (!config) return {};

  const { format, instruments, indices, peers } = config;
  const instrumentSetting = (instruments || [])
    .concat(indices.indices || [])
    .concat(peers.peers || [])
    .find(instrument => instrument.id === instrumentId);

  if (!instrumentSetting) {
    return {
      decimalDigits: format.decimalDigits
    };
  }

  return {
    ...instrumentSetting,
    decimalDigits: instrumentSetting.decimalDigits ?? format.decimalDigits
  };
};

export const formatNumberBySettings = (value, settings) => {
  if (!settings?.decimalDigits && typeof settings?.decimalDigits !== 'number') {
    return number(value);
  }
  const { decimalDigits, positiveFormatPattern } = settings;
  const formatNumber = convertFormatNumber({ decimalDigits });

  const options = {};

  if (positiveFormatPattern) {
    options.positiveFormatPattern = positiveFormatPattern;
  }

  return number(value, formatNumber, options);
};

/**
 *
 * @param {number} value
 * @param {number} instrumentId
 * @param {{
 *  decimalDigits: number
 *  thousandsSeparator: string
 *  decimalSeparator: string
 *  positiveFormatPattern: import("../..").IPositiveNumberFormat
 * }} options
 * @returns
 */
export const formatNumberByInstrument = (value, instrumentId, options = {}) => {
  if (!isValidatedNumber(value)) return i18n.translate('notAvailableValue');

  const instrumentSetting = getInstrumentSettingById(instrumentId);
  return formatNumberBySettings(value, {
    ...instrumentSetting,
    ...options
  });
};

/**
 *
 * @param {number} value
 * @param {number} instrumentId
 * @param {{
 *  decimalDigits: number
 *  thousandsSeparator: string
 *  decimalSeparator: string
 *  positiveFormatPattern: import("../..").IPositiveNumberFormat
 * }} options
 * @returns
 */
export const formatNumberChangeByInstrument = (value, instrumentId, options = {}) => {
  if (!isValidatedNumber(value)) return i18n.translate('notAvailableValue');

  const instrumentSetting = getInstrumentSettingById(instrumentId);
  const settings = appConfig.get();
  const positiveChangeFormat = settings?.format?.positiveChangeFormat || POSITIVE_CHANGE_NUMBER_FORMAT;
  return formatNumberBySettings(value, {
    positiveFormatPattern: positiveChangeFormat,
    ...instrumentSetting,
    ...options
  });
};
