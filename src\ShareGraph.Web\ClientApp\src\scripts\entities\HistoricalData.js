import { convertChangePercentDecimal, convertNumberDecimal, formatChangeNumber, formatDate } from '../helper';
import appConfig from '../services/app-config';

export default class HistoricalData {
  constructor(params = {}) {
    Object.assign(this, params);
    this.id = params.id || params.instrumentId;
    this.mA10 = params.mA10 ? convertNumberDecimal(params.mA10) : params.mA10;
    this.mA20 = params.mA20 ? convertNumberDecimal(params.mA20) : params.mA20;
    this.mA50 = params.mA50 ? convertNumberDecimal(params.mA50) : params.mA50;
    this.firstPrice = params.firstPrice ? convertNumberDecimal(params.firstPrice) : params.firstPrice;
    this.lastPrice = params.lastPrice ? convertNumberDecimal(params.lastPrice) : params.lastPrice;
    this.change = params.change ? formatChangeNumber(params.change) : params.change;
    this.changePercentage = typeof params.changePercentage === 'number'
      ? convertChangePercentDecimal(params.changePercentage) + '%'
      : params.changePercentage;
    this.highestPrice = params.highestPrice ? convertNumberDecimal(params.highestPrice) : params.highestPrice;
    this.lowestPrice = params.lowestPrice ? convertNumberDecimal(params.lowestPrice) : params.lowestPrice;
    this.totalVolume = params.totalVolume ? convertNumberDecimal(params.totalVolume) : params.totalVolume;
    this.totalReturn = params.totalReturn ? convertNumberDecimal(params.totalReturn) : params.totalReturn;
    const setting = appConfig.get();
    this.dateTimeFormat = params.dateTime
      ? formatDate(params.dateTime, { format: setting.accessibilities.formats?.date || 'DD MMMM, YYYY' })
      : params.dateTime;
    const sameInstrumentNumberObj = {};
    this.compares = (params.compares || []).map(compare => {
      let name = compare.name;
      if (this[name]) {
        sameInstrumentNumberObj[name] = (sameInstrumentNumberObj[name] || 0) + 1;
        name = `${name} (${sameInstrumentNumberObj[name]})`;
      }
      this[name] = convertChangePercentDecimal(compare.changePercentage) + '%';
      return {
        ...compare,
        columnDisplay: name,
        fieldName: name
      };
    });
  }
}
