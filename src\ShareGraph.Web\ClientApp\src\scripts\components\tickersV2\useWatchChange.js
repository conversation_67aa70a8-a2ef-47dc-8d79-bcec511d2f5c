/**
 * @typedef {
 * instrumentId: number,
 * marketName: string,
 * volumeChange: number,
 * ticker: string,
 * shareName: string,
 * low52W: number,
 * high52W: number,
 * currencyCode: string,
 * percent52W: number,
 * marketAbbreviation: string,
 * marketStatus: string,
 * bid: number,
 * ask: number,
 * last: number,
 * open: number,
 * high: number,
 * low: number,
 * volume: number,
 * change: number,
 * changePercentage: number,
 * lastUpdatedDate: string,
 * countdownToTheOpeningBell: number,
 * currency: string,
 * marketID: number,
 * normalDailyOpen: string,
 * normalDailyClose: string,
 * marketTimeZone: string,
 * businessDaysStoT: boolean,
 * id: number,
 * default: boolean,
 * color: string,
 * order: number,
 * isRT: boolean,
 * } Data
 */

import {useMemo, useRef} from 'react';

function isNumber (any) {
  return typeof any === 'number';
}

/**
 *
 * @param {Data} data
 */
export default function useWatchChange(data, fields) {
  const prev = useRef();
  
  return useMemo(() => {
    const changed = [];
    if(prev.current === undefined) {
      prev.current = {};
      fields.forEach(field => {
        prev.current[field] = data[field];
      });
    }
    fields.forEach(field => {
      const lastVal = data[field];
      const prevVal = prev.current[field];
      if(prevVal !== lastVal) {
        prev.current[field] = lastVal;

        if(isNumber(lastVal) && isNumber(prevVal)) {
          const space = lastVal - prevVal;
          if(space !== 0) {
            if(space > 0) {
              changed.push(`${field}:up`);
            } else {
              changed.push(`${field}:down`);
            }
          } 
        } else {
          changed.push(field);
        }
      }
    });

    return changed;
  }, [data, fields]);
}

