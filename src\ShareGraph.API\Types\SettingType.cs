using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.ShareGraph.API.Types
{
  public class SettingType : ObjectType<Setting>
  {
    protected override void Configure(IObjectTypeDescriptor<Setting> descriptor)
    {
      descriptor.Field(x => x.Currencies)
        .ResolveWith<SettingResolver>(x => x.GetCurrencies(default, default, default));

      descriptor
          .Field(t => t.Instruments)
          .ResolveWith<SettingResolver>(t => t.GetInstruments(default!));

      descriptor
         .Field(t => t.Ticker)
         .ResolveWith<SettingResolver>(t => t.GetTickers(default!, default!, default!));

      descriptor
          .Field(t => t.Peers)
          .ResolveWith<SettingResolver>(t => t.GetPeers(default!));

      descriptor
          .Field(t => t.Indices)
          .ResolveWith<SettingResolver>(t => t.GetIndices(default!));

      descriptor
          .Field(t => t.Performance)
          .ResolveWith<SettingResolver>(t => t.GetPerformance(default!));

      descriptor
          .Field(t => t.Chart)
          .ResolveWith<SettingResolver>(t => t.GetChart(default!));

      descriptor
          .Field(t => t.ShareDetails)
          .ResolveWith<SettingResolver>(t => t.GetShareDetails(default!));

      descriptor
          .Field(t => t.Format)
          .ResolveWith<SettingResolver>(t => t.GetFormats(default!));

      descriptor
          .Field(t => t.CustomPhrases)
          .ResolveWith<SettingResolver>(t => t.GetCustomPhrases(default!));

      descriptor
              .Field(t => t.CompanyLogo)
              .ResolveWith<SettingResolver>(t => t.GetCompanyLogo(default!));

      descriptor
              .Field(t => t.CompanyName)
              .ResolveWith<SettingResolver>(t => t.GetCompanyName(default!));

      descriptor
              .Field(t => t.PressReleases)
              .ResolveWith<SettingResolver>(t => t.GetPressReleases(default!));

      descriptor
             .Field(t => t.Accessibilities)
             .ResolveWith<SettingResolver>(t => t.GetAccessibilities(default!));

      descriptor
            .Field(t => t.Trade)
            .ResolveWith<SettingResolver>(t => t.GetTrade(default!));

      descriptor
           .Field(t => t.VideoSetting)
           .ResolveWith<SettingResolver>(t => t.GetVideoSetting(default!));

      descriptor
            .Field(t => t.OrderDepth)
            .ResolveWith<SettingResolver>(t => t.GetOrderDepth(default!));

      descriptor
            .Field(t => t.ColorBlindMode)
            .ResolveWith<SettingResolver>(t => t.GetColorBlindMode(default!));

      descriptor.Field(x => x.ForeignOwnership)
        .ResolveWith<SettingResolver>(x => x.GetForeignOwnership(default!));
    }
  }
}
