.create-alert-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.32);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.create-alert-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-width: 400px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  border: 1px solid #e8eaed;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8eaed;
    background: #ffffff;
    
    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: #202124;
      font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
  }
  
  &__close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    color: #5f6368;
    border-radius: 20px;
    transition: background-color 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background: #f1f3f4;
    }
    
    &:active {
      background: #e8eaed;
    }
  }
  
  &__body {
    padding: 24px;
    background: #ffffff;
  }
  
  &__content {
    min-height: 200px;
  }
  
  &__empty-content {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #5f6368;
    font-size: 14px;
    font-family: Roboto, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    
    p {
      margin: 0;
      text-align: center;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .create-alert-modal {
    width: 98%;
    max-width: none;
    margin: 16px;
    
    &__header {
      padding: 12px 16px;
      
      h2 {
        font-size: 16px;
      }
    }
    
    &__body {
      padding: 16px;
    }
  }
} 

euroland-create-share-alert {
  width: 100%;
  height: 100%;
}