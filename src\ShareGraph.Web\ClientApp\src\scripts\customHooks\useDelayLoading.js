import { useEffect, useState } from 'react';

/**
 * 
 * @param {boolean} loading 
 * @param {number} delayTime 
 * @returns {[boolean]}
 */
const useDelayLoading = (loading = false, delayTime = 2000) => {
    const  [delayLoading, setDelayLoading] = useState(loading);
   
    useEffect(() => {
        if(loading) {
            setDelayLoading(loading);
            return;
        } 
        setTimeout(() => {
            setDelayLoading(loading);
        },delayTime );
    }, [loading, delayTime]);
    
    return [delayLoading];
};

export default useDelayLoading;