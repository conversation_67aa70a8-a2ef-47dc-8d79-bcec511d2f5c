import { useContext } from 'react';
import { useDispatch, useStore } from 'react-redux';

import { AppContext } from '../../AppContext';
import PeerIndicesItem from './PeerIndicesItem';
import useStocksRealtime from '../../real-time/useStockRealtime';
import {updateStock} from '../../actions';
import isSameDayWithLastTick from '../../real-time/isSameDayWithLastTick';
import useOrderedInstruments from '../../customHooks/useOrderedInstruments';
import usePeers from '../../customHooks/usePeers';
import { getCustomPhrasePeer } from '../../helper';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';
import { fetchPeers } from '../../actions/peerActions';

export const Peer = () => {
  const { selectedPeerIds, data, onPeerSelectedChange, peerContainerRef } = usePeers();
  const store = useStore();
  const settings = useContext(AppContext);
  const tickerAnimation = settings.peers.animation || 'fade';
  const dispatch = useDispatch();
  
  useChangeQuoteCurrency(() => dispatch(fetchPeers()));

  const [{ orderedData }] = useOrderedInstruments({
    data,
    settings: settings.peers.peers
  });


  useStocksRealtime(function(data) {
    if(!isSameDayWithLastTick({stockId: data.id, date: data.date, store })) return;
    dispatch(updateStock(data.id, { close: data.price, date: data.date}));
  }, settings.peers.peers.map(item => item.id));

  return (
    <div
      ref={peerContainerRef}
      className={`comparison-v2__inner comparison-v2__animation--${tickerAnimation.toLowerCase()}`}
    >
      {orderedData.map((item, index) => {
          const currentIns = settings.peers.peers.find(x => x.id === item.instrumentId);
          const settingColorIns = currentIns.color;

        return (
          <PeerIndicesItem
            isSelected={selectedPeerIds.includes(item.instrumentId)}
            onItemClick={onPeerSelectedChange}
            data={getCustomPhrasePeer(item)}
            settingColor={settingColorIns}
            key={index}
          />
        );
      })}
    </div>
  );
};
