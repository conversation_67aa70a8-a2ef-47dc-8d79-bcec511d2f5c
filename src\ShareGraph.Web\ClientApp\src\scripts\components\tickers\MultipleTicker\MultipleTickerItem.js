import { useContext, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { AppContext } from '../../../AppContext';
import { TICKER_SWITCH_TYPE } from '../../../common';
import { default as templateGraphDefault } from '../../../../markup/ticker/MULTIPLE_TICKER_1.html';
import { default as templateTableDefault } from '../../../../markup/ticker/TABLE_TICKER_SINGLE.html';
import {
  replaceKey,
  formatDateTime,
  convertNumber,
  convertNumberDecimal,
  classByValue,
  getCustomPhraseTicker,
  convertPercentDecimal,
  convertMinutesToString,
  i18nTranslate,
  marketStatusByValue,
  getOpenDateTimeByMinute,
  getCountDownMinutesOpenMarket,
  getTimeZoneDisplay,
  classNames,
  clickWithoutMove,
  translateStringFormat,
  formatChangeNumber,
  convertChangePercentDecimal
} from '../../../helper';
import i18n from '../../../services/i18n';
import { useTreatLikeDom } from '../../../customHooks';
import {TIME_DELAY_SHOW_TICKER} from '../../../constant/common';
import {useDelay} from '../../../customHooks/useDelayUpdate';
import useWatchChange from '../useWatchChange';
import useDelayLoading from '../../../customHooks/useDelayLoading';
import useRefreshing from '../useRefreshing';
import {isValidColorHexCode} from '../../../utils/color';

const MultipleTickerItem = ({ prevData, data: _data, isSelected, onItemClick = () => {}, tickerFormat }) => {
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER );
  const fieldChanged = useWatchChange(data, ['bid', 'ask', 'last', 'open', 'high', 'low', 'volume']);
  const settings = useContext(AppContext);
  const containerRef = useRef(null);
  const refreshing = useRefreshing(data);
  const [delayLoading] = useDelayLoading(refreshing);

  const { className, events, attr, ...domProps } = useTreatLikeDom();
  useLayoutEffect(() => {
    function _setBgMarketAbbreviation() {
      const tickerMarketAbbreviation = containerRef.current.querySelector('.ticker__market-abbreviation');
      if (!tickerMarketAbbreviation) return;
      const currentIns = settings.instruments.find(x => x.id === data.instrumentId);
      const color = currentIns.color;
      //const { marketAbbreviation, marketName } = data;
      // if (marketAbbreviation == null || !marketAbbreviation) {
      //   tickerMarketAbbreviation.textContent = marketName?.slice(0, 3);
      // }

      if (isValidColorHexCode(color) && tickerMarketAbbreviation.classList.contains('ticker__market-abbreviation--bg-color')) {
        tickerMarketAbbreviation.style.background = color;
      }
    }
    _setBgMarketAbbreviation();
  });

  const lastValue = useMemo(() => {
    if (!prevData) return;
    const prevItem = prevData.find(e => e.instrumentId == data.instrumentId);
    return data.last - prevItem.last;
  }, [data]);

  const template = useMemo(() => {
    function _getTemplateHtml() {
      if (tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE) {
        const templateTable = settings.ticker.tableTickerTemplate || templateTableDefault;
        const tableHTMLDOM = new DOMParser().parseFromString(templateTable, 'text/xml');
        const tableRowHTML =
          tableHTMLDOM.querySelector('.table__body-tr') &&
          tableHTMLDOM.querySelector('.table__body-tr').outerHTML;
        return tableRowHTML;
      }
      return settings.ticker.graphTickerTemplate || templateGraphDefault;
    }

    function _renderTemplate(item) {
      if (item) {
        const tableRow = _getTemplateHtml();
        const labels = _getLabel();
        const dataTicker = _normalizeData(item);
        const dataTemplate = { ...dataTicker, ...labels };
        const content = replaceKey(tableRow, dataTemplate);
        if (tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE) return content;
        const templateTable = settings.ticker.tableTickerTemplate;
        let templateHeading = replaceKey(templateTable, dataTemplate);
        let tableHTMLDOM = new DOMParser().parseFromString(templateHeading, 'text/xml');
        tableHTMLDOM.querySelector('.table__body-tr').remove();
        tableHTMLDOM.querySelector('.table__body').innerHTML = content;
        return tableHTMLDOM.querySelector('.table').outerHTML;
      }

      return '<h2> No data</h2>';
    }
    return _renderTemplate(data);
  }, [data]);

  function _normalizeData(item) {
    const data = { ...item };
    const countDownMinutes = getCountDownMinutesOpenMarket(item);
    data.bid = convertNumberDecimal(item.bid);
    data.ask = convertNumberDecimal(item.ask);
    data.change = formatChangeNumber(item.change);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = convertNumberDecimal(Math.max(item.high, item.last));
    data.high52W = convertNumberDecimal(Math.max(item.high52W, item.last));
    data.last = convertNumberDecimal(item.last);
    data.low = convertNumberDecimal(Math.min(item.low, item.last));
    data.low52W = convertNumberDecimal(Math.min(item.low52W, item.last));
    data.open = convertNumberDecimal(item.open);
    data.percent52W = convertPercentDecimal(item.percent52W);
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatChangeNumber(item.volumeChange);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.timeToCloseMarket = `${convertMinutesToString(
      countDownMinutes,
      i18n.translate('hrs'),
      i18n.translate('mins')
    )} ${getTimeZoneDisplay(item.id)}`;
    data.dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, item.id);
    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';

    return getCustomPhraseTicker(data);
  }

  function _getLabel() {
    const singleLabels = [
      'w52RangeLabel',
      'volumeLabel',
      'bidAskLabel',
      'marketCloseLabel',
      'marketOpenedLabel',
      'openLabel',
      'highLabel',
      'lowLabel',
      'marketOpenInLabel',
      'sharesLabel',
      'lastLabel',
      'changePercentageLabel',
      'changeLabel',
      'marketWillOpenLabel',
      'marketWillCloseLabel',
      'relativeVolumelabel'
    ];

    return {
      dayRangeLabel: i18n.translate('rangeLabel'),
      ...i18nTranslate(singleLabels)
    };
  }

  function onClick(event) {
    // Keypresses other than Enter and Space
    if (event instanceof KeyboardEvent && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }
    event.preventDefault();
    onItemClick(data.instrumentId);
  }

  return (
    <div
      {...events({
        ...clickWithoutMove(onClick),
        onKeyDown: onClick
      })}
      data-change={fieldChanged.join(',')}
      tabIndex="-1"
      role="button"
      {...attr({
        'aria-pressed': isSelected
      })}
      {...domProps}
      {...className(
        classNames(
          'ticker__item',
          { selected: isSelected },
          classByValue(data.change, data.isRT),
          `volume--${classByValue(data.volumeChange)}`,
          marketStatusByValue(data.marketStatus),
          {
            streaming: data.isRT,
            loading: delayLoading,
            'animate--increase': lastValue > 0,
            'animate--decrease': lastValue < 0,
            'animate--neutral': data.isRT ? false : (lastValue === 0) // do not add neutral when isRT = true
          }
        )
      )}
      ref={containerRef}
      dangerouslySetInnerHTML={{ __html: template }}
    ></div>
  );
};

export default MultipleTickerItem;
