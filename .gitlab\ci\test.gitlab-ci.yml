test-merge-request:
  tags:
    #- ubuntu-docker-dev
    - vietnam-dev-shell
  extends:
    - .default-retry
  stage: test
  script:
    # Lint the ClientApp
    - 'dotnet msbuild -target:JSLint -p:GitlabBuild=true -p:JsLint=true src/ShareGraph.Web'
    - 'dotnet test'
  only:
    refs:
      - merge_requests
    variables:
      - '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develope"'

# test-qa:
#   tags:
#     #- ubuntu-docker-dev
#     - vietnam-dev-shell
#   extends:
#     - .default-retry
#   stage: test
#   script:
#     # Lint the ClientApp
#     - 'dotnet msbuild -target:JSLint -p:GitlabBuild=true -p:JsLint=true src/ShareGraph.Web'
#     - 'dotnet test'
#   only:
#     refs:
#       - develope

# test-release:
#   tags:
#     - ee-buildtest-shell
#   extends:
#     - .default-retry
#   stage: test
#   script:
#     # Lint the ClientApp
#     - 'dotnet msbuild -target:JSLint -p:GitlabBuild=true -p:JsLint=true src/ShareGraph.Web'
#     - 'dotnet test'
#   only:
#     refs:
#       - next
#       - master
