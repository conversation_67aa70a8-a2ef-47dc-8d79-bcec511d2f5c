import produce from 'immer';

import {
  <PERSON>ETCH_TRADES_BEGIN,
  FETCH_TRADES_SUCCESS,
  FETCH_TRADES_FAILURE
} from '../actions/tradesAction';

const initialState = {
  tradesData: [],
  loading: true,
  fetchError: null
};

export default function createTradesReducer() {

  return function tradesReducer(state = initialState, action) {
    switch (action.type) {
      case FETCH_TRADES_BEGIN:
        return produce(state, draft => {
          draft.loading = true;
          draft.fetchError = null;
        });
      case FETCH_TRADES_SUCCESS:
        return produce(state, draft => {
          draft.loading = false;
          draft.tradesData = action.payload.tradesData;
        });
      case FETCH_TRADES_FAILURE:
        return produce(state, draft => {
          draft.loading = false;
          draft.fetchError = action.payload.error;
        });
      default:
        return state;
    }
  };
}
