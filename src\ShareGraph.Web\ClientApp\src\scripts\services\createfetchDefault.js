/**
 * Wrapper for fetch API to append default values for headers, url, etc..
 */

function deepExtend(target, source) {
  const isObject = (obj) => obj && typeof obj === 'object';

  if (!isObject(target) || !isObject(source)) {
    return source;
  }

  Object.keys(source).forEach(key => {
    const targetValue = target[key];
    const sourceValue = source[key];

    if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {
      target[key] = targetValue.concat(sourceValue);
    } else if (isObject(targetValue) && isObject(sourceValue)) {
      target[key] = deepExtend(Object.assign({}, targetValue), sourceValue);
    } else {
      target[key] = sourceValue;
    }
  });

  return target;
}


const toString = (obj) => {
  return Object.prototype.toString.call(obj);
};

const isDefined = (obj) => {
  return obj !== null && obj !== undefined;
};

const createFetch = (baseURL, defaults, resource, options, searchParams) => {
    const inputType = toString(resource);
    let url = resource;
    if (inputType === '[object String]') {
      url = baseURL != null ? new URL(resource, baseURL) : new URL(resource);
    } else if (inputType === '[object Request]') {
      url = new URL(resource.url);
    } else {
      throw new Error('The fetch resource must be a string or Request object. Ref: https://developer.mozilla.org/en-US/docs/Web/API/fetch#parameters');
    }

  if (searchParams) {

    for (var p of Object.keys(searchParams)) {
      if (Object.prototype.hasOwnProperty.call(searchParams, p) && !url.searchParams.has(p)) {
        if (isDefined(searchParams[p])) {
          url.searchParams.append(p, searchParams[p]);
        }
      }
    }

    // fix bug #151: Loading issue
    var uniq = Date.now().toString(36) + Math.random().toString(36).substr(2);
    url.searchParams.append('s', uniq);
  }

  if(options?.name) {
    url.searchParams.append('name', options.name);
  }

  if (typeof defaults === 'function') {
    defaults = defaults(url, options);
  }

  
  return fetch(url, {
    ...(options == null ? defaults : deepExtend(Object.assign({}, defaults), options)),
    signal: options?._signal
  }).then(handleErrors);
};

function handleErrors(response) {
  if (!response.ok) {
    throw Error(response.statusText);
  }

  return response;
}


export function createFetchDefault(baseURL, defaults, searchParams) {
  if (typeof baseURL !== 'string') {
    searchParams = defaults;
    defaults = baseURL;
    baseURL = null;
  }

  return function (resource, options) {
    return createFetch(baseURL, defaults, resource, options, searchParams);
  };
}
