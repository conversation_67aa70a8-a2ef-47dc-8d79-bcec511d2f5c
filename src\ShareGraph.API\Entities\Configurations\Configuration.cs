﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
    public class Configuration
    {
        public Setting Setting { get; set; }
        public string CompanyCode { get; set; }
        public string Version { get; set; }
        public string Lang { get; set; }
        public string PathBase { get; set; }
        public bool EnabledGoogleAnalytic { get; set; }
    }
}
