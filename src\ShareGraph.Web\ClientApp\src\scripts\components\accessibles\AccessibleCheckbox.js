const AccessibleCheckbox = ({ className, id, options = [], value = [], onChange = () => {} }) => {
  const isChecked = (value, selectedValues = []) =>
    selectedValues.map(v => String(v)).includes(String(value));
  return (
    <table className={className} id={id}>
      <tbody>
        {options.map(o => (
          <tr key={o.id}>
            <td>
              <input
                type="checkbox"
                id={o.id}
                name={o.name}
                checked={isChecked(o.value, value)}
                onChange={() => onChange(o)}
                value={o.value}
                aria-checked={isChecked(o.value, value)}
              />
              <label htmlFor={o.id}>{o.title}</label>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default AccessibleCheckbox;
