import { useContext, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useSearchParams } from 'react-router-dom';
import { AppContext } from '../../AppContext';
import { MAP_TYPE_PERIOD_QUERY, VIEW_TYPES } from '../../common';
import useQuery from '../../customHooks/useQuery';
import {
  convertChangePercentDecimal,
  convertNumber,
  formatChangeNumber,
  formatDate,
  isValidatedNumber,
  pickBy,
  stringifyQuery,
  translateStringFormat
} from '../../helper';

import i18n from '../../services/i18n';
import SharePriceDetailsNav from './SharePriceDetailsNav';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import { useChangeQuoteCurrency, useSelectedCurrency } from '../../customHooks/useSelectedCurrency';
import {fetchStockOverview} from '../../services/commonService';

const useSelectedInstrument = () => {
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const instruments = useSelector(state => state.tickers.instruments);
  const selectedInstrument = useMemo(() => {
    return instruments.find(item => item.instrumentId === selectedInstrumentId);
  }, [instruments, selectedInstrumentId]);

  return selectedInstrument || {};
};

const SharePriceDetails = () => {
  const setting = useContext(AppContext);
  const [stockOverview, setStockOverview] = useState({});
  const [searchParams] = useSearchParams();
  const search = useQuery();
  const { formatNumberByInstrument } = useFormatNumberByInstrument();
  const selectedCurrency = useSelectedCurrency();

  const periodParam = searchParams.get('period') || 'oneyear';

  const selectedInstrument = useSelectedInstrument();
  const selectedInstrumentId = selectedInstrument.instrumentId;
  const formatDateForAccessibility = date =>
    formatDate(date, {
      format: setting.accessibilities.formats?.date || 'DD MMMM, YYYY'
    });
  let startDate = new Date(searchParams.get('startDate'));
  let endDate = new Date(searchParams.get('endDate'));

  startDate = startDate !== 'Invalid Date' ? startDate : null;
  endDate = endDate !== 'Invalid Date' ? endDate : null;

  const handleFetchStockOverview = (instrumentId,param) => {
    if (instrumentId && param) {
        fetchStockOverview(instrumentId, {
          period: MAP_TYPE_PERIOD_QUERY[param],
          toCurrency: selectedCurrency?.code ? `"${selectedCurrency?.code}"` : ''
        })
        .then(response => {
          setStockOverview({
            ...response.data.stockOverview
          });
        });
    }
  };
  useEffect(() => {
    handleFetchStockOverview(selectedInstrumentId, periodParam);
  }, [selectedInstrumentId, periodParam]);

  useChangeQuoteCurrency(() => {
    handleFetchStockOverview(selectedInstrumentId, periodParam);
  });

  const sharePriceDetailsFirstPrice = formatNumberByInstrument(
    stockOverview.firstPrice,
    selectedInstrumentId
  );
  const sharePriceDetailsLastPrice = formatNumberByInstrument(stockOverview.lastPrice, selectedInstrumentId);
  const sharePriceDetailsChange = formatChangeNumber(stockOverview.change);
  const sharePriceDetailsChangePercentage = convertChangePercentDecimal(stockOverview.changePercentage);
  const sharePriceDetailsHighestPrice = formatNumberByInstrument(
    stockOverview.highestPrice,
    selectedInstrumentId
  );
  const sharePriceDetailsLowestPrice = formatNumberByInstrument(
    stockOverview.lowestPrice,
    selectedInstrumentId
  );
  const sharePriceDetailsTotalReturn = convertChangePercentDecimal(stockOverview.totalReturn);
  const sharePriceDetailsTotalVolume = convertNumber(stockOverview.totalVolume);

  return (
    <section className="share-price-details">
      <h2 id="periodSelectionPanel">{i18n.translate('sharePriceDetails')}</h2>
      <h3 id="titleSelectPeriod">{i18n.translate('sharePriceDetailsTitle')}</h3>
      <SharePriceDetailsNav />
      <div id="pnlMain">
        <div id="share-price-period__div">
          <h3
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsPeriodTime', [
                formatDateForAccessibility(startDate),
                formatDateForAccessibility(endDate)
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsFirstPrice', [
                sharePriceDetailsFirstPrice,
                sharePriceDetailsFirstPrice !== i18n.translate('notAvailableValue')
                  ? selectedInstrument.currencyCode
                  : ''
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsLastPrice', [
                sharePriceDetailsLastPrice,
                sharePriceDetailsLastPrice !== i18n.translate('notAvailableValue')
                  ? selectedInstrument.currencyCode
                  : ''
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsChange', [
                sharePriceDetailsChange,
                sharePriceDetailsChange !== i18n.translate('notAvailableValue')
                  ? selectedInstrument.currencyCode
                  : ''
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsChangePercentage', [
                sharePriceDetailsChangePercentage !== i18n.translate('notAvailableValue')
                  ? `${sharePriceDetailsChangePercentage}%`
                  : sharePriceDetailsChangePercentage
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsHighestPrice', [
                formatDateForAccessibility(stockOverview.highestPriceDate),
                sharePriceDetailsHighestPrice,
                sharePriceDetailsHighestPrice !== i18n.translate('notAvailableValue')
                  ? selectedInstrument.currencyCode
                  : ''
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsLowestPrice', [
                formatDateForAccessibility(stockOverview.lowestPriceDate),
                sharePriceDetailsLowestPrice,
                sharePriceDetailsLowestPrice !== i18n.translate('notAvailableValue')
                  ? selectedInstrument.currencyCode
                  : ''
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsTotalVolume', [
                !isValidatedNumber(stockOverview.totalVolume)
                  ? i18n.translate('notAvailableValue')
                  : sharePriceDetailsTotalVolume
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsHighestVolumeDate', [
                stockOverview.highestVolumeDate
                  ? formatDateForAccessibility(new Date(stockOverview.highestVolumeDate))
                  : i18n.translate('notAvailableValue')
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsLowestVolumeDate', [
                stockOverview.lowestVolumeDate
                  ? formatDateForAccessibility(new Date(stockOverview.lowestVolumeDate))
                  : i18n.translate('notAvailableValue')
              ])
            }}
          />

          <p
            dangerouslySetInnerHTML={{
              __html: translateStringFormat('sharePriceDetailsTotalReturn', [
                sharePriceDetailsTotalReturn !== i18n.translate('notAvailableValue')
                  ? `${sharePriceDetailsTotalReturn}%`
                  : sharePriceDetailsTotalReturn
              ])
            }}
          />
        </div>

        <p id="share-price-period__more-detail" className="share-price-details">
          {i18n.translate('sharePriceDetailsDescription')}
        </p>
      </div>
      <div id="viewmonthly">
        <nav id="navviewmonthly">
          <Link
            to={{
              ...location,
              pathname: '/accessibility/share-details',
              search: stringifyQuery(
                pickBy({
                  ...search,
                  typePage: VIEW_TYPES.MONTHLY
                })
              )
            }}
          >
            {i18n.translate('sharePriceDetailsMonthlyLink')}
          </Link>
          <span role="presentation" aria-hidden="true">
            &nbsp;|&nbsp;
          </span>
          <Link
            to={{
              ...location,
              pathname: '/accessibility/share-details',
              search: stringifyQuery(
                pickBy({
                  ...search,
                  typePage: VIEW_TYPES.WEEKLY
                })
              )
            }}
          >
            {i18n.translate('sharePriceDetailsWeeklyLink')}
          </Link>
        </nav>
      </div>
    </section>
  );
};

export default SharePriceDetails;
