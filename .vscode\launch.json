{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "ShareGraph.Web",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/src/ShareGraph.Web/bin/Debug/net6.0/Euroland.FlipIT.ShareGraph.Web.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/ShareGraph.Web",
      "stopAtEntry": false,
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": "ShareGraph.API",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/src/ShareGraph.API/bin/Debug/net6.0/Euroland.FlipIT.ShareGraph.API.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/ShareGraph.API",
      "stopAtEntry": false,
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": ".NET Core Attach",
      "type": "coreclr",
      "request": "attach"
    },
    {
      "name": "ShareGraph.Web Docker",
      "type": "docker",
      "request": "launch",
      "preLaunchTask": "docker-run:sharegraph-web:debug",
      "netCore": {
        "appProject": "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj",
      }
    },
    {
      "name": "ShareGraph.API Docker",
      "type": "docker",
      "request": "launch",
      "preLaunchTask": "docker-run:sharegraph-api:debug",
      "removeContainerAfterDebug": true,
      "netCore": {
        "appProject": "${workspaceFolder}/src/ShareGraph.API/ShareGraph.API.csproj",
      }
    }
  ]
}
