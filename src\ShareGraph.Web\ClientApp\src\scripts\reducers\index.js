import { combineReducers } from 'redux';
import { lastAction } from './last-action';
//import appInit from './app';
//import tickers from './ticker';
import tickerReducer from './tickerReducers';
import peerReducer from './peerReducers';
import weeks52Reducer from './52WeeksReducers';
import shareDetailsReducer from './shareDetailsReducers';
import sharePriceDevelopmentByYearsReducer from './sharePriceDevelopmentByYearsReducers';
import SharePriceDevelopmentReducer from './sharePriceDevelopmentReducers';
import indicesReducer from './indicesReducers';
import historicalReducer from './accessibles/historicalDataReducers';
import errorUIReducer from './errorUIReducers';
import realtimeReducer from './realtimeReducers';
import tradesReducers from './tradesReducers';
import orderDepthReducers from './orderDepthReducers';
import createCurrencyReducers from './currencyReducers';
import blindModeReducer from './blindmodeReducers';
/**
 * We do combine all reducers of application by using combineReducers().
 */

export default function createRootReducer({
  appSettings,
  instrumentIds,
  peerIds,
  indicesIds,
  w52Ids,
  perfomanceByYearIds,
  sharePriceDevelopmentIds,
  colorBlindMode
}) {
  return combineReducers({
    tickers: tickerReducer(instrumentIds),
    peers: peerReducer(peerIds),
    shareDetails: shareDetailsReducer(instrumentIds),
    indices: indicesReducer(indicesIds),
    week52: weeks52Reducer(w52Ids),
    performanceByYear: sharePriceDevelopmentByYearsReducer(perfomanceByYearIds),
    sharePriceDevelopment: SharePriceDevelopmentReducer(sharePriceDevelopmentIds),
    lastAction,
    historicalDatas: historicalReducer,
    errorUIs: errorUIReducer,
    realtime: realtimeReducer(),
    trades: tradesReducers(),
    orderDepth: orderDepthReducers,
    currency: createCurrencyReducers(),
    blindMode: blindModeReducer(colorBlindMode)
  });
}

/**
 * @typedef { ReturnType<ReturnType<typeof createRootReducer>>} RootState
 */
