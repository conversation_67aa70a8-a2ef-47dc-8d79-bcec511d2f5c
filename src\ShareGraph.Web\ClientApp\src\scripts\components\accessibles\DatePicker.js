import { useEffect, useLayoutEffect, useRef, useState, useCallback } from 'react';
import { useClickAway } from '@euroland/react';

import calendarImgSrc from '../../../assets/images/calendar.png';
import { loadSettingEUCalendar } from '../../helper';
import i18n from '../../services/i18n';
import { UserAgent } from '../../customHooks/useResize';
import appConfig from '../../services/app-config';

export const DatePicker = ({ id, className, maxDate, minDate, dateFormat, value, onSelect = () => {} }) => {
  const calendarDateFormat = Calendar.dotNet2CalendarFormat(dateFormat);
  const dateFromRef = useRef(null);
  const calendarFromRef = useRef(null);
  const currentDate = new Date();
  maxDate = maxDate || currentDate;
  minDate = minDate || new Date('12/01/1995');
  const maxDte = Calendar.dateToInt(maxDate);
  const minDte = Calendar.dateToInt(minDate);
  const datepickerFromRef = useRef(null);
  const [showSelectedDate, setShowSelectedDate] = useState(false);
  const showSelectedDateRef = useRef(showSelectedDate);
  const imgRef = useRef();

  const getDate = calendarDate => {
    const [, Y, m, d] =
      String(calendarDate.selection.sel[0]).match(/(\d{4})(\d{2})(\d{2})/) ||
      [];
    return new Date(Number(Y), Number(m) - 1, Number(d));
  };

  const hideCalendar = calendar => {
    setTimeout(() => {
      calendar.hide();
      setShowSelectedDate(false);
      if (calendarFromRef.current) {
        calendarFromRef.current.showYearMenu(calendarFromRef.current, false);
      }
    }, 300);
  };

  useClickAway(
    [datepickerFromRef, dateFromRef],
    () => {
      showSelectedDate && hideCalendar(calendarFromRef.current);
    },
    ['mousedown', 'touchstart', 'click']
  );

  useEffect(() => {
    showSelectedDateRef.current = showSelectedDate;
    if (!calendarFromRef.current) return;
    setTimeout(() => {
      const queryStr = `#${id} .EUCalendar-bottomBar__close`;
      const bottomBarNode = document.querySelector(queryStr);
      if (calendarFromRef.current && !bottomBarNode && showSelectedDate) {
        const eurolandBottomBarDOM = document.querySelector(`#${id} .EUCalendar-topCont`);
        const closeNode = document.createElement('div');
        closeNode.setAttribute('tabindex', 0);
        closeNode.setAttribute('role', 'button');
        closeNode.classList.add('EUCalendar-bottomBar__close');
        closeNode.appendChild(document.createTextNode(i18n.translate('close')));
        eurolandBottomBarDOM.appendChild(closeNode);
        const handleClose = () => hideCalendar(calendarFromRef.current);
        const handleKey = event => {
          if (event.code === 'Enter') {
            hideCalendar(calendarFromRef.current);
          }
        };
        closeNode.addEventListener('click', handleClose);
        closeNode.addEventListener('keydown', handleKey);
        closeNode.addEventListener('keyup', handleKey);
        return () => {
          closeNode.removeEventListener('click', handleClose);
          closeNode.removeEventListener('keydown', handleKey);
          closeNode.removeEventListener('keyup', handleKey);
        };
      }
    }, 300);
  }, [showSelectedDate]);

  const disabledHandle = useCallback(
    (date) => {
      if (!!maxDte && Calendar.dateToInt(date) > maxDte) {
        return true;
      }
      if (!!minDte && Calendar.dateToInt(date) < minDte) {
        return true;
      } else {
        return false;
      }
    },
    [maxDte, minDte]
  );

  useLayoutEffect(() => {
    if (calendarFromRef.current) {
      calendarFromRef.current.args.disabled = disabledHandle;
      calendarFromRef.current.redraw();
    }
  }, [disabledHandle]);

  useLayoutEffect(() => {
    if (!calendarFromRef.current) {
      loadSettingEUCalendar({
        sdn: [
          i18n.translate('accessible_su'),
          i18n.translate('accessible_mo'),
          i18n.translate('accessible_tu'),
          i18n.translate('accessible_we'),
          i18n.translate('accessible_th'),
          i18n.translate('accessible_fr'),
          i18n.translate('accessible_sa'),
          i18n.translate('accessible_su')
        ]
      }, appConfig.get());
      calendarFromRef.current = new window.Calendar({
        inputField: dateFromRef.current,
        trigger: dateFromRef.current,
        bottomBar: false,
        dateFormat: calendarDateFormat,
        min: minDte,
        max: maxDte,
        align: 'Br/ / /T/r',
        titleFormat: '%B %Y',
        fdow: 1,
        disabled: disabledHandle,
        onSelect: function () {
          const selecteDate = getDate(calendarFromRef.current);
          onSelect(selecteDate);
          showSelectedDateRef.current && hideCalendar(this);
        },
        onBlur: function () {
          hideCalendar(this);
        }
      });
    }

    if (value) {
      calendarFromRef.current.selection.set(value);
    } else {
      calendarFromRef.current.selection.set(maxDte);
    }
  }, [value && value.toString()]);

  const handleKeyTrigger = event => {
    if (event.code === 'Enter') {
      setShowSelectedDate(true);
    }
  };

  useEffect(() => {
    const handleClickImg = () => {
      if (UserAgent.isMobile || UserAgent.isSafari) {
        dateFromRef.current.focus();
      }
    };
    imgRef.current.addEventListener('click', handleClickImg);
    return () => {
      imgRef.current?.removeEventListener('click', handleClickImg);
    };
  }, []);

  useEffect(() => {
    if (calendarFromRef.current) {
      calendarFromRef.current.args.min = minDate;
      calendarFromRef.current.args.max = maxDate;

      calendarFromRef.current.redraw();
    }
  }, [maxDte, minDte]);

  return (
    <div id={id} className={className}>
      <label
        aria-hidden="true"
        onClick={() => setShowSelectedDate(true)}
        onKeyDown={handleKeyTrigger}
        onKeyUp={handleKeyTrigger}
      >
        <div className="accessible-calendar__wrapper" ref={datepickerFromRef}>
          <input
            className="accessible-calendar__input"
            type="text"
            placeholder="From Date"
            ref={dateFromRef}
            readOnly
          />
          <i></i>
          <img ref={imgRef} src={calendarImgSrc} alt="Start date" className="accessible-calendar__icon"></img>
        </div>
      </label>
    </div>
  );
};
