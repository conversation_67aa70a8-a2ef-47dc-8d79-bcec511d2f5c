import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import useAppSelector from '../../customHooks/useAppSelector';
import { switchCurrency, toggleEnabledCurrencyOption, updateCurrencyRate } from '../../actions/currencyAction';
import { getCurrencyOptionSetting } from '../../configs/configuration-app';
import { REFRESH_UPDATE_CURRENCY_RATE } from '../../constant/common';
import appConfig from '../../services/app-config';
import { SearchableDropdown, Option} from '@euroland/dropdown-react';
import { classNames } from '../../helper';
import i18n from '../../services/i18n';

export const DEFAULT_CURRENCY = 'DEFAULT_CURRENCY';

const CurrencySelection = () => {
  const [isVisibleDefaultCurrency, setIsVisibleDefaultCurrency] = useState(true);

  const dispatch = useDispatch();
  const selected = useAppSelector((state) => state.currency.currency);
  const enabledCurrencyOption = useAppSelector(state => state.currency.enabledCurrencyOption);

  const defaultCurrencyText = i18n.translate('defaultCurrency');
  const optionData = getCurrencyOptionSetting();
  const options = Object.keys(optionData).map((code) => optionData[code]);
  options.unshift({
    code: DEFAULT_CURRENCY,
    text: defaultCurrencyText
  });

  const selectedCode = selected?.code ?? DEFAULT_CURRENCY;
  const label = optionData[selectedCode]?.text || optionData[selectedCode]?.code || defaultCurrencyText;
  const settings = appConfig.get();
  const refreshTime = settings.currency?.refreshSeconds || REFRESH_UPDATE_CURRENCY_RATE;


  useEffect(() => {
    function fetchData() {
      if (selectedCode === DEFAULT_CURRENCY) return;
      dispatch(updateCurrencyRate({ quoteCurrency: selectedCode }));
    }

    const timer = setInterval(() => {
      fetchData();
    }, refreshTime * 1000);

    fetchData();

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [dispatch, refreshTime, selectedCode]);

  const handleSelectCurrency = (currency) => {
    if(!enabledCurrencyOption) return;
    dispatch(switchCurrency(optionData[currency]));
    if(selectedCode !== currency) {
      dispatch(toggleEnabledCurrencyOption(false));
    }
  };

  const handleSearchChange = (searchValue) => {
    const isVisibleDefaultCurrency = defaultCurrencyText.toLowerCase().includes(searchValue.toLowerCase());
    setIsVisibleDefaultCurrency(isVisibleDefaultCurrency);
  };

  const renderButton = ()=>{
    return <>
    {
      selectedCode !== DEFAULT_CURRENCY && <div className={`currency-flag currency-${selectedCode}`} />}
      {label}
    </>;
  };

  return (
    <div className="currency-select">
      <SearchableDropdown
        value={selectedCode}
        className={classNames({'display-default-currency':isVisibleDefaultCurrency})}
        onOptionSelect={handleSelectCurrency}
        onSearchChange={handleSearchChange}
        renderButton={renderButton}
        aria-label={i18n.translate('currencyDropdownLabel')}
        menuArialLabel={i18n.translate('currencyDropdownMenuLabel')}
        icon={<span className="fs-caret-top"></span>}
        customPlaceholder={
          <p>
            <span className="fs-search"></span>
            <span className='placeholder'>{i18n.translate('searchCurrency')}</span>
          </p>
        }
        notFoundMessage={<p className='no-currency-found'>{i18n.translate('noCurrencyFound')}</p>}
      >
        {options.map((option) => {
          const { code, text, fullName } = option;
          const selected = selectedCode === code;
          return (
            <Option
              key={code}
              value={code}
              aria-label={fullName}
              aria-disabled={!enabledCurrencyOption}
              className={classNames({
                disabled: !enabledCurrencyOption
              })}
              aria-selected={selected}
            >
              <div
                className={classNames(`currency-flag currency-${code}`, {
                  ['default-currency-flag']: code === DEFAULT_CURRENCY
                })}
              />
              {text || code}
              {selected && <span className="fs-tick-mark"></span>}
            </Option>
          );
        })}
      </SearchableDropdown>
    </div>
  );
};

export default CurrencySelection;
