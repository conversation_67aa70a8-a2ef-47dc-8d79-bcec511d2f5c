import { convertChangePercentDecimal, getCustomShareNameByInstrumentId } from '../helper';

export default class SharePriceDevelopments {
  constructor(params = {}, formatNumberByInstrument, formatNumberChangeByInstrument) {
    Object.assign(this, params);
    this.shareName = getCustomShareNameByInstrumentId(params.instrumentId) || params.shareName || '';
    this.id = params.id || params.instrumentId;
    this.last = formatNumberByInstrument(params.last || 0, this.id);
    this.change = formatNumberChangeByInstrument(params.change || 0, this.id);
    this.high52W = formatNumberByInstrument(params.high52W || 0, this.id);
    this.low52W = formatNumberByInstrument(params.low52W || 0, this.id);
    this.allTimeHigh = formatNumberByInstrument(params.allTimeHigh || 0, this.id);
    this.allTimeLow = formatNumberByInstrument(params.allTimeLow || 0, this.id);
    this.week = convertChangePercentDecimal(params.week || 0) + '%';
    this.month = convertChangePercentDecimal(params.month || 0) + '%';
    this.threeMonthChange = convertChangePercentDecimal(params.threeMonthChange || 0) + '%';
    this.sixMonthsChange = convertChangePercentDecimal(params.sixMonthsChange || 0) + '%';
    this.yTD = convertChangePercentDecimal(params.yTD || 0) + '%';
    this.percent52W = convertChangePercentDecimal(params.percent52W || 0) + '%';
    this.threeYearsChange = convertChangePercentDecimal(params.threeYearsChange || 0) + '%';
    this.fiveYearsChange = convertChangePercentDecimal(params.fiveYearsChange || 0) + '%';
    this.tenYearsChange = convertChangePercentDecimal(params.tenYearsChange || 0) + '%';
  }
}
