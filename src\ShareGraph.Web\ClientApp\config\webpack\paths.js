const { resolve } = require('path');

const contextPath = resolve(__dirname, '../', '../', 'src');
const outputPath = resolve(__dirname, '../', '../', 'dist');
const bundleReportPath = resolve(outputPath, 'report.html');
//const entryPath = resolve(contextPath, 'index.js');
const entryPath = resolve(contextPath, 'index.js');
//const integrationEntryPath = resolve(contextPath, 'integration/', 'index.js');
const envDevPath = resolve(__dirname, '../', 'environment', '.env.development');
const envProdPath = resolve(__dirname, '../', 'environment', '.env.production');
const envPath = resolve(__dirname, '../', 'environment', '.env');
const eslintConfigPath = resolve(__dirname, '../', '../', '.eslintrc.js');
const nodeModulesPath = resolve(__dirname, '../', '../', 'node_modules');
//const faviconPath = resolve(contextPath, 'assets', 'favicon.png');
//const templatePath = resolve(contextPath, 'index.html');
//const frameTemplatePath = resolve(contextPath, 'iframe.html');

module.exports = {
    contextPath,
    outputPath,
    bundleReportPath,
    entryPath,
    //integrationEntryPath,
    //templatePath,
    //frameTemplatePath,
    envDevPath,
    envProdPath,
    envPath,
    eslintConfigPath,
    nodeModulesPath,
    //faviconPath
};
