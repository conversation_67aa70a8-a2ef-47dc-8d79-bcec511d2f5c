import { WebPubSubClient } from '@azure/web-pubsub-client';
import {streamSnapshotAction, streamTradesAction} from '../actions/realtimeAction';
import {getAllInstrumentsSetting, getTickersSetting} from '../configs/configuration-app';
import dayjs from 'dayjs';

window.globalData = [];

/**
 * @typedef ShareRealtime
 * @prop { number } id
 * @prop { boolean } isRT
 */

/**
 *
 * @typedef TradesData
 * @prop { string } InstrumentID
 * @prop { string } hDate
 * @prop { string } EnqueuedTime
 * @prop { number } hClose
 * @prop { number } hSize
 * @prop { number } InstrumentType
 */

/**
 * 
 * @typedef StreamSnapshotData 
 * @prop { number } InstrumentID
 * @prop { number | null } Bid
 * @prop { number | null } BidSize
 * @prop { number | null } Ask
 * @prop { number | null } AskSize
 * @prop { number | null } Last
 * @prop { string } Date
 * @prop { number | null } High
 * @prop { number | null } Low
 * @prop { number | null } Open
 * @prop { number | null } Volume
 * @prop { null } TodayTurnover
 * @prop { string } EnqueuedTime
 */



function createPubSub(url) {
  return new WebPubSubClient({
    getClientAccessUrl: async () => {
      const result = await (
        await fetch(url)
      ).json();

      return result;
    }
  });
}

/**
 *
 * @param {*} appSettings
 * @param {import('redux').Store<any, import('redux').AnyAction>} store
 */
async function initRealtimeTrades (store) {
  const groupsTrades = /** @type {ShareRealtime[]} */ Object.values(getAllInstrumentsSetting())
    .filter((item) => item.isRT)
    .map((item) => item.id.toString());

  if(groupsTrades.length === 0) return;
  const trades = createPubSub(window.appSettings.negotiateTradeUrl);
  const lastestTradeTimes = {};
  trades.on('group-message', ({ message }) => {
    const data = /** @type { TradesData } */(JSON.parse(message.data));
    if(!lastestTradeTimes[data.InstrumentID]) {
      lastestTradeTimes[data.InstrumentID] = data.hDate;
    }
    if(dayjs(data.hDate).isBefore(lastestTradeTimes[data.InstrumentID])) return;
    lastestTradeTimes[data.InstrumentID] = data.hDate;
    const normalize = {
      id: parseInt(data.InstrumentID),
      volume: parseInt(data.hSize),
      price: parseFloat(data.hClose),
      date: data.hDate
    };
    window.globalData.push(normalize);
    store.dispatch(
      streamTradesAction(normalize)
    );
  });

  await trades.start();
  return Promise.all(groupsTrades.map(item => trades.joinGroup(item)));
}

/**
 *
 * @param {import('redux').Store<any, import('redux').AnyAction>} store
 */
async function initRealtimeSnapshot (store) {
  const groupsSnapshot = /** @type {ShareRealtime[]} */ Object.values(getTickersSetting())
    .filter((item) => item.isRT)
    .map((item) => item.id.toString());

  if(groupsSnapshot.length === 0) return;
  
  const snapshot = createPubSub(window.appSettings.negotiateSnapshotUrl);
  const lastestSnapshotTimes = {};
  snapshot.on('group-message', ({ message }) => {
    const data = /** @type { StreamSnapshotData } */(JSON.parse(message.data));
    if(!lastestSnapshotTimes[data.InstrumentID]) {
      lastestSnapshotTimes[data.InstrumentID] = data.Date;
    }
    if(dayjs(data.Date).isBefore(lastestSnapshotTimes[data.InstrumentID])) return;
    lastestSnapshotTimes[data.InstrumentID] = data.Date;
    store.dispatch(streamSnapshotAction({
      id: data.InstrumentID,
      bid: data.Bid ?? undefined,
      bidSize: data.BidSize ?? undefined,
      ask: data.Ask ?? undefined,
      askSize: data.AskSize ?? undefined,
      date: data.Date,
      high: data.High ?? undefined,
      last: data.Last ?? undefined,
      low: data.Low ?? undefined,
      open: data.Open ?? undefined,
      volume: data.Volume ?? undefined
    }));
  });
  
  await snapshot.start();
  
  return Promise.all(groupsSnapshot.map(item => snapshot.joinGroup(item)));
}

/**
 *
 * @param {import('redux').Store<any, import('redux').AnyAction>} store
 */
export default async function initRealtime(store) {
  return Promise.all([
    initRealtimeTrades(store),
    initRealtimeSnapshot(store)
  ]);
}


