import { useEffect } from 'react';

export default function useFocusTrap(focusTrapElementRef) {
  useEffect(() => {
    const focusTrapElement = focusTrapElementRef?.current;
    if (!focusTrapElement) return;

    let doc = document;

    const getFocusableElements = () => {
      const focusableSelectors =
        'a[href], button, textarea, input, select, [tabindex]:not([tabindex="-1"])';

      const focusableElementsInTrapElement =
        focusTrapElement.querySelectorAll(focusableSelectors);
      const focusableElements = [...Array.from(focusableElementsInTrapElement)];

      return focusableElements;
    };

    const focusableElements = getFocusableElements();

    const firstFocusingElement = focusableElements[0];
    const lastFocusingElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (e) => {
      const isPressTabKey = e.key === 'Tab';
      if (!isPressTabKey) return;
      if (e.shiftKey) {
        if (doc.activeElement === firstFocusingElement) {
          lastFocusingElement.focus();
          e.preventDefault();
        }
      } else {
        if (doc.activeElement === lastFocusingElement) {
          firstFocusingElement.focus();
          e.preventDefault();
        }
      }
    };

    focusTrapElement.addEventListener('keydown', handleKeyDown);

    return () => {
      focusTrapElement.removeEventListener('keydown', handleKeyDown);
    };
  }, [focusTrapElementRef]);
}
