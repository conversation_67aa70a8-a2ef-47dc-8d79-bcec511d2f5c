﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using Euroland.NetCore.ToolsFramework.Setting.Xml;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Primitives;

namespace ShareGraph.UnitTests
{
    public static class StreamHelper
    {
        public static Stream StringToStream(string str)
        {
            var memStream = new MemoryStream();
            var textWriter = new StreamWriter(memStream);
            textWriter.Write(str);
            textWriter.Flush();
            memStream.Seek(0, SeekOrigin.Begin);

            return memStream;
        }

        public static string StreamToString(Stream stream)
        {
            stream.Seek(0, SeekOrigin.Begin);
            var reader = new StreamReader(stream);

            return reader.ReadToEnd();
        }
    }
    public class MemoryFileProvider : IFileProvider
    {
        private readonly string _content;
        private readonly string _fileName;
        public MemoryFileProvider(string content, string fileName)
        {
            _content = content;
            _fileName = fileName;
        }
        public IDirectoryContents GetDirectoryContents(string subpath)
        {
            throw new NotImplementedException();
        }

        public IFileInfo GetFileInfo(string subpath)
        {
            return new MemoryFileInfo(_content, _fileName);
        }

        public IChangeToken Watch(string filter)
        {
            throw new NotImplementedException();
        }
    }

    public class MemoryFileInfo : IFileInfo
    {
        private readonly string _content;
        private readonly string _fileName;
        public MemoryFileInfo(string content, string fileName)
        {
            _content = content;
            _fileName = fileName;
        }
        public bool Exists => true;

        public long Length => throw new NotImplementedException();

        public string PhysicalPath => _fileName;

        public string Name => _fileName;

        public DateTimeOffset LastModified => throw new NotImplementedException();

        public bool IsDirectory => throw new NotImplementedException();

        public Stream CreateReadStream()
        {
            return StreamHelper.StringToStream(_content);
        }
    }
    public class DumpXmlSettingProviderFactory : FileSettingProviderFactoryBase
    {
        public DumpXmlSettingProviderFactory(string xml) : this(xml, "test.xml") { }
        public DumpXmlSettingProviderFactory(string xml, string fileName)
        {
            FileProvider = new MemoryFileProvider(xml, "test.xml");
            EnsureFileProvider();
        }

        public override ISettingProvider Create()
        {
            return new XmlSettingProvider(this);
        }

        public override IFileInfo GetSettingFileInfo()
        {
            return FileProvider.GetFileInfo(Path);
        }
    }

}
