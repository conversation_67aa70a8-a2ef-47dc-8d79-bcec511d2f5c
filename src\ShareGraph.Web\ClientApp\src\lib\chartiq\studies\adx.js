import { CIQ } from 'chartiq/js/components';

CIQ.Euroland.Studies.calculateADX = function (stx, sd) {
  // Average Directional Index (ADX) formula: https://www.investopedia.com/terms/a/adx.asp

  var quotes,
    period,
    smoothingPeriod,
    ATR,
    smoothedPosDM,
    smoothedNegDM,
    sumDX,
    currentQuote,
    posDM,
    negDM,
    posDI,
    negDI,
    DX;
  CIQ.Studies.calculateStudyATR(stx, sd);
  quotes = sd.chart.scrubbed;
  period = sd.days;
  smoothingPeriod = parseInt(sd.inputs['Smoothing Period'], 10);
  if (!smoothingPeriod && smoothingPeriod !== 0) {
    smoothingPeriod = period;
  }
  if (quotes.length < sd.days + 1) {
    sd.error = true;
    return;
  }

  ATR = 0;
  smoothedPosDM = 0;
  smoothedNegDM = 0;
  sumDX = 0;
  for (var i = Math.max(1, sd.startFrom); i < quotes.length; i++) {
    currentQuote = quotes[i];
    posDM = Math.max(0, currentQuote.High - quotes[i - 1].High);
    negDM = Math.max(0, quotes[i - 1].Low - currentQuote.Low);
    if (posDM > negDM) {
      negDM = 0;
    } else if (negDM > posDM) {
      posDM = 0;
    } else {
      posDM = negDM = 0;
    }
    if (i <= period) {
      smoothedPosDM += posDM;
      smoothedNegDM += negDM;
      ATR += currentQuote['True Range ' + sd.name];
    } else {
      smoothedPosDM =
        (quotes[i - 1]['_sm+DM ' + sd.name] * (period - 1)) / period + posDM;
      smoothedNegDM =
        (quotes[i - 1]['_sm-DM ' + sd.name] * (period - 1)) / period + negDM;
      ATR =
        (quotes[i - 1]['_smTR ' + sd.name] * (period - 1)) / period +
        currentQuote['True Range ' + sd.name];
    }
    currentQuote['_sm+DM ' + sd.name] = smoothedPosDM;
    currentQuote['_sm-DM ' + sd.name] = smoothedNegDM;
    currentQuote['_smTR ' + sd.name] = ATR;
    if (i < period) continue;
    posDI = (smoothedPosDM * 100) / ATR;
    negDI = (smoothedNegDM * 100) / ATR;
    DX = (Math.abs(posDI - negDI) * 100) / (posDI + negDI);
    currentQuote['+DI ' + sd.name] = posDI;
    currentQuote['-DI ' + sd.name] = negDI;
    if (sd.inputs.Series !== !!0 && smoothingPeriod) {
      if (i < period + smoothingPeriod - 1) {
        if (i == sd.startFrom) {
          for (var j = period; j < sd.startFrom; j++) {
            sumDX +=
              (Math.abs(
                quotes[j]['+DI ' + sd.name] - quotes[j]['-DI ' + sd.name]
              ) *
                100) /
              (quotes[j]['+DI ' + sd.name] + quotes[j]['-DI ' + sd.name]);
          }
        }
        sumDX += DX;
      } else if (i == period + smoothingPeriod - 1) {
        currentQuote['ADX ' + sd.name] = sumDX / smoothingPeriod;
      } else {
        currentQuote['ADX ' + sd.name] =
          (quotes[i - 1]['ADX ' + sd.name] * (smoothingPeriod - 1) + DX) /
          smoothingPeriod;
      }
    }
    if (sd.inputs.Histogram) {
      if (
        !currentQuote['+DI ' + sd.name] &&
        currentQuote['+DI ' + sd.name] !== +'0'
      )
        continue;
      if (
        !currentQuote['-DI ' + sd.name] &&
        currentQuote['-DI ' + sd.name] !== +'0'
      )
        continue;
      currentQuote[sd.name + '_hist'] =
        currentQuote['+DI ' + sd.name] - currentQuote['-DI ' + sd.name];
      if (sd.inputs.Series === !{}) {
        currentQuote['+DI ' + sd.name] = null;
        currentQuote['-DI ' + sd.name] = null;
      }
      sd.outputMap[sd.name + '_hist'] = '';
    }
  }
};

CIQ.Euroland.Studies.displayADX = function (stx, sd, quotes) {
  var centered, posDIName, negDIName, posDIColor, negDIColor, yAxis, params;
  centered = sd.underlay ? 0.3 : sd.inputs.Series ? '0.4' * 1 : 1;
  if (sd.inputs.Series && sd.inputs.Shading) {
    posDIName = '+DI ' + sd.name;
    negDIName = '-DI ' + sd.name;
    posDIColor = CIQ.Studies.determineColor(
      sd.outputs[sd.outputMap[posDIName]]
    );
    negDIColor = CIQ.Studies.determineColor(
      sd.outputs[sd.outputMap[negDIName]]
    );
    yAxis = sd.getYAxis(stx);
    params = {
      topBand: posDIName,
      bottomBand: negDIName,
      topColor: posDIColor,
      bottomColor: negDIColor,
      skipTransform: stx.panels[sd.panel].name != sd.chart.name,
      topAxis: yAxis,
      bottomAxis: yAxis,
      opacity: 0.3
    };
    if (!sd.highlight && stx.highlightedDraggable) {
      params.opacity *= 0.3;
    }
    CIQ.fillIntersecting(stx, sd.panel, params);
  }
  if (sd.inputs.Histogram) {
    CIQ.Studies.createHistogram(stx, sd, quotes, false, centered);
  }
  if (sd.inputs.Series !== !{}) {
    CIQ.Studies.displaySeriesAsLine(stx, sd, quotes);
  } else if (!sd.inputs.Series && !sd.inputs.Histogram) {
    stx.displayErrorAsWatermark(
      sd.panel,
      stx.translateIf(sd.name) + ': ' + stx.translateIf('Nothing to display')
    );
  }
};
CIQ.Euroland.Studies.studyLibrary = CIQ.extend(CIQ.Studies.studyLibrary, {
  'ADX_DMS': {
    name: 'ADX/DMS',
    calculateFN: CIQ.Euroland.Studies.calculateADX,
    seriesFN: CIQ.Euroland.Studies.displayADX,
    inputs: {
      Period: 14,
      'Smoothing Period': 14,
      Series: true,
      Shading: false,
      Histogram: false
    },
    outputs: {
      '+DI': '#00FF00',
      '-DI': '#FF0000',
      ADX: 'auto',
      'Positive Bar': '#00DD00',
      'Negative Bar': '#FF0000'
    }
  }
});

export { CIQ };
