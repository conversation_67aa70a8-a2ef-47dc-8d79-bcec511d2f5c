//
// Sample translation file
//
//import { CIQ, i18n } from '../../js/standard.js';

import { CIQ, i18n } from './chartiq-import';
import { func } from 'prop-types';
import { appSettings } from '../../../appSettings';
//CIQ.activateImports(i18n);
// The default language
CIQ.I18N.language = 'en-US';
// Supported languages object, see documentation in i18n.js
CIQ.I18N.languages = {
  'en-US': 'English',
  'ar-EG': 'عربى',
  'fr-FR': 'Français',
  'de-DE': 'Deutsche',
  'hu-HU': 'Magyar',
  'it-IT': 'Italiano',
  'pt-PT': 'Português',
  'ru-RU': 'русский',
  'es-ES': 'Español',
  'zh-CN': '中文',
  'ja-JP': '日本語'
};
// Supported locale-region mapping, see documentation in i18n.js
CIQ.I18N.langConversionMap = {
  en: 'en-US',
  ar: 'ar-EG',
  fr: 'fr-FR',
  de: 'de-DE',
  hu: 'hu-HU',
  it: 'it-IT',
  pt: 'pt-PT',
  ru: 'ru-RU',
  es: 'es-ES',
  zh: 'zh-CN',
  ja: 'ja-JP'
};
//Supported languages that have full months, see documentation in i18n.js
CIQ.I18N.longMonths = { 'zh-CN': true };
// Supported languages that reverse candle color, see documentation in i18n.js
CIQ.I18N.reverseColorsByLocale = { 'zh-CN': true, 'ja-JP': true };


CIQ.I18N.setLanguage = function (stx, language, translationCallback, csv, root) {
  let wordLists = {};
  let chartTranslation = getChartTranslation();

  if (!language) {
    language = appSettings.language || 'en';
  }
  wordLists[language] = chartTranslation;
  CIQ.I18N.wordLists = wordLists;
  CIQ.I18N.language = language;
  CIQ.I18N.translateUI(language, root ?? stx?.uiContext?.topNode);
  if (!translationCallback) translationCallback = CIQ.I18N.translate;
  stx.translationCallback = translationCallback;
};

export const getChartTranslation = function () {

  ////chartTranslation
  //key_english:
  //{
  //  key: 'key_CustomPhrases',
  //  value: 'Text Display'

  //}

  //// customPhrases
  //{
  //  'key_CustomPhrases': 'Text Display Custom'
  //}


  //// Result
  //{
  //  key_english: 'Text Display Custom' || 'Text Display'
  //}
  var chartTranslation = appSettings.chartTranslation;
  var customPhrases = appSettings.customPhrases;
  var chartCustomPhrases = {};

  for (var phrase in chartTranslation) {
    let currentPhrase = chartTranslation[phrase];
    if (currentPhrase !== null && Object.keys(currentPhrase).includes('key')) {

      let customPhrase = customPhrases[currentPhrase.key];
      if (customPhrase) {
        chartCustomPhrases[phrase] = customPhrase;
      } else {
        chartCustomPhrases[phrase] = currentPhrase.value;
      }
    }
  }

  return chartCustomPhrases;
};

export { CIQ };
