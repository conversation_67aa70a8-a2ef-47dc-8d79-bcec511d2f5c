import { useContext, useEffect, useRef, useState } from 'react';
import { CIQ } from '../../scripts/components/chart/chartiq-import';

import ChartContext from '../context/ChartContext';
import { getPriceAndChangeFromQuote } from '../components/chart/custom-web-components/common';

/**
 * @typedef {import('../../../types/chart-iq').IQuote} IQuote
 * @typedef {import('../../../types/chart-iq').IChartSymbol} IChartSymbol
 * @typedef {import('../../../types/chart-iq').TCloseStatus} TCloseStatus
 * @typedef {import('react').Ref} Ref
 *
 * @returns {{
 *  symbol: IChartSymbol['symbol'],
 *  symbolDescription: string,
 *  currentPrice: string,
 *  todaysChange: string,
 *  todaysChangePct: string,
 *  symbolObject: IChartSymbol,
 *  currentQuote: IQuote,
 *  closeStatus: TCloseStatus,
 *  ref: Ref
 * }}
 */
const useChartTitle = () => {
  const {
    chart: [chartEngine]
  } = useContext(ChartContext);
  const currentPriceDOMRef = useRef();
  const oldPrice = useRef({});

  const [chartTitle, setChartTitle] = useState({
    symbol: '',
    symbolDescription: '',
    currentPrice: '',
    todaysChange: '',
    todaysChangePct: '',
    symbolObject: {},
    currentQuote: {},
    closeStatus: ''
  });

  useEffect(() => {
    if (!chartEngine) return;
    const createDataSetRefId = chartEngine.append('createDataSet', function () {
      const data = {};
      const stx = chartEngine;
      data.symbol = stx.chart.symbol;
      data.symbolDescription = stx.chart.symbolDisplay;
      const currentQuote = stx.getFirstLastDataRecord(stx.chart.dataSet, 'Close', true);

      const {
        price: currentPrice,
        changeString: todaysChange,
        changePctString: todaysChangePct,
        closeStatus
      } = getPriceAndChangeFromQuote(currentQuote, stx);

      data.currentPrice = currentPrice;
      data.todaysChangePct = todaysChangePct;
      data.todaysChange = todaysChange;
      data.currentQuote = currentQuote;
      data.closeStatus = closeStatus;
      data.symbolObject = stx.chart.symbolObject;

      if (
        currentPriceDOMRef.current &&
        oldPrice.current.instrumentId === stx.chart.symbolObject.instrumentId &&
        oldPrice.current.currentPrice !== currentPrice
      ) {
        CIQ.UI.animatePrice(currentPriceDOMRef.current, currentPrice, oldPrice.current.currentPrice, false);
      }

      oldPrice.current = { currentPrice, ...data.symbolObject };
      setChartTitle(data);
    });

    return () => {
      chartEngine.removeInjection(createDataSetRefId);
    };
  }, [chartEngine]);

  return { ...chartTitle, ref: currentPriceDOMRef };
};

export default useChartTitle;
