import {gql} from './utils';

export const GET_SHARE_PRICE_DEVELOPMENT = gql(/* GraphQL */`query SharePriceDevelopment($ids: [Int!]!,$adjClose: Boolean, $toCurrency: String) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    ...SharePriceDevelopmentData
  }
}

fragment SharePriceDevelopmentData on Instrument {
  id
  shareName
  currency {
    code
    name
  }
  currentPrice {
    last
    change
  }
  _52W: performance(period: FIFTY_TWO_WEEKS) {
    highest
    lowest
    changePercentage
  }
  _allTime: performance(period: CUSTOM, fromDate: "1900-01-01T00:00:00Z") {
    highest
    lowest
  }
  _week: performance(period: ONE_WEEK) {
    changePercentage
  }
  _month: performance(period: ONE_MONTH) {
    changePercentage
  }
  _threeMonths: performance(period: THREE_MONTHS) {
    changePercentage
  }
  _sixMonths: performance(period: SIX_MONTHS) {
    changePercentage
  }
  _ytd: performance(period: YTD) {
    changePercentage
  }
  _threeYears: performance(period: THREE_YEARS) {
    changePercentage
  }
  _fiveYears: performance(period: FIVE_YEARS) {
    changePercentage
  }
  market {
    abbreviation
  }
  _tenYears: performance(period: TEN_YEARS) {
    changePercentage
  }
}`);