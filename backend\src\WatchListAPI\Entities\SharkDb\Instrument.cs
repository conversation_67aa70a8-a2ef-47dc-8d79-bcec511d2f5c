﻿using System.ComponentModel.DataAnnotations.Schema;

namespace WatchListAPI.Entities.SharkDb
{
    public class Instrument
    {
        public int Id { get; set; }
        [Column("InstrumentType")]
        public byte? InstrumentTypeId { get; set; }
        public short MarketId { get; set; }
        public int? CompanyId { get; set; }
        [Column("customer")]
        public byte? CustomerTypeId { get; set; }
        public string? CurrencyCode { get; set; }
        public string? Ticker { get; set; }
        public string? Name { get; set; }
        public string? ISIN { get; set; }
        public byte? PrimaryMarket { get; set; }
    }
}
