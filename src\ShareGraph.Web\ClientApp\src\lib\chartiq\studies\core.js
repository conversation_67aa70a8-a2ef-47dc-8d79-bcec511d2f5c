import { CIQ } from 'chartiq/js/components';

CIQ.Euroland = CIQ.Euroland || function () {};

CIQ.Euroland.Studies = CIQ.Euroland.Studies || function () {};
CIQ.Euroland.Studies.createStudy = function (name, studyProps) {
  if (!name) throw new Error('Must provide a valid study name.');
  var studyName = name;

  CIQ.Euroland.Studies[name] = studyName;
  CIQ.Studies.studyLibrary[studyName] = CIQ.extend({}, studyProps);
};

export { CIQ };
