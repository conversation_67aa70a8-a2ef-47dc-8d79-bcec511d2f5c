import { useContext, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';

import { AppContext } from '../../AppContext';
import { TICKER_ANIMATION } from '../../common';
import { usePrevious } from '../../customHooks';
import { default as templateTableDefault } from '../../../markup/ticker/TABLE_TICKER_SINGLE.html';
import {
  classByValue,
  classNames,
  convertChangePercentDecimal,
  convertMinutesToString,
  convertNumber,
  convertNumberDecimal,
  convertPercentDecimal,
  formatChangeNumber,
  formatDateTime,
  getCountDownMinutesOpenMarket,
  getCustomPhraseTicker,
  getOpenDateTimeByMinute,
  i18nTranslate,
  marketStatusByValue,
  replaceKey
} from '../../helper';
import i18n from '../../services/i18n';

const TableTicker = ({ className = '', data, tickerAnimation, onItemClick = () => {} }) => {
  const prevData = usePrevious(data);
  const settings = useContext(AppContext);
  const containerRef = useRef(null);
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);

  function handleInstrumentIdSelect(e) {
    const instrumentId = e.target.closest('.table__body-tr').dataset.instrumentid;
    onItemClick(parseInt(instrumentId));
  }

  const addAnimation = (type, fieldChange, item) => {
    if (prevData && item) {
      const instrumentId = item.getAttribute('data-instrumentId');
      let prevCurrentData = prevData?.find(e => e.instrumentId == instrumentId);
      let currentData = data?.find(e => e.instrumentId == instrumentId);
      if (!currentData || !prevCurrentData) return;
      let prevItem = _normalizeData(prevCurrentData);
      let currentItem = _normalizeData(currentData);
      //add animation for change
      let changeValue = currentItem[fieldChange] - prevItem[fieldChange] + 1;
      if (changeValue !== 0) {
        const queryType = `.ticker__change--${type}`;
        const queryChangeValue = queryType + ' .ticker__change-value';
        const queryAnimation = queryType + ' .ticker__animation-inner';
        const queryTransform = `transforming-top-${type}`;
        item.querySelector(queryChangeValue).innerText = prevItem[fieldChange];
        const cloneChangeNode = item.querySelector(queryChangeValue).cloneNode();
        cloneChangeNode.innerText = currentItem[fieldChange];
        if (item.querySelectorAll(`${queryChangeValue}`).length < 2) {
          item.querySelector(`${queryAnimation}`).append(cloneChangeNode);
        }
        changeValue >= 0
          ? item.classList.add(`${queryTransform}`, `${queryTransform}--increase`)
          : item.classList.add('transforming-top', `${queryTransform}--decrease`);
        setTimeout(() => {
          item.classList.remove(
            `${queryTransform}`,
            `${queryTransform}--increase`,
            `${queryTransform}--decrease`
          );
          item.querySelectorAll(`${queryChangeValue}`).length >= 2 &&
            item.querySelector(`${queryChangeValue}:first-child`).remove();
        }, 1000);
      }
    }
  };

  useEffect(() => {
    let currentNode = containerRef.current;
    containerRef.current &&
      containerRef.current.querySelectorAll('.table__body-tr').forEach(item => {
        if (!item) return;
        item.addEventListener('click', handleInstrumentIdSelect);
      });

    return () => {
      currentNode &&



        currentNode.querySelectorAll('.table__body-tr').forEach(item => {
          if (!item) return;
          if (tickerAnimation.toUpperCase() === TICKER_ANIMATION.TRANSFORM) {
            addAnimation('number', 'change', item);
            addAnimation('percent', 'changePercentage', item);
          }
          item.removeEventListener('click', handleInstrumentIdSelect);
        });
    };
  });

  function _getTemplateHtml() {
    const templateTable = settings.ticker.tableTickerTemplate || templateTableDefault;
    let tableHTMLDOM = new DOMParser().parseFromString(templateTable, 'text/html');
    const tableRowHTML = tableHTMLDOM.querySelector('.table__body-tr');
    if (!tableRowHTML) return;
    tableRowHTML.setAttribute('data-instrumentId', '{instrumentId}');
    tableRowHTML.classList.add(
      '{instrumentSelected}',
      '{changeStatus}',
      '{changePerStatus}',
      '{marketStatus}'
    );
    return tableRowHTML.outerHTML;
  }

  function _renderTemplate(item) {
    if (item) {
      const tableRow = _getTemplateHtml();
      if (!tableRow) return '<h2>Table HTML template not recognized</h2>';
      const labels = _getLabel();
      const templateTable = settings.ticker.tableTickerTemplate;
      let templateHeading = replaceKey(templateTable, labels);
      let tableHTMLDOM = new DOMParser().parseFromString(templateHeading, 'text/html');
      tableHTMLDOM.querySelector('.table__body-tr').remove();
      let content = data
        .map(x => {
          const dataTicker = _normalizeData(x);
          return replaceKey(tableRow, dataTicker);
        })
        .join('');
      tableHTMLDOM.querySelector('.table__body').innerHTML = '';
      tableHTMLDOM.querySelector('.table__body').innerHTML = content;
      return tableHTMLDOM.querySelector('.table').outerHTML;
    }

    return '<h2> No data</h2>';
  }

  function _normalizeData(item) {
    //TODO Add time;
    const data = { ...item };
    const countDownMinutes = getCountDownMinutesOpenMarket(item);
    data.bid = convertNumberDecimal(item.bid);
    data.ask = convertNumberDecimal(item.ask);
    data.change = formatChangeNumber(item.change);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = convertNumberDecimal(Math.max(item.high, item.last));
    data.high52W = convertNumberDecimal(Math.max(item.high52W, item.last));
    data.last = convertNumberDecimal(item.last);
    data.low = convertNumberDecimal(Math.min(item.low, item.last));
    data.low52W = convertNumberDecimal(Math.min(item.low52W, item.last));
    data.open = convertNumberDecimal(item.open);
    data.percent52W = convertPercentDecimal(item.percent52W);
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatChangeNumber(item.volumeChange);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.instrumentSelected = selectedInstrumentId === item.instrumentId ? 'selected' : '';
    data.changeStatus = classByValue(item.change);
    data.changePerStatus = 'day-range--' + classByValue(item.changePercentage);
    data.marketStatus = marketStatusByValue(item.marketStatus);
    data.timeToCloseMarket = convertMinutesToString(
      countDownMinutes,
      i18n.translate('hrs'),
      i18n.translate('mins')
    );
    data.dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, item.id);
    return getCustomPhraseTicker(data);
  }

  function _getLabel() {
    const singleLabels = [
      'w52RangeLabel',
      'volumeLabel',
      'bidAskLabel',
      'marketCloseLabel',
      'marketOpenedLabel',
      'openLabel',
      'highLabel',
      'lowLabel',
      'marketOpenInLabel',
      'sharesLabel',
      'lastLabel',
      'changePercentageLabel',
      'changeLabel',
      'weeks52HighLabel'
    ];

    return {
      dayRangeLabel: i18n.translate('rangeLabel'),
      ...i18nTranslate(singleLabels)
    };
  }
  return (
    <div
      className={classNames(className, `table-responsive table-responsive--${tickerAnimation.toLowerCase()} ticker__inner--${tickerAnimation.toLowerCase()}`)}
      ref={containerRef}
      dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}
    ></div>
  );
};

export default TableTicker;
