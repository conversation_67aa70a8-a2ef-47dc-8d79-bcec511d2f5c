using AutoMapper;
using Euroland.FlipIT.ShareGraph.API.Entities;

namespace Euroland.FlipIT.ShareGraph.API.Mappers
{
  public static class ConfigurationMappers
  {
    internal static IMapper Mapper { get; }

    static ConfigurationMappers()
    {
      Mapper = new MapperConfiguration(cfg => cfg.AddProfile<ConfigurationMapperProfile>())
          .CreateMapper();
    }

    public static Performance ToModel(this PerformanceConfig source)
    {
      return source == null ? null : Mapper.Map<Performance>(source);
    }

    public static Chart ToModel(this ChartConfig source)
    {
      return source == null ? null : Mapper.Map<Chart>(source);
    }

    public static ShareDetails ToModel(this ShareDetailsConfig source)
    {
      return source == null ? null : Mapper.Map<ShareDetails>(source);
    }

    public static Ticker ToModel(this TickerConfig source)
    {
      return source == null ? null : Mapper.Map<Ticker>(source);
    }
  }
}
