import i18n from '../../../services/i18n';
import appConfig from '../../../services/app-config';
import {EVENT_TYPES} from '../../../common';
import {EventHandler, EventLoader, EventMarker, EventNode} from './marker';
import {client, clientRT} from '../../../services/graphql-client';
import {EARNING_EVENTS_PAGING_QUERY} from '../../../graphql-queries/earningEventsQuery';
import {appSettings} from '../../../../appSettings';


class EarningEventMarker extends EventMarker {
  constructor(options) {
    super({...options, label: EVENT_TYPES.EARNING});
    this.contentEl.innerHTML = i18n.translate('marketAbbreviate');
    this.mainNode.classList.add(EVENT_TYPES.EARNING);
  }

  initializeTooltipContent() {
    super.initializeTooltipContent();
    this.tooltipInnerEl = this.tooltipEl;
    this.tooltipItemEl = this.mainNode.querySelector('.item');
  }
}

/**
 * @typedef {{isRT:boolean}} EarningEventOptions
 */

export class EarningEventLoader extends EventLoader {
  async fetch(cursor) {
    const result = await (this.options.isRT ? clientRT : client).query(EARNING_EVENTS_PAGING_QUERY, {
      companyCode: appSettings.companyCode,
      fromDate: this.from,
      toDate: this.to,
      cursor,
      cultureName: appSettings.language || 'en-gb'  }, { fetchOptions: { signal: this.abortController.signal }});

    const fcEventsByTypes = result.data?.company.fcEventsByTypes;
    const hasNextPage = fcEventsByTypes?.pageInfo.hasNextPage;
    let endCursor;
    const records = fcEventsByTypes?.edges?.map(item => ({date: item.node.dateTime, Date: new Date(item.node.dateTime), title: item.node.eventName})) ?? [];

    if(fcEventsByTypes && hasNextPage) endCursor = fcEventsByTypes.pageInfo.endCursor;

    return {
      data: records,
      endCursor
    };
  }
}

export class EarningEventNode extends EventNode {
  createMarker(date, records) {
    return new EarningEventMarker({
      setting: this.setting,
      stx: this.stx,
      template: this.template,
      eventCollection: this.eventCollection,
      date,
      records
    });
  }
}
export class EarningEventHandler extends EventHandler {
  eventLoaderCreator(from, to) {
    return EarningEventLoader.create(
      from,
      to,
      this.options,
      this.eventNode.onLoad.bind(this.eventNode)
    );
  }

  eventNodeCreator() {
    return new EarningEventNode(
      this.stx,
      document.querySelector('#chartEventPrototype'),
      appConfig.get()
    );
  }
}

export const EarningFactor = {
  _cache: undefined,
  /**
   *
   * @param {any} stx
   * @param {EarningEventOptions} options
   * @returns
   */
  get(stx, options) {
    if(!this._cache) {
      this._cache = new EarningEventHandler(stx, options);
    }
    return this._cache;
  }
};