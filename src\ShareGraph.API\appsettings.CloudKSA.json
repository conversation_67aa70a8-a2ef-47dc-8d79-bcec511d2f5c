{"ConnectionStrings": {"ToolsConfig": "DefaultEndpointsProtocol=https;AccountName=netoolssettings;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "TranslationDbContext": "Server=buffalo;Database=shark;User=ushark;PWD=**********;TrustServerCertificate=true;Application Name=ShareGraph.API;"}, "Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**************", "port": "5141", "appName": "FlipIT.ShareGraph.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.ShareGraph.API"}}, "UseAzureFileSettingStorage": true, "AzureFileShareName": "tools-settings", "GeneralSettingsPath": "config", "ToolSettingsPath": "tools\\sharegraph3\\config", "InternalSDataApiUrl": "http://localhost/tools/sdata-api/graphql"}