using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using WatchListAPI.Common.Extensions;
using WatchListAPI.Common.Middlewares;
using WatchListAPI.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

var logger = new LoggerConfiguration()
          .WriteTo.Console()
          .WriteTo.File("logs/log.txt", rollingInterval: RollingInterval.Day)
          .ReadFrom.Configuration(builder.Configuration)
          .CreateLogger();

builder.Logging.AddSerilog(logger);

builder.Services.AddCustomDbContexts(builder.Configuration);

builder.Services.RegisterServices();
builder.Services.RegisterRepositories();
builder.Services.AddUnitOfWorks();
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerService();
builder.Services.AddHttpContextAccessor();
builder.Services.AddHttpClientService(builder.Configuration);
builder.Services.AddHealthChecks();
builder.Services.AddKeycloakAuthentication(builder.Configuration);
builder.Services.AddConfiguredCors(builder.Configuration);

builder.Services.Configure<SecurityStampValidatorOptions>(options =>
{
    options.ValidationInterval = TimeSpan.FromMinutes(1);
});

builder.Services.AddControllers();
//// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
//builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen();

var app = builder.Build();
var env = builder.Environment;
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("CorsPolicy");
app.UseStaticFiles();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHealthChecks("/health");
app.UseMiddleware<GlobalResponseMiddleware>();
TokenExtentions.Configure(app.Services.GetRequiredService<IHttpContextAccessor>());

app.Run();
