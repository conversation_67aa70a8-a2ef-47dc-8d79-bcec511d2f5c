import { useLayoutEffect, useState } from 'react';

const useResize = () => {
  const [width, setWidth] = useState();
  useLayoutEffect(() => {
    const handleResize = e => {
      setWidth(e.target.innerWidth);
    };
    setWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return { width };
};

/*
 screen width : 320 xs
 screen width : 428 sm
 screen width : 768 md
 screen width : 1366 lg
 screen width : 1920 xl 
 */
/**
 * @returns {{xs: boolean, sm: boolean, md: boolean, lg: boolean, xl: boolean}}
 */
export const useBreakpoint = () => {
  const initialValues = { xs: false, sm: false, md: false, lg: false, xl: false };
  const [breakpoint, setBreakpoint] = useState(initialValues);
  const { width } = useResize();

  useLayoutEffect(() => {
    if (width >= 1920) {
      setBreakpoint({ ...initialValues, xl: true });
      return;
    }
    if (width >= 1366) {
      setBreakpoint({ ...initialValues, lg: true });
      return;
    }
    if (width >= 768) {
      setBreakpoint({ ...initialValues, md: true });
      return;
    }
    if (width >= 428) {
      setBreakpoint({ ...initialValues, sm: true });
      return;
    }
    setBreakpoint({ ...initialValues, xs: true });
  }, [width]);

  return breakpoint;
};

export class UserAgent {
  static get nav() {
    return typeof navigator !== 'undefined' ? navigator : { userAgent: '' };
  }
  static get userAgent() {
    return this.nav.userAgent;
  }
  static get ipad() {
    return (
      this.userAgent.indexOf('iPad') != -1  ||
      (this.nav.platform === 'MacIntel' && this.nav.maxTouchPoints > 1)
    ); 
  }

  static get iphone() {
    return this.userAgent.indexOf('iPhone') != -1;
  }
  static get isAndroid() {
    return this.userAgent.toLowerCase().indexOf('android') > -1;
  }
  static get isSafari() {
    return this.userAgent.indexOf('Safari/') > -1;
  }
  static get isMobile() {
    return this.ipad || this.iphone || this.isAndroid;
  }
}

export default useResize;
