import  { useEffect, useContext, useLayoutEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Chart from '../components/chart/Chart';
import { AppContext } from '../AppContext';
import { selectTicker, fetchTickers } from '../actions/tickerActions';
import { fetchPeers} from '../actions/peerActions';
import { fetchIndices } from '../actions/indicesActions';
import ChartContext from '../context/ChartContext';
import { switchCurrency } from '../actions/currencyAction';

export default function PopoutComponent() {
  const settings = useContext(AppContext);
  const dispatch = useDispatch();
  const isTickerInfoLoading = useSelector(state => state.tickers.loading);
  const isPeersInfoLoading = useSelector(state => state.peers.loading);
  const isIndicesInfoLoading = useSelector(state => state.indices.loading);
  const chartState = useState(null);
  const chartContext = useState(null);

  const scrollPopout =  () => {
    document.querySelector('body').classList.add('pop-out');
  };

  useLayoutEffect(() => {
    const selectedCurrency = window.xprops.data.selectedCurrency;
    if(selectedCurrency){
      dispatch(switchCurrency(selectedCurrency));
    }
    dispatch(fetchTickers());
    dispatch(fetchPeers());
    dispatch(fetchIndices());
    dispatch(selectTicker(window.xprops.data.mainTickerId));
    scrollPopout();
  }, []);
  
  useEffect(() => {
    if(!chartState[0]) return;
    // let customStorageState = JSON.parse(CustomLocalStorage.getItem(chartState[0].customStorageKey)) || {};
    // const {peers,indices} = customStorageState;

    // if(!peers) {
    //   setCustomItemLocalStorage(chartState[0].customStorageKey, {peers: window.xprops.data.peers});
    // }
    // if(!indices) {
    //   setCustomItemLocalStorage(chartState[0].customStorageKey, {indices: window.xprops.data.indices});
    // }
  }, [chartState[0]]);

  return (
    <div className='app__inner '>
      <div className='graph'>
      {!isTickerInfoLoading && !isPeersInfoLoading && !isIndicesInfoLoading && <ChartContext.Provider value={{chart: chartState, chartContext}}>
         <Chart 
         isPopout 
         settings={settings} 
         selectedRange={window.xprops.data.selectedRange}
         currentEvent={window.xprops.data.currentEvent}
         layout={window.xprops.data.layout}
         preferences={window.xprops.data.preferences }
         drawings={window.xprops.data.drawings}
          />
    </ChartContext.Provider>} 
      </div>
    </div>
  );
}
