import { useContext, useEffect, useState } from 'react';
import { CIQ } from '../chartiq-import';

import ChartContext from '../context/ChartContext';
import { convertNumber, translateStringFormat } from '../helper';

/**
 * @typedef {import('../../../types/chart-iq').IQuote} IQuote
 *
 * @returns {{
 *  DT: IQuote['DT'],
 *  price: IQuote['Close'],
 *  currency: string,
 *  open: IQuote['Open'],
 *  close: IQuote['Close'],
 *  high: IQuote['Low'],
 *  low: IQuote['Low'],
 *  volume: IQuote['Volume'],
 * }}
 */
const useHeadsUpHR = (callback = () => {}) => {
  const {
    chart: [chartEngine]
  } = useContext(ChartContext);

  const [headsUpHR, setHeadsUpHR] = useState({});
  function valOrNA(text) {
    return CIQ.isValidNumber(parseFloat(text)) ? text : 'N/A';
  }

  useEffect(() => {
    if (!chartEngine) return;
    const headsUpHRRefId = chartEngine.append('headsUpHR', function () {
      const stx = chartEngine;
      const bar = stx.barFromPixel(stx.cx);
      const prices = stx.chart.xaxis[bar];
      if (!prices || !prices.data) {
        setHeadsUpHR({});
        return;
      }

      const quote = CIQ.clone(prices.data);

      function formatPrice(value) {
        let numStr = '';
        let chartScale = stx.layout.chartScale,
          panel = stx.chart.panel,
          yAxis = stx.chart.yAxis;
        if (yAxis.originalPriceFormatter && yAxis.originalPriceFormatter.func) {
          numStr = yAxis.originalPriceFormatter.func(stx, panel, value);
        } else if (yAxis.priceFormatter && chartScale != 'percent' && chartScale != 'relative') {
          numStr = yAxis.priceFormatter(stx, panel, value);
        } else {
          numStr = stx.formatYAxisPrice(value, panel);
        }
        // return numStr.replace(/(\.[0-9]*[1-9])0+$|\.0*$/, '$1');
        return numStr;
      }
      //   console.log('headsUpHR', prices);
      let plotField = stx.chart.defaultPlotField;
      let highLowBars = stx.chart.highLowBars;
      if (!plotField || highLowBars) plotField = 'Close';
      const currency = translateStringFormat('currency', [stx.chart.symbolObject.currencyCode]);
      const open = formatPrice(valOrNA(quote.Open));
      const price = formatPrice(valOrNA(quote[plotField]));
      const close = formatPrice(valOrNA(quote.Close));
      const high = formatPrice(valOrNA(quote.High));
      const low = formatPrice(valOrNA(quote.Low));

      let volume = CIQ.condenseInt(quote.Volume);
      volume = convertNumber(quote.Volume);
      const rollup = volume.charAt(volume.length - 1);
      if (rollup > '9') {
        volume = volume.substring(0, volume.length - 1);
      }
      setHeadsUpHR({ DT: prices.DT, price, currency, open, close, high, low, volume });
      callback();
    });

    return () => {
      chartEngine.removeInjection(headsUpHRRefId);
    };
  }, [chartEngine]);

  return headsUpHR;
};

export default useHeadsUpHR;
