import { useEffect, useMemo, useState } from "preact/hooks";
import {
  Plus,
  X,
  Edit2,
  Check,
  Search,
  TrendingUp,
  TrendingDown,
  Trash2,
} from "lucide-preact";
import { toast } from "react-toastify";
import type { Instrument, Watchlist } from "../services/watchlistTypes";
import { useWatchlistQueries } from "../services/watchlistQueries";
import type { TargetedEvent } from "preact/compat";
import {
  INSTRUMENTS_QUERY,
  type GraphQLInstrument,
} from "../services/graphql/queries";
import { useQuery } from "urql";
import VirtualList from "rc-virtual-list";
import { debounce } from "es-toolkit";
import { instrumentService } from "../services/instrumentService";

type InstrumentRemap = {
  id: string;
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  currency: string;
  market: string;
  marketStatus: string;
  fiftyTwoWeeks: string;
};
const Watchlist = () => {
  const {
    watchlistsQuery,
    createWatchlistMutation,
    updateWatchlistMutation,
    deleteWatchlistMutation,
    addInstrumentMutation,
    removeInstrumentMutation,
  } = useWatchlistQueries();

  const { data: watchlists, isLoading, error } = watchlistsQuery;
  const watchlistsArray = useMemo(() => {
    return watchlists?.data || [];
  }, [watchlists?.data]);
  console.log({ watchlists });
  const [activeWatchlistId, setActiveWatchlistId] = useState<string | null>(
    null
  );
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newWatchlistName, setNewWatchlistName] = useState("");
  const [editingWatchlistId, setEditingWatchlistId] = useState<string>("");
  const [editedName, setEditedName] = useState("");
  const [instrumentQuery, setInstrumentQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Instrument[]>([]);

  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string>("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  // Search handler with debounce
  const debouncedSetInstrumentQuery = debounce((value: string) => setInstrumentQuery(value), 500)
  useEffect(() => {
    instrumentService.searchInstruments(1, 25, instrumentQuery).then((data) => {
      setSearchResults(data);
    });
  }, [instrumentQuery]);
  console.log({ instrumentQuery });
  useEffect(() => {
    if (watchlistsArray.length > 0 && activeWatchlistId === null) {
      setActiveWatchlistId(watchlistsArray[0].id);
    }

    if (activeWatchlistId && watchlistsArray.length > 0) {
      const exists = watchlistsArray.some(
        (w: Watchlist) => w.id === activeWatchlistId
      );
      if (!exists) {
        setActiveWatchlistId(watchlistsArray[0].id);
      }
    }
  }, [watchlistsArray, activeWatchlistId]);

  const activeWatchlist = useMemo(() => {
    if (!activeWatchlistId || watchlistsArray.length === 0) return null;
    return (
      watchlistsArray.find((w: Watchlist) => w.id === activeWatchlistId) || null
    );
  }, [watchlistsArray, activeWatchlistId]);

  const [result] = useQuery({
    query: INSTRUMENTS_QUERY,
    variables: {
      adjClose: false,
      ids: activeWatchlist?.instrumentIds || [],
    },
  });

  const { data: instrumentDatas, fetching } = result;

  // Process instrument data from GraphQL response
  const instruments: InstrumentRemap[] = useMemo(() => {
    if (!instrumentDatas?.instrumentByIds) return [];

    return instrumentDatas.instrumentByIds
      ?.filter((instrument: GraphQLInstrument) => instrument !== null)
      .map((instrument: GraphQLInstrument) => ({
        id: instrument?.id?.toString(),
        symbol: instrument?.symbol,
        name: instrument?.shareName,
        price: instrument?.currentPrice?.tickerData?.last || 0,
        change: instrument?.currentPrice?.tickerData?.change || 0,
        changePercent:
          instrument?.currentPrice?.tickerData?.changePercentage || 0,
        volume: instrument?.currentPrice?.volume || 0,
        currency: instrument?.currency?.code,
        market: instrument?.market?.translation?.value,
        marketStatus: instrument?.market?.status,
        fiftyTwoWeeks: instrument?.fifty_two_weeks,
      }));
  }, [instrumentDatas]);

  const handleStartEdit = (watchlistId: string, currentName: string) => {
    setEditingWatchlistId(watchlistId);
    setEditedName(currentName);
  };

  const handleCancelEdit = () => {
    setEditingWatchlistId("");
    setEditedName("");
  };

  const handleRemoveInstrument = async (instrumentId: string) => {
    if (!activeWatchlistId) return;

    try {
      removeInstrumentMutation.mutate({
        watchlistId: activeWatchlistId,
        instrumentId: parseInt(instrumentId),
      });
      toast.success("Instrument removed from watchlist");
    } catch {
      toast.error("Failed to remove instrument");
    }
  };

  const handleCreateWatchlist = async () => {
    if (!newWatchlistName.trim()) {
      toast.error("Watchlist name cannot be empty");
      return;
    }
    try {
      createWatchlistMutation.mutate(newWatchlistName);
      setNewWatchlistName("");
      setShowCreateForm(false);
      toast.success("Watchlist created successfully");
    } catch {
      toast.error("Failed to create watchlist");
    }
  };

  const handleUpdateWatchlistName = async (watchlistId: string) => {
    if (!editedName.trim()) {
      toast.error("Watchlist name cannot be empty");
      return;
    }
    try {
      updateWatchlistMutation.mutate({ id: watchlistId, name: editedName });
      setEditingWatchlistId("");
      toast.success("Watchlist updated successfully");
    } catch {
      toast.error("Failed to update watchlist");
    }
  };

  const handleDeleteWatchlist = async (id: string) => {
    try {
      deleteWatchlistMutation.mutate(id);
      toast.success("Watchlist deleted successfully");
    } catch {
      toast.error("Failed to delete watchlist");
    }
  };
  const handleAddInstrument = async (instrument: Instrument) => {
    try {
      addInstrumentMutation.mutate({
        instrumentId: parseInt(instrument.id),
        watchlistId: activeWatchlistId as string,
      });
      setInstrumentQuery("");
      setSearchResults([]);
      toast.success(`${instrument.symbol} added to watchlist`);
    } catch {
      toast.error(`Failed to add ${instrument.symbol}`);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading watchlists</div>;

  // Show empty state when no watchlists exist
  if (!isLoading && watchlistsArray.length === 0) {
    return (
      <div className="watchlist-container">
        <div className="empty-watchlist-state">
          <div className="empty-watchlist-content">
            <div className="empty-watchlist-icon">
              <TrendingUp size={64} />
            </div>
            <h2 className="empty-watchlist-title">No Watchlists Yet</h2>
            <p className="empty-watchlist-description">
              Create your first watchlist to start tracking your favorite
              instruments
            </p>
            <button
              className="create-first-watchlist-btn"
              onClick={() => setShowCreateForm(true)}
            >
              <Plus size={16} />
              Create Your First Watchlist
            </button>

            {/* Create Form */}
            {showCreateForm && (
              <div className="first-watchlist-form">
                <input
                  type="text"
                  value={newWatchlistName}
                  onChange={(e: TargetedEvent<HTMLInputElement>) =>
                    setNewWatchlistName(e.currentTarget.value)
                  }
                  placeholder="Enter watchlist name"
                  className="first-watchlist-input"
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === "Enter") handleCreateWatchlist();
                    if (e.key === "Escape") {
                      setShowCreateForm(false);
                      setNewWatchlistName("");
                    }
                  }}
                />
                <div className="first-watchlist-actions">
                  <button
                    onClick={handleCreateWatchlist}
                    className="confirm-first-btn"
                  >
                    <Check size={16} />
                    Create
                  </button>
                  <button
                    onClick={() => {
                      setShowCreateForm(false);
                      setNewWatchlistName("");
                    }}
                    className="cancel-first-btn"
                  >
                    <X size={16} />
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="watchlist-container">
      {/* Tab Bar */}
      <div className="tab-bar">
        <div className="tabs-container">
          {watchlistsArray.map((watchlist: Watchlist) => (
            <div
              key={watchlist.id}
              className={`tab ${
                activeWatchlistId === watchlist.id ? "active" : ""
              }`}
              onClick={() => setActiveWatchlistId(watchlist.id)}
            >
              {editingWatchlistId === watchlist.id ? (
                <div
                  className="tab-edit-form"
                  onClick={(e) => e.stopPropagation()}
                >
                  <input
                    type="text"
                    value={editedName}
                    onChange={(e: TargetedEvent<HTMLInputElement>) =>
                      setEditedName(e.currentTarget.value)
                    }
                    className="tab-edit-input"
                    autoFocus
                    onKeyDown={(e) => {
                      if (e.key === "Enter")
                        handleUpdateWatchlistName(watchlist.id);
                      if (e.key === "Escape") handleCancelEdit();
                    }}
                    onBlur={() => handleUpdateWatchlistName(watchlist.id)}
                  />
                </div>
              ) : (
                <>
                  <div className="tab-content">
                    <span className="tab-name">{watchlist.name}</span>
                    {/* <span className="tab-count">({watchlist.instruments.length})</span> */}
                  </div>
                  <div className="tab-actions">
                    <button
                      className="tab-action-btn edit-tab-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStartEdit(watchlist.id, watchlist.name);
                      }}
                    >
                      <Edit2 size={12} />
                    </button>
                    {showDeleteConfirm === watchlist.id ? (
                      <div
                        className="delete-confirm"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <button
                          className="confirm-delete-btn"
                          onClick={() => handleDeleteWatchlist(watchlist.id)}
                        >
                          <Check size={12} />
                        </button>
                        <button
                          className="cancel-delete-btn"
                          onClick={() => setShowDeleteConfirm("")}
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ) : (
                      <button
                        className="tab-action-btn close-tab-btn"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (watchlistsArray.length <= 1) return;
                          setShowDeleteConfirm(watchlist.id);
                        }}
                        disabled={watchlistsArray.length <= 1}
                      >
                        <X size={12} />
                      </button>
                    )}
                  </div>
                </>
              )}
            </div>
          ))}

          {/* Add Tab Button */}
          {showCreateForm ? (
            <div className="add-tab-form">
              <input
                type="text"
                value={newWatchlistName}
                onChange={(e: TargetedEvent<HTMLInputElement>) =>
                  setNewWatchlistName(e.currentTarget.value)
                }
                placeholder="Watchlist name"
                className="add-tab-input"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleCreateWatchlist();
                  if (e.key === "Escape") {
                    setShowCreateForm(false);
                    setNewWatchlistName("");
                  }
                }}
              />
              <div className="add-tab-actions">
                <button
                  onClick={handleCreateWatchlist}
                  className="confirm-add-btn"
                >
                  <Check size={12} />
                </button>
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    setNewWatchlistName("");
                  }}
                  className="cancel-add-btn"
                >
                  <X size={12} />
                </button>
              </div>
            </div>
          ) : (
            <button
              className="add-tab-btn"
              onClick={() => setShowCreateForm(true)}
            >
              <Plus size={16} />
            </button>
          )}
        </div>
      </div>
      {/* Add Instrument Section */}
      <div className="add-instrument-section">
        <h4 className="add-section-title">Add Instrument</h4>
        <div className="search-container">
          <Search size={16} className="search-icon" />
          <input
            type="text"
            value={instrumentQuery}
            onChange={(e: TargetedEvent<HTMLInputElement>) => {
              const value = e.currentTarget.value;
              debouncedSetInstrumentQuery(value);
            }}
            onFocus={() => setIsSearchFocused(true)}
            onBlur={() => setIsSearchFocused(false)}
            placeholder="Search instruments to add..."
            className="search-input"
          />
        </div>
        {isSearchFocused && searchResults.length > 0 && (
          <div
            className="search-results"
            onMouseDown={(e) => e.preventDefault()}
          >
            <VirtualList
              data={searchResults}
              height={200}
              itemHeight={60}
              itemKey="id"
            >
              {(instrument: Instrument) => (
                <div key={instrument.id} className="search-result-item">
                  <div className="instrument-info">
                    <span className="symbol">{instrument.symbol}</span>
                    <span className="name">{instrument.name}</span>
                    <span className="price">
                      ${instrument.price.toFixed(2)}
                    </span>
                    <span
                      className={`change ${
                        instrument.change >= 0 ? "positive" : "negative"
                      }`}
                    >
                      {instrument.change >= 0 ? "+" : ""}
                      {instrument.change.toFixed(2)} (
                      {instrument.changePercent.toFixed(2)}%)
                    </span>
                  </div>
                  <button
                    className="add-instrument-btn"
                    onClick={() => handleAddInstrument(instrument)}
                    // disabled={activeWatchlist?.instrumentIds?.some(
                    //   (id: number) => id.toString() === instrument.id
                    // )}
                    aria-label={`Add ${instrument.symbol} to watchlist`}
                  >
                    <Plus size={16} />
                  </button>
                </div>
              )}
            </VirtualList>
          </div>
        )}
      </div>
      {/* Content Area */}
      <div className="content-area">
        {activeWatchlist && (
          <>
            {/* Instruments Table */}
            <div className="instruments-section">
              <div className="instruments-table">
                <div className="table-header">
                  <div className="header-cell">Symbol</div>
                  <div className="header-cell">Name</div>
                  <div className="header-cell">Price</div>
                  <div className="header-cell">Change</div>
                  <div className="header-cell">Change %</div>
                  <div className="header-cell">Volume</div>
                  <div className="header-cell">Actions</div>
                </div>

                <div className="table-body">
                  {fetching ? (
                    <div className="loading-state">
                      <p>Loading instruments...</p>
                    </div>
                  ) : // : instrumentError ? (
                  //   <div className="error-state">
                  //     <p>Error loading instruments: {instrumentError.message}</p>
                  //   </div>
                  // )
                  instruments.length === 0 ? (
                    <div className="empty-state">
                      <p>No instruments in this watchlist</p>
                      <p>Use the search below to add instruments</p>
                    </div>
                  ) : (
                    instruments
                      .concat(instruments)
                      .concat(instruments)
                      .concat(instruments)
                      .map((instrument) => (
                        <div key={instrument.id} className="table-row">
                          <div className="cell symbol">
                            <span className="symbol-text">
                              {instrument.symbol}
                            </span>
                            <span className="market-text">
                              {instrument.market}
                            </span>
                          </div>
                          <div className="cell name">{instrument.name}</div>
                          <div className="cell price">
                            {instrument.price.toFixed(2)} {instrument.currency}
                          </div>
                          <div
                            className={`cell change ${
                              instrument.change >= 0 ? "positive" : "negative"
                            }`}
                          >
                            {instrument.change >= 0 ? (
                              <TrendingUp size={16} />
                            ) : (
                              <TrendingDown size={16} />
                            )}
                            {instrument.change >= 0 ? "+" : ""}
                            {instrument.change.toFixed(2)}
                          </div>
                          <div
                            className={`cell change-percent ${
                              instrument.changePercent >= 0
                                ? "positive"
                                : "negative"
                            }`}
                          >
                            {instrument.changePercent >= 0 ? "+" : ""}
                            {instrument.changePercent.toFixed(2)}%
                          </div>
                          <div className="cell volume">
                            {instrument.volume.toLocaleString()}
                          </div>
                          <div className="cell actions">
                            <button
                              className="remove-btn"
                              onClick={() =>
                                handleRemoveInstrument(instrument.id)
                              }
                              aria-label={`Remove ${instrument.symbol}`}
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      ))
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Watchlist;
