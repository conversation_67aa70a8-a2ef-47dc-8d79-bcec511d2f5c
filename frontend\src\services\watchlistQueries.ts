import {
  useQuery$,
  useMutation$,
  useQueryClient$,
} from "@preact-signals/query";
import { watchlistService } from "./watchlistService";

export const useWatchlistQueries = () => {
  const queryClient = useQueryClient$();

  const watchlistsQuery = useQuery$(() => ({
    queryKey: ["watchlists"],
    queryFn: () => watchlistService.fetchWatchlists(),
    staleTime: 1000 * 60 * 5, 
  }));

  const createWatchlistMutation = useMutation$(() => ({
    mutationFn: (name: string) => watchlistService.createWatchlist(name),
    onSuccess: () => {
      queryClient.value.invalidateQueries({ queryKey: ["watchlists"] });
    },
  }));

  const updateWatchlistMutation = useMutation$(() => ({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      watchlistService.updateWatchlist(id, name),
    onSuccess: () => {
      queryClient.value.invalidateQueries({ queryKey: ["watchlists"] });
    },
  }));

  const deleteWatchlistMutation = useMutation$(() => ({
    mutationFn: (id: string) => watchlistService.deleteWatchlist(id),
    onSuccess: () => {
      queryClient.value.invalidateQueries({ queryKey: ["watchlists"] });
      queryClient.value.invalidateQueries({ queryKey: ["instruments"] });
    },
  }));

  // Instrument mutations (updated for multi-watchlist support)
  const addInstrumentMutation = useMutation$(() => ({
    mutationFn: ({
      instrumentId,
      watchlistId,
    }: {
      watchlistId: string;
      instrumentId: number;
    }) => watchlistService.addInstrument(instrumentId, watchlistId),
    onSuccess: () => {
      queryClient.value.invalidateQueries({ queryKey: ["instruments"] });
      queryClient.value.invalidateQueries({ queryKey: ["watchlists"] });
    },
  }));

  const removeInstrumentMutation = useMutation$(() => ({
    mutationFn: ({
      watchlistId,
      instrumentId,
    }: {
      watchlistId: string;
      instrumentId: number;
    }) => watchlistService.removeInstrument(watchlistId, instrumentId),
    onSuccess: () => {
      queryClient.value.invalidateQueries({ queryKey: ["instruments"] });
      queryClient.value.invalidateQueries({ queryKey: ["watchlists"] });
    },
  }));


  return {
    watchlistsQuery,
    createWatchlistMutation,
    updateWatchlistMutation,
    deleteWatchlistMutation,
    addInstrumentMutation,
    removeInstrumentMutation,
  };
};
