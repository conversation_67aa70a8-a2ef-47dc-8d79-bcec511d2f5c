import { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { softPercent } from '@euroland/libs';

import { TableV2 } from '../commons/tableV2/Table';
import { classNames, convertNumber } from '../../helper';
import i18n from '../../services/i18n';
import useOrderDepth from '../../customHooks/useOrderDepth';
import { Skeleton } from '../Skeleton';
import { AppContext } from '../../AppContext';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';

export default function OrderDepthTable() {
  const { formatNumberByInstrument } = useFormatNumberByInstrument();
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const settings = useContext(AppContext);
  const {
    data,
    loading,
    maxBuyVolume,
    maxBuyPrice,
    maxSellPrice,
    maxSellVolume,
    totalBuyVolume,
    totalSellVolume
  } = useOrderDepth();
  const isBlindMode = useSelector(state => state.blindMode.isBlindMode);

  const isCalculateByVolume = settings.orderDepth?.calculateBy === 'VOLUME';
  const maxCalculatedBuy = isCalculateByVolume ? maxBuyVolume : maxBuyPrice;
  const maxCalculatedSell = isCalculateByVolume ? maxSellVolume : maxSellPrice;

  const tableHeadings = [
    { columnDisplay: i18n.translate('buyVolumeLabel'), fieldName: 'buyVolume' },
    { columnDisplay: i18n.translate('buyLabel'), fieldName: 'buy' },
    { columnDisplay: '', fieldName: 'buyProcess' },
    { columnDisplay: '', fieldName: 'sellProcess' },
    { columnDisplay: i18n.translate('sellLabel'), fieldName: 'sell' },
    { columnDisplay: i18n.translate('sellVolumeLabel'), fieldName: 'sellVolume' }
  ];

  const datasTable = useMemo(
    () =>
      data.map(item => {
        const { buyPrice, buyVolume, sellPrice, sellVolume } = item || {};
        const currentCalculatedBuy = isCalculateByVolume ? buyVolume : buyPrice;
        const currentCalculatedSell = isCalculateByVolume ? sellVolume : sellPrice;
        return {
          buyVolume: [{ display: convertNumber(buyVolume) }],
          buy: [
            {
              display: <span className="progress-label">{formatNumberByInstrument(buyPrice, selectedInstrumentId)}</span>
            }
          ],
          buyProcess: [
            {
              display: (
                <div
                  className="progress-buy"
                  style={{ width: `${softPercent(1, currentCalculatedBuy, maxCalculatedBuy)}%` }}
                ></div>
              )
            }
          ],
          sellProcess: [
            {
              display: (
                <div
                  className="progress-sell"
                  style={{ width: `${softPercent(1, currentCalculatedSell, maxCalculatedSell)}%` }}
                ></div>
              )
            }
          ],
          sell: [
            {
              display: <span className="progress-label">{formatNumberByInstrument(sellPrice, selectedInstrumentId)}</span>
            }
          ],

          sellVolume: [{ display: convertNumber(sellVolume) }]
        };
      }),
    [data, selectedInstrumentId]
  );

  const footer = {
    buyVolume: {
      display: convertNumber(totalBuyVolume)
    },
    buy: {
      display: ''
    },
    buyProcess: {
      display: '',
      colSpan: '0'
    },
    total: {
      display: i18n.translate('totalVolume'),
      colSpan: '2'
    },
    sellProcess: {
      display: '',
      colSpan: '0'
    },
    sell: {
      display: ''
    },
    sellVolume: {
      display: convertNumber(totalSellVolume)
    }
  };

  if (loading) return <Skeleton style={{ height: '300px' }}></Skeleton>;

  return (
    <div className={classNames('order--depth__table table-responsive', { 'blind-mode': isBlindMode })}>
      <TableV2 datas={datasTable} headings={tableHeadings} footerElement={footer} caption={i18n.translate('orderDepth')} />
    </div>
  );
}
