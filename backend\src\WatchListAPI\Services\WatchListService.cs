﻿using Microsoft.EntityFrameworkCore;
using WatchListAPI.Common.Extensions;
using WatchListAPI.DTOs;
using WatchListAPI.Entities;
using WatchListAPI.Infrastructure;
using WatchListAPI.Infrastructure.UnitOfWorks;

namespace WatchListAPI.Services
{
    public interface IWatchListService
    {
        Task<AddWatchListResponse> AddWatchListAsync(AddWatchListDto input);
        Task<AddWatchListResponse> UpdateWatchListAsync(UpdateWatchListDto input);
        Task<bool> DeleteWatchListAsync(Guid id);
        Task AddWatchListDetailAsync(AddWatchListDetailDto input);
        Task<WatchListDto> GetWatchListAsync(Guid id);
        Task<IEnumerable<WatchListDto>> GetAllWatchListAsync(string? keyword);
        Task DeleteWatchListDetailAsync(DeleteWatchListDetailDto input);
    }
    public class WatchListService : IWatchListService
    {
        private readonly IEurolandIDProfileUnitOfWork _eurolandIDProfileUnitOfWork;
        public WatchListService(IEurolandIDProfileUnitOfWork eurolandIDProfileUnitOfWork)
        {
            _eurolandIDProfileUnitOfWork = eurolandIDProfileUnitOfWork;
        }

        public async Task<AddWatchListResponse> AddWatchListAsync(AddWatchListDto input)
        {
            var watchList = new WatchList
            {
                Id = new Guid(),
                Name = input.Name,
                Username = TokenExtentions.GetUsername()
            };
            await _eurolandIDProfileUnitOfWork.WatchListRepository.AddAsync(watchList);
            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
            return new AddWatchListResponse()
            {
                Id = watchList.Id,
                Name = watchList.Name,
            };
        }

        public async Task AddWatchListDetailAsync(AddWatchListDetailDto input)
        {
            var username = TokenExtentions.GetUsername();
            // Validate input
            if (input.InstrumentIds == null || !input.InstrumentIds.Any())
            {
                throw new ArgumentException("InstrumentIds cannot be null or empty");
            }
            if (input.WatchListId != null)
            {
                var watchList = await _eurolandIDProfileUnitOfWork.WatchListRepository.Find(input.WatchListId.Value, username);

                if (watchList == null)
                {
                    throw new InvalidDataException($"WatchList not found. Id = {input.WatchListId}");
                }

                var watchListDetails = _eurolandIDProfileUnitOfWork.WatchListDetailRepository
                    .FindAllInstruments(input.WatchListId.Value);

                // Find instruments that don't exist in the current watchlist
                var existingInstrumentIds = watchListDetails.Select(e => e.InstrumentId);

                var newInstrumentIds = input.InstrumentIds
                .Except(existingInstrumentIds)
                .ToList();

                if (newInstrumentIds.Any())
                {
                    _eurolandIDProfileUnitOfWork.WatchListRepository.AddWatchListDetail(new AddWatchListDetailDto
                    {
                        WatchListId = input.WatchListId,
                        InstrumentIds = newInstrumentIds
                    });
                }
            }
            else
            {
                if (input.InstrumentIds.Count != 0)
                {
                    var count = await _eurolandIDProfileUnitOfWork.WatchListRepository.CountWatchListByUsername(username);
                    var watchList = new WatchList
                    {
                        Id = Guid.NewGuid(),
                        Name = $"My watchlist {count + 1}",
                        Username = username
                    };
                    await _eurolandIDProfileUnitOfWork.WatchListRepository.AddAsync(watchList);
                    _eurolandIDProfileUnitOfWork.WatchListRepository.AddWatchListDetail(new AddWatchListDetailDto
                    {
                        WatchListId = watchList.Id,
                        InstrumentIds = input.InstrumentIds
                    });
                }
            }
            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
        }

        public async Task<bool> DeleteWatchListAsync(Guid id)
        {
            var username = TokenExtentions.GetUsername();
            var watchList = await _eurolandIDProfileUnitOfWork.WatchListRepository
                .Find(id, username);

            if (watchList == null)
            {
                throw new InvalidDataException($"WatchList not found. Id = {id}");
            }
            _eurolandIDProfileUnitOfWork.WatchListRepository.Remove(watchList);
            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task DeleteWatchListDetailAsync(DeleteWatchListDetailDto input)
        {
            var username = TokenExtentions.GetUsername();
            var watchList = await _eurolandIDProfileUnitOfWork.WatchListRepository
                .Find(input.WatchListId, username);

            if (watchList == null)
            {
                throw new InvalidDataException($"WatchList not found. Id = {input.WatchListId}");
            }

            var watchListDetail = await _eurolandIDProfileUnitOfWork.WatchListDetailRepository
                .FindInstrumentAsync(input.Id, input.WatchListId);
            if (watchListDetail == null)
            {
                throw new InvalidDataException($"WatchListDetail not found. Id = {input.Id}");
            }
            _eurolandIDProfileUnitOfWork.WatchListDetailRepository.Remove(watchListDetail);
            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
        }

        public async Task<IEnumerable<WatchListDto>> GetAllWatchListAsync(string? keyword)
        {
            var username = TokenExtentions.GetUsername();

            var watchLists = await _eurolandIDProfileUnitOfWork.WatchListRepository
                .GetAllWatchList(username, keyword)
                .ToListAsync();

            var watchListIds = watchLists.Select(x => x.Id).ToList();

            var allDetails = await _eurolandIDProfileUnitOfWork.WatchListDetailRepository
                .FindByWatchListIdsAsync(watchListIds);

            var grouped = allDetails
                .GroupBy(x => x.WatchListId)
                .ToDictionary(g => g.Key, g => g.Select(d => d.InstrumentId).ToList());

            var result = watchLists.Select(w => new WatchListDto
            {
                Id = w.Id,
                Name = w.Name,
                InstrumentIds = grouped.ContainsKey(w.Id) ? grouped[w.Id] : new List<int>()
            });

            return result;
        }

        public async Task<WatchListDto> GetWatchListAsync(Guid id)
        {
            var username = TokenExtentions.GetUsername();
            var watchList = await _eurolandIDProfileUnitOfWork.WatchListRepository.Find(id, username);
            if (watchList == null)
            {
                throw new InvalidDataException($"WatchList not found. Id = {id}");
            }
            var watchListDdetail = _eurolandIDProfileUnitOfWork.WatchListDetailRepository
                .FindAllInstruments(id);
            return new()
            {
                Id = watchList.Id,
                Name = watchList.Name,
                InstrumentIds = [.. watchListDdetail.Select(e => e.InstrumentId)]
            };
        }

        public async Task<AddWatchListResponse> UpdateWatchListAsync(UpdateWatchListDto input)
        {
            var username = TokenExtentions.GetUsername();
            var watchList = await _eurolandIDProfileUnitOfWork.WatchListRepository
                .Find(input.Id, username);

            if (watchList == null)
            {
                throw new Exception("WatchList not found.");
            }

            watchList.Name = input.Name;
            _eurolandIDProfileUnitOfWork.WatchListRepository.Update(watchList);
            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
            return new AddWatchListResponse
            {
                Id = watchList.Id,
                Name = watchList.Name,
            };
        }
    }
}
