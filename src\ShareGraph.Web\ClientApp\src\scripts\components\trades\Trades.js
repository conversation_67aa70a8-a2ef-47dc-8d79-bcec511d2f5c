import { useContext } from 'react';
import TradeDownLoad from './TradeDownLoad';
import TradesTable from './TradesTable';
import { AppContext } from '../../AppContext';
import { LAYOUT } from '../../common';
import i18n from '../../services/i18n';

function Trades() {
  const settings = useContext(AppContext);
  const layout = settings.layout || LAYOUT.FULL;

  return (
    <>
      {layout === LAYOUT.FULL && (
        <h2 className="title-section performance-details__title">
          {i18n.translate('trades')}
        </h2>
      )}

      <TradesTable />
      <TradeDownLoad />
    </>
  );
}

export default Trades;
