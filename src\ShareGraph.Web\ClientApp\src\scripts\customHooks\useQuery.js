import { useSearchParams } from 'react-router-dom';

/**
 * @param {arrays: []string}
 * @returns {object}
 */
const useQuery = ({ arrays } = { arrays: [] }) => {
  const [searchParams] = useSearchParams();
  const result = {};
  const sameKeys = {};

  for (const [key] of searchParams.entries()) {
    if (sameKeys[key] || arrays.includes(key)) {
      result[key] = searchParams.getAll(key);
    } else {
      sameKeys[key] = true;
      result[key] = searchParams.get(key);
    }
  }
  return result;
};
export default useQuery;
