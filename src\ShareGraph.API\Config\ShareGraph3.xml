<?xml version="1.0" encoding="utf-8"?>

<settings>
  <Layout>FULL</Layout>
  <TimeZone>Asia/Seoul</TimeZone>
  <UseLatinNumber>true</UseLatinNumber>
  <TickerRefreshSeconds>10</TickerRefreshSeconds>

  <Currencies>
    <Enabled>false</Enabled>
  </Currencies>
 <ColorBlindMode>
    <Enabled>false</Enabled>
    <DefaultSelected>false</DefaultSelected>
  </ColorBlindMode>

  <!--<Instruments>
    <Instrument>
      <Id>32864</Id>
      <Color>#7DCA48</Color>
      -->
  <!-- <Order>1</Order> -->
  <!--
      <Default>true</Default>
      -->
  <!--Custom Ticker Name-->
  <!--
      <TickerName>
        <en-GB>Share 3</en-GB>
        <ar-AE>CARL B</ar-AE>
      </TickerName>
      -->
  <!-- Enable sourceID for press release-->
  <!--
      <PressReleases>
        <SourceFilter></SourceFilter>
      </PressReleases>
      -->
  <!-- Enable realtime data-->
  <!--
      <IsRT>TRUE</IsRT>
      -->
  <!-- Config decimal digits-->
  <!--
      <DecimalDigits>3</DecimalDigits>
      -->
  <!-- Enable calculate adjust price -->
  <!--
      <EnabledAdjustPrice>True</EnabledAdjustPrice>
    </Instrument>
  </Instruments>-->

  <Ticker>
    <!-- Possible values: GRAPH, TABLE -->
    <EnabledFormat>GRAPH, TABLE</EnabledFormat>
    <!-- Possible values: SINGLE, MULTIPLE -->
    <TickerType>SINGLE</TickerType>
    <!-- Possible values: SINGLE_TICKER_1, SINGLE_TICKER_2, MULTIPLE_TICKER_1, MULTIPLE_TICKER_2,
    {CustomTemplate}  -->
    <GraphTickerTemplate>SINGLE_TICKER_1</GraphTickerTemplate>
    <!-- Possible values: TABLE_TICKER_SINGLE, TABLE_TICKER_MULTIPLE -->
    <TableTickerTemplate>TABLE_TICKER_SINGLE</TableTickerTemplate>
    <!-- Define how many Ticker visible on slider -->
    <SlidesPerView>4</SlidesPerView>
    <!-- Possible values: FADE, BLINK_PRICE, BLINK_MARKET -->
    <GraphAnimation>FADE</GraphAnimation>
    <!-- Possible values: FADE, TRANSFORM -->
    <TableAnimation>FADE</TableAnimation>
  </Ticker>

  <Performance>
    <!-- Posible values: GRAPH, TABLE
			Empty for disable -->
    <EnabledFormat>TABLE, GRAPH</EnabledFormat>
    <!-- Posible values: SHARE_PRICE_DEVELOPEMENT, SHARE_PRICE_DEVELOPEMENT_BY_YEARS,
    52_WEEKS_HIGH_LOW
		Empty for disable -->
    <PerformanceType>SHARE_PRICE_DEVELOPMENT, SHARE_PRICE_DEVELOPMENT_BY_YEARS, 52_WEEKS_HIGH_LOW</PerformanceType>
    <!-- List field available shareName,low52W,high52W,percent52WLow,percent52WHigh -->
    <Enable52WTableColumn>shareName,low52W,high52W,percent52WLow,percent52WHigh</Enable52WTableColumn>
    <!-- List field available
    shareName,currencyCode,change,w52HighLow,allTimeHighLow,week,month,threeMonthChange,sixMonthsChange,yTD,percent52W,threeYearsChange,fiveYearsChange,tenYearsChange -->
    <EnableSharePriceDevelopmentColumn>
      shareName,last,currencyCode,change,w52HighLow,allTimeHighLow,week,month,threeMonthChange,yTD,percent52W,fiveYearsChange,
    </EnableSharePriceDevelopmentColumn>
    <!-- Option values: 2,3,4,5,6 -->
    <NumberOfYearSPByYear>5</NumberOfYearSPByYear>
    <!--Option
    values: true, false-->
    <ShowEarliestYearFirstSPByYear>False</ShowEarliestYearFirstSPByYear>
    <!--List Ids will not using in the Performance -->
    <ExcludeIds></ExcludeIds>
  </Performance>

  <Chart>
    <!-- Posible values: DYNAMIC_CALLOUT, TOOLTIP, STATIC Default value = DYNAMIC_CALLOUT -->
    <DefaultTooltipType>DYNAMIC_CALLOUT</DefaultTooltipType>
    <!-- Posible values: CANDLE, LINE, VERTEX_LINE, STEP, MOUNTAIN, HISTOGRAM, BAR. Default value =
    MOUNTAIN -->
    <DefaultChartType>MOUNTAIN</DefaultChartType>
    <!-- Posible values: CANDLE, LINE, VERTEX_LINE, STEP, MOUNTAIN, HISTOGRAM, BAR. Default value =
    MOUNTAIN -->
    <EnabledChartTypes>CANDLE, LINE, VERTEX_LINE, STEP, MOUNTAIN, HISTOGRAM, BAR</EnabledChartTypes>
    <!-- Posible values: 1D, 5D, 1M, 3M, 6M, YTD, 1Y, 3Y, 5Y, ALL, CUSTOM_RANGE. -->
    <EnabledPeriods>1D, 5D, 6M, YTD, 1Y, 3Y, 5Y, CUSTOM_RANGE, ALL</EnabledPeriods>
    <!--Default
    value = 1Y -->
    <DefaultPeriod>6M</DefaultPeriod>
    <!-- Hide chart title: true, false -->
    <HideChartTitle>false</HideChartTitle>
    <!-- Posible values: EXCEL, PRINT, EXPORT, SHARE Empty for disable -->
    <EnabledAdditionalOptions>EXCEL, PRINT, EXPORT, SHARE</EnabledAdditionalOptions>
    <ExcelDownloadOption>
      <!--The option to include Total Return column in the excel file download-->
      <IncludedTotalReturn>true</IncludedTotalReturn>
      <!--The option to include peers and indices was selected in the Excel file download-->
      <IncludedSelectedPeersAndIndicies>true</IncludedSelectedPeersAndIndicies>
    </ExcelDownloadOption>
    <!-- Posible values: EXCEL, PRINT, EXPORT, SHARE Empty for disable -->
    <EnabledSocialMedias>FACEBOOK, TWITTER, LINKEDIN</EnabledSocialMedias>
    <!-- Possible values: BOLLINGER_BANDS, ICHIMOKU_CLOUDS, MACD, RSI, STOCHASTICS, TYPICAL_PRICE,
    VOLUME_CHART, ADX_DMS,TOTAL_RETURN, MOVING_AVERAGE_CROSS -->
    <ExcludeStudies></ExcludeStudies>
    <!-- Possible values: DIVIDEND, EARNING, PRESSRELEASES, VIDEO -->
    <EnabledEvents>DIVIDEND, EARNING, PRESSRELEASES</EnabledEvents>
    <!-- Possible values: DIVIDEND, EARNING, PRESSRELEASES, VIDEO -->
    <DefaultEvents>VIDEO, EARNING</DefaultEvents>
    <!-- Possible values: HIGH_LOW_VALUES, RANGE_SELECTOR, EXTENDED_HOURS -->
    <EnabledChartPreferences>HIGH_LOW_VALUES, RANGE_SELECTOR, EXTENDED_HOURS</EnabledChartPreferences>
    <!-- Possible values: PERCENT_VIEW, LOG_SCALE, INVERT, LINEAR, STRIPE_BACKGROUND -->
    <EnabledYAxisPreferences>INVERT, LOG_SCALE, PERCENT_VIEW, LINEAR, STRIPE_BACKGROUND</EnabledYAxisPreferences>
    <EnabledDefaultVolumeChart>true</EnabledDefaultVolumeChart>
    <!--Default Volume Underlay or Volumne chart. Otherelse is empty it mean disable. OPTIONS:VOLUME_CHART|VOLUME_UNDERLAY|'' .-->
    <DefaultVolumeChart>VOLUME_CHART</DefaultVolumeChart>
    <!-- When enabled,
      If Dynamic call out and tooltip are selected, event details should be displayed when the user
    clicks on it.
      If Dynamic call out and tooltip are not selected, event details should be displayed when the user
    hovers on it.
      When disabled,
      Event details should be displayed when the user clicks on it-->
    <EnabledHoverEvent>True</EnabledHoverEvent>
    <HighPriceIndicatorColor>#00B55B</HighPriceIndicatorColor>
    <LowPriceIndicatorColor>#F52A2A</LowPriceIndicatorColor>
    <Studies>
      <MACD>
        <MACD>#000000</MACD>
        <Signal>#FF0000</Signal>
        <IncreasingBar>#00DD00</IncreasingBar>
        <DecreasingBar>#FF0000</DecreasingBar>
      </MACD>
      <RSI>
        <RSI>#000000</RSI>
      </RSI>
      <BollingerBands>
        <BollingerBandsTop>#08415C</BollingerBandsTop>
        <BollingerBandsMedian>#cc2936</BollingerBandsMedian>
        <BollingerBandsBottom>#388697</BollingerBandsBottom>
      </BollingerBands>
      <IchimokuClouds>
        <ConversionLine>#08415C</ConversionLine>
        <BaseLine>#cc2936</BaseLine>
        <LeadingSpanA>#388697</LeadingSpanA>
        <LeadingSpanB>#77C3BC</LeadingSpanB>
        <LeadingSpan>#AEF8DD</LeadingSpan>
      </IchimokuClouds>
      <Stochastics>
        <K>#000000</K>
        <D>#FF0000</D>
        <OverBought>#AEF8DD</OverBought>
        <OverSold>#77C3BC</OverSold>
      </Stochastics>
      <TotalReturn>
        <TTRT>#000000</TTRT>
      </TotalReturn>
      <ADX>
        <DiPlus>#08415C</DiPlus>
        <DiMinus>#cc2936</DiMinus>
        <ADX>#388697</ADX>
        <PositiveBar>#77C3BC</PositiveBar>
        <NegativeBar>#AEF8DD</NegativeBar>
      </ADX>
      <Volume>
        <DownVolumeColor>#9FD1AC</DownVolumeColor>
        <UpVolumeColor>#D79999</UpVolumeColor>
      </Volume>
      <VolumeUnderlay>
        <DownVolumeColor>#9FD1AC</DownVolumeColor>
        <UpVolumeColor>#D79999</UpVolumeColor>
        <YAxisHeightFactor>0.2</YAxisHeightFactor>
      </VolumeUnderlay>
    </Studies>
    <IsStripedBackground>True</IsStripedBackground>
    <!-- Configure for blinking price pointer -->
    <BlinkingPricePointer>
      <Enabled>true</Enabled>
    </BlinkingPricePointer>
  </Chart>

  <Peers>
    <Enabled>true</Enabled>
    <!-- Possible values: FADE, BLINK_PRICE -->
    <Animation>FADE</Animation>
  </Peers>
  <Indices>
    <Enabled>false</Enabled>
    <!-- Possible values: FADE, BLINK_PRICE -->
    <Animation>FADE</Animation>
  </Indices>

  <ShareDetails>
    <!-- Options: True|False -->
    <Enabled>True</Enabled>
    <!-- Fields available:
		TIME,CURRENCY,MARKET,MARKET_STATUS,ISIN,SYMBOL,BID,BID_SIZE,ASK,ASK_SIZE,OPEN,LAST,CHANGE,
		CHANGE_PERCENT,HIGH,LOW,VOLUME,TOTAL_TRADES,PREVIOUS_CLOSE,YTD_HIGH,YTD_LOW,WEEKS_52_HIGH,WEEKS_52_LOW,
        ALL_TIME_HIGH,ALL_TIME_LOW,YTD_PERCENT,WEEKS_52_PERCENT,LIST,INDUSTRY,NUMBER_OF_SHARES,MARKET_CAP,
        LOT_SIZE,P_E,AVERAGE_PRICE,TURNOVER...-->
    <ShareDataItems>TIME,MARKET_STATUS</ShareDataItems>
    <!-- Options: True|False -->
    <DisplayOnSelectedTicker>True</DisplayOnSelectedTicker>
    <!-- Options: Grid|Flow -->
    <DisplayType>Grid</DisplayType>
    <!-- Options: True|False -->
    <PartialDisplay>True</PartialDisplay>
    <!-- Options: ALWAYS|NEVER|CONFIRM, Default: CONFIRM-->
    <Print>CONFIRM</Print>
    <NumberItemsInCollapsedMode>6</NumberItemsInCollapsedMode>
    <!-- number item printing  -->
    <NumberItemsInPrinting>8</NumberItemsInPrinting>
    <!-- configure display for marketCap: Exact, Thousands, Lakhs, Hundreds, Millions, Crores -->
    <MarketCapDisplay></MarketCapDisplay>
    <!-- configure display for marketCap: Exact, Thousands, Lakhs, Hundreds, Millions, Crores -->
    <TurnOverDisplay></TurnOverDisplay>
    <!-- configure display for marketCap: Exact, Thousands, Lakhs, Hundreds, Millions, Crores -->
    <NumberOfShareDisplay></NumberOfShareDisplay>
  </ShareDetails>

  <PressReleases>
    <LanguageForwarding></LanguageForwarding>
    <TypeFilter></TypeFilter>
    <ExcludedTypeFilter></ExcludedTypeFilter>
    <!-- Popup, NewPage. Default popup-->
    <OpenAs>Popup</OpenAs>
    <!-- only for NewPage.
    https://dev.vn.euroland.com/tools/iframe-test/sg3-press-release-detail.html?ID={ID}%26lang={lang}%26companycode={companycode}%26=press-release-url={pressReleaseUrl}-->
    <NewPageUrlPattern>
      https://dev.vn.euroland.com/tools/iframe-test/sg3-press-release-detail.html?ID={ID}%26lang={lang}%26companycode={companycode}
    </NewPageUrlPattern>
  </PressReleases>

  <Format>
    <en-GB>
      <TickerDateTimeFormat>DD MMMM YYYY HH:mm ([UTC]Z)</TickerDateTimeFormat>
      <ShortDate xml:space="preserve">MMM/DD/YYYY</ShortDate>
      <LongDate xml:space="preserve">MMM/DD/YYYY HH:mm:ss</LongDate>
      <TimeFormat>HH:mm A [UTC]Z</TimeFormat>
      <DecimalDigits>2</DecimalDigits>
      <PercentDigits>2</PercentDigits>
      <!--DecimalSeparator:
      ,| . | space-->
      <DecimalSeparator xml:space="preserve">.</DecimalSeparator>
      <!--ThousandsSeparator:
       ,| . | space-->
      <ThousandsSeparator xml:space="preserve">,</ThousandsSeparator>
      <!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
      <NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
    </en-GB>
  </Format>

  <Accessibilities>
    <Enabled>true</Enabled>
  </Accessibilities>
  <GoogleTagEnabled>False</GoogleTagEnabled>

  <Trade>
    <!--To enable trade.Default value: false-->
    <Enabled>True</Enabled>
    <!--Configure numbers row data will showed in trade component. Default value: 10-->
    <NumberOfTrades>10</NumberOfTrades>
    <!--Configure time format in current day. If data is historical then Date column will combine ShortDate and this configure to display.Default value: HH:mm:ss-->
    <TimeFormat>HH:mm:ss</TimeFormat>
    <!--Configure time refresh for trade list. Unit is second-->
    <TradeRefreshSeconds>60</TradeRefreshSeconds>
  </Trade>
  <VideoSetting>
    <!--Currently only support two types: Html video and Vimeo video which used to present video. Options: HTML, VIMEO.Default value is VIMEO-->
    <VideoType>HTML</VideoType>
  </VideoSetting>
  <OrderDepth>
    <!--To enable Order depth. Default value: false-->
    <Enabled>true</Enabled>
    <!--Configure time refresh for trade list. Unit is second. Default: 60-->
    <RefreshSeconds>60</RefreshSeconds>
    <!--The base unit is used display in chart. OPIONS: PRICE, VOLUME. Default VOLUME-->
    <CalculateBy>VOLUME</CalculateBy>
  </OrderDepth>

  <ForeignOwnership>
    <Enabled>true</Enabled>
  </ForeignOwnership>

  <CustomPhrases>
  </CustomPhrases>
  <UsePrevCloseDayForMcap>False</UsePrevCloseDayForMcap>

</settings>
