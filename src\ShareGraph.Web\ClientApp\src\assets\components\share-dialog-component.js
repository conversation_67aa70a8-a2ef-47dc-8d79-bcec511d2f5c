
(function ShareDialogComponentFactory(euroland) {
  const props = {
    shareUrl: {
      type: 'string',
      required: false
    },
    thumbnailUrl: {
      type: 'string',
      required: false
    }
  };

  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;

/**
 * Creates the ShareDialogComponent.
 * @returns {euroland.components.ShareDialogComponent}
 */
  euroland.createComponent('ShareDialogComponent', {
    tag: 'share-dialog-component',
    url: baseUrl + 'social' + location.search,
    dimensions: {
      width: '366px !important',
      height: '180px !important'
    },
    template: {
      name: 'modal',
      clickOverlayToClose: false
    },
    props: props,
    attributes: {
      iframe: {
        allow: 'clipboard-write *;'
      }
    }
  });
})(window.euroland);
