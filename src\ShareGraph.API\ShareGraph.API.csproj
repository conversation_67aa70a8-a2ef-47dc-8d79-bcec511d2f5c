<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\Version.props" />
  <PropertyGroup>
    <AssemblyName>Euroland.FlipIT.ShareGraph.API</AssemblyName>
    <RootNamespace>Euroland.FlipIT.ShareGraph.API</RootNamespace>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileRunArguments>--mount type=bind,source="C:/ProgramData/sharegraph-api",target="/usr/share/sharegraph-api"</DockerfileRunArguments>
    <UserSecretsId>5fccdaaa4d9b409b72720b3eadb13654</UserSecretsId>

    <Version Condition=" '$(ApiBuildVersion)' != '' ">$(ApiBuildVersion)</Version>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="DataLoaders\**" />
    <Content Remove="DataLoaders\**" />
    <EmbeddedResource Remove="DataLoaders\**" />
    <None Remove="DataLoaders\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Config\Company\dk-cbg\MULTIPLE_TICKER_1.html" />
    <None Remove="Config\ShareGraph3.xml" />
    <None Remove="Config\Ticker\MULTIPLE_TICKER_1.html" />
    <None Remove="Config\Ticker\MULTIPLE_TICKER_2.html" />
    <None Remove="Config\Ticker\SINGLE_TICKER_1.html" />
    <None Remove="Config\Ticker\SINGLE_TICKER_2.html" />
    <None Remove="Config\Ticker\TABLE_TICKER_MULTIPLE.html" />
    <None Remove="Config\Ticker\TABLE_TICKER_SINGLE.html" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Config\Company\dk-cbg\MULTIPLE_TICKER_1.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\ShareGraph3.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Ticker\MULTIPLE_TICKER_1.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Ticker\MULTIPLE_TICKER_2.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Ticker\SINGLE_TICKER_1.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Ticker\SINGLE_TICKER_2.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Ticker\TABLE_TICKER_MULTIPLE.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Ticker\TABLE_TICKER_SINGLE.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="11.0.1" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.11.0" />
    <PackageReference Include="Euroland.FlipIT.CompanyStylesheetMiddleware" Version="1.1.1" />
    <PackageReference Include="Euroland.NetCore.ToolsFramework.AzureSetting" Version="2.0.4" />
    <PackageReference Include="Euroland.NetCore.ToolsFramework.Localization" Version="3.0.0" />
    <PackageReference Include="Euroland.NetCore.ToolsFramework.Setting" Version="3.0.1" />
    <PackageReference Include="Euroland.NetCore.ToolsFramework.Translation" Version="2.0.8" />
    <PackageReference Include="GraphQL.Client" Version="6.1.0" />
    <PackageReference Include="GraphQL.Client.Abstractions" Version="6.1.0" />
    <PackageReference Include="GraphQL.Client.Serializer.Newtonsoft" Version="6.1.0" />
    <PackageReference Include="HotChocolate.AspNetCore" Version="12.15.2" />
    <PackageReference Include="HotChocolate.Data" Version="12.15.2" />
    <PackageReference Include="HotChocolate.Data.EntityFramework" Version="12.15.2" />
    <PackageReference Include="HtmlSanitizer" Version="8.0.645" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.6.3" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="6.0.0" />
  </ItemGroup>

  <!--
    Exclude certain appSettings.*.json from publishing
  -->
  <!-- <ItemGroup>
    <Content Update="appSettings.*.json" CopyToPublishDirectory="Never" />
    <Content Update="appSettings.$(EnvironmentName).json" CopyToPublishDirectory="PreserveNewest" />
  </ItemGroup> -->
</Project>
