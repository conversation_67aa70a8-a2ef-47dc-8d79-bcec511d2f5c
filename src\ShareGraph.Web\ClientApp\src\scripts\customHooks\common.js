import { useContext, useEffect } from 'react';
import { CIQ } from '../../scripts/components/chart/chartiq-import';

import ChartContext from '../context/ChartContext';

export const useMouseOutChart = (callback = () => {}) => {
  const {
    chart: [chartEngine]
  } = useContext(ChartContext);

  useEffect(() => {
    if (!chartEngine) return;
    const handleMouseOutRefId = chartEngine.append('handleMouseOut', function () {
      callback();
    });

    return () => {
      chartEngine.removeInjection(handleMouseOutRefId);
    };
  }, [chartEngine]);
};


export const useDrawingAnnotation = () => {
  const {
    chart: [chartEngine]
  } = useContext(ChartContext);

  useEffect(() => {
    if (!chartEngine) return;
    const hanldeSelectedRange = e => {
      if (e.property !== 'vectorType') return;

      const toggle = chartEngine?.uiContext?.topNode?.querySelector(
        'cq-toggle.cq-drawing-annotation'
      );

      if (!toggle) return;

      if (
        (!toggle.className?.includes('active') && e.value === 'annotation') ||
        (toggle.className?.includes('active') && e.value !== 'annotation')
      ) {
        toggle.dispatchEvent(new Event('stxtap'));
      }
    };
    CIQ.UI.observeProperty('vectorType', chartEngine.currentVectorParameters, hanldeSelectedRange);
    return () => {
      CIQ.UI.unobserveProperty('vectorType', chartEngine.currentVectorParameters, hanldeSelectedRange);
    };
  }, [chartEngine]);
};
