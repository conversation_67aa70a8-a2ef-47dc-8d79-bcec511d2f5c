using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
    public class Format
    {
        public string TickerDateTimeFormat { get; set; }
        public string ShortDate { get; set; }
        public string LongDate { get; set; }
        public int? DecimalDigits { get; set; }
        public int? PercentDigits { get; set; }
        public string DecimalSeparator { get; set; }
        public string ThousandsSeparator { get; set; }
        public string NegativeNumberFormat { get; set; }

        public string PositiveNumberFormat { get; set; }
        public string PositiveChangeFormat { get; set; }
        public string TimeFormat { get; set; }

  }
}
