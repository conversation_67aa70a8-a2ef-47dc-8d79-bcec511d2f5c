import { appSettings } from '../../appSettings';
import fetchApi from '../services/fetch-api';

export const SWITCH_CURRENCY = 'SWITCH_CURRENCY';
export const UPDATE_CURRENCY = 'UPDATE_CURRENCY';
export const UPDATE_CURRENCY_RATE_BEGIN = 'UPDATE_CURRENCY_RATE_BEGIN';
export const UPDATE_CURRENCY_RATE_SUCCESS = 'UPDATE_CURRENCY_RATE_SUCCESS';
export const UPDATE_CURRENCY_RATE_FAILURE = 'UPDATE_CURRENCY_RATE_FAILURE';
export const ENABLED_CURRENCY_OPTION = 'ENABLED_CURRENCY_OPTION';


/**
 *
 * @param {boolean} enabledCurrencyOption
 */
export function toggleEnabledCurrencyOption(enabledCurrencyOption) {
  return {
    type: ENABLED_CURRENCY_OPTION ,
    payload: { enabledCurrencyOption }
  };
}

/**
 *
 * @param {import('../reducers/currencyReducers').CurrencyOption} currencyCode
 */
export function switchCurrency(currencyCode) {
  return {
    type: SWITCH_CURRENCY ,
    payload: { currencyCode }
  };
}

export const updateCurrencyRateBegin = () => ({
  type: UPDATE_CURRENCY_RATE_BEGIN
});

/**
 * @typedef {import("../reducers/currencyReducers").CurrencyRate} CurrencyRate
 * @param {{
 * baseCurrencyCode: string
 * currencyRate: CurrencyRate
 * }[]} currencyRates
 */
export const updateCurrencyRateSuccess = (currencyRates = []) => ({
  type: UPDATE_CURRENCY_RATE_SUCCESS,
  payload: { currencyRates }
});

export const updateCurrencyRateFailure = (error) => {
  console.error(error);
  return ({
    type: UPDATE_CURRENCY_RATE_FAILURE,
    payload: { error }
  });
};

/**
 * @param {{ quoteCurrency: string }}
 */
export function updateCurrencyRate({ quoteCurrency}) {
  return async (dispatch, getState) => {

    dispatch(updateCurrencyRateBegin());
    try {
      const tickers = getState().tickers.instruments;
      const peers = getState().peers.instruments;
      const baseCurrencyCodes = [...tickers, ...peers].map(ins => ins.originalCurrency).reduce((s, currencyCode) => {
        if(!s.includes(currencyCode)) {
          s.push(currencyCode);
        }
        return s;
      }, []);

      const currencyRateData =  await getUpdateCurrencyRate({baseCurrency: baseCurrencyCodes.filter(x => x), quoteCurrency});
      const result = currencyRateData.data.currency.currentRates.map(data => ({
        baseCurrencyCode: data.baseCurrency,
        currencyRate: data
      }));

      dispatch(updateCurrencyRateSuccess(result));

    } catch (error) {
      console.log(error);
      dispatch(updateCurrencyRateFailure(error));
    }
  };
}

/**
 * @param {{baseCurrency: [string], quoteCurrency: string}}
 */
function getUpdateCurrencyRate({baseCurrency, quoteCurrency}) {
  return fetchApi(appSettings.sDataApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
        currency {
          currentRates(baseCurrencies: [${baseCurrency.map(currencyCode => `"${currencyCode}"`)}], quoteCurrency: "${quoteCurrency}") {
            date
            value
            baseCurrency
            quoteCurrency
          }
        }
      }`
    })
  })
    .then(res => res.json());
}
