import appConfig from '../services/app-config';
import { getTickersSetting } from '../configs/configuration-app';
import { getRealTimeAndNotInstruments } from './tickerActions';
import { getCurrencyText } from '../helper';

import {client, clientRT} from '../services/graphql-client';
import {buildShareDetailsQuery, getVariableRequire, normalizeGraphqlData} from '../graphql-queries/shareDetailQuery';


async function fetchShareDetailsApiNewQuery(instrumentIds = [], isRT, toCurrency) {
  if (!instrumentIds.length) return;
  let setting = appConfig.get();
  let shareDetailSetting = setting.shareDetails.shareDataItems;

  const fieldSetting = new Set([
    'INSTRUMENT_ID',
    'SHARE_NAME',
    'MARKET_ABBREVIATION',
    ...shareDetailSetting
  ]);

  const dateFieldsMap = {
    ALL_TIME_HIGH: 'ALL_TIME_HIGH_DATE',
    ALL_TIME_LOW: 'ALL_TIME_LOW_DATE',
    WEEKS_52_HIGH: 'WEEKS_52_HIGH_DATE',
    WEEKS_52_LOW: 'WEEKS_52_LOW_DATE',
    YTD_HIGH: 'YTD_HIGH_DATE',
    YTD_LOW: 'YTD_LOW_DATE'
  };

  Object.entries(dateFieldsMap).forEach(([key, dateField]) => {
    if (shareDetailSetting.includes(key)) {
      fieldSetting.add(dateField);
    }
  });

  const finalFieldSetting = Array.from(fieldSetting);

  const instrumentSettings = getTickersSetting();

  const ids = instrumentIds.filter(id => !instrumentSettings[id].enabledAdjustPrice);
  const adjIds = instrumentIds.filter(id => instrumentSettings[id].enabledAdjustPrice);
  const variableRequire = getVariableRequire(finalFieldSetting);

  const clientInstance = isRT ? clientRT : client;

  const instruments = (await Promise.all([
    clientInstance.query(buildShareDetailsQuery(variableRequire), {
      ids,
      adjClose: false,
      toCurrency
    }),
    clientInstance.query(buildShareDetailsQuery(variableRequire), {
      ids: adjIds,
      adjClose: true,
      toCurrency
    })
  ])).map(
    result => {
      return result.data.instrumentByIds?.map(item => normalizeGraphqlData(finalFieldSetting, item)) ?? [];
    }
  ).flat();

  return {
    data: {
      instruments
    }
  };
}

// ShareDetails Actions
export const FETCH_SHAREDETAILS_BEGIN = 'FETCH_SHAREDETAILS_BEGIN';
export const FETCH_SHAREDETAILS_SUCCESS = 'FETCH_SHAREDETAILS_SUCCESS';
export const FETCH_SHAREDETAILS_FAILURE = 'FETCH_SHAREDETAILS_FAILURE';


export const fetchShareDetailsBegin = () => ({
  type: FETCH_SHAREDETAILS_BEGIN
});

export const fetchShareDetailsSuccess = (instruments = []) => {
  const instrumentsSetting = getTickersSetting();
  return (dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);
    return dispatch({
      type: FETCH_SHAREDETAILS_SUCCESS,
      payload: {
        instruments: instruments.map(ins => {
          const instrumentData = ins;

          if(ins.currencyCode) {
            instrumentData['currencyCode'] = selectedCurrencyText || instrumentsSetting[ins.instrumentId]?.currencyCode || ins.currencyCode;
          }
          if(ins.marketName) {
            instrumentData['marketName'] = instrumentsSetting[ins.instrumentId]?.marketName || ins.marketName;
          }
          if(ins.ticker) {
            instrumentData['ticker'] = instrumentsSetting[ins.instrumentId]?.ticker || ins.ticker;
          }

        return instrumentData;
      })
      }
    });
  };
};

export const fetchShareDetailsFailure = (error) => ({
  type: FETCH_SHAREDETAILS_FAILURE,
  payload: { error }
});


async function getShareDetailsData(instrumentIds = [], toCurrency) {
  const { realTimeIds, notRealTimeIds } = getRealTimeAndNotInstruments(instrumentIds);

  try {
    const response = await Promise.all([fetchShareDetailsApiNewQuery(realTimeIds, true, toCurrency), fetchShareDetailsApiNewQuery(notRealTimeIds, false, toCurrency)]);
    const firstInstruments = response[0]?.data?.instruments || [];
    const secondInstruments = response[1]?.data?.instruments || [];

    const responseData = {data: {
      instruments: [...firstInstruments, ...secondInstruments]
    }};
    return Promise.resolve(responseData);
  }
  catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
}


export function fetchShareDetails(instrumentIds = []) {
  return (dispatch, getState) => {
    dispatch(fetchShareDetailsBegin());

    if (!instrumentIds || !instrumentIds.length) {
      const ids = getState().tickers.instruments.map(ins => ins.instrumentId);
      //const selectedTickerId = getState().tickers.selectedInstrumentId || ids[0];

      instrumentIds = [...ids];
    }

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    return getShareDetailsData(instrumentIds, toCurrency)
      .then(json => {
        dispatch(fetchShareDetailsSuccess(json.data.instruments));
        return json.data.instruments;
      })
      .catch(err => {
        console.error(err);
        dispatch(fetchShareDetailsFailure(err));
      });
  };

}
