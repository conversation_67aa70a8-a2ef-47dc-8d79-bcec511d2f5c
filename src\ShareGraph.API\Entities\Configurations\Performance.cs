using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class Performance
  {
    public IEnumerable<string> EnabledFormats { get; set; }
    public IEnumerable<string> PerformanceTypes { get; set; }
    public IEnumerable<string> Enable52WTableColumns { get; set; }
    public IEnumerable<string> EnableSharePriceDevelopmentColumns { get; set; }
    public int NumberOfYearSPByYear { get; set; }
    public bool ShowEarliestYearFirstSPByYear { get; set; }

    public IEnumerable<int> ExcludeIds { get; set; }
  }

  public class PerformanceConfig
  {
    public string EnabledFormat { get; set; }
    public string PerformanceType { get; set; }
    public string Enable52WTableColumn { get; set; }
    public string EnableSharePriceDevelopmentColumn { get; set; }
    public int NumberOfYearSPByYear { get; set; }
    public bool ShowEarliestYearFirstSPByYear { get; set; }

    public string ExcludeIds { get; set; }
  }
}
