import { getAllInstrumentsSetting } from '../../configs/configuration-app';
import { getDefaultEvents } from '../../helper';

const fetchDataChartDone = (chartElementRef, param) => {
  const chartEngine = param.stx;
  const { loader } = chartEngine.uiContext;
  if (!chartEngine || !chartElementRef.current) return;
  if (chartEngine.masterData?.length === 0) {
    chartEngine.draw();
  }
  loader?.hide();

  if (
    chartEngine.uiContext &&
    chartEngine.uiContext.advertised &&
    chartEngine.uiContext.advertised.Markers.implementation
  ) {
    const Markers = chartEngine.uiContext.advertised.Markers;
    const implementation = Markers.implementation;

    if(implementation.initialized) {
      implementation.reloadMarkers();
    } else {
      implementation.initialize(getDefaultEvents());
    }
  }
};


/**
 * 
 * @param {{
 * chartDataService: unknown,
 * chartEngine: unknown,
 * chartElementRef: unknown,
 * store: import('redux').Store<any, import('redux').AnyAction>
 * }} param0 
 */
export default function attachQuoteFeed({
  chartDataService,
  chartEngine,
  chartElementRef,
  store
}) {
  const stockRealTime = getAllInstrumentsSetting();

  Object.keys(stockRealTime).forEach(key => {
    if(!stockRealTime[key].isRT) {
      delete stockRealTime[key];
    }
  });

  function isFetchStrategy(symbolObject) {
    const state = store.getState();
    const instrumentId = symbolObject.instrumentId;
    switch(symbolObject.type) {
      case 'T':
        return state.tickers.updateStrategy[instrumentId] === 'fetch';
      case 'P':
        return state.peers.updateStrategy[instrumentId] === 'fetch';
      case 'I':
        return state.indices.updateStrategy[instrumentId] === 'fetch';
      default:
        return true;
    }
  }

  // refreshInterval when config stock realtime
  chartEngine.attachQuoteFeed(
    chartDataService,
    {
      refreshInterval: 60,
      callback: fetchDataChartDone.bind(this, chartElementRef)
    }
    // (data) => isFetchStrategy(data.symbolObject)
  );
  // store.getState().
  // do not set refreshInterval when config stock realtime
  // chartEngine.attachQuoteFeed(
  //   chartDataService,
  //   {
  //     callback: fetchDataChartDone.bind(this, chartElementRef)
  //   },
  //   ({ symbolObject }) => !isFetchStrategy(symbolObject)
  // );
}
