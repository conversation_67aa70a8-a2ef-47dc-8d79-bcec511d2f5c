import dayjs from 'dayjs';

/**
 * 
 * @param {number[]} queryYears 
 */
function createFragment(queryYears = []) {
  const queryYearsString = queryYears.map(year => {
    const startDayOfYear = dayjs().year(year).startOf('year');
    const startDayLastOfYear = startDayOfYear.startOf('d').format('YYYY-MM-DD[T00:00:00Z]');
    const startDayNextYear = startDayOfYear
      .add(1, 'year')
      .startOf('d')
      .format('YYYY-MM-DD[T00:00:00Z]');

    return `_${year}: historicals(
        where: { dateTime: { gte: "${startDayLastOfYear}", lt: "${startDayNextYear}" } }
        first: 1
        order: {
            dateTime: DESC
        }
        ) {
        nodes {
            instrumentId
            close
            open
            high
            dateTime
        }
    }`;
  }).join('\n');
  return `fragment SharePriceDevelopmentByYearsQueryData on Instrument {
    id
    shareName
    market {
        abbreviation
    }
    ${queryYearsString}
  }`;
}

export function getSharePriceDevelopmentByYearsQuery({ queryYears = [] }){
  return `
  query SharePriceDevelopmentByYearsQuery($ids: [Int!]!,$adjClose: Boolean, $toCurrency: String) {
    instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose){
      ...SharePriceDevelopmentByYearsQueryData
    }
  }

  ${createFragment(queryYears)}
`;
}