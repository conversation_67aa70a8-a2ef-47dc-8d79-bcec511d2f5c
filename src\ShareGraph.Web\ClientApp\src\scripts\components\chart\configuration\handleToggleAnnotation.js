export default function handleDrawingAnnotation(isOpen) {
  const chartEngine = this.context.stx;
  if (!chartEngine) return;
  const cqDrawingPalette = chartEngine.uiContext.topNode.querySelector('cq-drawing-palette');
  if (!cqDrawingPalette) return;

  this.classList.toggle('active', isOpen);
  
  if (!isOpen) {
    chartEngine.channel.drawing = false;
    return;
  }
  cqDrawingPalette.setActiveTool('annotation');
  chartEngine.channel.drawing = true;
}
