import { CIQ } from ".";


CIQ.Renderer.Bars = function (config) {
	this.construct(config);
	var params = this.params;
	this.highLowBars = this.barsHaveWidth = this.standaloneBars = true;
	params.type = 'bar';

	params.hlc = params.volume = params.hollow = params.histogram = false;
};

CIQ.inheritsFrom(CIQ.Renderer.Bars, CIQ.Renderer.OHLC, false);

CIQ.Renderer.HLC = function (config) {
	this.construct(config);
	var params = this.params;
	params.type = 'bar';
	this.highLowBars = this.barsHaveWidth = this.standaloneBars = true;
	params.volume = params.hollow = params.histogram = false;
};

CIQ.inheritsFrom(CIQ.Renderer.HLC, CIQ.Renderer.Bars, false);

CIQ.Renderer.OHLC.requestNew = function(featureList, params) {
	var chartType, hlc, colored, holow, volume, histogram;

	chartType = null;
	hlc = params.hlc;
	colored = params.colored;
	holow = params.hollow;
	volume = params.volume;
	histogram = params.histogram;

	for(var i = 0; i < featureList.length; i++){
		switch (featureList[i]) {
			case "bar":
			case "candle":
				chartType = featureList[i];
				break;
			case "volume":
				volume = true;
				break;
			case "hollow":
				holow = true;
				break;
			case "colored":
				colored = true;
				break;
			case "histogram":
				histogram = true;
				chartType = "candle";
				break;
			case "hlc":
				hlc = true;
				chartType = "bar";
				break;
			default:
				return null;
		}
	}
	if(chartType === null){
		return null;
	}

	return new CIQ.Renderer.OHLC({
		params: CIQ.extend(params, {
			type: chartType,
			hlc: hlc,
			colored: colored,
			hollow: holow,
			volume: volume,
			histogram: histogram
		})
	});
}

CIQ.Renderer.OHLC.getChartParts = function (style, colored) {

	var none 		= CIQ.ChartEngine.NONE,// 0
		closeUp 	= CIQ.ChartEngine.CLOSEUP,// 1
		closeDown 	= CIQ.ChartEngine.CLOSEDOWN,// 2
		closeEven 	= CIQ.ChartEngine.CLOSEEVEN,// 4
		candleUp 	= CIQ.ChartEngine.CANDLEUP,// 8
		candleDown 	= CIQ.ChartEngine.CANDLEDOWN,// 16
		candleEven 	= CIQ.ChartEngine.CANDLEEVEN;// 32

	return [{
			type: "histogram",
			drawType: "histogram",
			style: "stx_histogram_up",
			condition: candleUp,
			fill: "fill_color_up",
			border: "border_color_up",
			useColorInMap: true,
			useBorderStyleProp: true
		},
		{
			type: "histogram",
			drawType: "histogram",
			style: "stx_histogram_down",
			condition: candleDown,
			fill: "fill_color_down",
			border: "border_color_down",
			useColorInMap: true,
			useBorderStyleProp: true
		},
		{
			type: "histogram",
			drawType: "histogram",
			style: "stx_histogram_even",
			condition: candleEven,
			fill: "fill_color_even",
			border: "border_color_even",
			skipIfPass: true,
			useColorInMap: true,
			useBorderStyleProp: true
		},
		{
			type: "bar",
			drawType: "bar",
			style: style || "stx_bar_chart",
			border: "border_color",
			useColorInMap: true
		},
		{
			type: "bar",
			drawType: "bar",
			style: "stx_bar_up",
			condition: colored ? candleUp : closeUp,
			border: "border_color_up",
			useColorInMap: true
		},
		{
			type: "bar",
			drawType: "bar",
			style: "stx_bar_down",
			condition: colored ? candleDown : closeDown,
			border: "border_color_down",
			useColorInMap: true
		},
		{
			type: "bar",
			drawType: "bar",
			style: "stx_bar_even",
			condition: colored ? candleEven : closeEven,
			border: "border_color_even",
			skipIfPass: true,
			useColorInMap: true
		},
		{
			type: "candle",
			drawType: "shadow",
			style: "stx_candle_shadow",
			border: "border_color_up"
		},
		{
			type: "candle",
			drawType: "shadow",
			style: "stx_candle_shadow_up",
			condition: candleUp,
			border: "border_color_up"
		},
		{
			type: "candle",
			drawType: "shadow",
			style: "stx_candle_shadow_down",
			condition: candleDown,
			border: "border_color_down"
		},
		{
			type: "candle",
			drawType: "shadow",
			style: "stx_candle_shadow_even",
			condition: candleEven,
			border: "border_color_even",
			skipIfPass: true
		},
		{
			type: "candle",
			drawType: "candle",
			style: "stx_candle_up",
			condition: candleUp,
			fill: "fill_color_up",
			border: "border_color_up",
			useColorInMap: true,
			useBorderStyleProp: true
		},
		{
			type: "candle",
			drawType: "candle",
			style: "stx_candle_down",
			condition: candleDown,
			fill: "fill_color_down",
			border: "border_color_down",
			useColorInMap: true,
			useBorderStyleProp: true
		},
		{
			type: "hollow",
			drawType: "shadow",
			style: "stx_hollow_candle_up",
			condition: closeUp,
			border: "border_color_up"
		},
		{
			type: "hollow",
			drawType: "shadow",
			style: "stx_hollow_candle_down",
			condition: closeDown,
			border: "border_color_down"
		},
		{
			type: "hollow",
			drawType: "shadow",
			style: "stx_hollow_candle_even",
			condition: closeEven,
			border: "border_color_even",
			skipIfPass: true
		},
		{
			type: "hollow",
			drawType: "candle",
			style: "stx_hollow_candle_up",
			condition: closeUp, // 17?
			fill: "fill_color_up",
			border: "border_color_up",
			useColorInMap: true
		},
		{
			type: "hollow",
			drawType: "candle",
			style: "stx_hollow_candle_down",
			condition: closeDown, // 18?
			fill: "fill_color_down",
			border: "border_color_down",
			useColorInMap: true
		},
		{
			type: "hollow",
			drawType: "candle",
			style: "stx_hollow_candle_even",
			condition: closeEven, // 20?
			fill: "fill_color_even",
			border: "border_color_even",
			skipIfPass: true,
			useColorInMap: true
		},
		{
			type: "hollow",
			drawType: "candle",
			style: "stx_hollow_candle_up",
			condition: 9,
			fill: "fill_color_up",
			border: "border_color_up"
		},
		{
			type: "hollow",
			drawType: "candle",
			style: "stx_hollow_candle_down",
			condition: 10,
			fill: "fill_color_down",
			border: "border_color_down"
		},
		{
			type: "hollow",
			drawType: "candle",
			style: "stx_hollow_candle_even",
			condition: 12,
			fill: "fill_color_even",
			border: "border_color_even"
		}
	];
}

export { CIQ };
