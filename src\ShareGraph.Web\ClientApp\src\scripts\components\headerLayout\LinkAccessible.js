import classNames from 'classnames';
import { useContext } from 'react';
import { AppContext } from '../../AppContext';
import i18n from '../../services/i18n';
import { Link } from 'react-router-dom';

function LinkAccessible() {
  const settings = useContext(AppContext);
  const enable = settings.accessibilities.enabled;

  if (!enable) return null;

  return (
    <Link
      className={classNames('accessibility-link', {
        'no-switcher': settings.ticker.enabledFormat.length < 2
      })}
      to={{
        ...location,
        pathname: '/accessibility'
      }}
    >
      {i18n.translate('accessibleShareGraph')}
    </Link>
  );
}

export default LinkAccessible;
