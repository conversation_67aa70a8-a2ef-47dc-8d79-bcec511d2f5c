using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Infrastructure;
using GraphQL;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Configuration;
using Euroland.FlipIT.ShareGraph.API.Models;

namespace Euroland.FlipIT.ShareGraph.API.Services
{
  public class CurrencyService : ICurrencyService
  {
    private readonly IGraphQLClientFactory _graphQLClientFactory;
    private readonly ILogger<CurrencyService> _logger;
    private readonly IConfiguration _configuration;

    public CurrencyService(IGraphQLClientFactory graphQLClientFactory,
                           ILogger<CurrencyService> logger,
                           IConfiguration configuration)
    {
      _graphQLClientFactory = graphQLClientFactory;
      _logger = logger;
      _configuration = configuration;
    }

    public async Task<IEnumerable<Models.Currency>> GetCurrenciesByCodes(List<String> codes)
    {
      var request = new GraphQLRequest
      {
        Query = @"
        query($codes: [String!]!) {
          currency {
            currenciesByCodes(codes: $codes) {
              code
              name
              translationId
            }
          }
        }",
        Variables = new
        {
          codes = codes
        }
      };
      try
      {
        var apiUrl = _configuration["InternalSDataApiUrl"];
        if (string.IsNullOrEmpty(apiUrl) || !System.Uri.TryCreate(apiUrl, System.UriKind.Absolute, out var apiUri))
        {
          throw new ConfigurationErrorsException($"Invalid value or not provide configuration `InternalInvestmentCalculatorApiUrl`. Check it out in `appSettings.json`");
        }
        var response = await _graphQLClientFactory.CreateGraphQLClient(apiUri).SendQueryAsync<CurrencyResponse>(request);
        if (response.Errors != null && response.Errors.Length > 0)
        {
          foreach (var error in response.Errors)
          {
            _logger.LogError($"Error call SData.API from ShareGraph.API: {error.Message}");
          }
        }

        return response.Data.Currency.CurrenciesByCodes;
      }
      catch (Exception ex)
      {
        _logger.LogError($"Error call SData.API from ShareGraph.API: {ex.ToString()}");
        throw;
      }
    }
  }
}
