{"GeneralSettingsPath": "..\\Config", "ToolSettingsPath": "Config", "OpifexToolCompanySettingsPath": "..\\opifex2-api\\wwwroot\\ShareGraph3\\Config\\Company", "OpifexGeneralSettingDirectory": "..\\opifex2-api\\wwwroot\\Config\\Company", "OpifexPreviewEnabled": true, "ConnectionStrings": {"TranslationDbContext": "Server=************;Database=shark;User=uShark;PWD=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;"}, "InternalSDataApiUrl": "http://localhost/tools/sdata-api/graphql"}