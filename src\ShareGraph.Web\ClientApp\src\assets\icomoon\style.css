@font-face {
  font-family: 'euroland';
  src:  url('fonts/euroland.eot?xu6kmg');
  src:  url('fonts/euroland.eot?xu6kmg#iefix') format('embedded-opentype'),
    url('fonts/euroland.ttf?xu6kmg') format('truetype'),
    url('fonts/euroland.woff?xu6kmg') format('woff'),
    url('fonts/euroland.svg?xu6kmg#euroland') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="fs-"], [class*=" fs-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'euroland' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fs-arrow-decrease:before {
  content: "\e90a";
}
.fs-arrow-down:before {
  content: "\e90b";
}
.fs-arrow-increase:before {
  content: "\e90c";
}
.fs-arrow-up:before {
  content: "\e90e";
}
.fs-caret-left:before {
  content: "\e90f";
}
.fs-caret-right:before {
  content: "\e910";
}
.fs-chart:before {
  content: "\e92a";
}
.fs-checked-radio:before {
  content: "\e92b";
}
.fs-close-radio:before {
  content: "\e92c";
}
.fs-graph:before {
  content: "\e92e";
}
.fs-range-arrow-down:before {
  content: "\e92f";
}
.fs-table:before {
  content: "\e930";
}
.fs-triangle-down:before {
  content: "\e931";
}
.fs-triangle-up:before {
  content: "\e932";
}
.fs-isin:before {
  content: "\e900";
}
.fs-list:before {
  content: "\e901";
}
.fs-industry:before {
  content: "\e902";
}
.fs-no-of-shares:before {
  content: "\e903";
}
.fs-symbol:before {
  content: "\e904";
}
.fs-market:before {
  content: "\e905";
}
.fs-market-cap:before {
  content: "\e906";
}
.fs-currency:before {
  content: "\e907";
}
.fs-bid:before {
  content: "\e908";
}
.fs-all-time-high:before {
  content: "\e909";
}
.fs-all-time-low:before {
  content: "\e90d";
}
.fs-ask:before {
  content: "\e911";
}
.fs-ask-size:before {
  content: "\e912";
}
.fs-average-price:before {
  content: "\e913";
}
.fs-bid-size:before {
  content: "\e914";
}
.fs-change:before {
  content: "\e915";
}
.fs-change-percent:before {
  content: "\e916";
}
.fs-high:before {
  content: "\e917";
}
.fs-last:before {
  content: "\e918";
}
.fs-lot-size:before {
  content: "\e919";
}
.fs-low:before {
  content: "\e91a";
}
.fs-market-status:before {
  content: "\e91b";
}
.fs-open:before {
  content: "\e91c";
}
.fs-p-e:before {
  content: "\e91d";
}
.fs-previous-close:before {
  content: "\e91e";
}
.fs-time:before {
  content: "\e91f";
}
.fs-total-trades:before {
  content: "\e920";
}
.fs-turnover:before {
  content: "\e921";
}
.fs-volume:before {
  content: "\e922";
}
.fs-weeks-52-high:before {
  content: "\e923";
}
.fs-weeks-52-low:before {
  content: "\e924";
}
.fs-weeks-52-percent:before {
  content: "\e925";
}
.fs-ytd:before {
  content: "\e926";
}
.fs-ytd-high:before {
  content: "\e927";
}
.fs-ytd-low:before {
  content: "\e928";
}
.fs-ytd-percent:before {
  content: "\e929";
}
