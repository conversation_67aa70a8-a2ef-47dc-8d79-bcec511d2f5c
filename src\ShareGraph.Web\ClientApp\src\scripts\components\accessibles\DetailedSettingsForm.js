import { useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { PERIOD_TYPES, VIEW_TYPES } from '../../common';

import useForm from '../../customHooks/useForm';
import useQuery from '../../customHooks/useQuery';
import { useSelectedTicker } from '../../customHooks/useTickers';
import { formatWithDateTime, isEmptyArray, pickBy, stringifyQuery } from '../../helper';
import i18n from '../../services/i18n';
import AccessibleDateRangePicker from './AccessibleDateRangePicker';
import IndiciesCheckbox from './IndiciesCheckbox';
import MovingAveragesCheckbox from './MovingAveragesCheckbox';
import PeerCheckbox from './PeerCheckbox';

const initialValues = {
  peerIds: [],
  indexIds: [],
  movingAverage: [],
  startDate: null,
  endDate: null
};

const DetailedSettingsForm = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const selectedInstrument = useSelectedTicker();
  const startingDate = useMemo(() => {
    return selectedInstrument?.startingDate ? new Date(selectedInstrument.startingDate) : null;
  }, [selectedInstrument?.startingDate]);

  // [p: peerIds, i: indices, mas: movingAverages]
  const search = useQuery({ arrays: ['p', 'i', 'mas'] });
  const { formState, setFieldValue, setFieldsValue } = useForm(initialValues);
  const onFinish = () => {
    const { startDate, endDate, peerIds, indexIds, movingAverage, ...params } = formState;
    navigate({
      ...location,
      pathname: '/accessibility/share-details',
      search: stringifyQuery(
        pickBy({
          ...search,
          ...params,
          typePage: search.typePage === VIEW_TYPES.DAILY ? undefined : search.typePage,
          period: PERIOD_TYPES.CUSTOM_RANGE,
          p: isEmptyArray(peerIds) ? null : peerIds,
          i: isEmptyArray(indexIds) ? null : indexIds,
          mas: isEmptyArray(movingAverage) ? null : movingAverage,
          startDate: formatWithDateTime(startDate),
          endDate: formatWithDateTime(endDate)
        })
      )
    });
  };

  useEffect(() => {
    const { i: indexIds, p: peerIds, mas, startDate, endDate, typePage, dailyFrom, dailyTo } = search;
    let fromDate = startDate || '';
    let toDate = endDate || '';

    if (typePage === VIEW_TYPES.DAILY) {
      fromDate = dailyFrom;
      toDate = dailyTo;
    }

    setFieldsValue({
      startDate: fromDate ? new Date(fromDate) : initialValues.startDate,
      endDate: toDate ? new Date(toDate) : initialValues.endDate,
      indexIds: indexIds || initialValues.indexIds,
      peerIds: peerIds || initialValues.peerIds,
      movingAverage: mas || initialValues.movingAverage
    });
  }, [location, search.startDate, search.endDate]);

  const mergeDataArray = (originData = [], id) => {
    if (originData.map(o => String(o)).includes(String(id))) {
      return originData.filter(i => String(i) !== String(id));
    }
    originData.push(id);
    return originData;
  };
  const onChangePeer = data => {
    setFieldValue('peerIds', mergeDataArray(formState['peerIds'] || [], data.value));
  };
  const onChangeIndicies = data => {
    setFieldValue('indexIds', mergeDataArray(formState['indexIds'] || [], data.value));
  };
  const onChangeMovingAverage = data => {
    setFieldValue('movingAverage', mergeDataArray(formState['movingAverage'] || [], data.value));
  };
  const onChangeRangeDate = (type, data) => {
    setFieldValue(type, data);
  };

  return (
    <div id="settingdetail" className="detailed-setting-form">
      <h3 id="detailsetting">{i18n.translate('detailedSettings')}</h3>
      <p id="selectcustomperiod">{i18n.translate('selectCustomPeriod')}</p>
      <AccessibleDateRangePicker
        startingDate={startingDate}
        startDate={formState.startDate}
        endDate={formState.endDate}
        onChange={onChangeRangeDate}
      />
      <PeerCheckbox value={formState.peerIds} onChange={onChangePeer} />
      <IndiciesCheckbox value={formState.indexIds} onChange={onChangeIndicies} />
      <MovingAveragesCheckbox value={formState.movingAverage} onChange={onChangeMovingAverage} />
      <div id="submitbutton">
        <button className="detailed-setting-form__submit-button" onClick={onFinish}>
          <span id="lnkSubmit">{i18n.translate('submitToUpdateTable')}</span>
        </button>
      </div>
    </div>
  );
};

export default DetailedSettingsForm;
