/* Tab Bar Styles */
.tab-bar {
  background: #f8f9fa;
  border-bottom: 1px solid #e1e3e6;
  padding: 0;
  overflow-x: auto;
  overflow-y: hidden;
}

.tabs-container {
  display: flex;
  align-items: flex-end;
  min-height: 48px;
  gap: 2px;
}

.tab {
  display: flex;
  align-items: center;
  min-width: 120px;
  max-width: 240px;
  height: 36px;
  background: #e9ecef;
  border: 1px solid #d1d4dc;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  padding: 0 8px;
  margin-bottom: 1px;
}

.tab:hover {
  background: #dee2e6;
}

.tab.active {
  background: #ffffff;
  border-color: #e1e3e6;
  margin-bottom: 0;
  height: 37px;
  z-index: 1;
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.tab-name {
  font-size: 14px;
  font-weight: 500;
  color: #131722;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.tab-star {
  color: #ffc107;
  flex-shrink: 0;
}

.tab-count {
  font-size: 12px;
  color: #6c757d;
  flex-shrink: 0;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-left: 16px;
}

.tab:hover .tab-actions {
  opacity: 1;
}

.tab.active .tab-actions {
  opacity: 1;
}

.tab-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 3px;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
}

button.tab-action-btn {
  width: 20px;
  height: 20px;
  padding: 0;
}

.tab-action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #131722;
}

.close-tab-btn:hover {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.tab-action-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.tab-action-btn:disabled:hover {
  background: transparent;
  color: #6c757d;
}

/* Tab Edit Form */
.tab-edit-form {
  flex: 1;
  padding: 2px;
}

.tab-edit-input {
  width: 100%;
  padding: 4px 6px;
  border: 1px solid #2962ff;
  border-radius: 3px;
  background: #ffffff;
  color: #131722;
  font-size: 14px;
  font-weight: 500;
}

.tab-edit-input:focus {
  outline: none;
  border-color: #2962ff;
}

/* Delete Confirmation */
.delete-confirm {
  display: flex;
  align-items: center;
  gap: 2px;
}

.confirm-delete-btn {
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 3px;
  background: #f44336;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

button.confirm-delete-btn {
  width: 20px;
  height: 20px;
  padding: 0;
}

.confirm-delete-btn:hover {
  background: #d32f2f;
}

.cancel-delete-btn {
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 3px;
  background: #6c757d;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

button.cancel-delete-btn {
  width: 20px;
  height: 20px;
  padding: 0;
}

.cancel-delete-btn:hover {
  background: #5a6268;
}

/* Add Tab */
.add-tab-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e9ecef;
  border: 1px solid #d1d4dc;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.2s;
  color: #6c757d;
  margin-bottom: 1px;
}

button.add-tab-btn {
  width: 36px;
  height: 36px;
  padding: 0;
}

.add-tab-btn:hover {
  background: #dee2e6;
  color: #131722;
}

.add-tab-form {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 160px;
  height: 36px;
  background: #ffffff;
  border: 1px solid #2962ff;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  padding: 0 8px;
  margin-bottom: 1px;
}

.add-tab-input {
  padding: 4px 6px;
  border: none;
  background: transparent;
  color: #131722;
  font-size: 14px;
  font-weight: 500;
}

.add-tab-input:focus {
  outline: none;
}

.add-tab-input::placeholder {
  color: #6c757d;
}

.add-tab-actions {
  display: flex;
  align-items: center;
  gap: 2px;
}

.confirm-add-btn {
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 3px;
  background: #00c853;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

button.confirm-add-btn {
  width: 20px;
  height: 20px;
  padding: 0;
}

.confirm-add-btn:hover {
  background: #00a843;
}

.cancel-add-btn {
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 3px;
  background: #6c757d;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

button.cancel-add-btn {
  width: 20px;
  height: 20px;
  padding: 0;
}

.cancel-add-btn:hover {
  background: #5a6268;
}

/* Content Area */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #131722;
}

/* Watchlists Section */
.watchlists-section {
  padding: 20px;
  border-bottom: 1px solid #e1e3e6;
  background: #f8f9fa;
}

.watchlists-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.watchlist-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #e1e3e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.watchlist-item:hover {
  border-color: #2962ff;
  box-shadow: 0 2px 4px rgba(41, 98, 255, 0.1);
}

.watchlist-item.active {
  border-color: #2962ff;
  background: #e3f2fd;
  box-shadow: 0 2px 8px rgba(41, 98, 255, 0.15);
}

.watchlist-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  cursor: pointer;
}

.watchlist-name {
  font-weight: 500;
  color: #131722;
  font-size: 16px;
}

.default-star {
  color: #ffc107;
}

.instrument-count {
  color: #6c757d;
  font-size: 14px;
}

.watchlist-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.watchlist-item:hover .watchlist-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f8f9fa;
  color: #131722;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  background: transparent;
  color: #6c757d;
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

/* Edit Form */
.edit-form,
.add-watchlist-form {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #2962ff;
  border-radius: 6px;
}

.edit-input,
.create-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d4dc;
  border-radius: 4px;
  background: #ffffff;
  color: #131722;
  font-size: 14px;
}

.edit-input:focus,
.create-input:focus {
  outline: none;
  border-color: #2962ff;
}

.confirm-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #00c853;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirm-btn:hover {
  background: #00a843;
}

.cancel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover {
  background: #5a6268;
}

/* Instruments Section */
.instruments-section {
  flex: 1;
  padding: 10px 0 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.instruments-table {
  flex: 1;
  background: #ffffff;
  border: 1px solid #e1e3e6;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 172px);
}

.table-header {
  display: grid;
  grid-template-columns: 140px 1fr 100px 120px 100px 120px 80px;
  gap: 1px;
  background: #f8f9fa;
  padding: 16px 20px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #6c757d;
  border-bottom: 1px solid #e1e3e6;
  flex-shrink: 0;
}

.header-cell {
  display: flex;
  align-items: center;
}

.table-body {
  flex: 1;
  background: #ffffff;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 140px 1fr 100px 120px 100px 120px 80px;
  gap: 1px;
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.cell {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.cell.symbol {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.symbol-text {
  font-weight: 600;
  color: #131722;
}

.market-text {
  font-size: 11px;
  color: #6c757d;
  font-weight: 400;
}

.favorite-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #d1d4dc;
  transition: all 0.2s;
}

.favorite-btn:hover {
  color: #ffc107;
}

.favorite-btn.active {
  color: #ffc107;
}

.cell.name {
  color: #6c757d;
  font-weight: 400;
}

.cell.price {
  color: #131722;
  font-weight: 600;
}

.cell.change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.cell.change.positive {
  color: #00c853;
}

.cell.change.negative {
  color: #f44336;
}

.cell.change-percent {
  font-weight: 600;
}

.cell.change-percent.positive {
  color: #00c853;
}

.cell.change-percent.negative {
  color: #f44336;
}

.cell.volume {
  color: #6c757d;
  font-weight: 400;
}

.cell.actions {
  justify-content: center;
}

.remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s;
}

.remove-btn:hover {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.empty-state,
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  text-align: center;
  color: #6c757d;
}

.empty-state p,
.loading-state p,
.error-state p {
  margin: 4px 0;
  font-size: 14px;
}

.error-state {
  color: #f44336;
}

/* Empty Watchlist State */
.empty-watchlist-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8f9fa;
  padding: 40px 20px;
}

.empty-watchlist-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.empty-watchlist-icon {
  margin-bottom: 24px;
  color: #6c757d;
  opacity: 0.6;
}

.empty-watchlist-title {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 600;
  color: #131722;
}

.empty-watchlist-description {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
}

.create-first-watchlist-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #2962ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 24px;
}

.create-first-watchlist-btn:hover {
  background: #1e4fd6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(41, 98, 255, 0.3);
}

.first-watchlist-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 300px;
  padding: 24px;
  background: white;
  border: 1px solid #e1e3e6;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.first-watchlist-input {
  padding: 12px 16px;
  border: 1px solid #d1d4dc;
  border-radius: 8px;
  background: #ffffff;
  color: #131722;
  font-size: 16px;
  transition: border-color 0.2s;
}

.first-watchlist-input:focus {
  outline: none;
  border-color: #2962ff;
  box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
}

.first-watchlist-input::placeholder {
  color: #6c757d;
}

.first-watchlist-actions {
  display: flex;
  gap: 12px;
}

.confirm-first-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  padding: 12px 16px;
  background: #00c853;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  justify-content: center;
}

.confirm-first-btn:hover {
  background: #00a843;
}

.cancel-first-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  padding: 12px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  justify-content: center;
}

.cancel-first-btn:hover {
  background: #5a6268;
}

/* Add Instrument Section */
.add-instrument-section {
  position: relative;
  width: 100%;
  padding: 10px 0;
  background: #f8f9fa;
  border-top: 1px solid #e1e3e6;
}

.add-section-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #131722;
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d1d4dc;
  border-radius: 6px;
  background: #ffffff;
  color: #131722;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #2962ff;
}

.search-results {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  border: 1px solid #e1e3e6;
  border-radius: 6px;
  background: #ffffff;
  margin-bottom: 16px;
  z-index: 999;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Virtual List Styles */
.search-results .rc-virtual-list {
  border-radius: 6px;
}

.search-results .rc-virtual-list-holder {
  border-radius: 6px;
}

.search-results .rc-virtual-list-holder-inner {
  border-radius: 6px;
}

.search-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
  height: 60px;
  box-sizing: border-box;
}

.search-result-item:hover {
  background: #f8f9fa;
}

.search-result-item:last-child {
  border-bottom: none;
}

.instrument-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.instrument-info .symbol {
  font-weight: 600;
  color: #131722;
  min-width: 60px;
}

.instrument-info .name {
  color: #6c757d;
  flex: 1;
}

.instrument-info .price {
  font-weight: 600;
  color: #131722;
  min-width: 80px;
}

.instrument-info .change {
  font-weight: 500;
  min-width: 100px;
}

.instrument-info .change.positive {
  color: #00c853;
}

.instrument-info .change.negative {
  color: #f44336;
}

.add-instrument-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a4a6ac;
  border: none;
  border-radius: 4px;
  padding-left: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

button.add-instrument-btn {
  width: 36px;
  height: 36px;
  padding: 0;
}

button.add-instrument-btn:hover {
  background: #a4a6ac;
  color: white;
}

button.add-instrument-btn:disabled {
  background: #d1d4dc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .table-header,
  .table-row {
    grid-template-columns: 120px 1fr 80px 100px 80px 100px 60px;
  }
}

@media (max-width: 768px) {
  .watchlist-container {
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }

  .tab {
    min-width: 100px;
    max-width: 180px;
  }

  .table-header,
  .table-row {
    grid-template-columns: 100px 1fr 80px 80px 60px;
    padding: 12px 16px;
  }

  .header-cell.volume,
  .cell.volume,
  .header-cell.actions,
  .cell.actions {
    display: none;
  }

  .instrument-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .instrument-info .symbol,
  .instrument-info .price,
  .instrument-info .change {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .tab {
    min-width: 80px;
    max-width: 120px;
    padding: 0 4px;
  }

  .tab-name {
    font-size: 12px;
  }

  .tab-count {
    display: none;
  }

  .table-header,
  .table-row {
    grid-template-columns: 80px 1fr 70px;
    gap: 8px;
    padding: 10px 12px;
  }

  .header-cell.change,
  .cell.change,
  .header-cell.change-percent,
  .cell.change-percent {
    display: none;
  }

  .cell.symbol {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  /* Empty Watchlist State - Mobile */
  .empty-watchlist-state {
    padding: 20px 16px;
  }

  .empty-watchlist-title {
    font-size: 24px;
  }

  .empty-watchlist-description {
    font-size: 14px;
    margin-bottom: 24px;
  }

  .create-first-watchlist-btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .first-watchlist-form {
    padding: 20px;
    max-width: 280px;
  }

  .first-watchlist-input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .first-watchlist-actions {
    gap: 8px;
  }

  .confirm-first-btn,
  .cancel-first-btn {
    padding: 10px 12px;
    font-size: 12px;
  }
}
