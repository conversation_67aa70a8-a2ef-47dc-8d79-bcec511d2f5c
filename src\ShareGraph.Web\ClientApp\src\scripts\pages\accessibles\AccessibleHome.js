import DetailedSettingsForm from '../../components/accessibles/DetailedSettingsForm';
import SharePricePerformance from '../../components/accessibles/SharePricePerformance';
import ShareSelect from '../../components/accessibles/ShareSelect';
import i18n from '../../services/i18n';
import SharePriceDetails from '../../components/accessibles/SharePriceDetails';

export default function AccessibleHome() {
  return (
    <div>
      <h1 className="share-performance__titlePage">{i18n.translate('accessibleShareGraph')}</h1>
      <ShareSelect />
      <SharePricePerformance />
      <SharePriceDetails />
      <DetailedSettingsForm />
    </div>
  );
}
