import { useContext, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';

import { AppContext } from '../../AppContext';
import { TICKER_ANIMATION } from '../../common';
import { usePrevious } from '../../customHooks';
import { default as templateTableDefault } from '../../../markup/ticker/TABLE_TICKER_SINGLE.html';
import {
  classByValue,
  classNames,
  convertChangePercentDecimal,
  convertMinutesToString,
  convertNumber,
  convertPercentDecimal,
  formatChangeNumber,
  formatDateTime,
  formatNumberDisplay,
  formatShortDateAsUTC,
  getCountDownMinutesOpenMarket,
  getCustomPhraseTicker,
  getOpenDateTimeByMinute,
  marketStatusByValue,
  replaceKey,
  translateStringFormat
} from '../../helper';
import i18n from '../../services/i18n';
import useCheckHorizontalScroll from '../../customHooks/useCheckHorizontalScroll';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import useTickerLabels from './useTickerLabels';
import { useSelectedTicker } from '../../customHooks/useTickers';

const TableTicker = ({ className = '', data, tickerAnimation, onItemClick = () => {} }) => {
  const { formatNumberByInstrument, formatNumberChangeByInstrument } = useFormatNumberByInstrument();
  const prevData = usePrevious(data);
  const settings = useContext(AppContext);
  const containerRef = useRef(null);
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const selectedTicker = useSelectedTicker();
  const { hasHorizontalScroll } = useCheckHorizontalScroll(containerRef);
  const { getLabelsByTemplate } = useTickerLabels();

  const {turnOverDisplay,marketCapDisplay,numberOfShareDisplay} = settings?.shareDetails;



  function handleInstrumentIdSelect(e) {
    const row = e.target.closest('.table__body-tr');
    const instrumentId = row.dataset.instrumentid;

    // Update ARIA state
    const radioElement = row.querySelector('.table__body-share');
    document.querySelectorAll('.table__body-share').forEach(el => {
      el.setAttribute('aria-checked', 'false');
    });
    radioElement.setAttribute('aria-checked', 'true');
    requestAnimationFrame(() => {
      radioElement.focus(); // Focus the element so screen reader announces the change

      // Use another requestAnimationFrame to ensure focus event has been processed
      requestAnimationFrame(() => {
        onItemClick(parseInt(instrumentId));
      });
    });

  }

  function handleKeyDown(e) {
    if (e.key === 'Enter' || e.key === ' ') { // Handle both Enter and Space
      e.preventDefault();
      const parentRow = e.target.closest('.table__body-tr');
      if (parentRow) {
        handleInstrumentIdSelect({ target: parentRow });
      }
    }
  }

  const addAnimation = (type, fieldChange, item) => {
    if (prevData && item) {
      const instrumentId = item.getAttribute('data-instrumentId');
      let prevCurrentData = prevData?.find(e => e.instrumentId == instrumentId);
      let currentData = data?.find(e => e.instrumentId == instrumentId);
      if (!currentData || !prevCurrentData) return;
      let prevItem = _normalizeData(prevCurrentData);
      let currentItem = _normalizeData(currentData);
      //add animation for change
      let changeValue = currentItem[fieldChange] - prevItem[fieldChange] + 1;
      if (changeValue !== 0) {
        const queryType = `.ticker__change--${type}`;
        const queryChangeValue = queryType + ' .ticker__change-value';
        const queryAnimation = queryType + ' .ticker__animation-inner';
        const queryTransform = `transforming-top-${type}`;
        item.querySelector(queryChangeValue).innerText = prevItem[fieldChange];
        const cloneChangeNode = item.querySelector(queryChangeValue).cloneNode();
        cloneChangeNode.innerText = currentItem[fieldChange];
        if (item.querySelectorAll(`${queryChangeValue}`).length < 2) {
          item.querySelector(`${queryAnimation}`).append(cloneChangeNode);
        }
        changeValue >= 0
          ? item.classList.add(`${queryTransform}`, `${queryTransform}--increase`)
          : item.classList.add('transforming-top', `${queryTransform}--decrease`);
        setTimeout(() => {
          item.classList.remove(
            `${queryTransform}`,
            `${queryTransform}--increase`,
            `${queryTransform}--decrease`
          );
          item.querySelectorAll(`${queryChangeValue}`).length >= 2 &&
            item.querySelector(`${queryChangeValue}:first-child`).remove();
        }, 1000);
      }
    }
  };

  useEffect(() => {
    let currentNode = containerRef.current;
    if (!currentNode) return;
    currentNode.querySelectorAll('.table__body-tr').forEach(item => {
      if (!item) return;
      item.addEventListener('click', handleInstrumentIdSelect);
    });

    currentNode.querySelectorAll('.table__body-share').forEach(item => {
      item.addEventListener('keydown', handleKeyDown);
    });

    return () => {
      currentNode &&
        currentNode.querySelectorAll('.table__body-tr').forEach(item => {
          if (!item) return;
          if (tickerAnimation.toUpperCase() === TICKER_ANIMATION.TRANSFORM) {
            addAnimation('number', 'change', item);
            addAnimation('percent', 'changePercentage', item);
          }
          item.removeEventListener('click', handleInstrumentIdSelect);
        });

      currentNode &&
        currentNode.querySelectorAll('.table__body-share').forEach(item => {
          item.removeEventListener('keydown', handleKeyDown);
        });
    };
  });

  function _getTemplateHtml() {
    const templateTable = settings.ticker.tableTickerTemplate || templateTableDefault;
    let tableHTMLDOM = new DOMParser().parseFromString(templateTable, 'text/html');
    const tableRowHTML = tableHTMLDOM.querySelector('.table__body-tr');
    if (!tableRowHTML) return;
    tableRowHTML.setAttribute('data-instrumentId', '{instrumentId}');
    tableRowHTML.classList.add(
      '{instrumentSelected}',
      '{changeStatus}',
      '{changePerStatus}',
      '{marketStatus}'
    );
    return tableRowHTML.outerHTML;
  }

  function _renderTemplate(item) {
    if (item) {
      const tableRow = _getTemplateHtml();
      if (!tableRow) return '<h2>Table HTML template not recognized</h2>';
      const templateTable = settings.ticker.tableTickerTemplate;
      const labels = {
        ...getLabelsByTemplate(templateTable),
        ...getLabelsByTemplate(tableRow)
      };
      let templateHeading = replaceKey(templateTable, labels);
      let tableHTMLDOM = new DOMParser().parseFromString(templateHeading, 'text/html');
      tableHTMLDOM.querySelector('.table__body-tr').remove();
      let content = data
        .map(x => {
          const dataTicker = _normalizeData(x);
          return replaceKey(tableRow, dataTicker);
        })
        .join('');

      const tableBody = tableHTMLDOM.querySelector('.table__body');
      // tableBody.setAttribute('role', 'radiogroup');
      tableBody.innerHTML = content;

      // Setup accessibility attributes for each row
      const rows = tableBody.querySelectorAll('.table__body-tr');
      rows.forEach(row => {
        const instrumentId = row.getAttribute('data-instrumentId');
        const shareElement = row.querySelector('.table__body-share');
        if (shareElement) {
          shareElement.setAttribute('role', 'radio');
          shareElement.setAttribute('tabindex', '0');
          shareElement.setAttribute('aria-checked', Number(instrumentId) === selectedInstrumentId);

        }
      });

      return tableHTMLDOM.querySelector('.table').outerHTML;
    }

    return '<h2>No data</h2>';
  }

  function _normalizeData(item) {
    //TODO Add time;
    const data = { ...item };
    const instrumentId = item.instrumentId;
    const countDownMinutes = getCountDownMinutesOpenMarket(item);
    data.bid = formatNumberByInstrument(item.bid, instrumentId);
    data.ask = formatNumberByInstrument(item.ask, instrumentId);
    data.change = formatNumberChangeByInstrument(item.change, instrumentId);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = formatNumberByInstrument(Math.max(item.high, item.last), instrumentId);
    data.high52W = formatNumberByInstrument(Math.max(item.high52W, item.last), instrumentId);
    data.last = formatNumberByInstrument(item.last, instrumentId);
    data.low = formatNumberByInstrument(Math.min(item.low, item.last), instrumentId);
    data.low52W = formatNumberByInstrument(Math.min(item.low52W, item.last), instrumentId);
    data.open = formatNumberByInstrument(item.open, instrumentId);
    data.percent52W = convertPercentDecimal(item.percent52W);
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatChangeNumber(item.volumeChange);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.instrumentSelected = selectedInstrumentId === item.instrumentId ? 'selected' : '';
    data.changeStatus = classByValue(item.change);
    data.changePerStatus = 'day-range--' + classByValue(item.changePercentage);
    data.marketStatus = marketStatusByValue(item.marketStatus);
    data.timeToCloseMarket = convertMinutesToString(
      countDownMinutes,
      i18n.translate('hrs'),
      i18n.translate('mins')
    );
    data.dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, item.id, selectedTicker?.timezoneIANA);

    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';
    data.todayTurnover = formatNumberByInstrument(
          formatNumberDisplay(item.todayTurnover, turnOverDisplay), instrumentId
        );
    data.marketCap = formatNumberByInstrument(
      formatNumberDisplay(item.last*item.noShares, marketCapDisplay), instrumentId
    );
    data.noShares = convertNumber(
      formatNumberDisplay(item.noShares, numberOfShareDisplay),
      { decimalDigits: 0 }
    );
    data.lowYtdDate = formatShortDateAsUTC(item.lowYtdDate);
    data.highYtdDate = formatShortDateAsUTC(item.highYtdDate);
    data.highest52wDate = formatShortDateAsUTC(item.highest52wDate);
    data.lowest52wDate = formatShortDateAsUTC(item.lowest52wDate);
    data.allTimeHighDate = formatShortDateAsUTC(item.allTimeHighDate);
    data.allTimeLowDate = formatShortDateAsUTC(item.allTimeLowDate);

    data.allTimeHigh = formatNumberByInstrument(item.allTimeHigh, instrumentId);
    data.allTimeLow = formatNumberByInstrument(item.allTimeLow, instrumentId);
    data.lowYtd = formatNumberByInstrument(item.lowYtd, instrumentId);
    data.highYtd = formatNumberByInstrument(item.highYtd, instrumentId);
    data.percentYtd = convertChangePercentDecimal(item.percentYtd);
    data.totalTrades = convertNumber(item.totalTrades);
    data.prevClose = formatNumberByInstrument(item.prevClose, instrumentId);
    data.lotSize = convertNumber(item.lotSize);
    data.pE = formatNumberByInstrument(item.eps === 0 ? 0 : item.last / item.eps, instrumentId);
    data.averagePrice = formatNumberByInstrument(item.averagePrice, instrumentId);
    data.askSize = convertNumber(item.askSize);
    data.bidSize = convertNumber(item.bidSize);
    return getCustomPhraseTicker(data);
  }

  return (
    <div
      className={classNames(className,
        `table-responsive table-responsive--${tickerAnimation.toLowerCase()} ticker__inner--${tickerAnimation.toLowerCase()}`
        , { 'scrolled-table': hasHorizontalScroll })}
      ref={containerRef}
      dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}
    ></div>
  );
};

export default TableTicker;
