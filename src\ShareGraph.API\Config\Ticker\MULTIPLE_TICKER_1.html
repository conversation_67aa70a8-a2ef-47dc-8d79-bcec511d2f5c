﻿<div class="ticker__item-inner ticker__item-inner--multiple-tickers-1 ticker__item-inner--{instrumentId}">
  <div class="ticker__col">
    <p class="ticker__heading">
      <span class="ticker__name">{tickerName}</span>
      <span class="ticker__market-abbreviation">({marketAbbreviation})</span>
    </p>
    <p class="ticker__price">
      <strong class="ticker__price-number">{last}</strong>
      <span class="ticker__currency-code">{currencyCodeStr}</span>
    </p>
    <p class="ticker__change">
      <span class="ticker__change-value">
        <i class="fs fs-triangle-up"></i>
        <i class="fs fs-triangle-down"></i>
        <span>{change}</span>&nbsp;
        <span class="ticker__change-percentage">({changePercentage}%)</span>
      </span>
    </p>
    <!-- <p class="status-closed">
      <i class="fs fs-close-radio"></i>
      <span class="market-name">{marketName}</span>
      <span class="market-status">{marketCloseLabel}.</span>
    </p>
    <p class="status-opened">
      <i class="fs fs-checked-radio"></i>
      <span class="market-name">{marketName} {marketOpenedLabel}.</span>
    </p> -->

  </div>
</div>
