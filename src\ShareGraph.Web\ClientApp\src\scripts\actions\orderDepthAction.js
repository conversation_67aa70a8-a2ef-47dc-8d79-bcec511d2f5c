import fetchApi from '../services/fetch-api';
import { appSettings } from '../../appSettings';
import appConfig from '../services/app-config';
import { getAllInstrumentsSetting } from '../configs/configuration-app';
import {client, clientRT} from '../services/graphql-client';
import {ORDER_DEPTH_QUERY} from '../graphql-queries/orderDepthQuery';


function getOrderDepthData({selectedInstrumentId, toCurrency}) {

  const configSettings = appConfig.get();
  const isRT = getAllInstrumentsSetting()[selectedInstrumentId]?.isRT ?? false;

  return fetchApi(appSettings.sDataApiUrl, {
    name: 'get-order-depth',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
        orderDepths(
          instrumentId: ${selectedInstrumentId} 
          ${toCurrency ? `toCurrency: "${toCurrency}"` : ''}
          ){
          rowUpdated
          marketDepth{
            buyPrice
            buyVolume
            sellPrice
            sellVolume
          }
        }
      }`
    })
  })
    .then(res => res.json());
}

async function getOrderDepthDataNewQuery({selectedInstrumentId, toCurrency}) {
  const {isRT = false, enabledAdjustPrice} = getAllInstrumentsSetting()[selectedInstrumentId];

  const result = await (isRT ? clientRT.query : client.query)(ORDER_DEPTH_QUERY, { id: selectedInstrumentId, toCurrency, adjClose: enabledAdjustPrice });

  const orderDepth = result.data.instrumentById.orderDepth;
  return {
    data: {
      orderDepths: {
        rowUpdated: orderDepth.rowUpdated,
        marketDepth: orderDepth.marketDepths
      }
    }
  };
}

export const FETCH_ORDER_DEPTH_BEGIN = 'FETCH_ORDER_DEPTH_BEGIN';
export const FETCH_ORDER_DEPTH_SUCCESS = 'FETCH_ORDER_DEPTH_SUCCESS';
export const FETCH_ORDER_DEPTH_FAILURE = 'FETCH_ORDER_DEPTH_FAILURE';

export const fetchOrderDepthBegin = () => ({
  type: FETCH_ORDER_DEPTH_BEGIN
});

export const fetchOrderDepthSuccess = (orderDepths = {}) => {
  return {
    type: FETCH_ORDER_DEPTH_SUCCESS,
    payload: {
      orderDepths
    }
  };
};

export const fetchOrderDepthFailure = (error) => ({
  type: FETCH_ORDER_DEPTH_FAILURE,
  payload: { error }
});


export function fetchOrderDepth() {
  return (dispatch, getState) => {
    const selectedInstrumentId = getState().tickers.selectedInstrumentId;

    if (!selectedInstrumentId) return;

    dispatch(fetchOrderDepthBegin());

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    return getOrderDepthDataNewQuery({selectedInstrumentId, toCurrency})
      .then(json => {
        const data = json?.data?.orderDepths || {};
        dispatch(fetchOrderDepthSuccess(data));
        return data;
      })
      .catch(err =>
        dispatch(fetchOrderDepthFailure(err))
      );
  };
}
