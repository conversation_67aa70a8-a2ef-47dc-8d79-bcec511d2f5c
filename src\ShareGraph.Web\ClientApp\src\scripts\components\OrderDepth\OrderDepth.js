import React, { useContext } from 'react';
import { AppContext } from '../../AppContext';
import { LAYOUT } from '../../common';
import { useSelectedTicker } from '../../customHooks/useTickers';
import i18n from '../../services/i18n';
import OrderDepthTable from './OrderDepthTable';

export default function OrderDepth() {
  const settings = useContext(AppContext);
  const layout = settings.layout || LAYOUT.FULL;
  const selectedInstrument = useSelectedTicker();
  return (
      <>
        {layout === LAYOUT.FULL && (
          <h2 className="title-section performance-details__title">{i18n.translate('orderDepth')}</h2>
        )}
        <div className="order--depth">
          {selectedInstrument?.currencyCode && (
            <p className="order--depth__currency">
              {i18n.translate('currencyLabel')}
              <span>:&nbsp;</span>
              <span>{selectedInstrument.currencyCode}</span>
            </p>
          )}
          <OrderDepthTable />
        </div>
      </>
  );
}
