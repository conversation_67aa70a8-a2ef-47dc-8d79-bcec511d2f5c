import { useMemo } from 'react';
import { useSelector } from 'react-redux';

export const useSelectedTicker = () => {
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const instruments = useSelector(state => state.tickers.instruments);

  const selectedInstrument = useMemo(() => {
    return instruments.find(item => item.instrumentId === selectedInstrumentId);
  }, [instruments, selectedInstrumentId]);

  return selectedInstrument || {};
};
