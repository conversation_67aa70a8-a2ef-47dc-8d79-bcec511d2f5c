import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useClickAway } from '@euroland/react';

import i18n from '../../../services/i18n';
import { classNames, formatLongDate, formatShortDate } from '../../../helper';
import {uniqueID} from '../../../utils/util';

export const DatePicker = ({ className, titleFormat, maxDate, minDate, dateFormat, value, onSelect = () => {} }) => {
  const calendarDateFormat = Calendar.dotNet2CalendarFormat(dateFormat);
  const dateRef = useRef(null);
  const calendarRef = useRef(null);
  const pickerDateContainerRef = useRef();
  const inputRef = useRef();
  const currentDate = new Date();
  maxDate = maxDate || currentDate;
  minDate = minDate || new Date('12/01/1995');
  const maxDte = Calendar.dateToInt(maxDate);
  const minDte = Calendar.dateToInt(minDate);
  const [selectDate, setSelectDate] = useState(currentDate);

  const [showSelectedDate, setShowSelectedDate] = useState(false);
  const showSelectedDateRef = useRef(showSelectedDate);
  const containerRef = useRef(null);

  const datepickerFromRef = useRef(null);
  const fromDateInputId = uniqueID();

  const getDate = calendarDate => {
    const [, Y, m, d] = String(calendarDate.selection.sel[0]).match(/(\d{4})(\d{2})(\d{2})/) || [];
    return new Date(Number(Y), Number(m) - 1, Number(d));
  };

  const hideCalendar = () => {
    setTimeout(() => {
      setShowSelectedDate(false);
    }, 200);
  };

  useClickAway(
    [pickerDateContainerRef, inputRef],
    () => {
      showSelectedDate && setShowSelectedDate(false);
    },
    ['mousedown', 'touchstart', 'click']
  );

  useEffect(() => {
    showSelectedDateRef.current = showSelectedDate;
  }, [showSelectedDate]);

  const disabledHandle = useCallback(
    (date) => {
      if (!!maxDte && Calendar.dateToInt(date) > maxDte) {
        return true;
      }
      if (!!minDte && Calendar.dateToInt(date) < minDte) {
        return true;
      } else {
        return false;
      }
    },
    [maxDte, minDte]
  );

  useLayoutEffect(() => {
    if (calendarRef.current) {
      calendarRef.current.args.disabled = disabledHandle;
      calendarRef.current.redraw();
    }
  }, [disabledHandle]);

  useEffect(() => {
    if (!calendarRef.current) {
      calendarRef.current = new window.Calendar({
        inputField: dateRef.current,
        trigger: dateRef.current,
        bottomBar: false,
        dateFormat: calendarDateFormat,
        min: minDte,
        max: maxDte,
        align: 'Bl/ / /T/r',
        titleFormat: titleFormat || '%B %Y',
        fdow: 0,
        animation: true,
        fixed: true,
        onSelect: function () {
          const selectDateData = getDate(calendarRef.current);
          onSelect(selectDateData);
          setSelectDate(selectDateData);
          showSelectedDateRef.current && hideCalendar();
        }
      });
      calendarRef.current.popup(containerRef.current);
    }

    if (value) {
      calendarRef.current.selection.set(value);
      calendarRef.current.moveTo(value);
    } else {
      calendarRef.current.selection.set(maxDte);
    }
  }, [value && value.toString()]);

  useEffect(() => {
    if (calendarRef.current) {
      calendarRef.current.args.min = minDate;
      calendarRef.current.args.max = maxDate;

      calendarRef.current.redraw();
    }
  }, [maxDte, minDte]);

  const handleShowPopup = () =>{
    setShowSelectedDate(true);
    if (calendarRef.current) {
      calendarRef.current.showYearMenu(calendarRef.current, false);
    }
  };

  return (
    <div className="custom-date-container">
      <div className={classNames('datepicker datepickerFrom', className)} ref={datepickerFromRef}>
        <div ref={pickerDateContainerRef} style={{ display: showSelectedDate ? 'block' : 'none' }}>
          <div className="datepicker__selected-date">
            <p className="datepicker__title">{i18n.translate('selectDate')}</p>
            <p className="datepicker__value">{formatShortDate(selectDate, {isTimezone: false})}</p>
          </div>
          <div ref={containerRef} />
          <input type="text" placeholder="From Date" ref={dateRef} readOnly style={{ display: 'none' }} />
        </div>
        <span className="input-container" aria-hidden="true" onClick={handleShowPopup}>
          <input
            ref={inputRef}
            type="text"
            placeholder="From Date"
            value={formatShortDate(selectDate, {isTimezone: false})}
            readOnly
          />
          <span className="fs-calendar"></span>
        </span>
      </div>
    </div>
  );
};
