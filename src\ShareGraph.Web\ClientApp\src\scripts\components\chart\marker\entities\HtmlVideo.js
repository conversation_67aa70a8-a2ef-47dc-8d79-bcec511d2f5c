export class HtmlVideo {
  constructor(config) {
    this.container = document.createElement('div');
    this.container.style.padding = '56.25% 0 0 0';
    this.container.style.position = 'relative';
    this.container.playerVideo = this;
    this.config = config;

  }

  initHtml() {
    if(this.video) return;
    const video = document.createElement('video');
    video.style.position = 'absolute';
    video.style.top = '0';
    video.style.left = '0';
    video.style.width = '100%';
    video.style.height = '100%';
    video.style.display = 'block';
    this.video = video;
    const {videoUrl, thumbnailUrl} = this.config;
    if (video.canPlayType('video/mp4')) {
      video.setAttribute('src', videoUrl);
      video.setAttribute('poster', thumbnailUrl);
      video.setAttribute('preload', 'none');
    }
    // Edge does not appear to respond to controls
    if (!/Edge/.test(navigator.userAgent)) {
      video.setAttribute('controls', 'controls');
    }
    this.container.appendChild(video);
    this.callback = () => this.config.callback(this.container);
    this.video.addEventListener('play', this.callback);
  }
  
  play() {
    this.initHtml();
    this.video.play();
  }
  pause() {
    if(!this.video) return;
    this.video.pause();
  }

  destroy() {
    this.video.removeEventListener('play', this.callback);
  }

}
