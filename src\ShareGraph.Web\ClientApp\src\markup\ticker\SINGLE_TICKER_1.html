﻿<div class="ticker--single-ticker-1">
  <div class="ticker__col">
    <p class="ticker__heading">
      <span class="ticker__name">{ticker}</span>
      <span class="ticker__market-abbreviation">({marketAbbreviation})</span>
    </p>
    <p class="ticker__prices">
      <span class="ticker__last-price">{last}</span>
      <span class="ticker__currency-code">{currencyCodeStr}</span>
      <span class="ticker__change">
        <i class="fs fs-triangle-up"></i>
        <i class="fs fs-triangle-down"></i>
        <span class="ticker__change-value">
          {change}
          ({changePercentage}%)
        </span>
      </span>
    </p>
    <p class="status-closed">      
      <i class="fs fs-close-radio"></i>
      <span class="market-name">{marketName}</span>  
      <span class="market-status">{marketCloseLabel}.</span>
      <span class="market-date">{dateTimeToMarketOpen}.</span>
    </p>
    <p class="status-opened">
      <i class="fs fs-checked-radio"></i>
      <span class="market-name">{marketName} {marketOpenInLabel}. </span>
      <span class="market-date">{marketWillOpenLabel} {dateTimeToMarketOpen}</span>
    </p>
    
  </div>
  <div class="ticker__col day-range">
    <p class="range__heading">
      <span class="day-range__label">{dayRangeLabel}</span>
      <span class="day-range__change-percent">
        <i class="fs fs-triangle-up"></i>
        <i class="fs fs-triangle-down"></i>
        {changePercentage}%
      </span>
    </p>
    <p class="slider__rectangle">
      <i class="fs fs-triangle-down"></i>
      <span class="slider__rectangle-bar"></span>
    </p>
    <p class="range__highlow">
      <span class="day-range__low">{low}</span>
      <span class="day-range__high">{high}</span>
    </p>
  </div>
  <div class="ticker__col w52-range">
    <p class="range__heading">
      <span class="w52-range__label">{w52RangeLabel}</span>
      <span class="w52-range__change-percent">
        <i class="fs fs-triangle-up"></i>
        <i class="fs fs-triangle-down"></i>
        {percent52W}%
      </span>
    </p>
    <p class="slider__rectangle">
      <i class="fs fs-triangle-down"></i>
      <span class="slider__rectangle-bar"></span>
    </p>
    <p class="range__highlow">
      <span class="day-range__low">{low52W}</span>
      <span class="day-range__high">{high52W}</span>
    </p>
  </div>
  <div class="ticker__col">
    <p>
      <span class="bid-ask__label">{bidAskLabel}:</span>
      <span class="bid-ask__value">{bid}/{ask}</span>
    </p>
    <p>
      <span class="volume__label">{volumeLabel}:</span>
      <span class="volume__value">{volume}</span>
      <i class="fs fs-arrow-increase"></i>
      <span class="relative-volume__value">{volumeChange} X </span>
    </p>
  </div>
</div>
