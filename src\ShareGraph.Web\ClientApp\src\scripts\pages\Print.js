import  {  useContext, useLayoutEffect, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { classNames } from '@euroland/libs';

import { AppContext } from '../AppContext';
import useQuery from '../customHooks/useQuery';
import {fetchTickers, selectTicker} from '../actions/tickerActions';
import {fetchShareDetails} from '../actions/shareDetailsActions';
import {Ticker} from '../components/tickersV2/Ticker';
import ShareDetails from '../components/ShareDetails';
import ChartContext from '../context/ChartContext';
import Chart from '../components/chart/Chart';
import i18n from '../services/i18n';
import { appSettings } from '../../appSettings';
import { fetchPeers } from '../actions/peerActions';
import { fetchIndices } from '../actions/indicesActions';
import { hiddenUnlessElement, toBoolean } from '../helper';
import useCompany from '../customHooks/useCompany';
import { switchCurrency } from '../actions/currencyAction';
import useAutoFocusToPopup from '../customHooks/useAutoFocusToPopup';

export default function PrintComponent() {
  const settings = useContext(AppContext);
  const { mainTickerId, isPrintShareDetail} = useQuery();
  const dispatch = useDispatch();
  const chartState = useState(null);
  const chartContext = useState(null);
  const isTickerInfoLoading = useSelector(state => state.tickers.loading);
  const isPeersInfoLoading = useSelector(state => state.peers.loading);
  const isIndicesInfoLoading = useSelector(state => state.indices.loading);
  const company = useCompany();
  const [currentTickerType, setCurrentTickerType] = useState(settings.ticker.enabledFormat?.[0]);
  const graphTickerType = settings.ticker.graphTickerType?.toLocaleLowerCase();

  const scrollPrint =  () => {
    document.querySelector('body').classList.add('print-graph-container', 'ticker-' + settings.ticker.tickerType.toLocaleLowerCase(), graphTickerType);
  };

  useLayoutEffect(() => {
    const selectedCurrency = window.xprops.data.selectedCurrency;
    if(selectedCurrency){
      dispatch(switchCurrency(selectedCurrency));
    }
    scrollPrint();
  }, []);

  useEffect(() => {
    if(!window.appSettings?.isRtl) return;

    const handle = () => {
      hiddenUnlessElement(document.body.getBoundingClientRect(), document.body);
    };
    window.addEventListener('beforeprint', handle);

    return () => {
      window.removeEventListener('beforeprint', handle);
    };
  }, []);

  useEffect(() => {
    dispatch(fetchTickers());
    dispatch(fetchPeers());
    dispatch(fetchIndices());
    dispatch(selectTicker(parseInt(mainTickerId)));
    dispatch(fetchShareDetails());
  }, []);

  useEffect(() => {
    if(!chartState[0]) return;
    // let customStorageState = JSON.parse(CustomLocalStorage.getItem(chartState[0].customStorageKey)) || {};
    // const {peers,indices} = customStorageState;

    // if(!peers) {
    //   setCustomItemLocalStorage(chartState[0].customStorageKey, {peers: window.xprops.data.peers});
    // }
    // if(!indices) {
    //   setCustomItemLocalStorage(chartState[0].customStorageKey, {indices: window.xprops.data.indices});
    // }

  }, [chartState[0]]);

  const logoCompany = settings.companyLogo ? `${appSettings.customStylesheetUrlBase}${settings.companyLogo.path}` : '';
  const companyObject = settings.companyName ? settings.companyName.find(cn => cn.cultureCode.toLocaleLowerCase() === (appSettings.language || 'en-gb')) : null;
  const companyName =  companyObject ? companyObject.value : '' ;

  const handlePrint = () => {
    window.print();
  };

  useAutoFocusToPopup();
  return (
    <div className={classNames('app__inner', `print--ticker-${currentTickerType?.toLocaleLowerCase()}`)}>
      <div className="print-header">
            {logoCompany && <figure className="print-header__logo">
              <img src={logoCompany} alt=""></img>
            </figure>}
            <div className="print-header__name">{companyName || company.companyName || ''}</div>
            <button className="print-button" onClick={handlePrint}>{i18n.translate('printOption')}</button>
          </div>
    <div className="print-footer"></div>
    <div className='print-ticker-graph-wrapper'>
      <div id="tickerLayout" className="print-ticker-layout">
        <h2 className="print-title">{i18n.translate('printShareTickers')}</h2>
        {settings.ticker.enabledFormat.length > 0 && (
          <div className='print-ticker-wrapper'>
            <Ticker isPrint tickerType={settings.ticker.tickerType} onSwitchTickerType={type => setCurrentTickerType(type)}/>
          </div>
        )}
      </div>
      <div className='print-graph-wrapper'>
          <div className="print-graph">
            <h2 className='print-title'>{i18n.translate('printShareGraph')}</h2>
              {!isTickerInfoLoading && !isPeersInfoLoading && !isIndicesInfoLoading && (
                <ChartContext.Provider value={{chart: chartState, chartContext}}>
                  {/* <Chart isPopout settings={settings}  /> */}
                  <Chart isPopout settings={settings}
                    selectedRange = {window.xprops.data.selectedRange}
                    currentEvent = {window.xprops.data.currentEvent}
                    layout = {window.xprops.data.layout}
                    preferences = {window.xprops.data.preferences}
                    drawings = {window.xprops.data.drawings}
                  />
                </ChartContext.Provider>
              )}
          </div>
          </div>

      {toBoolean(isPrintShareDetail) && (
        <div className='print-share-details'>
          <h2 className='print-title'>{i18n.translate('printShareDetails')}</h2>
          <div className="share-detail">
            <ShareDetails isPrint></ShareDetails>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
