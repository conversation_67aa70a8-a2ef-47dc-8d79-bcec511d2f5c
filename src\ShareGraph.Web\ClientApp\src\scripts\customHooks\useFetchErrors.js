import { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import i18n from '../services/i18n';

function usePrevious(value) {
  const ref = useRef();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

export const openErrorDialog = (message, onConfirm = () => {}) => {
  const integration = window['euroland'];
  const isIFrame = window.self !== window.top;
  /**
   * Creates the integration component that represent for social share dialog.
   * @returns {window.euroland.components.ErrorDialogComponent}
   */
  function createErrorDialogComponent(props) {
    return integration.components.ErrorDialogComponent(props);
  }

  return new Promise((resolve, reject) => {
    try {
      const integrationLayoutPosition = window.xprops ? window.xprops.layout.middle : '#middleLayout';
      const component = createErrorDialogComponent({
        message: message || i18n.translate('somethingWentWrong'),
        onConfirm,
        onClose: () => {
          if (!isIFrame && document.getElementById('middleLayout')) {
            document.getElementById('middleLayout').remove();
          }
        },
        onRendered: () => {
          console.log('Share Dialog has completed its full render');
        }
      });

      if (isIFrame) {
        component.renderTo(window.parent, integrationLayoutPosition);
      } else {
        let middle = document.getElementById('middleLayout');
        if (!middle) {
          middle = document.createElement('div');
          middle.id = 'middleLayout';
          document.body.appendChild(middle);
        }
        component.renderTo(window.parent, integrationLayoutPosition);
      }
      resolve(component);
    } catch (e) {
      reject(e);
    }
  });
};

export const useFetchErrorsTickers = ({ handleError = () => {} }) => {
  
  const loading = useSelector(state => state.tickers.loading);
  const refreshing = useSelector(state => state.tickers.refreshing);
  const fetchError = useSelector(state => state.tickers.fetchError);
  const refreshError = useSelector(state => state.tickers.refreshError);
  const prevLoading = usePrevious(loading);
  const prevRefreshing = usePrevious(refreshing);

  useEffect(() => {
    if (loading || !prevLoading || !fetchError) return;
    handleError();
  }, [loading]);

  useEffect(() => {
    if (refreshing || !prevRefreshing || !refreshError) return;
    handleError();
  }, [refreshing]);
};

export const useFetchErrorPeers = ({ handleError = () => {} }) => {
  const loading = useSelector(state => state.peers.loading);
  const refreshing = useSelector(state => state.peers.refreshing);
  const fetchError = useSelector(state => state.peers.fetchError);
  const refreshError = useSelector(state => state.peers.refreshError);
  const prevLoading = usePrevious(loading);
  const prevRefreshing = usePrevious(refreshing);

  useEffect(() => {
    if (loading || !prevLoading || !fetchError) return;
    handleError();
  }, [loading]);

  useEffect(() => {
    if (refreshing || !prevRefreshing || !refreshError) return;
    handleError();
  }, [refreshing]);
};

export const useFetchErrorIndices = ({ handleError = () => {} }) => {
  const loading = useSelector(state => state.indices.loading);
  const refreshing = useSelector(state => state.indices.refreshing);
  const fetchError = useSelector(state => state.indices.fetchError);
  const refreshError = useSelector(state => state.indices.refreshError);
  const prevLoading = usePrevious(loading);
  const prevRefreshing = usePrevious(refreshing);

  useEffect(() => {
    if (loading || !prevLoading || !fetchError) return;
    handleError();
  }, [loading]);

  useEffect(() => {
    if (refreshing || !prevRefreshing || !refreshError) return;
    handleError();
  }, [refreshing]);
};

export const useFetchErrorShareDetails = ({ handleError = () => {} }) => {
  const loading = useSelector(state => state.shareDetails.loading);
  const fetchError = useSelector(state => state.shareDetails.fetchError);
  const prevLoading = usePrevious(loading);

  useEffect(() => {
    if (loading || !prevLoading || !fetchError) return;
    handleError();
  }, [loading]);
};

export const useFetchErrorWeek52 = ({ handleError = () => {} }) => {
  const loading = useSelector(state => state.week52.loading);
  const fetchError = useSelector(state => state.week52.fetchError);
  const prevLoading = usePrevious(loading);

  useEffect(() => {
    if (loading || !prevLoading || !fetchError) return;
    handleError();
  }, [loading]);
};

export const useFetchErrorPerformanceByYear = ({ handleError = () => {} }) => {
  const loading = useSelector(state => state.performanceByYear.loading);
  const fetchError = useSelector(state => state.performanceByYear.fetchError);
  const prevLoading = usePrevious(loading);

  useEffect(() => {
    if (loading || !prevLoading || !fetchError) return;
    handleError();
  }, [loading]);
};

export const useFetchErrorSharePriceDevelopment = ({ handleError = () => {} }) => {
    const loading = useSelector(state => state.sharePriceDevelopment.loading);
    const fetchError = useSelector(state => state.sharePriceDevelopment.fetchError);
    const prevLoading = usePrevious(loading);
  
    useEffect(() => {
      if (loading || !prevLoading || !fetchError) return;
      handleError();
    }, [loading]);
  };