import { useSelector } from 'react-redux';
import { Skeleton } from '../Skeleton';
import { Peer } from './Peer';
import { Indices } from './Indices';
import { SWITCHER_TYPE, COMPARISON_SWITCH_TYPE } from '../../common';
import Switcher from '../Switcher';
import useComparison from '../../customHooks/useComparison';
import usePeers from '../../customHooks/usePeers';
import useIndices from '../../customHooks/useIndices';
import i18n from '../../services/i18n';

export const Comparison = () => {
  const { comparisonType, handleSwitchFormat, enableComparisonTypes, isType } = useComparison();
  const {fetchLoadingPeer} = usePeers();
  const [{ fetchLoadingIndices }] = useIndices();

  return (
    <div className='comparison-v2'>
        <Switcher
          onClick={handleSwitchFormat}
          type={SWITCHER_TYPE.COMPARISON}
          tabs={enableComparisonTypes}
          tabActive={comparisonType}
          isButton={false}
          ariaLabel={i18n.translate('comparisonTabs')}
        />
        {(fetchLoadingPeer && isType(COMPARISON_SWITCH_TYPE.PEER_TYPE)) && <Skeleton />}
        {(fetchLoadingIndices && isType(COMPARISON_SWITCH_TYPE.INDICES_TYPE)) && <Skeleton />}

        {
          !fetchLoadingPeer && isType(COMPARISON_SWITCH_TYPE.PEER_TYPE) &&
          <div id="peers" role='tabpanel' aria-labelledby='tab-peers' tabIndex={-1} hidden={comparisonType !== COMPARISON_SWITCH_TYPE.PEER_TYPE} className="peers">
            <Peer />
          </div>
        }
        {
          !fetchLoadingIndices && isType(COMPARISON_SWITCH_TYPE.INDICES_TYPE) &&
          <div id="indices" role='tabpanel' aria-labelledby='tab-indices' tabIndex={-1} hidden={comparisonType !== COMPARISON_SWITCH_TYPE.INDICES_TYPE} className="indices"><Indices /></div>
        }

    </div>
  );
};
