import { useId } from 'react';
import i18n from '../../services/i18n';
import ToolTip from '../commons/ToolTip';
import { appSettings } from '../../../appSettings';
import { useSelector } from 'react-redux';
import { getAllInstrumentsSetting } from '../../configs/configuration-app';
import { useSelectedCurrency } from '../../customHooks/useSelectedCurrency';

function TradeDownLoad() {
  const uniqueId = useId();
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const isRT = getAllInstrumentsSetting()[selectedInstrumentId]?.isRT ?? false;
  const selectedCurrency = useSelectedCurrency();

  const downloadExcel = () => {
    const sDownloadLink = `download/trades?companyCode=${appSettings.companyCode}&lang=${appSettings.language}&selectInstrumentId=${selectedInstrumentId}&isRT=${isRT}&toCurrency=${selectedCurrency?.code ?? ''}&v=${appSettings.companyCodeVersion ?? appSettings.companyCode}`;

    const a = document.createElement('a');
    a.href = sDownloadLink;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div className="trades__download">
      <p className="trades__download-text">{i18n.translate('allTrades')}</p>

      <div className="additional-options">
        <div className="option-buttons">
          <button
            className="tooltip-wrapper option-button option-button--excel"
            onClick={downloadExcel}
            aria-labelledby={uniqueId}
          >
            <i aria-hidden="true" className="fs fs-icon-excel"></i>
            <ToolTip aria-hidden="true" id={uniqueId}>
              {i18n.translate('excelOption')}
            </ToolTip>
          </button>
        </div>
      </div>
    </div>
  );
}

export default TradeDownLoad;
