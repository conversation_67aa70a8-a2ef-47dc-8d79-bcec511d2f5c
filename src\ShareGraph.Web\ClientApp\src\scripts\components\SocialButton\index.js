import Sharer from './sharer';
const SocialButton = ({
    url,
    type,
    message
}) => {
  const handleClick = () => {
    const sharer = Sharer({
      height: 600,
      width: 575,
      url,
      message,
      type
    });

    if (window) {
      window.open(sharer.url, sharer.title, sharer.extra);
    }
  };
  return (
    <button aria-label={type} className={`aws-btn aws-btn--${type}`} onClick={handleClick}>
        <i className={`fs fs-${type}`}></i>
    </button>
  );
};

export default SocialButton;
