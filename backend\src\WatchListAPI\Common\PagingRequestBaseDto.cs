﻿using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace WatchListAPI.Common
{
    public class PagingRequestBaseDto
    {
        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 10;
        [FromQuery(Name = "pageIndex")]
        public int PageIndex { get; set; } = 1;

        private string? _keyword { get; set; }
        [FromQuery(Name = "keyword")]
        public string? Keyword
        {
            get => _keyword;
            set => _keyword = value?.Trim();
        }

        public int GetSkip()
        {
            int skip = (PageIndex - 1) * PageSize;
            if (skip < 0)
            {
                skip = 0;
            }
            return skip;
        }
        public List<string> Sort { get; set; } = new();
    }
}
