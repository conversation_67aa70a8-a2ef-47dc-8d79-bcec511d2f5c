import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { CIQ } from '../chartiq-import';
import { classNames } from '@euroland/libs';

import ChartContext from '../../../context/ChartContext';
import { CustomRangeButton } from '../CustomRange/CustomRangeButton';
import { formatShortDate, translateStringFormat } from '../../../helper';
import { useClickAway } from '@euroland/react';
import {MarketTime} from '../../../services/market';
import {useSelectedTicker} from '../../../customHooks/useTickers';
import {setAtLeastMaxTicks} from '../utils';
const ChartShowRange = () => {
  const {
    chart: [chartEngine],
    chartContext: [uiChartContext]
  } = useContext(ChartContext);
  const [active, setActive] = useState({});
  const [isShowRangeMobile, setIsShowRangeMobile] = useState(false);
  const ticker = useSelectedTicker();
  const [openedByKeyboard, setOpenedByKeyboard] = useState(false);
  const [showDialogCustomRange, setShowDialogCustomRange] = useState(false);

  const ref = useRef();
  const customRef = useRef();
  const marketTime = useMemo(() => MarketTime.factoryWindowTimezone(ticker.normalDailyOpen, ticker.normalDailyClose, ticker.marketTimeZone), [ticker]);

  useClickAway([ref], () => {
    setIsShowRangeMobile(false);
  });

  function setSelectionRange(stx, multiplier, base, interval, period, timeUnit, selectedRange) {
    stx.ranges = {
      multiplier: multiplier,
      base: base,
      padding: 40,
      periodicity: {
        interval: interval,
        period: period || 1,
        timeUnit: timeUnit
      }
    };
    stx.range = {
      ...stx.ranges,
      selectedRange
    };

    stx.setSpan(stx.range);
  }
  const onClick = ({ multiplier, base, interval, period, timeUnit, selectedRange }) => {

    if(base === 'today') {
      const { close, open } = marketTime.getOpenCloseInDate(new Date(ticker?.lastUpdatedDate));
      chartEngine.ranges = {
        multiplier: multiplier,
        base: base,
        periodicity: {
          interval: interval,
          period: period,
          timeUnit: timeUnit
        }
      };
      chartEngine.range = { ...chartEngine.range, selectedRange: '1D' };
      chartEngine.layout.setRange = {
        base,
        multiplier,
        padding: 0,
        periodicity: {
          interval, period, timeUnit
        }
      };
      chartEngine.setRange({
        dtLeft: open, // Date of the left edge of chart
        dtRight: close, // Date of the right edge of chart
        periodicity: {
          period: 1,
          interval: interval,
          timeUnit: timeUnit
        }
      }, () => {
        setAtLeastMaxTicks(chartEngine, 100);
      });

      return;
    }

    setSelectionRange(
      chartEngine,
      multiplier,
      base,
      interval,
      period,
      timeUnit,
      selectedRange
    );
  };

  useEffect(() => {
    if (!chartEngine) return;
    const hanldeSelectedRange = () => {
      setActive(chartEngine.range);
    };
    CIQ.UI.observeProperty('range', chartEngine, hanldeSelectedRange);
    return () => {
      CIQ.UI.unobserveProperty('range', chartEngine, hanldeSelectedRange);
    };
  }, [chartEngine]);

  const handleDatePickerChange = obj => {
    if (!chartEngine || !chartEngine.range) return;

    const range = { ...chartEngine.range, selectedRange: 'CUSTOM_RANGE' };

    let timeUnit = 'day';
    let interval = 1;
    if (dayjs() <= dayjs(obj.from).add(7, 'day')) {
      timeUnit = 'minute';
    } else if (dayjs() <= dayjs(obj.from).add(30, 'day')) {
      timeUnit = 'minute';
      interval = 5;
    }

    const dtLeft = obj.from;
    const dtRight = dayjs(obj.to).isSame(dayjs(obj.from))
      ? new Date(obj.to.getFullYear(), obj.to.getMonth(), obj.to.getDate(), 23, 59, 59)
      : obj.to;
    range.dtLeft = dtLeft;
    range.dtRight = dtRight;

    chartEngine.range = range;

    chartEngine.setRange({
      dtLeft: dtLeft, // Date of the left edge of chart
      dtRight: dtRight, // Date of the right edge of chart
      periodicity: {
        period: 1,
        interval: interval,
        timeUnit: timeUnit
      }
    });
  };
  const renderCustomRange = () => {
    const customRangeMenu = uiChartContext?.config?.rangeMenu?.find(range => range.key === 'CUSTOM_RANGE');
    if (!customRangeMenu) return '';
    if (!active || active?.selectedRange !== customRangeMenu?.key) {
      return (
        <p className="chart-show-range__item-custom-range">
          <span className="fs-calendar" />
          <span className="chart-show-range__custom-range-label">{customRangeMenu.label}</span>
        </p>
      );
    }
    return (
      <p className="chart-show-range__item-custom-range">
        <span className="fs-calendar" />
        <span>{formatShortDate(active.dtLeft, { isTimezone: false })}</span>
        <span> - </span>
        <span>{formatShortDate(active.dtRight, { isTimezone: false })}</span>
      </p>
    );
  };

  const handleClickRangeItem = (range) => {
    const currentPeriodicity = chartEngine.getPeriodicity();
    const periodicity = range.key === 'CUSTOM_RANGE' ? currentPeriodicity : range.periodicity;
    onClick({
      multiplier: range.multiplier,
      base: range.base,
      interval: periodicity.interval,
      period: periodicity.period,
      timeUnit: periodicity.timeUnit,
      selectedRange: range.key
    });
  };

  const handleKeyUp = (event, range) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if(range.key === 'CUSTOM_RANGE') {
        setShowDialogCustomRange(true);
        return;
      }
      handleClickRangeItem(range);
      setOpenedByKeyboard(true);
      setIsShowRangeMobile(!isShowRangeMobile);
    }
  };

  const handleShowDialogCustomRange = (isOpen) => {
    setShowDialogCustomRange(isOpen);
    if(!isOpen) {
      setOpenedByKeyboard(false);
    }
  };

  const selectedRangeConfig = uiChartContext?.config?.rangeMenu?.find(range => range.key === active.selectedRange);

  if (!chartEngine?.chart?.symbol) return null;
  return (
    <>
      <CustomRangeButton
        showDialogCustomRange={showDialogCustomRange}
        openedByKeyboard={openedByKeyboard}
        onShowDialogCustomRange={handleShowDialogCustomRange}
        onChange={handleDatePickerChange}
        onDialogClose={() => {
          setTimeout(() => {
            customRef.current?.focus();
          }, 100);
        }}
        range={active}
      />
      <div className="chart-show-range__wrapper">
        <button
          className="chart-show-range__item-mobile"
          aria-label={
            selectedRangeConfig?.ariaLabel
              ? translateStringFormat('buttonPeriod', [selectedRangeConfig.ariaLabel])
              : undefined
          }
          onClick={() => setIsShowRangeMobile(!isShowRangeMobile)}
        >
          <span className="fs-calendar" />
          <span className="chart-show-range__item-mobile-label">
            {active?.selectedRange === 'CUSTOM_RANGE'
              ? `${formatShortDate(active.dtLeft, {
                  isTimezone: false
                })} - ${formatShortDate(active.dtRight, { isTimezone: false })}`
              : chartEngine.translateIf(active?.selectedRange)}
          </span>
          <i className="fs fs-dropdown-arrow" />
        </button>
        <ul
          role="menu"
          className={classNames('chart-show-range', {
            'chart-show-range__mobile-hide': !isShowRangeMobile
          })}
          ref={ref}
        >
          {(uiChartContext?.config?.rangeMenu || []).map(range => (
            <li
              role="menuitem"
              tabIndex={0}
              aria-label={range.ariaLabel}
              ref={element => {
                if (range.key === 'CUSTOM_RANGE') {
                  customRef.current = element;
                }
              }}
              keyboard-selectable="true"
              className={classNames('chart-show-range__item', range.cls, {
                active:
                  active?.selectedRange === range.key ||
                  active?.selectedRange?.toUpperCase() === range.key?.toUpperCase()
              })}
              key={range.key}
              onClick={(e) => {
                if(range.key === 'CUSTOM_RANGE') {
                  setShowDialogCustomRange(true);
                } else {
                  setShowDialogCustomRange(false);
                  handleClickRangeItem(range);
                }
                setIsShowRangeMobile(!isShowRangeMobile);
              }}
              onKeyUp={e => handleKeyUp(e, range)}
            >
              {range.key !== 'CUSTOM_RANGE' ? range.label : renderCustomRange()}
            </li>
          ))}
        </ul>
      </div>
    </>
  );
};

export default ChartShowRange;
