/* eslint-disable jsx-a11y/no-noninteractive-tabindex */
import { useState, useRef, useEffect } from 'react';
import ShareItem from './ShareItem';
import { useDispatch, useSelector } from 'react-redux';
import Button from '../commons/Button';
import { Skeleton } from '../Skeleton';
import appConfig from '../../services/app-config';
import i18n from '../../services/i18n';
import { LAYOUT } from '../../common';
import { getHeightShareDetail, dynamicSort } from '../../helper';
import TickerName from '../TickerName';
import { fetchShareDetails } from '../../actions';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';

function ShareDetails({ isPrint = false }) {
  const settings = appConfig.get();
  let {
    displayType,
    partialDisplay,
    shareDataItems,
    numberItemsInCollapsedMode,
    numberItemsInPrinting,
    displayOnSelectedTicker,
    shareDetailFieldsCus
  } = settings.shareDetails;
  const { instruments } = settings;
  const { tickerType } = settings.ticker;
  const layout = settings.layout || LAYOUT.FULL;
  const shareDetailRef = useRef(null);
  let numberFieldsValid = 0;
  let dataItems = [];
  const selectedInstrumentId = useSelector(
    (state) => state.tickers.selectedInstrumentId
  );
  const shareDetailsData = useSelector(
    (state) => state.shareDetails.instruments
  );
  const fetchLoading = useSelector((state) => state.shareDetails.loading);
  const [enableShowMore, setEnableShowMore] = useState(partialDisplay);
  let numberItemInCollapse =
    numberItemsInCollapsedMode === 0 ? 0 : numberItemsInCollapsedMode || 8;

  const dispatch = useDispatch();

  //only change for print feature
  if (isPrint) {
    partialDisplay = false; // not display Show More
    displayOnSelectedTicker = true; // alway display data for main share
    numberItemInCollapse = shareDetailFieldsCus.length;
  }

  if (shareDataItems && shareDataItems.length) {
    shareDataItems.forEach((field) => {
      let fieldValue = shareDetailFieldsCus[field.trim().toUpperCase()];
      if (fieldValue) {
        numberFieldsValid++;
      }
    });
  }

  if (
    (enableShowMore && partialDisplay && displayOnSelectedTicker) || //show collapse and selected ticker
    (enableShowMore && displayOnSelectedTicker) || // only selected ticker
    (enableShowMore && partialDisplay) // only show collapse
  ) {
    const tickerDefault = instruments
      .filter((x) => x.default)
      .sort(dynamicSort('order'))
      .find((x) => x.default);
    const tickerDefaultId = tickerDefault
      ? tickerDefault.id
      : instruments[0].id;

    //get default item
    const defaultItemId = displayOnSelectedTicker
      ? selectedInstrumentId
      : tickerDefaultId;
    const dataCollapse = shareDetailsData.filter(
      (e) => e.instrumentId === defaultItemId
    );
    dataItems = dataCollapse;
  } else {
    dataItems = [...shareDetailsData];
  }

  useEffect(() => {
    dispatch(fetchShareDetails());
  }, [dispatch]);

  useChangeQuoteCurrency(() => {
    dispatch(fetchShareDetails());
  });

  return (
    <div
      ref={shareDetailRef}
      className={`share-details share-details--${displayType.toLowerCase()}`}
    >
      {fetchLoading && (
        <Skeleton
          style={{
            height: getHeightShareDetail(
              displayType,
              dataItems.length,
              numberFieldsValid
            )
          }}
        />
      )}
      {!fetchLoading &&
        layout.toLocaleLowerCase() === LAYOUT.FULL.toLocaleLowerCase() && (
          <h2 className='title-section share-details__title'>
            {i18n.translate('shareDataTab')}
          </h2>
        )}
      {!fetchLoading &&
        dataItems.map((shareDetail) => (
          <div
            role='region'
            tabIndex={0}
            aria-label={shareDetail.shareName}
            data-order={shareDetail.order}
            data-key={shareDetail.instrumentId}
            key={shareDetail.instrumentId}
            className={
              shareDetail.instrumentId === selectedInstrumentId
                ? 'share-detail main-share'
                : 'share-detail'
            }
          >
            {tickerType.toLowerCase() !== 'single' &&
              layout.toLocaleLowerCase() ===
                LAYOUT.FULL.toLocaleLowerCase() && (
                <h3 className='share-detail__title'>
                  <TickerName
                    instrumentId={shareDetail.instrumentId}
                    marketAbbreviation={shareDetail.marketAbbreviation}
                    shareName={shareDetail.shareName}
                  />
                </h3>
              )}
            {tickerType.toLowerCase() !== 'single' &&
              layout.toLocaleLowerCase() ===
                LAYOUT.FIXED.toLocaleLowerCase() && (
                <h2 className='share-detail__title'>
                  <TickerName
                    instrumentId={shareDetail.instrumentId}
                    marketAbbreviation={shareDetail.marketAbbreviation}
                    shareName={shareDetail.shareName}
                  />
                </h2>
              )}
            <div className='share-detail__list'>
              {enableShowMore
                ? (shareDetail.dataFields || [])
                    .slice(0, numberItemInCollapse)
                    .map((item, index) =>
                      index >= numberItemsInPrinting && isPrint ? null : (
                        <ShareItem key={index} item={item} />
                      )
                    )
                : (shareDetail.dataFields || []).map((item, index) =>
                    index >= numberItemsInPrinting && isPrint ? null : (
                      <ShareItem key={index} item={item} />
                    )
                  )}
            </div>
          </div>
        ))}
      {!fetchLoading &&
        partialDisplay &&
        dataItems[0].dataFields &&
        dataItems[0].dataFields.length > numberItemInCollapse && (
          <div className='share-details__footer'>
            <Button
              className={`btn-show btn-show--${
                enableShowMore ? 'more' : 'less'
              }`}
              onClick={() => {
                setEnableShowMore(!enableShowMore);
              }}
              type='button'
              aria-label={i18n.translate(
                enableShowMore ? 'showMore' : 'showLess'
              )}
            >
              <span className='btn-show__text'>
                {i18n.translate(enableShowMore ? 'showMore' : 'showLess')}
              </span>
              <i className='fs fs-triangle-up'></i>
            </Button>
          </div>
        )}
    </div>
  );
}

export default ShareDetails;
