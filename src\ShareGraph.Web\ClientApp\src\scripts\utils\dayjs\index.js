// locales
//import 'dayjs/locale/ar';
// plugins
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone'; // To convert timeZone
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import 'dayjs/locale/ar';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import preParsePostFormat from 'dayjs/plugin/preParsePostFormat';
import updateLocale from 'dayjs/plugin/updateLocale';

/**
 * @typedef {[string, string]} DateNameSettingsPeriods [am, pm]
 *
 * @typedef {{
 *  dayNames: string[],
 *  shortDayNames: string[],
 *  shortestDayNames: string [],
 *  monthNames: string[],
 *  shortMonthNames: string[],
 *  period: DateNameSettingsPeriods,
 * }} DateNameSettings
 */

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);
dayjs.extend(localizedFormat);
dayjs.extend(preParsePostFormat);
dayjs.extend(updateLocale);

// prevent die app when parse dateNames false
try {
  const dateNames = /** @type { DateNameSettings } */ (JSON.parse(window.appSettings.dateNames));

  // we use english for the base language and other language will overwrite them
  dayjs.updateLocale('en', {
    weekdays: dateNames.dayNames,
    weekdaysShort: dateNames.shortDayNames,
    weekdaysMin: dateNames.shortestDayNames,
    months: dateNames.monthNames,
    monthsShort: dateNames.shortMonthNames,
    meridiem: (hour, minute, isLowercase) => {
      // OPTIONAL, AM/PM
      return hour > 12 ? dateNames.period[1] : dateNames.period[0];
    }
  });
  dayjs.locale('en');
  window.dayjs = dayjs;
} catch (e) {
  // console.error(e)
}

export default dayjs;
