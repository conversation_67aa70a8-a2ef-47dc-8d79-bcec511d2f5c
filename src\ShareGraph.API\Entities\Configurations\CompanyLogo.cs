using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class CompanyLogo
  {
    public string Path { get; set; }
    public string Alpha { get; set; }
    public string Width { get; set; }
    public string Height { get; set; }
  }

  public class CompanyLogoConfig
  {
    public string Path { get; set; }
    public string Alpha { get; set; }
    public string Width { get; set; }
    public string Height { get; set; }
  }
  public class CompanyName
  {
    public string CultureCode { get; set; }
    public string Value { get; set; }
  }
}
