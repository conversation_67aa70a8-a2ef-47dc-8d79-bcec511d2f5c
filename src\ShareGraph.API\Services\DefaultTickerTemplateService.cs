using System;
using System.IO;
using System.Threading.Tasks;
using Ganss.Xss;
using Microsoft.Extensions.FileProviders;

namespace Euroland.FlipIT.ShareGraph.API.Services
{
  public class DefaultTickerTemplateService : ITickerTemplateService, System.IDisposable
  {
    private readonly IFileProvider _templateFileProvider;
    private readonly HtmlSanitizer _htmlSanitizer;
    public DefaultTickerTemplateService(IFileProvider templateFileProvider)
    {
      _templateFileProvider = templateFileProvider ?? throw new ArgumentNullException(nameof(templateFileProvider));
      _htmlSanitizer = new HtmlSanitizer();
      _htmlSanitizer.AllowedAttributes.Add("class");
    }

    public virtual async Task<string> BuildTickerTemplate(string companyCode, string templateName)
    {
      if (string.IsNullOrWhiteSpace(companyCode))
      {
        throw new ArgumentException("CompanyCode must has a valid value.", nameof(companyCode));
      }

      if (string.IsNullOrWhiteSpace(companyCode))
      {
        throw new ArgumentException("Ticker TemplateName must has a valid value.", nameof(templateName));
      }

      templateName = EnsureHtmlFileExtension(templateName);

      var fileInfo = GetFileInfo(GetCompanyTemplateFileName(companyCode, templateName));

      if (!fileInfo.Exists)
      {
        fileInfo = GetFileInfo(GetDefaultTemplateFileName(templateName));
      }

      if (!fileInfo.Exists)
      {
        return null;
      }

      using var reader = new StreamReader(fileInfo.CreateReadStream(), System.Text.Encoding.UTF8);
      return SanitizeHtml((await reader.ReadToEndAsync()));
    }

    private IFileInfo GetFileInfo(string subpath)
    {
      return _templateFileProvider.GetFileInfo(subpath);
    }

    private string GetCompanyTemplateFileName(string companyCode, string templateName)
    {
      // Config/Company/dk-cbg/template.html
      return string.Format("Company\\{0}\\{1}", companyCode, templateName);
    }

    private string GetDefaultTemplateFileName(string template)
    {
      // Config/Ticker/template.html
      return "Ticker\\" + template;
    }

    private string EnsureHtmlFileExtension(string filename)
    {
      if (!string.Equals(System.IO.Path.GetExtension(filename), ".html", StringComparison.InvariantCultureIgnoreCase))
      {
        filename = System.IO.Path.ChangeExtension(filename, ".html");
      }

      return filename;
    }

    protected virtual string SanitizeHtml(string html)
    {
      if (string.IsNullOrWhiteSpace(html))
      {
        return null;
      }

      return _htmlSanitizer.Sanitize(html);
    }

    public void Dispose()
    {

    }
  }
}