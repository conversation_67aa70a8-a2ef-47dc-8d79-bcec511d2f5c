import React, { Fragment } from 'react';
import { classNames, convertNumberDecimal, convertPercentDecimal, formatNegativeNumber, translateStringFormat } from '../../helper';
import i18n from '../../services/i18n';
import ToolTip from '../commons/ToolTip';
import { DEVICES } from '../../common';
import { getDeviceType } from '../../helper';
import useResize from '../../customHooks/useResize';
import { useSelector } from 'react-redux';
import TickerName from '../TickerName';

const appSettings = window.appSettings;
const Graph = ({ headings, datas, summary, caption}) => {
    useResize();
    const changeFrom52WeekLow = i18n.translate('changeFrom52WeekLow');
    const changeFrom52WeekHigh = i18n.translate('changeFrom52WeekHigh');
    const notFoundSummary = i18n.translate('notFoundSummary');
    const notFoundCaption = i18n.translate('notFoundCaption');
    const lastPriceLabel = i18n.translate('lastPriceLabel');
    const isBlindMode = useSelector(state => state.blindMode.isBlindMode);
    const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
    const mainTickerData = useSelector(state => state.tickers.instruments.find(ins => ins.instrumentId === selectedInstrumentId));

    if(!headings || headings.length == 0) return<></>;
    const shareNameHeading = i18n.translate('sharesLabel');
    const graphTableHeaders = (
                <thead className='table__head'>
                    <tr className='table__head-tr'>
                        {
                        headings.map((heading, i)=>{
                            // if(getDeviceType() === DEVICES.MD || getDeviceType() === DEVICES.LG || getDeviceType() === DEVICES.XL || getDeviceType() === DEVICES.XXL) {
                                return(
                                    <th scope="col" key={i}>{ heading.columnDisplay === shareNameHeading && ' ' || <span className={`${heading.columnDisplay}__heading`}>{heading.columnDisplay}</span>}</th>
                                );
                            // } else {
                            //     return(null);
                            // }
                            }
                        )}
                    </tr>
                </thead>
    );

    const graphTableBody = datas.map((data, j)=>{
        if(Object.keys(data).length !== 0){
            const priceLowList = datas.map((item)=> item.percent52WLow !== 'NaN' ? item.percent52WLow : 0);
            const priceHighList = datas.map((item)=> item.percent52WHigh !== 'NaN' ? item.percent52WHigh : 0);
            const highestPrice = Math.max(...priceLowList,...priceHighList);
            const lastPrice = data.last;

        return(
            <tr key={j}>
                {headings.map((heading, k)=>{
                    const fields = heading.fieldName;
                    let dataFieldName = data[fields].replace('-','');

                    const lowPercentValue = Math.abs(data.percent52WHigh);
                    const lowPercentDisplay = convertPercentDecimal(Math.abs(data.percent52WLow));
                    const lowBarwidthStyle = ((((lowPercentValue)/highestPrice)*75)+(7)+'%');
                    const mvlowBarwidthStyle = ((((lowPercentValue)/highestPrice)*100));
                    let mvlowBarleftStyle = mvlowBarwidthStyle;

                    const highPercentValue = Math.abs(data.percent52WHigh);
                    const highPercentValueDisplay = convertPercentDecimal(Math.abs(data.percent52WHigh) * (-1));
                    const highBarwidthStyle = ((((highPercentValue)/highestPrice)*75)+(7)+'%');
                    const mvhighBarwidthStyle = ((((highPercentValue)/highestPrice)*100));
                    let mvhighBarrightStyle = mvhighBarwidthStyle;

                    if(getDeviceType() === DEVICES.MD || getDeviceType() === DEVICES.LG || getDeviceType() === DEVICES.XL || getDeviceType() === DEVICES.XXL) {
                        return(
                            <Fragment key={k}>
                            { fields === 'shareName' && <td><TickerName instrumentId={data.instrumentId} marketAbbreviation={data.marketAbbreviation} shareName={data.shareName} /></td> ||
                              fields === 'low52W' && <td className='weeks52graph--tabledata-low'>
                                  <div className='weeks52graph--tabledata-low__currency'>{data.currencyCode ? translateStringFormat('currency', [data.currencyCode]): ''}</div>
                                <div className='table-datas'><span className='weeks52graph--table__value-low'>{dataFieldName} </span>
                                    <div className={classNames('value-low_bar tooltip-wrapper showPopup', { 'blind-mode': isBlindMode })} style={{width: `${lowBarwidthStyle}`}}>{lowPercentDisplay+'%'}
                                    <ToolTip>
                                    {lastPriceLabel} : {lastPrice}
                                    </ToolTip>
                                </div></div></td> ||
                              fields === 'high52W' && <td className='weeks52graph--tabledata-high'>
                            <div className='table-datas'><div className={classNames('value-high_bar tooltip-wrapper showPopup', { 'blind-mode': isBlindMode })} style={{width: `${highBarwidthStyle}`}}>{highPercentValueDisplay+'%'}
                                    <ToolTip>
                                    {lastPriceLabel} : {lastPrice}
                                    </ToolTip>
                                </div> <span className='weeks52graph--table__value-high'>{dataFieldName}</span></div></td>}
                            </Fragment>
                            );

                    } else {
                        if(mvlowBarleftStyle>=90 || mvhighBarrightStyle>=90){
                            mvlowBarleftStyle = parseFloat(mvlowBarleftStyle-23);
                            mvhighBarrightStyle = parseFloat(mvhighBarrightStyle-23);
                        }
                        return(
                            <Fragment key={k}>
                            { fields === 'shareName' && <td className='small-view'><TickerName instrumentId={data.instrumentId} marketAbbreviation={data.marketAbbreviation} shareName={data.shareName} /></td> || fields === 'high52W' &&
                            <td className='weeks52graph--tabledata-small-view'>

                                <div className='table-datas'>
                                    <div className='value-low_bar tooltip-wrapper showPopup' style={{width: `${mvlowBarwidthStyle}%`}}>{lowPercentDisplay+'%'}
                                    <ToolTip>
                                    {lastPriceLabel} : {lastPrice}
                                    </ToolTip>
                                </div></div>
                                <div className='table-datas'>
                                    <div className='value-high_bar tooltip-wrapper showPopup' style={{width: `${mvhighBarwidthStyle}%`}}>{highPercentValueDisplay+'%'}
                                 <ToolTip>
                                    {lastPriceLabel} : {lastPrice}
                                    </ToolTip>
                                </div></div>
                                <div className='dis-flex'>
                                <span className='weeks52graph--table__value-low' style={{
                                    marginRight: appSettings?.isRtl ? 0 : `${(mvlowBarleftStyle/2)}%`,
                                    marginLeft: appSettings?.isRtl ?  `${(mvlowBarleftStyle/2)}%` : 0
                                    }}>{data.low52W} </span>
                                <div className='weeks52graph--table__value-low__currency'>{translateStringFormat('currency', [mainTickerData.currencyCode])}</div>
                                <span className='weeks52graph--table__value-high' style={{
                                    marginLeft: appSettings?.isRtl ? 0 : `${(mvhighBarrightStyle/2)}%`,
                                    marginRight: appSettings?.isRtl ? `${(mvhighBarrightStyle/2)}%` : 0
                                    }}>{dataFieldName}</span>
                                </div>
                                </td>
                                }
                            </Fragment>
                            );
                    }
                })}
            </tr>
        );
        }
        else {
            return(
                <tr key={j}>
                    <td className='text-center'></td>
                    <td colSpan={headings.length-1} className='text-center'>{i18n.translate('notFoundData')}</td>
                </tr>
            );
        }
    });
    return (
        <>
        <div className='weeks52graph--box'>

            <table summary={summary ? summary : `${notFoundSummary}`}  className='table weeks52graph--table'>
            <caption>{caption ? caption : `${notFoundCaption}`}</caption>
                    {graphTableHeaders}
                <tbody className='table__body'>
                    {graphTableBody}

                </tbody>
            </table>
            </div>

            <div className='graph--labelsbottom'>
                <div className='value-indicator'><span className={classNames('graph__lowvalue-indicator', { 'blind-mode': isBlindMode })}></span> {changeFrom52WeekLow}</div>
                <div className='value-indicator'><span className={classNames('graph__highvalue-indicator', { 'blind-mode': isBlindMode })}></span> {changeFrom52WeekHigh}</div>
            </div>
        </>
    );
};

export default Graph;
