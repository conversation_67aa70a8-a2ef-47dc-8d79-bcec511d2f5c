﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using WatchListAPI.Common.Consts;
using WatchListAPI.Common.Filters;
using WatchListAPI.Common.Settings;
using WatchListAPI.Infrastructure;
using WatchListAPI.Services;

namespace WatchListAPI.Common.Extensions
{
    public static class ProgramExtentions
    {
        public static IServiceCollection RegisterServices(this IServiceCollection services)
        {
            services.AddScoped<IWatchListService, WatchListService>();
            services.AddScoped<IInstrumentService, InstrumentService>();
            return services;
        }
        public static IServiceCollection RegisterRepositories(this IServiceCollection services)
        {
            services.Scan(scan => scan
                .FromAssemblies(AppDomain.CurrentDomain.GetAssemblies())
                .AddClasses(classes => classes.AssignableTo(typeof(IRepositoryBase<,,>)))
                .AsImplementedInterfaces()
                .WithScopedLifetime()
            );

            return services;
        }

        public static IServiceCollection AddUnitOfWorks(this IServiceCollection services)
        {
            services.Scan(scan => scan
                .FromAssemblies(AppDomain.CurrentDomain.GetAssemblies())
                .AddClasses(classes => classes.AssignableTo(typeof(IUnitOfWorkBase<>)))
                .AsImplementedInterfaces()
                .WithScopedLifetime()
            );

            return services;
        }

        public static IServiceCollection AddSwaggerService(this IServiceCollection services, Action<SwaggerGenOptions> callback = null)
        {
            services.AddSwaggerGen(c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                  {
                    new OpenApiSecurityScheme
                    {
                      Reference = new OpenApiReference
                      {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                      },
                      Scheme = "oauth2",
                      Name = "Bearer",
                      In = ParameterLocation.Header,

                    },
                      new List<string>()
                  }
                });
                c.CustomSchemaIds(type => type.ToString().Replace("`", "").Replace("[", "Of").Replace(",", "And").Replace("]", ""));

                c.SchemaFilter<EnumSchemaFilter>();

                if (callback != null)
                {
                    callback(c);
                }
            });

            return services;
        }

        public static IServiceCollection AddHttpClientService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient(HttpClientFactoryNameConst.SDataRequest, client =>
            {
                var wiseBaseUri = configuration.GetSection("SDataGraphQL").Value
                                                        ?? throw new ArgumentNullException("SDataGraphQL");
                client.BaseAddress = new Uri(uriString: wiseBaseUri);
                client.DefaultRequestHeaders.UserAgent.ParseAdd("dotnet-docs");
            });

            services.AddHttpClient(HttpClientFactoryNameConst.KeyCloakRequest, client =>
            {
                var identityServerUri = configuration.GetSection("Keycloak:BaseUrl").Value
                                                        ?? throw new ArgumentNullException("Keycloak:BaseUrl");
                client.BaseAddress = new Uri(uriString: identityServerUri);
                client.DefaultRequestHeaders.UserAgent.ParseAdd("dotnet-docs");
            });

            return services;
        }

        public static IServiceCollection AddKeycloakAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            var appSettings = configuration.GetSection("Authentication").Get<JwtBearerSettings>()
                    ?? throw new ArgumentNullException("Missing \"AppSettings\" block in appsettings.json file");
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.IncludeErrorDetails = true;
                options.RefreshOnIssuerKeyNotFound = true;
                options.SaveToken = true;
                options.RequireHttpsMetadata = appSettings.RequireHttpsMetadata;
                options.Authority = appSettings.Authority;

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidIssuer = appSettings.ValidIssuer,
                    ValidateAudience = true,
                    ValidAudiences = appSettings.ValidAudiences,
                    ValidateIssuerSigningKey = true,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromSeconds(appSettings.ClockSkewSeconds),
                };
            });

            return services;
        }

        public static IServiceCollection AddConfiguredCors(this IServiceCollection services, IConfiguration configuration)
        {
            var allowedOrigins = configuration.GetSection("AllowedOrigins").Get<string[]>()
                                 ?? throw new ArgumentNullException("AllowedOrigins");

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", policyBuilder =>
                {
                    policyBuilder
                        .WithOrigins(allowedOrigins)
                        .AllowAnyMethod()
                        .AllowAnyHeader();
                });
            });

            return services;
        }

        public static IServiceCollection AddCustomDbContexts(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<EurolandIDProfileDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("EurolandIDProfileConnection")));

            services.AddDbContext<SharkDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("SharkDbContext")));

            return services;
        }
    }
}
