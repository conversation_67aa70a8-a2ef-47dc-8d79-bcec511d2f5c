import { useStore } from 'react-redux';
import { useContext, useRef } from 'react';

import i18n from '../../../services/i18n';
import ToolTip from '../../commons/ToolTip';
import { getKeyByCompany } from '../../../helper';
import appConfig from '../../../services/app-config';
import { PRINT_SHARE_DETAIL_OPTIONS } from '../../../common';
import ChartContext from '../../../context/ChartContext';
import { getLayout } from '../utils';
import { useSelectedCurrency } from '../../../customHooks/useSelectedCurrency';

export function PrintButton() {
  const store = useStore();
  const isIFrame = window.self !== window.top;
  const integration = window['euroland'];
  const containerRef = useRef(null);
  const { chart } = useContext(ChartContext);
  const selectedCurrency = useSelectedCurrency();

  function printPreview(key, agreed) {
    const isIFrame = window.top !== window.self;
    const mainTickerId = store.getState().tickers.selectedInstrumentId;
    const peers = store.getState().peers.selectedPeerIds;
    const indices = store.getState().indices.selectedIndicesIds;
    const selectedRange = chart[0].range.selectedRange;
    const currentEvent = chart[0].chart.CurrentEvent;

    const layout = getLayout(chart[0]);
    const preferences = JSON.stringify(chart[0].exportPreferences());
    const drawings = JSON.stringify(chart[0].exportDrawings());
    //setCustomItemLocalStorageByProperty(customStorageKey, 'selectedRange', chart[0].range.selectedRange);
    const component = window.euroland.components.PrintComponent({
      data: { peers, indices, selectedRange, currentEvent, layout, preferences, drawings, selectedCurrency },
      key,
      mainTickerId,
      isPrintShareDetail: agreed,
      onClose: () => {
        //setCustomItemLocalStorageByProperty(customStorageKey, 'peers', '');
        //setCustomItemLocalStorageByProperty(customStorageKey, 'indices', '');
        //setCustomItemLocalStorageByProperty(customStorageKey, 'selectedRange', null);
        if (!isIFrame && document.getElementById('printPreviewMiddleLayout')) {
          document.getElementById('printPreviewMiddleLayout').remove();
        }
      },
      onRendered: () => {
        console.log('Share Dialog has completed its full render');
      }
    });
    if (isIFrame) {
      component.renderTo(window.parent, window.xprops.layout.middle, 'popup');
    } else {
      let middle = document.getElementById('printPreviewMiddleLayout');
      if (!middle) {
        middle = document.createElement('div');
        middle.id = 'printPreviewMiddleLayout';
        document.body.appendChild(middle);
      }

      component.renderTo(window.parent, '#printPreviewMiddleLayout', 'popup');
    }
  }

  /**
   * Creates the integration component that represent for social share dialog.
   * @param {{ shareUrl, thumbnailUrl }} props List of prop values to pass to dialog/popup window
   * @returns {window.euroland.components.ShareDialogComponent}
   */
  function createPrintComponent(props) {
    return integration.components.PrintDialogComponent(props);
  }

  async function onHandleConfirm(agreed) {
    const { printStorageKey } = getKeyByCompany();
    printPreview(printStorageKey, agreed);
  }

  function openPrintDialog() {
    const thumbnailUrl = 'as';
    const settingsXml = appConfig.get();
    const {print} = settingsXml.shareDetails || {};
    // Always 
    if(String(print).toLocaleUpperCase() === PRINT_SHARE_DETAIL_OPTIONS.ALWAYS) {
      return onHandleConfirm(true);
    }
       // Never 
    if(String(print).toLocaleUpperCase() === PRINT_SHARE_DETAIL_OPTIONS.NEVER ) {
      return onHandleConfirm(false);
    }

    return new Promise((resolve, reject) => {
      try {
        const integrationLayoutPosition = window.xprops ? window.xprops.layout.middle : '#middleLayout';
        const component = createPrintComponent({
          shareUrl: 'https://myshare.com',
          thumbnailUrl: thumbnailUrl,
          onConfirm: onHandleConfirm,
          onClose: () => {
            containerRef.current?.focus();
            if (!isIFrame && document.getElementById('middleLayout')) {
              document.getElementById('middleLayout').remove();
            }
          },
          onRendered: () => {
            console.log('Share Dialog has completed its full render');
          }
        });

        if (isIFrame) {
          component.renderTo(window.parent, integrationLayoutPosition);
        } else {
          let middle = document.getElementById('middleLayout');
          if (!middle) {
            middle = document.createElement('div');
            middle.id = 'middleLayout';
            document.body.appendChild(middle);
          }
          component.renderTo(window.parent, integrationLayoutPosition);
        }

        resolve(component);
      } catch (e) {
        reject(e);
      }
    });
  }

  return (
    <button
      ref={containerRef}
      className="tooltip-wrapper option-button option-button--print"
      onClick={openPrintDialog}
      aria-labelledby="printChartBtn"
    >
      <i aria-hidden="true" className="fs fs-icon-print"></i>
      <ToolTip aria-hidden="true" id="printChartBtn">
        {i18n.translate('printOption')}
      </ToolTip>
    </button>
  );
}
