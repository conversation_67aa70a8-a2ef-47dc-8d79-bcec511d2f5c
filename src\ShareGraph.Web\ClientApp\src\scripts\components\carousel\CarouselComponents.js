/* eslint-disable no-irregular-whitespace */
// import styled from "styled-components";

export const NEXT = '<i class="fs fs-caret-right"></i>';
export const PREV = '<i class="fs fs-caret-left"></i>';

export const CarouselContainer = (props) => {
  const getClassNameContainer =() => {
    if(!props.sliding) {
      return 'carousel__container sliding';
    }

    if(props.dir === PREV) {
      return 'carousel__container sliding--prev';
    }
    return 'carousel__container';
  };


  return (
    <div className={getClassNameContainer()} style={{
       display: 'flex'/* ,
        transition: props.sliding ? 'none' : 'transform .5s ease',
        transform: getInfo() */
    }}>
      { props.children }
    </div>
  );
};

export const Wrapper = (props) => {
  return (
    <div className='carousel__wrapper'>
      { props.children }
    </div>
  );
};

export const CarouselSlot = (props) => {
  return (
    <div className={ props.order > 0 && props.order <= props.slidesPerView ? 'carousel__item active' : 'carousel__item'  } style={{ order: props.order }}>
      { props.children }
    </div>
  );
};

export const SlideButton = (props) => {
  return (
    <button {...props} title='button' className={ `carousel__btn ${props.className ? props.className: ''}` } >
      { props.children }
    </button>
  );
};
