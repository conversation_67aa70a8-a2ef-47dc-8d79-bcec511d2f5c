function getLastUpdatedDateByStockId(stockId, state) {
  const selectors = [
    (data) => data.tickers.instruments,
    (data) => data.peers.instruments,
    (data) => data.indices.instruments
  ];

  for (const selector of selectors) {
    const result = selector(state).find(
      (item) => item.instrumentId === stockId
    );
    if (!result) continue;

    return result.lastUpdatedDate;
  }

  throw new Error(`can not find lastUpdatedDate of stock ${stockId}`);
}

function getDay(date) {
  const d = typeof date === 'object' ? date : new Date(date);
  return [
    d.getUTCFullYear(),
    d.getUTCMonth(),
    d.getUTCDate()
  ];
}

/**
 * 
 * @param {Array<string, number, Date>} dates 
 */
function sameDay(dates) {
  if(dates.length < 2) throw new Error('set at least 2 date to compare');
  const [first, ...last] = dates.map(getDay);

  return last.every(item => item.every((i, index) => i === first[index]));
}

/**
 * @param {{stockId: number, date: string | number | Date, store: import("redux").Store<any, import("redux").AnyAction>}} param0
 */
export default function isSameDayWithLastTick({
  stockId,
  date,
  store
}) {
  const state = store.getState();

  const oldDate = getLastUpdatedDateByStockId(stockId, state);
  return sameDay([oldDate, date]);
}
