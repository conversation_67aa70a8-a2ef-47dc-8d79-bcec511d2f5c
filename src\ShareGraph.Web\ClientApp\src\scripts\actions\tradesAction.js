import fetchApi from '../services/fetch-api';
import { appSettings } from '../../appSettings';
import appConfig from '../services/app-config';
import { NUMBER_OF_TRADES } from '../constant/common';
import { getAllInstrumentsSetting, getTickersSetting } from '../configs/configuration-app';
import {client, clientRT} from '../services/graphql-client';
import {TRADE_QUERY} from '../graphql-queries/tradeQuery';


function getTradesData({selectedInstrumentId, toCurrency}) {
  const fields = [
      'close',
      'date',
      'size'
  ];

  const configSettings = appConfig.get();
  const numberOfTrades = configSettings.trade?.numberOfTrades || NUMBER_OF_TRADES;
  const isRT = getAllInstrumentsSetting()[selectedInstrumentId]?.isRT ?? false;

  return fetchApi(appSettings.sDataApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
        latestShareTrades(
          instrumentId: ${selectedInstrumentId},
          count: ${numberOfTrades},
          isRT: ${isRT}
          ${toCurrency ? `toCurrency: "${toCurrency}"` : ''}
          ){
          ${fields.join(',')}
        }
      }`
    })
  })
    .then(res => res.json());
}

async function getTradesDataNewQuery({selectedInstrumentId, toCurrency}) {
  const configSettings = appConfig.get();
  const numberOfTrades = configSettings.trade?.numberOfTrades || NUMBER_OF_TRADES;
  const isRT = getAllInstrumentsSetting()[selectedInstrumentId]?.isRT ?? false;
  
  const result = await (isRT ? clientRT.query : client.query)(TRADE_QUERY, { 
    id: selectedInstrumentId, 
    adjClose: getTickersSetting()[selectedInstrumentId].enabledAdjustPrice,
    toCurrency, 
    numberOfTrades
  });

  return {
    data: {
      latestShareTrades: result.data.instrumentById?.lastDayIntraday.nodes.map(item => ({close: item.close, date: item.date, size: item.volume})) ?? []
    }
  };
}

export const FETCH_TRADES_BEGIN = 'FETCH_TRADES_BEGIN';
export const FETCH_TRADES_SUCCESS = 'FETCH_TRADES_SUCCESS';
export const FETCH_TRADES_FAILURE = 'FETCH_TRADES_FAILURE';

export const fetchTradesBegin = () => ({
  type: FETCH_TRADES_BEGIN
});

export const fetchTradesSuccess = (tradesData = []) => {
  return {
    type: FETCH_TRADES_SUCCESS,
    payload: {
      tradesData
    }
  };
};

export const fetchTradesFailure = (error) => ({
  type: FETCH_TRADES_FAILURE,
  payload: { error }
});


export function fetchTrades() {
  return (dispatch, getState) => {
    const selectedInstrumentId = getState().tickers.selectedInstrumentId;

    if (!selectedInstrumentId) return;

    dispatch(fetchTradesBegin());
    const { currency } = getState().currency;
    const toCurrency = currency?.code;
    return getTradesDataNewQuery({selectedInstrumentId, toCurrency})
      .then(json => {
        const data = json?.data?.latestShareTrades || [];
        dispatch(fetchTradesSuccess(data));
        return data;
      })
      .catch(err =>
        dispatch(fetchTradesFailure(err))
      );
  };
}
