import { useRef, useState, useEffect, useContext, useLayoutEffect, useMemo } from 'react';
import { useClickAway } from '@euroland/react';

import ChartContext from '../../../context/ChartContext';
import i18n from '../../../services/i18n';
import Dialog from '../../commons/Dialog';
import { getCustomConfig } from '../chartiq-import';
import { DateRangePicker } from './DateRangePicker';
import { useSelectedTicker } from '../../../customHooks/useTickers';
import appConfig from '../../../services/app-config';

const getDefaultCustomRange = () => {
  const to = new Date();
  const from = new Date();
  from.setMonth(from.getMonth() - 1);
  return { from, to };
};

export const CustomRangeButton = ({ showDialogCustomRange, onShowDialogCustomRange, openedByKeyboard, onChange, onDialogClose, range }) => {
  const defaultRange = useMemo(getDefaultCustomRange, []);
  const refDialog = useRef(null);
  const overlayRef = useRef(null);
  const selectedInstrument = useSelectedTicker();
  const settings = appConfig.get();
  const dateFormat = settings.format.shortDate;

  const startingDate = useMemo(() => {
    return selectedInstrument?.startingDate ? new Date(selectedInstrument.startingDate) : null;
  }, [selectedInstrument?.startingDate]);

  const dateRange = useMemo(() => {
    if(range.selectedRange !== 'CUSTOM_RANGE' || !range.dtLeft || !range.dtRight) {
      return defaultRange;
    }

    return {
      from: new Date(range.dtLeft),
      to: new Date(range.dtRight)
    };
  }, [range.dtLeft, range.dtRight, defaultRange]);

  useClickAway([refDialog], () => {
    onShowDialogCustomRange(false);
  });

  const handleCloseDialogCustomRange = () => {
    onShowDialogCustomRange(false);
    if (typeof onDialogClose === 'function') {
      onDialogClose();
    }
  };

  const handleDatePickerChange = obj => {
    if (obj instanceof KeyboardEvent && obj.key !== 'Enter' && obj.key !== ' ') {
      return;
    }
    onChange(obj);
    onShowDialogCustomRange(false);
  };

  const handleKeyBoard = (event) => {
    if (event.key === 'Escape') {
      handleCloseDialogCustomRange();
    }
  };

  if(!showDialogCustomRange) return null;
  return (
    <div
      ref={overlayRef}
      className="dialog-wrapper dialog-wrapper-custom-range"
      style={{ display: showDialogCustomRange ? 'block' : 'none' }}
    >
      <Dialog ref={refDialog} className="dialog--custom-range" onKeyDown={handleKeyBoard}>
        <header className="dialog__header">
          <h2 className="dialog__title">{i18n.translate('customRange')}</h2>
        </header>
        <div className="dialog__body">
          <DateRangePicker
            openedByKeyboard={openedByKeyboard}
            startingDate={startingDate}
            onChange={handleDatePickerChange}
            dateFormat={dateFormat}
            dateRange={dateRange}
          ></DateRangePicker>
        </div>
      </Dialog>
    </div>
  );
};
