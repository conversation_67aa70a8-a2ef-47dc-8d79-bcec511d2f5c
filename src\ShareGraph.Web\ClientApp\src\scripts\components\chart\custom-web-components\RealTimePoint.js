import { CIQ } from 'chartiq/js/componentUI';
import { PointAnimation } from './point-animation';

export class RealTimePoint extends CIQ.UI.ContextTag {
  constructor() {
    super();
    this.stx = null;
    this.isVisible = true;
    this.previousQuote = null;
    this.width = undefined;
    this.height = undefined;
    this.clearFns = [];
    this.animation = null;
  }

  connectedCallback() {
    super.connectedCallback();

    Object.assign(this.style, {
      display: 'block',
      position: 'absolute',
      pointerEvents: 'none',
      zIndex: 1,
      overflow: 'hidden',
      backgroundColor: 'transparent',
      left: '0px',
      top: '0px'
    });

    const rootStyles = getComputedStyle(document.documentElement);
    const dotWidth = parseFloat(rootStyles.getPropertyValue('--blinking-price-dot-width'));
    const size = parseFloat(rootStyles.getPropertyValue('--blinking-price-animation-size'));

    const attribute = (name, dValue) => parseInt(this.getAttribute(name)) || dValue;
    this.animation = new PointAnimation({
      dotRadius: attribute('dot-radius', dotWidth/2),
      duration: attribute('duration', 2000),
      sleep: attribute('sleep', 1000),
      size: attribute('size', size)
    });

    Object.assign(this.animation.element.style, {
      position: 'absolute',
      transform: 'translate(-50%, -50%)'
    });

    this.appendChild(this.animation.element);
  }

  /**
   *
   * @param {'up' | 'down' | 'normal'} state
   */
  setState(state = 'normal') {
    const reset = () => {
      this.classList.remove('cq-point-animation-ring-up');
      this.classList.remove('cq-point-animation-ring-down');
    };
    reset();
    if (state === 'normal') return;
    this.classList.add(`cq-point-animation-ring-${state}`);
    clearTimeout(this.stateTimer);
    this.stateTimer = setTimeout(reset, this.animation.config.duration);
  }

  setContext(context) {
    this.stx = context.stx;

    this.setupChartHandlers();
  }

  setupChartHandlers() {
    const stx = this.stx;
    if (!stx) return;
    // Handle data updates
    const createDataSetClearId = stx.append(
      'createDataSet', this.updatePosition.bind(this, true)
    );
    const drawClearId = stx.append(
      'draw', this.updatePosition.bind(this, false)
    );
    const resizeChartClearId = stx.append(
      'resizeChart', this.updatePosition.bind(this, false)
    );

    this.channelSubscribe('chart.lineStyle', this.updateStyle.bind(this), stx);

    this.channelSubscribe(
      'layout.marketOpen', this.updateMarketOpen.bind(this), stx
    );

    this.clearFns.push(
      () => stx.removeInjection(createDataSetClearId),
      () => stx.removeInjection(drawClearId),
      () => stx.removeInjection(resizeChartClearId)
    );
  }

  resize() {
    const { width, height } = this.stx.chart;
    if (this.width === width && this.height === height) return;

    this.width = width;
    this.height = height;

    Object.assign(this.style, {
      width: `${width}px`,
      height: `${height}px`
    });
  }

  canShow(lastQuote = null) {
    if (!this.stx || !this.animation) return false;
    if (this.stx.quoteDriver?.loadingNewChart === true) return false;
    if (!this.isMarketOpen) return false;
    const chart = this.stx.chart;
    if (!chart.dataSet || !chart.dataSet.length) return false;
    if(lastQuote === null) lastQuote = this.stx.currentQuote('Close');
    if(!lastQuote) return false;

    const { chartType } = this.stx.layout;
    switch (chartType) {
      case 'line':
      case 'mountain':
      case 'step':
      case 'vertex_line':
        return true;
      default:
        return false;
    }
  }

  updatePosition(forceUpdate = false) {
    const chart = this.stx.chart;
    // Get the last valid quote
    const lastQuote = this.stx.currentQuote('Close');

    if (!this.canShow(lastQuote)) return this.hide();

    let x = this.stx.pixelFromTick(lastQuote.tick);
    if (chart.lastTickOffset) x = x + chart.lastTickOffset;
    const y = this.stx.pixelFromPrice(lastQuote.Close, chart.panel);

    this.resize();
    this.show();
    this.move(x, y);

    if(!forceUpdate) return;

    if (
      this.previousQuote &&
      this.previousQuote !== lastQuote &&
      this.previousQuote.Close !== lastQuote.Close &&
      this.previousQuote.instrumentId === lastQuote.instrumentId
    ) {
      this.setState(
        this.previousQuote.Close > lastQuote.Close ? 'down' : 'up'
      );
      this.animation.restart();
    }

    this.previousQuote = lastQuote;
  }

  move(x, y) {
    Object.assign(this.animation.element.style, {
      top: `${y}px`,
      left: `${x}px`
    });
  }

  show() {
    if (this.isVisible) return;
    this.style.display = 'block';
    this.isVisible = true;
  }

  hide() {
    if (!this.isVisible) return;
    this.style.display = 'none';
    this.isVisible = false;
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    if (this.clearFns) {
      this.clearFns.forEach((fn) => fn());
    }
    if (this.animation) {
      this.animation.destroy();
    }
  }

  adoptedCallback() {
    super.adoptedCallback();
    CIQ.UI.flattenInheritance(this, RealTimePoint);
  }

  /**
   *
   * @param {{color: string, width: number, fillStyle: string}} lineStyle
   * @returns
   */
  updateStyle(lineStyle) {
    if (!lineStyle) return;

    this.animation.element.style.color = lineStyle.color;
  }


  /**
   * Updates the real-time point visualization based on market open/closed status
   * @private
   */
  updateMarketOpen() {
    return this.canShow() ? this.show() : this.hide();
  }

  get isMarketOpen() {
    return this.stx?.layout?.marketOpen ?? false;
  }
}

CIQ.UI.addComponentDefinition('cq-real-time-point', RealTimePoint);
