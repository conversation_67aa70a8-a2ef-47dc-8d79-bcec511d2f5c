import fetchApi from '../services/fetch-api';
import { appSettings } from '../../appSettings';
import appConfig from '../services/app-config';
import { getAllInstrumentsSetting } from '../configs/configuration-app';
import dayjs from 'dayjs';
import { trimAbbreviation } from '../utils';
import { client, clientRT } from '../services/graphql-client';
import {getSharePriceDevelopmentByYearsQuery} from '../graphql-queries/sharePriceDevelopmentByYearsQuery';

/**
 *
 * @param {{
 * instrumentIds: Array<string>;
 * toCurrency: string;
 * isRT?: boolean
 * }} param0
 * @returns
 */
async function getSharePriceDevelopmentByYearsNewQuery({
  instrumentIds = [],
  toCurrency,
  isRT = false
}) {
  if (!instrumentIds.length) return;

  const { queryYears, minYear } = getQueryYears();
  const queryString = getSharePriceDevelopmentByYearsQuery({ queryYears });
  const allInstrumentSettings = getAllInstrumentsSetting();
  const clientInstance = isRT ? clientRT : client;
  const responseData = await clientInstance.query(queryString, {
    ids: instrumentIds.filter(id => !allInstrumentSettings[id].enabledAdjustPrice),
    adjIds: instrumentIds.filter(id => allInstrumentSettings[id].enabledAdjustPrice),
    toCurrency
  });

  const data = convertNewRespDataToOldRespData(responseData, { minYear });
  return data;
}
//Share Price Development by Years Action

export const FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_BEGIN = 'FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_BEGIN';
export const FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_SUCCESS = 'FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_SUCCESS';
export const FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_FAILURE = 'FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_FAILURE';

export const fetchSharePriceDevelopmentByYearsBegin = () => ({
    type: FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_BEGIN
});

export const fetchSharePriceDevelopmentByYearsSuccess = (instruments = []) => {
    const allInstrumentsSetting = getAllInstrumentsSetting();
    const sortedInstruments  = Object.keys(allInstrumentsSetting).map(instrumentId=>{
        const instrument = instruments.find(instrument=>instrument.instrumentId === Number(instrumentId));
        return instrument;
    }).filter(ins => !!ins);

    return {
        type: FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_SUCCESS,
        payload: {instruments: sortedInstruments}
    };
};

export const fetchSharePriceDevelopmentByYearsFailure = (error) => ({
    type: FETCH_SHAREPRICE_DEVELOPMENTBY_YEAR_FAILURE,
    payload: {error}
});

const filterInstrumentIdByRealTime = (instruments = []) => {
    return instruments.reduce((acc, instrument) => {
      if (instrument.isRT) {
        acc.realTimeInstrumentIds.push(instrument.instrumentId);
      } else {
        acc.notRealTimeInstrumentIds.push(instrument.instrumentId);
      }
      return acc;
    }, { realTimeInstrumentIds: [], notRealTimeInstrumentIds: [] });
  };

const convertNewRespDataToOldRespData = (responseJson, { minYear }) => {
  const mapping = (instrument) => {
    const yearKeys = Object.keys(instrument);

    const instrumentData = yearKeys
      .map(yearKey => {
        return {
          year: yearKey.replace('_', ''),
          ...instrument[yearKey]?.nodes?.[0]
        };
      })
      .sort((a, b) => {
        if (a.year > b.year) return -1;
        if (a.year < b.year) return 1;
        return 0;
      });

    const data = {};

    data.shareName = trimAbbreviation(instrument.shareName);
    data.marketAbbreviation = instrument.market?.abbreviation || '';
    data.instrumentId = instrument.id;
    data.yearlyPerformances = instrumentData
      .map((ins, i) => {
        const prevClose = instrumentData[i + 1]?.close;
        const changePercentage = ((ins.close - prevClose) / prevClose) * 100;
        return { year: Number(ins.year), changePercentage };
      })
      .filter(a => a.year >= minYear);

    return data;
  };

  return [
    ...responseJson.data.instrumentByIds?.map(mapping) ?? [],
    ...responseJson.data.adjInstrumentByIds?.map(mapping) ?? []
  ];
};

/**
 *
 * @returns {{
 *  minYear: number,
 *  queryYears: Array<number>
 * }}
 */
const getQueryYears = () => {
  const setting = appConfig.get();
  let numberOfYears = setting.performance.numberOfYearSPByYear || 5;
  numberOfYears = numberOfYears > 6 ? 6 : numberOfYears;
  numberOfYears = numberOfYears < 1 ? 1 : numberOfYears;

  const currentYear = dayjs().year();
  const years = Array.from({ length: numberOfYears }).map((_, i) => currentYear - i);
  const minYear = years[years.length - 1];
  const queryYears = years.concat(minYear - 1);
  return { queryYears, minYear };
};

export function fetchSharePriceDevelopmentByYears(instrumentIds = []) {
    return async (dispatch, getState) => {
        dispatch(fetchSharePriceDevelopmentByYearsBegin());

        const excludeInstrumentIds = appConfig.get().performance.excludeIds||[];


        let realTimeInstrumentIds = [];
        let notRealTimeInstrumentIds = [];

        if (!instrumentIds || !instrumentIds.length) {
            const {realTimeInstrumentIds:rtPeerIds, notRealTimeInstrumentIds:nrtPeerIds} = filterInstrumentIdByRealTime(getState().peers.instruments);
            const {realTimeInstrumentIds:rtIndicesIds, notRealTimeInstrumentIds:nrtIndicesIds} = filterInstrumentIdByRealTime(getState().indices.instruments);
            const {realTimeInstrumentIds:rtTickerIds, notRealTimeInstrumentIds:nrtTickerIds} = filterInstrumentIdByRealTime(getState().tickers.instruments);
            realTimeInstrumentIds = [...rtPeerIds, ...rtIndicesIds, ...rtTickerIds];
            notRealTimeInstrumentIds = [...nrtPeerIds, ...nrtIndicesIds, ...nrtTickerIds];
        }

        const { currency } = getState().currency;
        const toCurrency = currency?.code;
        const allInstrumentSettings = getAllInstrumentsSetting();

        // We can do fake fetching SharePriceDevelopmentByYears  data by return "fakegetSharePriceDevelopmentByYearsData()" as well.
        // return getSharePriceDevelopmentByYears(realTimeInstrumentIds,notRealTimeInstrumentIds, toCurrency)
        // .then( json => {
        //     let data = [];
        //     if(json.data.realtime) {
        //         data = data.concat(json.data.realtime);
        //     }
        //     if(json.data.notRealtime) {
        //         data = data.concat(json.data.notRealtime);
        //     }
        //     dispatch(fetchSharePriceDevelopmentByYearsSuccess(data));
        //     return json.data.instrumentPerformance;
        // }).catch((err) => {
        //    dispatch(fetchSharePriceDevelopmentByYearsFailure(err));
        // });

        const realTime = {
          ids: realTimeInstrumentIds.filter(
            (id) =>
              !allInstrumentSettings[id].enabledAdjustPrice &&
              !excludeInstrumentIds.includes(id)
          ),
          adjIds: realTimeInstrumentIds.filter(
            (id) =>
              allInstrumentSettings[id].enabledAdjustPrice &&
              !excludeInstrumentIds.includes(id)
          )
        };

        const notRealTime = {
          ids: notRealTimeInstrumentIds.filter(
            (id) =>
              !allInstrumentSettings[id].enabledAdjustPrice &&
              !excludeInstrumentIds.includes(id)
          ),
          adjIds: notRealTimeInstrumentIds.filter(
            (id) =>
              allInstrumentSettings[id].enabledAdjustPrice &&
              !excludeInstrumentIds.includes(id)
          )
        };

        const { queryYears, minYear } = getQueryYears();
        const queryString = getSharePriceDevelopmentByYearsQuery({ queryYears });
        const promiseAll = [];

        if(realTime.ids.length > 0) promiseAll.push(
          clientRT.query(queryString, {
            ids: realTime.ids,
            adjClose: false,
            toCurrency
          })
        );

        if(realTime.adjIds.length > 0) promiseAll.push(
          clientRT.query(queryString, {
            ids: realTime.adjIds,
            adjClose: true,
            toCurrency
          })
        );

        if(notRealTime.adjIds.length > 0) promiseAll.push(
          client.query(queryString, {
            ids: notRealTime.adjIds,
            adjClose: true,
            toCurrency
          })
        );

        if(notRealTime.ids.length > 0) promiseAll.push(
          client.query(queryString, {
            ids: notRealTime.ids,
            adjClose: false,
            toCurrency
          })
        );

        try {
          const instruments = (await Promise.all(promiseAll)).map(result => convertNewRespDataToOldRespData(result, { minYear })).flat();

          dispatch(fetchSharePriceDevelopmentByYearsSuccess(instruments));
        } catch (err) {
          dispatch(fetchSharePriceDevelopmentByYearsFailure(err));
          console.error(err);
          throw err;
        }
    };

}