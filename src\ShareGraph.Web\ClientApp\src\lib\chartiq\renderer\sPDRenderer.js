/**
 *	8.6.0
 *	Generation date: 2022-03-29T13:48:59.334Z
 *	Client name: euroland ir
 *	Package Type: Technical Analysis e98f22c
 *	License type: trial
 *	Expiration date: "2022/04/30"
 *	iFrame lock: true
 */

/***********************************************************
 * Copyright by ChartIQ, Inc.
 * Licensed under the ChartIQ, Inc. Developer License Agreement https://www.chartiq.com/developer-license-agreement
*************************************************************/
/*************************************** DO NOT MAKE CHANGES TO THIS LIBRARY FILE!! **************************************/
/* If you wish to overwrite default functionality, create a separate file with a copy of the methods you are overwriting */
/* and load that file right after the library has been loaded, but before the chart engine is instantiated.              */
/* Directly modifying library files will prevent upgrades and the ability for ChartIQ to support your solution.          */
/*************************************************************************************************************************/
/* eslint-disable no-extra-parens */

import { CIQ } from "chartiq/js/standard";

export { CIQ };

/**
 * @param  {object} params Rendering parameters
 * Creates a SharePriceDevelopment renderer.
 * @name  CIQ.Renderer.SharePriceDevelopment
 */
 CIQ.Renderer.SharePriceDevelopment = function(config) {
	this.construct(config),
	this.standaloneBars = this.barsHaveWidth = true,
	this.bounded = true;
}
,
CIQ.inheritsFrom(CIQ.Renderer.SharePriceDevelopment, CIQ.Renderer.Lines, false),
CIQ.Renderer.SharePriceDevelopment.requestNew = function(featureList, params) {
	var type = null;
	for (var pt = 0; pt < featureList.length; pt++)
		'sharepricedevelopment' == featureList[pt] && (type = 'sharepricedevelopment');
	return null === type ? null : new CIQ.Renderer.SharePriceDevelopment({
		params: CIQ.extend(params, {
			type: type
		})
	})
}
,
CIQ.Renderer.SharePriceDevelopment.prototype.drawIndividualSeries = function(chart, params) {
	var errMessage, panel, ret;
	errMessage = 'Error, SharePriceDevelopment renderer';
	panel = this.stx.panels[params.panel] || chart.panel,
	ret = {
		colors: []
	},
	this.stx.drawSharePriceDevelopment ? ret = this.stx.drawSharePriceDevelopment(panel, params) : console.warn(errMessage),
	ret;
}
CIQ.ChartEngine.prototype.drawSharePriceDevelopment = function (panel, params) {
	params || (params = {});
			var defaultPlotField = 'Close',
				chart = panel.chart,
				quotes = chart.dataSegment,
				context = chart.context;

			var plotField = params.field || chart.defaultPlotField,
				subField = params.subField || chart.defaultPlotField || defaultPlotField,
				fillColor = params.color;
			if(params.renderer != "SharePriceDevelopment") return;
			//context.fillStyle = fillColor;
						this.startClip(panel.name);
							context.globalAlpha *= params.opacity;
								for (var i = 0; i <= quotes.length; i++) {
									var quote = quotes[i];
									if (!quote) continue;
									var rawValue;
									(rawValue = quote[plotField]) && void 0 !== rawValue[subField] && (rawValue = rawValue[subField]);
									var x = this.pixelFromTick(quote.tick, chart);
									var y = this.pixelFromPrice(rawValue, panel);
									context.beginPath();
									context.arc(x, y, 6, 0, 2 * Math.PI);

									context.fillStyle = fillColor;
									context.fill();
									context.closePath();
									}

						this.endClip();

};

/* usage*/

// var ssdRenderer = stxx.setSeriesRenderer(
// 	new CIQ.Renderer.SharePriceDevelopment({
// 		params: {
// 			type: "SharePriceDevelopment"
// 		}
// 	})
// );


// stxx.addSeries("GOOG", {
// 	display: "GOOG",
// 	width: 4
// }, function () {
// 	ssdRenderer.attachSeries("GOOG", "#FFBE00").ready();
// });
