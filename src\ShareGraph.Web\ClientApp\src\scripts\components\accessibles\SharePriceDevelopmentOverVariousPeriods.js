import { useContext } from 'react';

import { AppContext } from '../../AppContext';
import useSharePriceDevelopment from '../../customHooks/useSharePriceDevelopment';
import { dynamicSort } from '../../helper';
import i18n from '../../services/i18n';
import Table from '../commons/accessibilities/Table';

const SharePriceDevelopmentOverVariousPeriods = () => {
  const [{ sharePriceDevelopments }] = useSharePriceDevelopment();
  const settings = useContext(AppContext);
  const configColumns = settings.performance.enableSharePriceDevelopmentColumns || [];
  const columnsAvailable = [
    { columnDisplay: i18n.translate('sharesLabel'), fieldName: 'shareName' },
    { columnDisplay: i18n.translate('currencyLabel'), fieldName: 'currencyCode' },
    { columnDisplay: i18n.translate('lastLabel'), fieldName: 'last' },
    { columnDisplay: i18n.translate('changeLabel'), fieldName: 'change' },
    { columnDisplay: i18n.translate('oneWeekChangePercentLabel'), fieldName: 'week' },
    { columnDisplay: i18n.translate('oneMonthChangePercentLabel'), fieldName: 'month' },
    { columnDisplay: i18n.translate('threeMonthChangePercentLabel'), fieldName: 'threeMonthChange' },
    { columnDisplay: i18n.translate('sixMonthChangePercentLabel'), fieldName: 'sixMonthsChange' },
    { columnDisplay: i18n.translate('ytdChangePercentLabel'), fieldName: 'yTD' },
    { columnDisplay: i18n.translate('weeks52ChangePercentLabel'), fieldName: 'percent52W' },
    { columnDisplay: i18n.translate('years3ChangePercentLabel'), fieldName: 'threeYearsChange' },
    { columnDisplay: i18n.translate('years5ChangePercentLabel'), fieldName: 'fiveYearsChange' },
    { columnDisplay: i18n.translate('years10ChangePercentLabel'), fieldName: 'tenYearsChange' },
    {
      fieldName: 'w52HighLow',
      columnDisplay: i18n.translate('weeks52HighandLow'),
      width: 150,
      render: row => {
        return (
          <>
            <div className={`field${i18n.translate('h').replace(':', '')}_value`}>
              <span className={`field${i18n.translate('h').replace(':', '')}-lable`}>
                {i18n.translate('h')}
              </span>{' '}
              <span className={`field${i18n.translate('h').replace(':', '')}-data`}>{row.high52W}</span>
            </div>
            <div className={`field${i18n.translate('l').replace(':', '')}_value`}>
              <span className={`field${i18n.translate('l').replace(':', '')}-lable`}>
                {i18n.translate('l')}
              </span>{' '}
              <span className={`field${i18n.translate('l').replace(':', '')}-data`}>{row.low52W}</span>
            </div>
          </>
        );
      }
    },
    {
      fieldName: 'allTimeHighLow',
      columnDisplay: i18n.translate('allTimeHighLowLabel'),
      render: row => {
        return (
          <>
            <div className={`field${i18n.translate('h').replace(':', '')}_value`}>
              <span className={`field${i18n.translate('h').replace(':', '')}-lable`}>
                {i18n.translate('h')}
              </span>{' '}
              <span className={`field${i18n.translate('h').replace(':', '')}-data`}>{row.allTimeHigh}</span>
            </div>
            <div className={`field${i18n.translate('l').replace(':', '')}_value`}>
              <span className={`field${i18n.translate('l').replace(':', '')}-lable`}>
                {i18n.translate('l')}
              </span>{' '}
              <span className={`field${i18n.translate('l').replace(':', '')}-data`}>{row.allTimeLow}</span>
            </div>
          </>
        );
      }
    }
  ];

  const columns = columnsAvailable
    .filter(p =>
      settings.performance.enableSharePriceDevelopmentColumns
        .map(s => s.toUpperCase())
        .includes(p.fieldName.toUpperCase())
    )
    .map(item => ({
      ...item,
      order: configColumns.indexOf(item.fieldName.toLocaleUpperCase())
    }))
    .sort(dynamicSort('order'));
  return (
    <Table
      className="share-performance-table__periods"
      caption={i18n.translate('sharePriceDevelopmentCaption')}
      columns={columns}
      data={sharePriceDevelopments}
    />
  );
};

export default SharePriceDevelopmentOverVariousPeriods;
