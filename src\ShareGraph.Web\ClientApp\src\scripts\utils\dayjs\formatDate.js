import dayjs from './index';
import pluginArabicNumber from './pluginArabicNumber';

function formatDateTime() {
  let _default = {
    timeZone: '',
    settingFormat: {},
    useLatinNumbers: true
  };

  /**
   *
   * @param {number|Date} value Number to format, or formated string to convert to original number
   */
  function getDate(value) {
    value = value || new Date();
    let timeZone = _default.timeZone;
    return dayjs.utc(value).tz(timeZone);
  }

  /**
   *
   * @param {number|Date} value Number to format, or formated string to convert to original number
   * @param {"shortDate" | "longDate" | "tickerDateTimeFormat" | {format: string}} typeFormat
   */
  function formatTime(value, typeFormat) {
    if(typeof typeFormat === 'object') {
      return getDate(value).format(typeFormat.format);
    }
    let formatDate = 'typeFormat' in _default.settingFormat ? _default.settingFormat[typeFormat] : typeFormat;
    return getDate(value).format(formatDate);
  }

  return {
    /**
     *
     * @param {number|Date} value
     * @param {string} typeFormat
     */
    formatDate: (value, typeFormat) => formatTime(value, typeFormat),
    /**
     *
     * @param {{timeZone: string, }} options
     */
    updateDefault: async (options = {}) => {
      _default = Object.assign({}, _default, options);
      if (!_default.useLatinNumbers) dayjs.extend(pluginArabicNumber);
    },
    getDate
  };
}

const formatter = formatDateTime();

const formatDateFn = formatter.formatDate;
const updateDefaultDateTime = formatter.updateDefault;
const getDate = formatter.getDate;
export { formatDateFn, updateDefaultDateTime, getDate };
