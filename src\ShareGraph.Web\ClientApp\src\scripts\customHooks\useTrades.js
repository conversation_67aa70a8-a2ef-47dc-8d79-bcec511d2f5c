import { useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppContext } from '../AppContext';
import { fetchTrades } from '../actions/tradesAction';
import { REFRESH_TRADES_TIME } from '../constant/common';
import { useChangeQuoteCurrency, useSelectedCurrency } from './useSelectedCurrency';

const useTrades = () => {
  const selectedInstrumentId = useSelector(
    (state) => state.tickers.selectedInstrumentId
  );

  const { loading, tradesData, fetchError } = useSelector(state => state.trades) || {};
  const dispatch = useDispatch();
  const settings = useContext(AppContext);
  const selectedCurrency = useSelectedCurrency();

  const refreshTime = settings.trade?.tradeRefreshSeconds || REFRESH_TRADES_TIME;


  useEffect(() => {

    function fetchData() {
      dispatch(fetchTrades());
    }

    fetchData();

    const timer = setInterval(() => {
      fetchData();
    }, refreshTime * 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [selectedInstrumentId, refreshTime]);

  useChangeQuoteCurrency(() => {
    dispatch(fetchTrades());
  });

  return { data: tradesData || [], loading, fetchError };
};

export default useTrades;
