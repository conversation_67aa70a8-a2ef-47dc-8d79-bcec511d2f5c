import { useEffect, useRef, useLayoutEffect } from 'react';
import { appSettings } from '../../appSettings';
import fetchApi from '../services/fetch-api';
import useAutoFocusToPopup from '../customHooks/useAutoFocusToPopup';
import { CloseDialogButton } from './Share';
import useFocusTrap from '../customHooks/useFocusTrap';

export default function PressReleaseComponent() {
  const htmlContentElement = useRef(null);
  const dialogRef = useRef();

  const bodyRegex = /^[\s\S]*<body[^>]*>([\s\S]*)<\/body>[\s\S]*$/igm;
  const stripScriptRegex = /<script.*?>.*?<\/script>/igm;

  useEffect(() => {

    fetch(window.xprops.pressreleaseId);
  }, []);

  useLayoutEffect(()=>{
    document.querySelector('body').classList.add('press-release-modal');
  }, []);

  function fetch(pressId) {
    getPressReleaseContent(pressId).then((response) => {
      //setPressReleaseContent(response);
      htmlContentElement.current.innerHTML = response.__html;
    });
  }

  function getPressReleaseContent(pressReleaseId) {
    return fetchApi(`${document.location.origin}${appSettings.toolUrlBase}press-release/detail?id=${pressReleaseId}`)
      .then(response => response.text())
      .then(extractHtmlBody)
      .then(toDangerouslyHTML);
  }

  function extractHtmlBody(html) {
    return new Promise((resolve) => {
      resolve(html.replace(bodyRegex, '$1').replace(stripScriptRegex, ''));
    });
  }

  function toDangerouslyHTML(html) {
    return new Promise(resolve => {
      resolve({ __html: html });
    });
  }

  useAutoFocusToPopup();
  useFocusTrap(dialogRef);

  return (
    <div className='app__inner'>
      <div className="pressrelease-content dialog__wrapper" ref={dialogRef}>
        <div ref={htmlContentElement}>
          <div className="pressrelease-content__loading">
            <div className="spinner"></div>
          </div>
        </div>
        <CloseDialogButton />
      </div>
    </div>
  );
}
