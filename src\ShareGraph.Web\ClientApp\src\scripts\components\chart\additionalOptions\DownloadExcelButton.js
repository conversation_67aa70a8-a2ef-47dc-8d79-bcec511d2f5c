import i18n from '../../../services/i18n';
import { useContext } from 'react';
import { AppContext } from '../../../AppContext';
import ToolTip from '../../commons/ToolTip';
import { appSettings } from '../../../../appSettings';
import { useSelector } from 'react-redux';
import { getStudies } from '../utils';
import { formatWithDateTime } from '../../../helper';
import { useSelectedCurrency } from '../../../customHooks/useSelectedCurrency';
import { useSelectedTicker } from '../../../customHooks/useTickers';
import useChartTitle from '../../../customHooks/useChartTitle';

export function DownloadExcelButton() {
  const appContext = useContext(AppContext);
  const chartDataService = appContext.chartDataService;
  const chartEngine = appContext.chartEngine;
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const selectedTicker = useSelectedTicker();
  const selectedPeerIds = useSelector(state => state.peers.selectedPeerIds);
  const selectedIndiciesIds = useSelector(state => state.indices.selectedIndicesIds);
  const selectedCurrency = useSelectedCurrency();
  const { currentQuote } = useChartTitle();
  const settings = useContext(AppContext);



  const downloadExcel = () => {
    if (!chartEngine) return;

    const {
      period,
      intraday: isIntraday
    } = chartDataService;

    const {
      range: { selectedRange, dtLeft, dtRight } = {}
    } = chartEngine;

    const {
      chart: { excelDownloadOption }
    } = settings;

    const {
      companyCode,
      language,
      isPreview,
      companyCodeVersion = ''
    } = appSettings;
    const studies =  getStudies(chartEngine);
    const includeTotalReturn = excelDownloadOption?.includedTotalReturn && Object.prototype.hasOwnProperty.call(studies, 'Total Return')
      ? '&includeTotalReturn=true'
      : '&includeTotalReturn=false';

    const includedSelectedPeersAndIndicies = [
      excelDownloadOption?.includedSelectedPeersAndIndicies && selectedPeerIds.length > 0 ? `&peerIds=${selectedPeerIds}` : '',
      excelDownloadOption?.includedSelectedPeersAndIndicies && selectedIndiciesIds.length > 0 ? `&indexIds=${selectedIndiciesIds}` : ''
    ].join('');

    let toDate = currentQuote?.DT || new Date();
    const fromDate = selectedRange === 'CUSTOM_RANGE' && dtLeft && dtRight
      ? formatWithDateTime(dtLeft, 'YYYY-MM-DD[T00:00:00.000Z]')
      : null;
    toDate = selectedRange === 'CUSTOM_RANGE' && dtLeft && dtRight
      ? formatWithDateTime(dtRight, 'YYYY-MM-DD[T23:59:00.000Z]')
      : isIntraday
        ? formatWithDateTime(toDate)
        : formatWithDateTime(toDate, 'YYYY-MM-DD[T00:00:00.000Z]');

    const isPreviewParam = isPreview ? '&isPreview' : '';
    const toCurrencyParam = selectedCurrency?.code ? `&toCurrency=${selectedCurrency.code}` : '';
    const isRTParam = selectedTicker?.isRT ? `&isRT=${selectedTicker.isRT}` : '';
    const version = companyCodeVersion || companyCode;
    const sDownloadLink = `download/excel?companyCode=${companyCode}&lang=${language}${isPreviewParam}&v=${version}${toCurrencyParam}&selectInstrumentId=${selectedInstrumentId}&isintraday=${isIntraday}${isRTParam}&fromDate=${encodeURIComponent(fromDate)}&toDate=${encodeURIComponent(toDate)}&period=${period}&selectedRange=${selectedRange}${includedSelectedPeersAndIndicies}${includeTotalReturn}`;

    const a = document.createElement('a');
    a.href = sDownloadLink;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    setTimeout(() => document.body.removeChild(a), 1);
  };
  return (
    <button
      className="tooltip-wrapper option-button option-button--excel"
      onClick={downloadExcel}
      aria-labelledby="downloadToExcelBtn"
    >
      <i aria-hidden="true" className="fs fs-icon-excel"></i>
      <ToolTip aria-hidden="true" id="downloadToExcelBtn">
        {i18n.translate('excelOption')}
      </ToolTip>
    </button>
  );
}
