import produce from 'immer';

import {
    FETCH_SHAREPRICE_DEVELOPMENT_BEGIN,
    FETCH_SHAREPRICE_DEVELOPMENT_SUCCESS,
    FETCH_SHAREPRICE_DEVELOPMENT_FAILURE
 } from '../actions/SharePriceDevelopmentAction';

 const initialState = {
     instruments: [],
     loading: true,
    fetchError: null
 };


export default function createSharePriceDevelopmentReducer(instrumentIds = []) {
    initialState.instruments = instrumentIds.map((i) => {
      return {
        instrumentId: i
      };
    });

    return function SharePriceDevelopmentReducer(state = initialState, action) {
      switch (action.type) {
        case FETCH_SHAREPRICE_DEVELOPMENT_BEGIN:
          return produce(state, draft => {
            draft.loading = true;
            draft.fetchError = null;
          });
        case FETCH_SHAREPRICE_DEVELOPMENT_SUCCESS:
          return produce(state, draft => {
            draft.loading = false;
            draft.instruments = action.payload.instruments;
          });
        case FETCH_SHAREPRICE_DEVELOPMENT_FAILURE:
          return produce(state, draft => {
            draft.loading = false;
            draft.fetchError = action.payload.error;
          });
        default:
          return state;
      }
    };
  }
