const i18n = (function () {
  function i18n() {
    this.locale = 'en-gb';
    this.translations = {};
    this.customPhrases = {};
  }

  i18n.prototype.load = function (locale, apiOrObj, callback = () => { }) {
    return this._request(apiOrObj).then((translations) => {
      this.locale = locale;
      this.translations = translations;
      if (callback) callback('success');
      return Promise.resolve(translations);
    }).catch((e) => {
      callback('error', e.message);
      return Promise.reject(e.message);
    });
  };

  i18n.prototype.find = function (key /*...params*/) {
    const argLength = arguments.length;

    if (this._hasKey(this.translations, key)) {
      let tran = this.translations[key];
      const ctx = this;
      if (argLength <= 1) {
        return tran;
      }

      const args = Array.prototype.slice.call(arguments, 1);

      if (args.length == 1 && this._isObject(args[0])) {
        // format: Text {propName1} {propName2} {propNameN}
        return tran.replace(/\{([^{}]+)\}/, function (match, prop) {
          return ctx._hasKey(args[0], prop) ? args[0][prop] : match;
        });
      }
      else {
        // format: Text {0} {1} {n}
        return tran.replace(/\{(\d+)\}/g, function (match, num) {
          return num * 1 < args.length ? args[num * 1] : match;
        });
      }

    }

    return undefined;
  };

  i18n.prototype.setCustomPhrases = function (customPhrases = {}) {
    this.customPhrases = Object.assign({}, this.customPhrases, customPhrases);
    Object.entries(this.customPhrases).forEach(([key, value]) => {
      if (this._hasKey(this.translations, key)) {
        this.translations[key] = value;
      }
    });
  };

  i18n.prototype._hasKey = function (obj, key) {
    return key in obj
      && Object.prototype.toString.call(obj[key]) === '[object String]';
  };

  i18n.prototype._isString = function (obj) {
    return Object.prototype.toString.call(obj) === '[object String]';
  };

  i18n.prototype._isObject = function (obj) {
    return Object.prototype.toString.call(obj) === '[object Object]';
  };

  i18n.prototype._request = function (url) {
    if (Object.prototype.toString.call(url) === '[object Object]') {
      return new Promise((resolve) => {
        resolve(url);
      });
    } else {
      return fetch(`${url}`).then(response => response.json());
    }
  };


  return new i18n();
})();

export default {
  load: function (locale, urlOrTranslations, callback) {
    return i18n.load(locale, urlOrTranslations, callback);
  },
  setCustomPhrases: function(customPhrases = {}) {
    i18n.setCustomPhrases(customPhrases);
  },
  translate: function (/*, params */) {
    return i18n.find(...arguments);
  }
};
