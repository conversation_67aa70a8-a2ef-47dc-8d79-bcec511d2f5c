import { arabicToNumber, numberToArabic } from '../format-number';

export default function pluginArabicNumber(option, dayjsClass) {
  const oldParse = dayjsClass.prototype.parse;
  dayjsClass.prototype.parse = function (cfg) {
    if (typeof cfg.date === 'string') {
      const locale = this.$locale();
      cfg.date = locale && locale.preparse ? locale.preparse(cfg.date) : arabicToNumber(cfg.date);
    }
    // original parse result
    return oldParse.bind(this)(cfg);
  };

  const oldFormat = dayjsClass.prototype.format;
  dayjsClass.prototype.format = function (...args) {
    // original format result
    const result = oldFormat.call(this, ...args);
    // return modified result
    const locale = this.$locale();
    return locale && locale.postformat ? locale.postformat(result) : numberToArabic(result);
  };
}
