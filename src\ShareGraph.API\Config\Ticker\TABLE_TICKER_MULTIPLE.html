﻿<table class="table table-striped">
    <caption>{caption}</caption>
    <thead class="table__head">
        <tr class="table__head-tr">
            <th scope="col" class="tickerName">{sharesLabel}</th>
            <th scope="col" class="last">{lastLabel}</th>
            <th scope="col" class="open">{openLabel}</th>
            <!-- <th scope="col" class="open">{previousCloseLabel}</th> -->
            <!-- <th scope="col">{highLabel}</th>
            <th scope="col">{lowLabel}</th> -->
            <th class="text-center change" scope="col">{changeLabel}</th>
            <th class="text-center changePercentage" scope="col">{changePercentageLabel}</th>
            <!-- <th scope="col">{volumeLabel}</th> -->
            <th scope="col" class="bid-ask">{bidAskLabel}</th>
            <!-- <th scope="col" class="bid-ask">{bidLabel}</th> -->
            <!-- <th scope="col" class="bid-ask">{askLabel}</th> -->
            <th scope="col" class="w52-high">{weeks52HighLabel}</th>
            <th scope="col" class="low">{lowLabel}</th>
        </tr>
    </thead>
    <tbody class="table__body">
        <tr class="table__body-tr">
            <td class="table__body-share tickerName">{tickerName} ({marketAbbreviation})</td>
            <td class="last"><span>{last}</span></td>
            <td class="open">{open}</td>
            <!-- <td class="open">{prevClose}</td> -->
            <!-- <td><span>{high}</span></td>
            <td><span>{low}</span></td> -->
            <td class="text-center ticker__change ticker__change--number change">
                <div class="ticker__animation">
                    <p class="ticker__animation-inner">
                        <span class="ticker__change-value">{change}</span>
                    </p>
                </div>
            </td>
            <td class="text-center ticker__change ticker__change--percent changePercentage">
                <div class="ticker__animation">
                    <p class="ticker__animation-inner">
                        <span class="ticker__change-value">{changePercentage}</span>
                    </p>
                </div>
            </td>
            <!-- <td>{volume}</td> -->
            <td class="bid-ask">{bid}/{ask}</td>
            <!-- <td class="bid-ask">{bid}</td> -->
            <!-- <td class="bid-ask">{ask}</td> -->
            <td class="w52-high">{high52W}</td>
            <td class="low">{low}</td>
        </tr>
    </tbody>
</table>
