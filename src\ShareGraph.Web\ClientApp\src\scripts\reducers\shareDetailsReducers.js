import produce from 'immer';
import appConfig from '../services/app-config';
import {dynamicSort, convertNumberDecimal, convertNumber, formatDateTime} from '../helper';
import i18n from '../services/i18n';
import { getMarketStatusLabel } from '../helper';

import {
  FETCH_SHAREDETAILS_BEGIN,
  FETCH_SHAREDETAILS_SUCCESS,
  FETCH_SHAREDETAILS_FAILURE
} from '../actions/shareDetailsActions';
import { formatNumberByInstrument } from '../utils/format-number';
import { shareDetailMapping } from '../graphql-queries/shareDetailQuery';

const initialState = {
  instruments: [],
  loading: true,
  fetchError: null
};

export default function createShareDetailsReducer(instrumentIds = []) {
  const setting = appConfig.get();
  initialState.instruments = instrumentIds.map((i) => {
    return {
      instrumentId: i
    };
  });

  const formatShareDetailsData = (field, value, instrumentId) => {
    return value;
  };

  const getShareDetailFields = (fieldSetting) => {
    fieldSetting = fieldSetting || setting.shareDetails.shareDataItems;
    let shareDetailFields = {};
    fieldSetting.forEach(field => {
      let temp = shareDetailMapping[field.trim().toUpperCase()];
      if(temp) {
        const { dataField } = temp;
        Object.assign(shareDetailFields, {
          [dataField]: {
            label: temp.label,
            icon: field.trim().toLowerCase().replace(/_/g, '-'),
            order: fieldSetting.indexOf(field)
          }
        });
      }
    });
    return shareDetailFields;
  };

  return function shareDetailsReducers(state = initialState, action) {
    switch (action.type) {
      case FETCH_SHAREDETAILS_BEGIN:
        return produce(state, draft => {
          draft.loading = true;
          draft.fetchError = null;
        });
      case FETCH_SHAREDETAILS_SUCCESS:
        return produce(state, draft => {
          draft.loading = false;
          let instruments = action.payload.instruments;
          const shareDetailFields = getShareDetailFields();
          let setting = appConfig.get();
            instruments = instruments.map(instrument => {
            let {
              instrumentId,
              shareName,
              marketAbbreviation,
              marketStatus,
              highYtdDate,
              lowYtdDate,
              allTimeHighDate,
              allTimeLowDate,
              lowest52wDate,
              highest52wDate,
              ...rest
            } = instrument;
            if('marketStatus' in instrument) {
              rest.marketStatus = getMarketStatusLabel(instrument);
            }
            const dataFields = Object.entries(rest).map(([field, value]) => {
              let fieldCus = shareDetailFields[field];
              return {label: i18n.translate(fieldCus.label), value: formatShareDetailsData(field, value, instrumentId), icon: fieldCus.icon, order: fieldCus.order, field, instrumentId};
            });
            const customPhrase = setting?.instruments?.find(x => x.id === instrumentId);
            return {
              instrumentId: instrumentId,
              shareName: customPhrase?.tickerName || shareName,
              marketAbbreviation,
              dataFields: dataFields.sort(dynamicSort('order')),
              order: setting.instruments.find(x=> x.id === instrumentId).order //TODO in case no order
            };
          });
          draft.instruments = instruments.sort(dynamicSort('order'));
        });
      case FETCH_SHAREDETAILS_FAILURE:
        return produce(state, draft => {
          draft.loading = false;
          draft.fetchError = action.payload.error;
        });
      default:
        return state;
    }
  };
}
