using System.Collections.Generic;

namespace Euroland.FlipIT.ShareGraph.API.Models
{
  public class Currency
  {
    public string Code { get; set; }
    public string Name { get; set; }
    public int? TranslationId { get; set; }
  }

  public class CurrencyData
  {
    public List<Currency> CurrenciesByCodes { get; set; }
  }

  public class CurrencyResponse
  {
    public CurrencyData Currency { get; set; }
  }
}
