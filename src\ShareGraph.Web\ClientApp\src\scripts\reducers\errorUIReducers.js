import produce from 'immer';

import { CLEAR_ERROR_UI, HAS_ERROR_UI } from '../actions/errorUIActions';

const initialState = {
  error: null
};

const errorUIReducer = produce((draft, action) => {
  switch (action.type) {
    case HAS_ERROR_UI:
      draft.error = action.payload;
      break;
    case CLEAR_ERROR_UI:
        draft.error = null;
      break;
    default:
      break;
  }
}, initialState);

export default errorUIReducer;
