/**
 * @typedef {Object} PointAnimationConfig
 * @property {number} [dotRadius=4] - The radius of the dot.
 * @property {number} [duration=1400] - The duration of the animation in milliseconds.
 * @property {number} [sleep=0] - The delay between animation iterations in milliseconds.
 * @property {number} [size=30] - The size of the animation in pixels.
 */

/**
 * Represents a point animation.
 * @extends Animation
 */
export class PointAnimation extends Animation {
  /**
   * Creates a new PointAnimation instance.
   * @param {PointAnimationConfig} config - The configuration for the animation.
   */
  constructor(config = {}) {
    const { dotRadius = 4, duration = 1400, sleep = 0, size = 30 } = config;
    
    const element = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    element.setAttribute('class', 'point-animation');
    element.setAttribute('viewBox', `0 0 ${size} ${size}`);
    element.setAttribute('width', size);
    element.setAttribute('height', size);
    element.innerHTML = `
      <circle class="point-ring" transform="translate(${size / 2},${size / 2})" fill="transparent" stroke="currentColor" stroke-width="1px"/>
      <circle class="point-dot" transform="translate(${size / 2},${size / 2})" r="${dotRadius}" fill="currentColor"/>
    `;

    super(
      new KeyframeEffect(
        element.querySelector('.point-ring'),
        [
          { r: '0%', opacity: 1, easing: 'ease-out' },
          { r: '50%', opacity: 0, offset: duration / (duration + sleep) },
          { r: '50%', opacity: 0 }
        ],
        {
          duration: duration + sleep,
          iterations: Infinity,
          composite: 'replace',
          fill: 'none'
        }
      )
    );

    this.config = config;
    this.element = element;
    
    this.play();
  }

  /**
   * Restarts the animation.
   */
  restart() {
    this.cancel();
    this.play();
  }

  destroy() {
    this.cancel();
    this.element.remove();
  }
}