using Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Studies;
using Euroland.FlipIT.ShareGraph.API.Extensions;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using HotChocolate;
using System.Linq;

namespace Euroland.FlipIT.ShareGraph.API.Resolvers
{
  public class StudiesResolver
  {
    public Macd GetMACD([Service] ISetting _settings)
    {
      var macd = _settings.GetChild($"Chart:Studies:MACD")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<Macd>();

      return macd;
    }

    public Rsi GetRSI([Service] ISetting _settings)
    {
      var rsi = _settings.GetChild($"Chart:Studies:RSI")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<Rsi>();

      return rsi;
    }

    public BollingerBands GetBollingerBands([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:BollingerBands")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<BollingerBands>();

      return res;
    }

    public IchimokuClouds GetIchimokuClouds([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:IchimokuClouds")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<IchimokuClouds>();

      return res;
    }

    public Stochastics GetStochastics([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:Stochastics")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<Stochastics>();

      return res;
    }

    public TotalReturn GetTotalReturn([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:TotalReturn")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<TotalReturn>();

      return res;
    }

    public Adx GetADX([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:ADX")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<Adx>();

      return res;
    }

    public Volume GetVolume([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:Volume")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<Volume>();

      return res;
    }

    public Volume GetVolumeUnderlay([Service] ISetting _settings)
    {
      var res = _settings.GetChild($"Chart:Studies:VolumeUnderlay")?.GetChildren()
        .ToDictionary(s => s.Name, s => s.Value)
        .ToObject<Volume>();

      return res;
    }
  }
}
