"ee-v-webcat151,16":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:production
  dependencies:
    - package-prod
  variables:
    DEPLOY_ENVIRONMENT: Production
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_SERVICE_URL_SERVER1: 'https://ee-v-webcat151.euroland.com:8172/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER2: 'https://ee-v-webcat161.euroland.com:8172/msdeploy.axd'
    DEPLOY_SERVER_NAME1: 'ee-v-webcat151'
    DEPLOY_SERVER_NAME2: 'ee-v-webcat161'
    DEPLOY_API_IIS_APP_PATH: 'production-site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'production-site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - master
  environment:
    name: Ground
    url: 'https://gr-web-ws1.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'

"ne-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:production
  dependencies:
    - package-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudNE
    DEPLOY_SERVICE_URL_SERVER2: 'https://ne-web-haproxy.northeurope.cloudapp.azure.com:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://ne-web-haproxy.northeurope.cloudapp.azure.com:8172/msdeploy.axd'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_SERVER_NAME1: 'ne-web-ws1'
    DEPLOY_SERVER_NAME2: 'ne-web-ws2'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - master
  environment:
    name: CloudNE
    url: 'https://ne-web-ws1.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'

"ea-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:production
  dependencies:
    - package-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudEA
    DEPLOY_SERVICE_URL_SERVER2: 'https://ea-web-haproxy.eastasia.cloudapp.azure.com:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://ea-web-haproxy.eastasia.cloudapp.azure.com:8172/msdeploy.axd'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_SERVER_NAME1: 'ea-web-ws1'
    DEPLOY_SERVER_NAME2: 'ea-web-ws2'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - master
  environment:
    name: CloudEA
    url: 'https://ea-web-ws1.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'

"uae-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:production
  dependencies:
    - package-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudUAE
    DEPLOY_SERVICE_URL_SERVER2: 'https://uaewebhaproxy1.uaenorth.cloudapp.azure.com:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://uaewebhaproxy1.uaenorth.cloudapp.azure.com:8172/msdeploy.axd'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_SERVER_NAME1: 'uae-web-ws1'
    DEPLOY_SERVER_NAME2: 'uae-web-ws2'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - master
  environment:
    name: CloudUAE
    url: 'https://uae-web-ws1.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'

"ksa-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:production
  dependencies:
    - package-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudKSA
    DEPLOY_SERVICE_URL_SERVER2: 'https://**************:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://**************:8172/msdeploy.axd'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_SERVER_NAME1: 'ksa-web-ws1'
    DEPLOY_SERVER_NAME2: 'ksa-web-ws2'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - master
  environment:
    name: CloudKSA
    url: 'https://ksa-web-ws1.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'

"cn-web-ws1,2":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:production
  dependencies:
    - package-prod
  variables:
    DEPLOY_ENVIRONMENT: CloudCN
    DEPLOY_SERVICE_URL_SERVER2: 'https://cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn:8173/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER1: 'https://cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn:8172/msdeploy.axd'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_SERVER_NAME1: 'cn-web-ws1'
    DEPLOY_SERVER_NAME2: 'cn-web-ws2'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME2%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to %DEPLOY_SERVER_NAME1%...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - master
  environment:
    name: CloudCN
    url: 'https://cn-web-ws1.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'
