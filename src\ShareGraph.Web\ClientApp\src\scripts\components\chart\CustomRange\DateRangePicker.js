import { useEffect, useMemo, useRef, useState } from 'react';
import { PickerDate } from '@euroland/calendar-react';

import i18n from '../../../services/i18n';
import { formatShortDate } from '../../../helper';
import useFocusTrap from '../../../customHooks/useFocusTrap';

export const DateRangePicker = ({ openedByKeyboard, onChange, dateFormat, dateRange, startingDate }) => {
  const inputFromRef = useRef();
  const inputToRef = useRef();
  const [disableBtnDone, setDisableBtnDone] = useState(false);
  const [{ from, to }, setDateRange] = useState({
    from: dateRange.from,
    to: dateRange.to
  });
  const dialogRef = useRef();
  useFocusTrap(dialogRef);
  const applyClick = (e) => {
    e.stopPropagation();
    const fromDate = from;
    const toDate = to;
    if (fromDate > toDate) return;
    onChange({ from: fromDate, to: toDate });
  };

  const validateRange = () => {
    if (!from || !to) return;
    const fromDate = from;
    const toDate = to;
    setDisableBtnDone(fromDate > toDate);
  };

  const onSelectCalendarFrom = time => {
    setDateRange(prev => ({ ...prev, from: time }));
    validateRange();
  };

  const onSelectCalendarTo = time => {
    setDateRange(prev => ({ ...prev, to: time }));
    validateRange();
  };

  useEffect(() => {
    if (!inputFromRef.current) return;
    inputFromRef.current.focus();
  }, [inputFromRef.current]);

  const fromHeaderRender = () => {
    return (
      <div className="datepicker__selected-date">
        <p className="datepicker__title">{i18n.translate('selectDate')}</p>
        <p className="datepicker__value">{formatShortDate(from, { isTimezone: false })}</p>
      </div>
    );
  };

  const toHeaderRender = () => {
    return (
      <div className="datepicker__selected-date">
        <p className="datepicker__title">{i18n.translate('selectDate')}</p>
        <p className="datepicker__value">{formatShortDate(to, { isTimezone: false })}</p>
      </div>
    );
  };
  const maxDateToDate = useMemo(() => new Date(), []);
  
  return (
    <div ref={dialogRef}>
      <div className="custom-range-container">
        <label className='pickerDateFrom' htmlFor='pickerDateFrom'>{i18n.translate('startDate')}</label>
        <PickerDate
          id="pickerDateFrom"
          headerRender={fromHeaderRender}
          hasFooter={false}
          ref={inputFromRef}
          minDate={startingDate}
          maxDate={to}
          dateFormat={dateFormat}
          value={from}
          onChange={onSelectCalendarFrom}
          autoComplete="off"
        />
        <label className='pickerDateTo' htmlFor='pickerDateTo'>{i18n.translate('endDate')}</label>
        <PickerDate
          id="pickerDateTo"
          headerRender={toHeaderRender}
          hasFooter={false}
          ref={inputToRef}
          className="datepicker__toDate"
          minDate={from || startingDate}
          maxDate={maxDateToDate}
          dateFormat={dateFormat}
          value={to}
          onChange={onSelectCalendarTo}
          autoComplete="off"
        />
      </div>
      <div className="button-apply-custom-range">
        <button className="btn-done" onClick={applyClick} disabled={disableBtnDone}>
          {i18n.translate('done')}
        </button>
      </div>
    </div>
  );
};
