import fetchApi from '../services/fetch-api';
import { appSettings } from '../../appSettings';
import { getAllInstrumentsSetting } from '../configs/configuration-app';
import { getCurrencyText } from '../helper';
import { trimAbbreviation } from '../utils';
import { GET_52_WEEKS_QUERY } from '../graphql-queries/52WeeksQuery';
import { client, clientRT } from '../services/graphql-client';
import appConfig from '../services/app-config';

function get52WeeksData(realTimeInstrumentIds = [], notRealTimeInstrumentIds= [], toCurrency) {
  const fields = [
      'instrumentId',
      'shareName',
      'last',
      'low52W',
      'high52W',
      'currencyCode',
      'marketAbbreviation'
  ];
  return fetchApi(appSettings.sDataApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
        ${realTimeInstrumentIds.length ? `realtime: instruments(instrumentIds: [${realTimeInstrumentIds.join(',')}] ${toCurrency ? `,toCurrency: "${toCurrency}"` : ''} ,isRT: true){
          ${fields.join(',')}
        }` : ''}
        ${notRealTimeInstrumentIds.length ? `notRealtime: instruments(instrumentIds: [${notRealTimeInstrumentIds.join(',')}] ${toCurrency ? `,toCurrency: "${toCurrency}"` : ''} ,isRT: false){
          ${fields.join(',')}
        }`: ''}
      }`
    })
  })
    .then(res => res.json());
}

const convertNewRespDataToOldRespData = (responseJson) => {
  return responseJson.data.instrumentByIds.map(instrument => {
    const { id, shareName, currency, currentPrice, market, _52W } = instrument;
    return {
        instrumentId: id,
        shareName: trimAbbreviation(shareName),
        last: currentPrice?.last,
        low52W: _52W?.lowest,
        high52W: _52W?.highest,
        currencyCode: currency?.code,
        marketAbbreviation: market?.abbreviation
    };
  });
};

async function get52WeeksDataNewQuery({
  instrumentIds = [],
  toCurrency,
  isRT = false
}) {
  if (!instrumentIds.length) return;
  const clientInstance = isRT ? clientRT : client;

  const responseData = await clientInstance.query(GET_52_WEEKS_QUERY, { ids: instrumentIds, toCurrency});

  const data = convertNewRespDataToOldRespData(responseData);
  return data;
}

// 52Weeks Actions
export const FETCH_52WEEKS_BEGIN = 'FETCH_52WEEKS_BEGIN';
export const FETCH_52WEEKS_SUCCESS = 'FETCH_52WEEKS_SUCCESS';
export const FETCH_52WEEKS_FAILURE = 'FETCH_52WEEKS_FAILURE';
// export const REFRESH_52WEEKS_BEGIN = 'REFRESH_52WEEKS_BEGIN';
// export const REFRESH_52WEEKS_SUCCESS = 'REFRESH_52WEEKS_SUCCESS';
// export const REFRESH_52WEEKS_FAILURE = 'REFRESH_52WEEKS_FAILURE';

export const fetch52WeeksBegin = () => ({
  type: FETCH_52WEEKS_BEGIN
});

export const fetch52WeeksSuccess = (instruments = []) => {
  const allInstrumentsSetting = getAllInstrumentsSetting();
  return (dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);

    const sortedInstruments  = Object.keys(allInstrumentsSetting).map(instrumentId=>{
        const instrument = instruments.find(instrument=>instrument.instrumentId === Number(instrumentId));
        return instrument ? {
          ...instrument,
          currencyCode: instrument.currencyCode ? 
          (selectedCurrencyText || allInstrumentsSetting[instrument.instrumentId]?.currencyCode || instrument.currencyCode) : ''
        } : null;
    }).filter(ins => !!ins);

    return dispatch({
      type: FETCH_52WEEKS_SUCCESS,
      payload: {
        instruments: sortedInstruments
      }
    });
  };
};

export const fetch52WeeksFailure = (error) => ({
  type: FETCH_52WEEKS_FAILURE,
  payload: { error }
});

// export const refresh52weeksBigin = () => ({
//   type: REFRESH_52WEEKS_BEGIN
// });

// export const refresh52weeksucess = (instruments = []) => ({
//   type: REFRESH_52WEEKS_SUCCESS,
//   payload: { instruments }
// });

// export const refresh52weeksFailure = (error) => ({
//   type: REFRESH_52WEEKS_FAILURE,
//   payload: { error }
// });

const filterInstrumentIdByRealTime = (instruments = []) => {
  return instruments.reduce((acc, instrument) => {
    if (instrument.isRT) {
      acc.realTimeInstrumentIds.push(instrument.instrumentId);
    } else {
      acc.notRealTimeInstrumentIds.push(instrument.instrumentId);
    }
    return acc;
  }, { realTimeInstrumentIds: [], notRealTimeInstrumentIds: [] });
};

export function fetch52Weeks(instrumentIds = []) {
  return async (dispatch, getState) => {
    dispatch(fetch52WeeksBegin());

    const excludeInstrumentIds = appConfig.get().performance.excludeIds||[];


    let realTimeInstrumentIds = [];

    let notRealTimeInstrumentIds = [];

    if (!instrumentIds || !instrumentIds.length) {
        const {realTimeInstrumentIds:rtPeerIds, notRealTimeInstrumentIds:nrtPeerIds} = filterInstrumentIdByRealTime(getState().peers.instruments);
        const {realTimeInstrumentIds:rtIndicesIds, notRealTimeInstrumentIds:nrtIndicesIds} = filterInstrumentIdByRealTime(getState().indices.instruments);
        const {realTimeInstrumentIds:rtTickerIds, notRealTimeInstrumentIds:nrtTickerIds} = filterInstrumentIdByRealTime(getState().tickers.instruments);
        realTimeInstrumentIds = [...rtPeerIds, ...rtIndicesIds, ...rtTickerIds];
        notRealTimeInstrumentIds = [...nrtPeerIds, ...nrtIndicesIds, ...nrtTickerIds];
    }

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    const allInstrumentSettings = getAllInstrumentsSetting();

    // We can do fake fetching 52Weeks data by return "fakeget52WeeksData()" as well.
    // return get52WeeksData(realTimeInstrumentIds, notRealTimeInstrumentIds, toCurrency)
    //   .then(json => {
    //     let data = [];
    //     if(json.data.realtime) {
    //         data = data.concat(json.data.realtime);
    //     }
    //     if(json.data.notRealtime) {
    //         data = data.concat(json.data.notRealtime);
    //     }
    //     dispatch(fetch52WeeksSuccess(data));
    //     return json.data.instruments;
    //   })
    //   .catch(err =>
    //     dispatch(fetch52WeeksFailure(err))
    //   );

    const realTime = {
      ids: realTimeInstrumentIds.filter(
        (id) =>
          !allInstrumentSettings[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      ),
      adjIds: realTimeInstrumentIds.filter(
        (id) =>
          allInstrumentSettings[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      )
    };

    const notRealTime = {
      ids: notRealTimeInstrumentIds.filter(
        (id) =>
          !allInstrumentSettings[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      ),
      adjIds: notRealTimeInstrumentIds.filter(
        (id) =>
          allInstrumentSettings[id].enabledAdjustPrice &&
          !excludeInstrumentIds.includes(id)
      )
    };

    const promiseAll = [];

    if(realTime.adjIds.length > 0) promiseAll.push(
      clientRT.query(GET_52_WEEKS_QUERY, { ids: realTime.adjIds, toCurrency, adjClose: true})
    );

    if(realTime.ids.length > 0) promiseAll.push(
      clientRT.query(GET_52_WEEKS_QUERY, { ids: realTime.ids, toCurrency, adjClose: false})
    );

    if(notRealTime.adjIds.length > 0) promiseAll.push(
      client.query(GET_52_WEEKS_QUERY, { ids: notRealTime.adjIds, toCurrency, adjClose: true})
    );

    if(notRealTime.ids.length > 0) promiseAll.push(
      client.query(GET_52_WEEKS_QUERY, { ids: notRealTime.ids, toCurrency, adjClose: false})
    );
    try{
      const instruments = (await Promise.all(promiseAll)).map(convertNewRespDataToOldRespData).flat();
      dispatch(fetch52WeeksSuccess(instruments));
    } catch(err) {
      dispatch(fetch52WeeksFailure(err));
      console.error(err);
      throw err;
    }
  };
}

// export function refresh52weeks(instrumentIds = []) {
//   return (dispatch, getState) => {
//     dispatch(refresh52weeksBigin());

//     if (!instrumentIds || !instrumentIds.length) {
//       const ids = getState().peers.instruments.map(ins => ins.instrumentId);
//       instrumentIds = [...ids];
//     }
//     // We can do fake fetching 52Weeks data by return "fakeget52WeeksData()" as well.
//     //
//     return get52WeeksData(instrumentIds)
//       .then(json => {
//         dispatch(refresh52weeksucess(json.data.instruments));
//         return json.data.instruments;
//       })
//       .catch(err =>
//         dispatch(refresh52weeksFailure(err))
//       );
//   };
// }
