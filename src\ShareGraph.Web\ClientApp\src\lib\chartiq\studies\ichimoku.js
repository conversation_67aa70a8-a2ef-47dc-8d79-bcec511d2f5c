import { CIQ } from 'chartiq/js/components';
import { appSettings } from '../../../appSettings';


CIQ.Euroland.Studies.calculateIchimoku = function (stx, sd) {
	var quotes, inputs, periodHighLow, leadingSpanA, leadingSpanB, tick, ticks;
	quotes = sd.chart.scrubbed;
	inputs = {
		Base: Number(sd.inputs['Base Line Period']),
		Conv: Number(sd.inputs['Conversion Line Period']),
		LeadB: Number(sd.inputs['Leading Span B Period']),
		Lag: Number(sd.inputs['Lagging Span Period'])
	};

	// calculate Conversion Line, Base Line and Lagging Span
	for (var i = sd.startFrom; i < quotes.length; i++) {
		if (!quotes[i]) continue;
		periodHighLow = getPeriodHighLow(inputs.Conv, i);
		quotes[i]['Conversion Line ' + sd.name] = (periodHighLow[1] + periodHighLow[0]) / 2;
		periodHighLow = getPeriodHighLow(inputs.Base, i);
		quotes[i]['Base Line ' + sd.name] = (periodHighLow[1] + periodHighLow[0]) / 2;
		if (i < inputs.Lag) continue;
		quotes[i - inputs.Lag]['Lagging Span ' + sd.name] = quotes[i].Close;
	}

	// calculate Leading Span A, Leading Span A
	ticks = [];
	for (var i = Math.max(0, sd.startFrom - inputs.Base); i < quotes.length; i++) {
		periodHighLow = getPeriodHighLow(inputs.LeadB, i);
		leadingSpanA = (quotes[i]['Conversion Line ' + sd.name] + quotes[i]['Base Line ' + sd.name]) / 2;
		leadingSpanB = (periodHighLow[1] + periodHighLow[0]) / 2;
		if (quotes[i + inputs.Base]) {
			quotes[i + inputs.Base]['Leading Span A ' + sd.name] = leadingSpanA;
			quotes[i + inputs.Base]['Leading Span B ' + sd.name] = leadingSpanB;
		} else {
		tick = {};
			tick['Leading Span A ' + sd.name] = leadingSpanA;
			tick['Leading Span B ' + sd.name] = leadingSpanB;
			ticks.push(tick);
		}
	}

	sd.appendFutureTicks(stx, ticks);

	function getPeriodHighLow(period, currentDay) {
		var lowVal, highVal;
		lowVal = Number.MAX_VALUE;
		highVal = Number.MAX_VALUE * -1;
		for (var i = currentDay - period + 1; i <= currentDay; i++) {
			if (i < 0) continue;
			lowVal = Math.min(lowVal, quotes[i].Low);
			highVal = Math.max(highVal, quotes[i].High);
		}
		return [lowVal, highVal];
	}
};

CIQ.Euroland.Studies.displayIchimoku = function (stx, sd, quotes) {
	var spanA, spanB, spanAColor, spanBColor, panel, yAxis, parameters;
	spanA = 'Leading Span A ' + sd.name;
	spanB = 'Leading Span B ' + sd.name;
	spanAColor = CIQ.Studies.determineColor(sd.outputs[sd.outputMap[spanA]]);
	spanBColor = CIQ.Studies.determineColor(sd.outputs[sd.outputMap[spanB]]);
	panel = stx.panels[sd.panel];
	yAxis = sd.getYAxis(stx);
	parameters = {
		topBand: spanA,
		bottomBand: spanB,
		topColor: spanAColor,
		bottomColor: spanBColor,
		skipTransform: panel.name != sd.chart.name,
		topAxis: yAxis,
		bottomAxis: yAxis,
		opacity: 0.3
	};
	if (!sd.highlight && stx.highlightedDraggable) {
		parameters.opacity *= 0.3;
	}
	CIQ.fillIntersecting(stx, sd.panel, parameters);
	CIQ.Studies.displaySeriesAsLine(stx, sd, quotes);
};

const studyName = CIQ.I18N.translate('Ichimoku Clouds');

CIQ.Euroland.Studies.studyLibrary = CIQ.extend(CIQ.Studies.studyLibrary, {
	[studyName]: {
		name: studyName,
		overlay: true,
		calculateFN: CIQ.Euroland.Studies.calculateIchimoku,
		seriesFN: CIQ.Euroland.Studies.displayIchimoku,
		inputs: {
			'Conversion Line Period': 9,
			'Base Line Period': 26,
			'Leading Span B Period': 52,
			'Lagging Span Period': 26
		},
		outputs: {
			'Conversion Line': '#0000FF',
			'Base Line': '#FF0000',
			'Leading Span A': '#00FF00',
			'Leading Span B': '#FF0000',
			'Lagging Span': '#808000'
		}
	}
});

export { CIQ };
