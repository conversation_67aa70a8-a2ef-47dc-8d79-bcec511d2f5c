import { CIQ } from 'chartiq/js/components';

const createPattern = (type = 'up', color) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = 10;
  canvas.height = 10;

  if (type === 'down') {
    ctx.strokeStyle = color;
    ctx.beginPath();
    ctx.moveTo(10, 0);
    ctx.lineTo(0, 10);
    ctx.stroke();
    return ctx.createPattern(canvas, 'repeat');
  }

  // Draw a simple diagonal line pattern
  ctx.strokeStyle = color;
  ctx.beginPath();
  ctx.moveTo(0, 0);
  ctx.lineTo(10, 10);
  ctx.stroke();
  return ctx.createPattern(canvas, 'repeat');
};

CIQ.Euroland.Studies.createStudy('volume', {
  name: 'Volume Chart',
  range: '0 to max',
  yAxis: { ground: true, initialMarginTop: 0, zoom: 0 },
  seriesFN: function (stx, sd, quotes) {
    var panel = sd.panel,
      inputs = sd.inputs,
      underlay = sd.underlay,
      overlay = sd.overlay;
    var inAnotherPanel = underlay || overlay;
    var colorUp = CIQ.Studies.determineColor(sd.outputs['Up Volume']);
    var colorDown = CIQ.Studies.determineColor(sd.outputs['Down Volume']);
    var style = underlay ? 'stx_volume_underlay' : 'stx_volume';
    stx.setStyle(style + '_up', 'color', colorUp);
    stx.setStyle(style + '_down', 'color', colorDown);
    const isBlindMode = sd.study?.parameters?.isBlindMode;
    // console.log('parameters, inputs ...........................', sd.study.parameters, inputs);

    var seriesParam = [
      {
        field: sd.volumeField || 'Volume',
        // fill_color_up: stx.canvasStyle(style + '_up').color ,
        fill_color_up: !isBlindMode
          ? stx.canvasStyle(style + '_up').color
          : createPattern('up', colorUp),
        border_color_up: stx.canvasStyle(style + '_up').borderLeftColor,
        opacity_up: stx.canvasStyle(style + '_up').opacity,
        // fill_color_down: stx.canvasStyle(style + '_down').color ,
        fill_color_down: !isBlindMode
          ? stx.canvasStyle(style + '_down').color
          : createPattern('down', colorDown),
        border_color_down: stx.canvasStyle(style + '_down').borderLeftColor,
        opacity_down: stx.canvasStyle(style + '_down').opacity,
        color_function: sd.colorFunction
      }
    ];
    var seriesParam0 = seriesParam[0];

    // Major backward compatibility hack. If the border color is the same as our standard color
    // then most likely the customer is missing border: #000000 style on stx_volume_up and stx_volume_down
    if (!underlay && seriesParam0.border_color_down === 'rgb(184, 44, 12)') {
      seriesParam0.border_color_down = '#000000';
      seriesParam0.border_color_up = '#000000';
    }

    var yAxis = sd.getYAxis(stx);
    var params = {
      name: 'Volume',
      panel: panel,
      yAxis: yAxis,
      widthFactor: 1,
      bindToYAxis: true,
      highlight: sd.highlight
    };

    CIQ.extend(params, sd.study.parameters);
    CIQ.extend(params, sd.parameters);

    if (stx.colorByCandleDirection && !sd.colorFunction) {
      seriesParam0.color_function = function (quote) {
        var O = quote.Open,
          C = quote.Close;
        //if((!O && O!==0) || (!C && C!==0) || O===C) return stx.defaultColor;

        return {
          fill_color:
            O > C ? seriesParam0.fill_color_down : seriesParam0.fill_color_up,
          border_color:
            O > C
              ? seriesParam0.border_color_down
              : seriesParam0.border_color_up,
          opacity: O > C ? seriesParam0.opacity_down : seriesParam0.opacity_up
        };
      };
    }
    stx.drawHistogram(params, seriesParam);
  },
  calculateFN: CIQ.Studies.calculateVolume,
  inputs: {
    // id: 'volume',
    // display: 'volume',
    // isBlindMode: true
  },
  outputs: {
    'Up Volume': '#8cc176',
    'Down Volume': '#b82c0c'
  },
  parameters: {
    isBlindMode: true
  }
});

export { CIQ };
