import { VIDEO_TYPE } from '../createVideo';

export class VimeoVideo {
  constructor(config) {
    this.container = document.createElement('div');
    this.container.style.padding = '56.25% 0 0 0';
    this.container.style.position = 'relative';
    this.container.playerVideo = this;
    this.config = config;
    this.isPlaying = false;
  }

  initVimeo() {
    if(this.video) return;
    const {videoId, title, appId} = this.config;

    this.container.innerHTML = getTemplateEmbeddedVideo({ videoId, videoType: VIDEO_TYPE.VIMEO, title, appId });
    this.iframe = this.container.querySelector('iframe');
    /*global Vimeo */
    this.video = new Vimeo.Player(this.iframe);
    this.callback = () => this.config.callback(this.container);
    this.video.on('play', this.callback);
  }
  play() {
    this.initVimeo();
    this.video.play();
    this.isPlaying = true;
  }
  pause() {
    if(!this.video) return;
    this.video.pause();
    this.isPlaying = false;
  }

  destroy() {
    this.video.off('play', this.callback);
  }

}

function getTemplateEmbeddedVideo({ videoType, videoId, title, appId }) {
  return `<iframe src="https://player.vimeo.com/video/${videoId}?badge=0&amp;autopause=false&amp;player_id=0&amp;app_id=${appId}"
    frameborder="0" allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style="position:absolute;top:0;left:0;width:100%;height:100%;" title="${title}">
  </iframe>`;
}
