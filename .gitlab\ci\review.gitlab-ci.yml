"Deploy QA":
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:qa
  dependencies:
    - package-qa
  variables:
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_ENVIRONMENT: QA
    DEPLOY_SERVICE_URL: 'https://BINGO:8172/msdeploy.axd'
    DEPLOY_API_IIS_APP_PATH: 'tools site/tools/FlipIT-ShareGraph-API'
    DEPLOY_WEB_IIS_APP_PATH: 'tools site/tools/ShareGraph3'
  script:
    - |
      ECHO Deploying ShareGraph3-API...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%DEV_VN_DEPLOY_USER%" --pwd "%DEV_VN_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying ShareGraph3-Web...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%DEV_VN_DEPLOY_USER%" --pwd "%DEV_VN_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - develope
  environment:
    name: Development/$CI_COMMIT_REF_SLUG
    url: 'https://dev.vn.euroland.com/tools/ShareGraph3/?companycode=dk-cbg'
