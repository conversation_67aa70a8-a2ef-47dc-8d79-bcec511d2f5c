import { useContext, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppContext } from '../AppContext';
import { fetchOrderDepth } from '../actions/orderDepthAction';
import { REFRESH_ORDER_DEPTH_TIME } from '../constant/common';
import { useSelectedCurrency } from './useSelectedCurrency';

/**
 * 
 *
 * @returns {{
 *  data: object[],
 *  loading: boolean,
 *  error: object | null,
 *  maxBuyPrice: number,
 *  totalBuyVolume: number,
 *  maxSellPrice: number,
 *  totalSellVolume: number,
 *  maxBuyVolume: number,
 *  maxSellVolume: number
 * }}
 */
const useOrderDepth = () => {
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);

  const { loading, orderDepthData, fetchError } = useSelector(state => state.orderDepth) || {};
  const dispatch = useDispatch();
  const settings = useContext(AppContext);
  const selectedCurrency = useSelectedCurrency();

  const refreshTime = settings.orderDepth?.refreshSeconds || REFRESH_ORDER_DEPTH_TIME;

  const aggregateData = useMemo(
    () =>
      orderDepthData.reduce(
        (acc, item) => {
          acc.totalBuyVolume += item.buyVolume;
          acc.totalSellVolume += item.sellVolume;
          acc.maxBuyVolume = Math.max(acc.maxBuyVolume, item.buyVolume);
          acc.maxSellVolume = Math.max(acc.maxSellVolume, item.sellVolume);

          acc.maxBuyPrice = Math.max(acc.maxBuyPrice, item.buyPrice);
          acc.maxSellPrice = Math.max(acc.maxSellPrice, item.sellPrice);

          return acc;
        },
        {
          maxBuyPrice: 0,
          totalBuyVolume: 0,
          maxSellPrice: 0,
          totalSellVolume: 0,
          maxBuyVolume: 0,
          maxSellVolume: 0
        }
      ),
    [orderDepthData]
  );

  useEffect(() => {
    function fetchData() {
      dispatch(fetchOrderDepth({ currencyCode: selectedCurrency?.code }));
    }

    fetchData();

    const timer = setInterval(() => {
      fetchData();
    }, refreshTime * 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [selectedInstrumentId, refreshTime, selectedCurrency?.code]);


  return { data: orderDepthData || [], loading, fetchError, ...aggregateData };
};

export default useOrderDepth;
