# DATE TIME FORMAT PARTERN FOR SHAREGRAPH 3 FRONT-END

* The characters wrapped between two square brackets ([]) are escaped.
e.g. 
```
[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z] -> YYYYescape 2019-01-25T00:00:00-02:00Z
```

#### List of all available formats

| Format | Output           | Description                           |
| ------ | ---------------- | ------------------------------------- |
| `YY`   | 18               | Two-digit year                        |
| `YYYY` | 2018             | Four-digit year                       |
| `M`    | 1-12             | The month, beginning at 1             |
| `MM`   | 01-12            | The month, 2-digits                   |
| `MMM`  | Jan-Dec          | The abbreviated month name            |
| `MMMM` | January-December | The full month name                   |
| `D`    | 1-31             | The day of the month                  |
| `DD`   | 01-31            | The day of the month, 2-digits        |
| `d`    | 0-6              | The day of the week, with Sunday as 0 |
| `dd`   | Su-Sa            | The min name of the day of the week   |
| `ddd`  | Sun-Sat          | The short name of the day of the week |
| `dddd` | Sunday-Saturday  | The name of the day of the week       |
| `H`    | 0-23             | The hour                              |
| `HH`   | 00-23            | The hour, 2-digits                    |
| `h`    | 1-12             | The hour, 12-hour clock               |
| `hh`   | 01-12            | The hour, 12-hour clock, 2-digits     |
| `m`    | 0-59             | The minute                            |
| `mm`   | 00-59            | The minute, 2-digits                  |
| `s`    | 0-59             | The second                            |
| `ss`   | 00-59            | The second, 2-digits                  |
| `SSS`  | 000-999          | The millisecond, 3-digits             |
| `Z`    | +05:00           | The offset from UTC, ±HH:mm           |
| `ZZ`   | +0500            | The offset from UTC, ±HHmm            |
| `A`    | AM PM            |                                       |
| `a`    | am pm            |                                       |
| ...    |  ...             | Other formats @>>AdvancedFormat |


#### List of advandced formats

| Format | Output                | Description                                           |
| ------ | --------------------- | ----------------------------------------------------- |
| `Q`    | 1-4                   | Quarter                                               |
| `Do`   | 1st 2nd ... 31st      | Day of Month with ordinal                             |
| `k`    | 1-24                  | The hour, beginning at 1                              |
| `kk`   | 01-24                 | The hour, 2-digits, beginning at 1                    |
| `X`    | 1360013296            | Unix Timestamp in second                              |
| `x`    | 1360013296123         | Unix Timestamp in millisecond                         |
| `w`    | 1 2 ... 52 53         | Week of year @>>WeekOfYear                 |
| `ww`   | 01 02 ... 52 53       | Week of year, 2-digits @>>WeekOfYear       |
| `W`    | 1 2 ... 52 53         | ISO Week of year @>>IsoWeek     |
| `WW`   | 01 02 ... 52 53       | ISO Week of year, 2-digits @>>IsoWeek |
| `wo`   | 1st 2nd ... 52nd 53rd | Week of year with ordinal @>>WeekOfYear    |
| `gggg` | 2017                  | Week Year @>>WeekYear                      |
| `GGGG` | 2017                  | ISO Week Year @>>IsoWeek   |
| `z`    | EST                   | Abbreviated named offset @>>Timezone                   |
| `zzz`  | Eastern Standard Time | Unabbreviated named offset @>>Timezone                 |

### Localized formats
Because preferred formatting differs based on locale, there are a few localized format tokens that can be used based on its locale.

#### List of localized formats
| Format | English Locale            | Sample Output                     |
| ------ | ------------------------- | --------------------------------- |
| `LT`   | h:mm A                    | 8:02 PM                           |
| `LTS`  | h:mm:ss A                 | 8:02:18 PM                        |
| `L`    | MM/DD/YYYY                | 08/16/2018                        |
| `LL`   | MMMM D, YYYY              | August 16, 2018                   |
| `LLL`  | MMMM D, YYYY h:mm A       | August 16, 2018 8:02 PM           |
| `LLLL` | dddd, MMMM D, YYYY h:mm A | Thursday, August 16, 2018 8:02 PM |
| `l`    | M/D/YYYY                  | 8/16/2018                         |
| `ll`   | MMM D, YYYY               | Aug 16, 2018                      |
| `lll`  | MMM D, YYYY h:mm A        | Aug 16, 2018 8:02 PM              |
| `llll` | ddd, MMM D, YYYY h:mm A   | Thu, Aug 16, 2018 8:02 PM         |



