import i18n from '../services/i18n';
import useAutoFocusToPopup from '../customHooks/useAutoFocusToPopup';
import { useRef } from 'react';
import useFocusTrap from '../customHooks/useFocusTrap';
import { CloseDialogButton } from './Share';

export default function PrintDialogComponent() {
  const dialogRef = useRef();

  function onHandlePrintConfirmation(isPrintShareDetails) {
    window.xprops.onConfirm(isPrintShareDetails);
    window.xprops?.close();
  }

  useAutoFocusToPopup();
  useFocusTrap(dialogRef);

  return (
    <div className="print-dialog-confirm dialog__wrapper" id="printDialogContainer" ref={dialogRef}>
    <h2 className="print-dialog-confirm__title">{i18n.translate('confirmPrintShareDetails')}</h2>
    <div className="mt-3">
      <button className="print-dialog-confirm__no-btn" onClick={() => onHandlePrintConfirmation(false)}>
        {i18n.translate('no')}
      </button>
      <button className="print-dialog-confirm__yes-btn" onClick={() => onHandlePrintConfirmation(true)}>
      {i18n.translate('yes')}
      </button>
    </div>
    <CloseDialogButton />
</div>

  );
}
