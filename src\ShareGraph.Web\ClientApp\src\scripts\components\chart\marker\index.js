//
// Sample markers file
// This file contains functions which create sample markers.  There is a stylesheet which goes along with it as well.
// Usage: new MarkersCustom(stxx);
//
import { CIQ } from 'chartiq/js/standard';
import { chartLoading } from '../ChartHelper';
import { EVENT_TYPES } from '../../../common';
import { debounce } from '../../../utils';
import { createVideoMarkers } from './createVideo';
import {EventHandler, PressReleaseEventHandler, PressReleaseFactor} from './press-release-marker';
import {setVideoData} from '../../../services/commonService';
import {EarningFactor} from './earning-marker';
import {DividendFactor} from './dividend-marker';



export const MarkersCustom = function (stx) {
  this.stx = stx;
  this.activeLabels = [];
  this.initialized = false;
};
/**
 * The specialTypes property provides a lookup of type -> method dictionary
 * that allows to extend MarketSample#showMarkers functionality with
 * additional event types not available in this file.
 * Use #registerType to populate it as it will provide additional extensibility in future
 */
MarkersCustom.specialTypes = {
  dividend: EVENT_TYPES.DIVIDEND,
  earning: EVENT_TYPES.EARNING,
  pressRelease: EVENT_TYPES.PRESSRELEASES,
  video: EVENT_TYPES.VIDEO
};
MarkersCustom.chartEventCancellableFunc = Object.keys(MarkersCustom.specialTypes).reduce((acc, key) => {
  acc[key] = { cancel: () => { } };
  return acc;
}, /** @type {Record<keyof typeof MarkersCustom.specialTypes, { cancel: () => void }>} */({}));
/**
 * Registers new prototype method to as type handler be available in #showMarkers
 * When invoked the registered method will receive type and renderType parameters
 *
 * Example: register video event handler
 *
 *		MarkersCustom.registerType('video', 'showVideoMarkers');
 *		MarkersCustom.prototype.showVideoMarkers = function (type) {
 *		}
 *
 */
MarkersCustom.registerType = function (type, methodName, overwrite) {
  if (this.specialTypes[type] && !overwrite) {
    console.error(
      'ERROR: failed to register event type ' +
        type +
        '. Event already registered'
    );
    return;
  }
  this.specialTypes[type] = methodName;
};
MarkersCustom.prototype.processLabelsAndDraw = function (labels) {
  if(!this.activeLabels.includes(labels)) {
    this.activeLabels = this.activeLabels.concat(labels);
  }
  this.stx.draw();
  return labels;
};

const createPressRelease = (selectedInstrumentId, stx, startDate, endDate)=>{
  const isRT = stx.chart.query.isRTSelectedInstrumentId;
  const instance = PressReleaseFactor.get(stx, {selectedInstrumentId, isRT});

  instance.load(startDate, endDate);

  if(instance.isPending) {
    chartLoading.show('press-release');
    instance.addEventListenerOnce('loaded', () => {
      chartLoading.hide('press-release');
    });
  }

  MarkersCustom.chartEventCancellableFunc['pressRelease'] = { cancel: () => instance.sleep() };
};

const createEarning = (selectedInstrumentId, stx, startDate, endDate) => {
  const isRT = stx.chart.query.isRTSelectedInstrumentId;
  const instance = EarningFactor.get(stx, {selectedInstrumentId, isRT});
  instance.load(startDate, endDate);

  if(instance.isPending) {
    chartLoading.show('earning');
    instance.addEventListenerOnce('loaded', () => {
      chartLoading.hide('earning');
    });
  }

  MarkersCustom.chartEventCancellableFunc['earning'] = { cancel: () => instance.sleep() };
};


const debounceCreateVideo = debounce((companyCode, stx, startDate, endDate)=>{
  const isDynamicCallout = stx.layout.headsUp.dynamic;
  const isHeadsUpFloating = stx.layout.headsUp.floating;

  chartLoading.show('video');
  setVideoData(companyCode, stx, startDate, endDate).then(
    ()=>{
      const { cancel, promise } = createVideoMarkers(
        stx,
        isDynamicCallout,
        isHeadsUpFloating,
        EVENT_TYPES.VIDEO,
        null
      );
      MarkersCustom.chartEventCancellableFunc['video'] = { cancel };

      return promise;
    }
  ).catch((e) =>{
    console.error('get data video error:', e);
  }).finally(() => {
    chartLoading.hide('video');
  });
}, 200);

const createDividend = (selectedInstrumentId, stx, startDate, endDate) => {
  const isRT = stx.chart.query.isRTSelectedInstrumentId;
  const instance = DividendFactor.get(stx, { isRT, selectedInstrumentId });
  instance.load(startDate, endDate);

  if(instance.isPending) {
    chartLoading.show('dividend');
    instance.addEventListenerOnce('loaded', () => {
      chartLoading.hide('dividend');
    });
  }

  MarkersCustom.chartEventCancellableFunc['dividend'] = { cancel: () => instance.sleep() };
};
MarkersCustom.prototype.createMarkers = function (label, markerType) {
  const stx = this.stx;
  const { selectedInstrumentId, startDate, endDate} = stx.chart.query;

  switch (label) {
    case EVENT_TYPES.DIVIDEND:
      createDividend(selectedInstrumentId, stx, startDate, endDate);
      break;
    case EVENT_TYPES.EARNING:
      createEarning(null, stx, startDate, endDate);
      break;
    case EVENT_TYPES.PRESSRELEASES:
      createPressRelease(selectedInstrumentId, stx, startDate, endDate);
      break;
    case EVENT_TYPES.VIDEO:
      debounceCreateVideo(null, stx, startDate, endDate);
      break;
  }
  return label;
};
MarkersCustom.prototype.createAbstractMarker = function (abstractType) {
  var stx = this.stx;
  var abstract = document
    .querySelector('.stx-marker-templates')
    .querySelector('.stx-marker.abstract')
    .cloneNode(true);
  Object.assign(abstract.style, {
    'z-index': 30,
    left: (0.4 * stx.chart.width).toString() + 'px'
  });
  new CIQ.Marker({
    stx: stx,
    xPositioner: 'none',
    yPositioner: 'above_candle',
    label: abstractType,
    permanent: true,
    chartContainer: true,
    node: abstract
  });
  return abstractType;
};

/**
 *
 * @param {keyof typeof MarkersCustom.specialTypes} type
 */
MarkersCustom.prototype.hideMarkers = function (type) {
  MarkersCustom.chartEventCancellableFunc[type]?.cancel();
};

MarkersCustom.prototype.setActiveLabel = function (type) {
  if(typeof(type)  === 'undefined') return;
  var chartWarraperDOM = this.stx.container.closest('.chart-wrapper');

  /** @type { HTMLElement } */
  var eventsDOM = chartWarraperDOM.querySelector('cq-menu-container[cq-name="menuChartEvents"]');

  var currEventActive = eventsDOM.querySelector(`cq-item[stxtap=${type?`"Markers.showMarkers('${type}')"`: '"Markers.showMarkers()"'}]`);
  currEventActive?.querySelector('.ciq-checkbox')?.classList.add('ciq-active');
  currEventActive?.classList.add('ciq-active');
};

MarkersCustom.prototype.reloadMarkers = function () {
  const activeLabels = [ ...this.activeLabels ];
  activeLabels.forEach(type => {
    this.showMarkers(type);
  });
};

/**
 *
 * @param {string | string[]} types
 */
MarkersCustom.prototype.initialize = function (types) {
  if(this.initialized) return;
  this.initialized = true;
  if(!types) return;

  if(!Array.isArray(types)) {
    types = [types];
  }

  this.activeLabels = types.filter(Boolean);
};

MarkersCustom.prototype.showMarkers = function (type, renderType) {
  this.initialized = true;
  
  var specialType = MarkersCustom.specialTypes[type];
  this.stx.chart.CurrentEvent = this.activeLabels;
  if (specialType) {
    return this.processLabelsAndDraw(
      this.createMarkers(specialType, renderType)
    );
  }
};

CIQ.UI.Markers.prototype.getMarkerType = function (node, type) {
  const {activeClassName, implementation } = this;
  if(!implementation) return;
  CIQ.UI.observeProperty('activeLabels', implementation,({ value }) => {
    const isActive = node.classList.contains(activeClassName);
    if(value.includes(type)) {
      if(!isActive) {
        implementation.showMarkers(type);
        node.classList.add(activeClassName);
      }
    } else if(isActive) {
      implementation.hideMarkers(type);
      node.classList.remove(activeClassName);
    }
  });
};

CIQ.UI.Markers.prototype.setMarkerType = function (node, type) {
  const { implementation } = this;
  if(!implementation) return;
  const activeLabels = implementation.activeLabels;
  if(activeLabels.includes(type)) {
    implementation.activeLabels = activeLabels.filter(item => item !== type);
  } else {
    implementation.activeLabels = activeLabels.concat(type);
  }
};

