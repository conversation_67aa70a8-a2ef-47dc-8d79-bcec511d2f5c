import { useDispatch } from 'react-redux';

import { fetchHistorical } from '../actions/accessibles/historicalActions';
import { MOVING_AVERAGES } from '../common';
import i18n from '../services/i18n';
import useQuery from './useQuery';
import useAppSelector from './useAppSelector';
import {useMemo} from 'react';
import TickerName from '../components/TickerName';

const useHistoricalDatas = () => {
  const { instruments, loading, fetchError: error } = useAppSelector(state => state.historicalDatas);
  const dispatch = useDispatch();
  const search = useQuery({ arrays: ['mas', 'i', 'p'] });
  const { mas, i: indicesIds, p: peerIds } = search;

  const peers = useAppSelector(state => state.peers.instruments);
  const indices = useAppSelector(state => state.indices.instruments);

  const instrumentIndexed = useMemo(() => peers.concat(indices).reduce((acc, item) => {
    acc[item.instrumentId] = item;
    return acc;
  }, {}), [peers, indices]);

  const compareColumns = useMemo(() => (instruments[0]?.compares ?? []).map(item => {
    const instrument = instrumentIndexed[item.id];
    const columnDisplay = instrument ? <TickerName instrumentId={item.id} marketAbbreviation={instrument.marketAbbreviation} shareName={instrument.shareName} /> : item.columnDisplay;
    
    return ({...item, columnDisplay});
  }), [instruments[0], instrumentIndexed]);

  const movingAverageColumns = [
    {
      columnDisplay: i18n.translate(MOVING_AVERAGES.MA_10.key),
      fieldName: MOVING_AVERAGES.MA_10.key,
      visible: mas && mas.includes(MOVING_AVERAGES.MA_10.value)
    },
    {
      columnDisplay: i18n.translate(MOVING_AVERAGES.MA_20.key),
      fieldName: MOVING_AVERAGES.MA_20.key,
      visible: mas && mas.includes(MOVING_AVERAGES.MA_20.value)
    },
    {
      columnDisplay: i18n.translate(MOVING_AVERAGES.MA_50.key),
      fieldName: MOVING_AVERAGES.MA_50.key,
      visible: mas && mas.includes(MOVING_AVERAGES.MA_50.value)
    }
  ].filter(m => m.visible);
  const additionalColumns =
    (indicesIds && indicesIds.length) || (peerIds && peerIds.length) || movingAverageColumns.length
      ? [
          { columnDisplay: i18n.translate('lastPriceLabel'), fieldName: 'lastPrice' },
          { columnDisplay: i18n.translate('changePercentLabel'), fieldName: 'changePercentage' }
        ]
      : [
          {
            columnDisplay: i18n.translate('firstPriceLabel'),
            fieldName: 'firstPrice'
          },
          { columnDisplay: i18n.translate('lastPriceLabel'), fieldName: 'lastPrice' },
          { columnDisplay: i18n.translate('changePercentLabel'), fieldName: 'changePercentage' },
          {
            columnDisplay: i18n.translate('highestPriceLabel'),
            fieldName: 'highestPrice'
          },
          {
            columnDisplay: i18n.translate('lowestPriceLabel'),
            fieldName: 'lowestPrice'
          },
          {
            columnDisplay: i18n.translate('sharesTradedLabel'),
            fieldName: 'totalVolume'
          },
          {
            columnDisplay: i18n.translate('totalReturnLabel'),
            fieldName: 'totalReturn'
          }
        ];
  const getHistoricalDatas = ({ instrumentId, ...params }) => {
    dispatch(fetchHistorical(instrumentId, params));
  };

  return [
    {
      instruments,
      compareColumns,
      additionalColumns: [...additionalColumns, ...movingAverageColumns],
      getState: {
        loading,
        error
      }
    },
    {
      getHistoricalDatas
    }
  ];
};

export default useHistoricalDatas;
