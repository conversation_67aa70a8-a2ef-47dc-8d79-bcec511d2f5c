(function PopoutComponentFactory(euroland) {
  const props = {
    data: {
      type: 'object',
      required: true
    }
  };

  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;

/**
 * Creates the PressReleaseComponent.
 * @returns {euroland.components.PopoutComponent}
 */
  euroland.createComponent('PopoutComponent', {
    tag: 'popout-component',
    url: baseUrl + 'popout' + location.search,
    dimensions: {
      width: '100%',
      height: '100%'
    },
    template: {
      name: 'popup',
      backdrop: false
    },
    props: props
  });
})(window.euroland);
