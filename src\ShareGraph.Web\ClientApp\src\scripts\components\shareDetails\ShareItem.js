import { useSelector } from 'react-redux';
import { AppContext } from '../../AppContext';
import useAppSelector from '../../customHooks/useAppSelector';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import {
  convertNumber,
  formatDateTime,
  formatNumberDisplay,
  translateStringFormat,
  formatShortDateAsUTC
} from '../../helper';
import i18n from '../../services/i18n';
import { useContext, useMemo } from 'react';

function ShareItem({ item }) {
  const settings = useContext(AppContext);
  const selectedCurrency = useAppSelector((state) => state.currency.currency);
  const selectedInstrumentId = useSelector(
    (state) => state.tickers.selectedInstrumentId
  );
  const instruments = useSelector((state) => state.tickers.instruments);
  const { formatNumberByInstrument } = useFormatNumberByInstrument();

  const enabledCurrencies = settings.currencies.enabled;
  const marketCapDisplay = settings?.shareDetails?.marketCapDisplay;
  const turnOverDisplay = settings?.shareDetails?.turnOverDisplay;
  const numberOfShareDisplay = settings?.shareDetails?.numberOfShareDisplay;

  const shareDataItem = useMemo(() => {
    const { value, label } = item;
    let shareDataResult = {
      label,
      value
    };

    switch (item.field) {
      case 'marketCap':
        shareDataResult.value = formatNumberByInstrument(
          formatNumberDisplay(value, marketCapDisplay),
          item.instrumentId
        );
        shareDataResult.label =
          marketCapDisplay !== 'exact'
            ? translateStringFormat('marketCapLabel', [
                i18n.translate(marketCapDisplay)
              ])
            : translateStringFormat('marketCapLabel', ['']);
        break;
      case 'noShares':
        shareDataResult.value = convertNumber(
          formatNumberDisplay(value, numberOfShareDisplay),
          { decimalDigits: 0 }
        );

        shareDataResult.label =
          numberOfShareDisplay !== 'exact'
            ? translateStringFormat('numberOfSharesLabel', [
                i18n.translate(numberOfShareDisplay)
              ])
            : translateStringFormat('numberOfSharesLabel', ['']);

        break;
      case 'todayTurnover':
        shareDataResult.value = formatNumberByInstrument(
          formatNumberDisplay(value, turnOverDisplay),  item.instrumentId
        );
        shareDataResult.label =
          turnOverDisplay !== 'exact'
            ? translateStringFormat('turnoverLabel', [
                i18n.translate(turnOverDisplay)
              ])
            : translateStringFormat('turnoverLabel', ['']);

        break;
      case 'volume':
      case 'bidSize':
      case 'askSize':
      case 'totalTrades':
      case 'lotSize':
        shareDataResult.value = convertNumber(value);
        break;
      case 'lowYTD':
      case 'highest52w':
      case 'lowest52w':
      case 'allTimeHigh':
      case 'allTimeLow':
      case 'highYTD': {
        const dateLabel = formatShortDateAsUTC(value.date);
        shareDataResult.value = (
          <>
            {formatNumberByInstrument(value.value, item.instrumentId)}
            <span className={`share-detail__item-date ${item.field}`}> | {dateLabel}</span>
          </>
        );
        break;
      }
      case 'lastUpdatedDate':
        shareDataResult.value = formatDateTime(value);
        break;
      case 'currencyCode':
        if (enabledCurrencies) {
          const selectedInstrument = instruments.find(
            (item) => item.instrumentId === selectedInstrumentId
          );
          const instrumentSetting = settings.instruments.find(
            (item) => item.id === selectedInstrumentId
          );

          const customCurrency = instrumentSetting?.currencyCode;
          const defaultCurrency =
            customCurrency || selectedInstrument?.originalCurrency || value;
          const convertedCurrency = selectedCurrency?.code || value;

          if (
            defaultCurrency.toLowerCase() === convertedCurrency.toLowerCase()
          ) {
            shareDataResult.label = i18n.translate('currencyLabel');
            shareDataResult.value = defaultCurrency;
          } else {
            shareDataResult.label = (
              <div className='currencies-wrapper'>
                <span>{i18n.translate('local')}</span>/
                <span>{i18n.translate('convertedCurrency')}</span>
              </div>
            );
            shareDataResult.value = (
              <div className='currencies-wrapper'>
                <span>{defaultCurrency}</span>/<span>{convertedCurrency}</span>
              </div>
            );
          }
        }
        break;
      case 'pE':
        shareDataResult.value = formatNumberByInstrument(value, item.instrumentId);
        break;
      default:
        if (typeof value === 'number') {
          shareDataResult.value = formatNumberByInstrument(
            value,
            item.instrumentId
          );
        }
        break;
    }
    return shareDataResult;
  }, [
    item.value,
    item.instrumentId,
    formatNumberByInstrument,
    instruments,
    selectedInstrumentId,
    selectedCurrency
  ]);

  return (
    <div
      className={`share-detail__item share-detail__item--${item.icon.toLowerCase()}`}
    >
      <div
        style={{
          position: 'absolute',
          width: 0,
          height: 0,
          overflow: 'hidden'
        }}
      >
        {`${shareDataItem.label} : ${
          shareDataItem.value || i18n.translate('notAvailableValue')
        }`}
      </div>
      <span className='share-detail__item-icon'>
        <i className={`fs fs-${item.icon.toLowerCase()}`}></i>
      </span>
      <span className='share-detail__item-label' aria-hidden>
        {shareDataItem.label}
      </span>
      <span className='share-detail__item-value' aria-hidden>
        {shareDataItem.value || i18n.translate('notAvailableValue')}
      </span>
    </div>
  );
}

export default ShareItem;
