import 'bootstrap/js/dist/dropdown';
import { useEffect, useRef } from 'react';
import { translateStringFormat } from '../../helper';
import i18n from '../../services/i18n';

function DropdownList({ className, onSelect, type, options, langchange }) {
  const dropdownRef = useRef();

  const handleClickItem = (item) => {
    onSelect?.(item);
    dropdownRef.current?.focus();
  };

  useEffect(() => {
    const buttonElement = dropdownRef.current;

    const removeAttributes = () => {
      if (buttonElement) {
        buttonElement.removeAttribute('aria-expanded');
      }
    };

    if (buttonElement ) {
      buttonElement.addEventListener('shown.bs.dropdown', removeAttributes);
      buttonElement.addEventListener('hidden.bs.dropdown', removeAttributes);
    }
    return () => {
      if (buttonElement ) {
        buttonElement.removeEventListener('shown.bs.dropdown', removeAttributes);
        buttonElement.removeEventListener('hidden.bs.dropdown', removeAttributes);
      }
    };
  }, []);

  return (
    <div
      className={`dropdown-list ${className ? className + '--dropdown' : ''}`}
    >
      <button
        ref={dropdownRef}
        className="dropdown-toggle"
        data-bs-toggle="dropdown"
        aria-roledescription={i18n.translate('buttonSelected')}
        aria-label={translateStringFormat('sharePriceType', [langchange(type)])}
      >
        {langchange(type)}
        <i className="fs fs-dropdown-arrow"></i>
      </button>

      <div
        className={`dropdown-menu ${
          className ? className + '__dropdown-menu' : ''
        }`}
      >
        {options.map((item) => {
          return (
            <button
              onClick={() => handleClickItem(item)}
              key={item}
              className={`dropdown-item ${
                className ? className + '__dropdown-item' : ''
              }`}
              value={item}
            >
              {langchange(item)}
            </button>
          );
        })}
      </div>
    </div>
  );
}

export default DropdownList;
