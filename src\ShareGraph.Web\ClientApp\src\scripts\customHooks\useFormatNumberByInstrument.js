import { useCallback, useMemo } from 'react';
import { useSelectedCurrency } from './useSelectedCurrency';
import {
  formatNumberByInstrument as formatNumberByInstrumentFN,
  formatNumberChangeByInstrument as formatNumberChangeByInstrumentFN
} from '../utils/format-number';

export const useFormatNumberByInstrument = () => {
  const selectedCurrency = useSelectedCurrency();

  const optionFormat = useMemo(() => {
    const option = {};
    if (typeof selectedCurrency?.decimalDigits === 'number' || selectedCurrency?.decimalDigits) {
      option.decimalDigits = selectedCurrency.decimalDigits;
    }
    return option;
  }, [selectedCurrency?.decimalDigits]);

  const formatNumberByInstrument = useCallback(
    (value, instrumentId, option = {}) =>
      formatNumberByInstrumentFN(value, instrumentId, {
        ...optionFormat,
        ...option
      }),
    [selectedCurrency?.decimalDigits, optionFormat.decimalDigits]
  );

  const formatNumberChangeByInstrument = useCallback(
    (value, instrumentId, option = {}) =>
      formatNumberChangeByInstrumentFN(value, instrumentId, {
        ...optionFormat,
        ...option
      }),
    [selectedCurrency?.decimalDigits, optionFormat.decimalDigits]
  );

  return {
    formatNumberByInstrument,
    formatNumberChangeByInstrument
  };
};
