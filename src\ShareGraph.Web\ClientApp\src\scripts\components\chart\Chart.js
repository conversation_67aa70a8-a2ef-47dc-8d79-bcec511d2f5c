﻿import React, { useEffect, useRef, useCallback, useContext, useLayoutEffect } from 'react';
import { useSelector, connect, useStore, useDispatch } from 'react-redux';
import { AppContext } from '../../AppContext';
import { deSelectPeer } from '../../actions/peerActions';
import { deSelectIndice } from '../../actions/indicesActions';
import ChartObject, {CIQ, getCustomConfig} from './ChartObject';
import { CHART } from '../../common';
import { convertNumberDecimal, getCurrencyText, getInstrumentNameFromInstrumentIdHelper } from './../../helper';
import ChartContext from '../../context/ChartContext';
import { chartLoading } from './ChartHelper';
import { getLayout, getStudies, handleHighLightSeries, handleShowMoreShowLessStudyLegend, hideEvents, setAtLeastMaxTicks, showEvents, windowTimeZoneToIANA } from './utils';
import { peerSelector } from '../../reducers/peerReducers';
import { indicesSelector } from '../../reducers/indicesReducers';
import useDataRealTime from './useDataRealTime';
import attachQuoteFeed from './attachQuoteFeed';
import {darker, isValidColorHexCode, lighter} from '../../utils/color';
import mountainChart from './mountainChart';
import {MarketTime} from '../../services/market';
import { useSelectedCurrency } from '../../customHooks/useSelectedCurrency';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import { toggleEnabledCurrencyOption } from '../../actions/currencyAction';
import { appSettings } from '../../../appSettings';
import { useSelectedTicker } from '../../customHooks/useTickers';
import { generateChartPatternFromNumber } from '../../utils';

export { CIQ };

function arePropsEquals(prevProps, nextProps) {
  return (
    prevProps.mainTickerId === nextProps.mainTickerId && prevProps.colorBlindMode === nextProps.colorBlindMode
  );
}

function getDefaultPeriod(settings, marketStatus = '') {
  let defaultPeriod = settings?.chart?.defaultPeriod || '1Y';

  if (marketStatus && marketStatus.toLowerCase() === 'open') {
    if (settings.chart.enabledPeriods.includes('1D')) {
      defaultPeriod = '1D';
    } else if (settings.chart.enabledPeriods.includes('5D')) {
      defaultPeriod = '5D';
    }
  }
  return defaultPeriod;
}

const Chart = React.memo(
  ({
    config = getCustomConfig(),
    mainTickerId,
    settings,
    isPopout = false,
    isSwitchTab = false,
    selectedRange = '',
    currentEvent = '',
    layout = '',
    drawings = '',
    preferences = ''
  }) => {
    const { chartDataService } = useContext(AppContext);
    const {
      chart: [chartEngine, setChartEngine],
      chartContext: [,setUIChartContext],
      firstRenderChart
    } = useContext(ChartContext);
    const isBlindMode = useSelector(state => state.blindMode.isBlindMode);

    const selectedTicker = useSelectedTicker();
    const dispatch = useDispatch();
    const store = useStore();
    config.selectedRange = selectedRange;
    config.currentEvent = currentEvent;
    const { localStorageChartKey, customStorageKey, tempStorageKey } = config;
    if(isPopout) {
      const layoutObj = JSON.parse(layout);
      config.chartEngineParams.layout.headsUp = layoutObj.headsUp;
    }
    const selectedPeerIds = useSelector(state => state.peers.selectedPeerIds);
    const selectedIndiciesIds = useSelector(state => state.indices.selectedIndicesIds);
    const allPeers = useSelector(peerSelector);
    const allIndices = useSelector(indicesSelector);

    const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
    const mainTickerData = useSelector(state => state.tickers.instruments.find(ins => ins.instrumentId === mainTickerId));
    const selectedCurrency = useSelectedCurrency();
    const { formatNumberByInstrument } = useFormatNumberByInstrument();

    const ChartObjectContainer = useRef(null);
    const cqComparisonKeyWrapperRef = useRef();
    const skipFirstMountTogglePopOutRef = useRef(true);

    const getInstrumentNameFromInstrumentId = (instrumentId, instrumentType) => {
      const state = store.getState();

      const instrumentName = getInstrumentNameFromInstrumentIdHelper({instrumentId, instrumentType, state, settings});
      return instrumentName;
    };


  const getPattern = (serieId) => {
    if(isBlindMode) {
      return generateChartPatternFromNumber(serieId);
    }
    return null;
  };

    const addSerieChart = (series, type) => {
      if (series && series.length > 0) {
        series.forEach(serieId => {
          const serieSymbol = getInstrumentNameFromInstrumentId(serieId, type);
          // if (chartEngine.chart.series[serieSymbol]) {
          //   ChartObjectContainer.current.querySelector('cq-loader').hide();
          //   var series = chartEngine.chart.series[serieSymbol];
          //   series.parameters.symbolObject.isFirstLoad = true;
          //   chartEngine.removeSeries(series);
          // }
          let currentIns = {};
          let color = '#E3E3E3';
          let currencyCode = '';
          let currencySymbol = '';
          let serieData;
          let decimalDigits = selectedCurrency?.decimalDigits;

          switch (type) {
            case 'P':
              currentIns = settings.peers.peers.find(x => x.id === serieId);
              currencyCode = currentIns.currencyCode || allPeers[serieId]?.currencyCode;
              currencySymbol = currentIns.symbol || allPeers[serieId]?.symbol || '';
              color = currentIns.color || allPeers[serieId]?.color;
              serieData = allPeers[serieId];
              break;
            case 'I':
              currentIns = settings.indices.indices.find(x => x.id === serieId);
              currencyCode = currentIns.currencyCode || allIndices[serieId]?.currencyCode;
              currencySymbol = currentIns.symbol || allIndices[serieId]?.symbol || '';
              color = currentIns.color|| allIndices[serieId]?.color;
              serieData = allIndices[serieId];
              break;
            default:
              break;
          }

          if (chartEngine.chart.series[serieSymbol]) {
            return;
          }
          const currency = store.getState().currency.currency;
          const abortController = new AbortController();
          chartEngine.addSeries(
            serieSymbol,
            {
              isComparison: true,
              pattern: getPattern(serieId),
              color: color,
              symbolObject: {
                symbol: serieSymbol,
                instrumentId: serieId,
                type: type,
                selectedInstrumentId: mainTickerId,
                currencyCode: currencyCode,
                currencySymbol: currency?.code ? '' : currencySymbol,
                toCurrency: currency?.code,
                normalDailyOpen: serieData.normalDailyOpen,
                normalDailyClose: serieData.normalDailyClose,
                marketTimeZone: windowTimeZoneToIANA(serieData.marketTimeZone),
                lastUpdatedDate: serieData.lastUpdatedDate,
                last: serieData.last,
                decimalDigits,
                getAbortController: () => abortController,
                getCurrency: () => {
                  return store.getState().currency.currency;
                }
              },
              gapDisplayStyle: true //,
              //pattern:[16,2]
            },
            function () {
              chartLoading.hide('fetchData-' + serieSymbol);
              const chart = chartEngine.chart;
              const renderer = chart.seriesRenderers[serieSymbol];

              if(!chart.series[serieSymbol] && renderer) {
                chartEngine.removeSeriesRenderer(renderer);
              } else if(Object.keys(chart.series).length > 0) {
                chartEngine.setChartScale('percent');
              } else {
                chartEngine.setChartScale('linear');
              }
            }
          );
        });
        //
      }
    };

    const removeSericesChart = () => {
      for (var serie in chartEngine.chart.series) {
        // if serie is not exist in list peers or indices selected then remove this serie.
        if (
          !selectedPeerIds.find(peerId => getInstrumentNameFromInstrumentId(peerId, 'P') === serie) &&
          !selectedIndiciesIds.find(indiceId => getInstrumentNameFromInstrumentId(indiceId, 'I') === serie)
        ) {
          var series = chartEngine.chart.series[serie];
          if (series && series.parameters) {
            series.parameters.symbolObject.isFirstLoad = true;
            series.parameters.symbolObject?.getAbortController().abort();
            chartEngine.removeSeries(series);
          }
        }
      }
    };

    const handleMouseOverInner = () => {
      setCurrentPriceForSeriesLabel();
      // handleHighLightSeries(chartEngine);
    };

    const setCurrentPriceForSeriesLabel = () => {
      var chart = chartEngine.chart;
      var tick = chartEngine.tickFromPixel(chartEngine.cx);
      var quote = chart.dataSet[tick];
      if (chartEngine.overYAxis) return;
      if (!quote) return;
      const totalReturnDom = ChartObjectContainer.current.querySelector('cq-study-legend[chart-legend] [cq-translate-original="‌Total Return‌"]');
      const totalReturn = quote['TTRT ‌Total Return‌'];
      if(totalReturnDom && totalReturn) {
        totalReturnDom.setAttribute('cq-total-return', convertNumberDecimal(totalReturn));
      }

      var seriesName = Object.keys(chartEngine.chart.series);
      if (seriesName.length) {
        seriesName.forEach(serieName => {
          let serieInfo = quote[serieName];
          if (serieInfo) {
            // set current price for peers
            let currentPrice = serieInfo.Close;
            const seriePriceDOM = ChartObjectContainer.current.querySelector(
              `cq-study-legend[chart-legend] cq-comparison .item[cq-symbol="${serieName}"] div.price[current-price]`
            );
            // seriePriceDOM && (seriePriceDOM.innerHTML = convertNumberDecimal(currentPrice));
            seriePriceDOM && (seriePriceDOM.innerHTML = formatNumberByInstrument(currentPrice, serieInfo.instrumentId));
          }
        });
      }
    };

    function snapCrosshairLineToClosePrice() {
      var stx = this;
      if (!stx || !stx.chart.dataSegment) return;
      var chart = stx.chart;
      var panel = chart.panel;
      var yAxis = panel.yAxis;
      var bar = stx.barFromPixel(stx.cx);

      if (stx.chart.dataSegment[bar]) {
        stx.crossYActualPos = stx.pixelFromPrice(stx.chart.dataSegment[bar].Close, panel);
        if (yAxis.bottom < stx.crossYActualPos) stx.crossYActualPos = yAxis.bottom;
        stx.controls.crossY.style.top = stx.crossYActualPos + 'px';
      }
    }

    useLayoutEffect(() => {
      if (isPopout || isSwitchTab) return;
      // CustomLocalStorage.removeItem(localStorageChartKey);
      // CustomLocalStorage.removeItem(customStorageKey);
    }, []);

    useEffect(() => {
      if (!chartEngine) return;
      //Add a dot to highlight the mouse-over tick
      chartEngine.append('mousemoveinner', handleMouseOverInner);
      chartEngine.prepend('doDisplayCrosshairs', function () {
        if(!CIQ.touchDevice) return;
        snapCrosshairLineToClosePrice.call(this);
      });
      chartEngine.prepend('positionCrosshairsAtPointer', snapCrosshairLineToClosePrice);
    }, [chartEngine]);

    useEffect(() => {
      if (!chartEngine) return;

      chartEngine.selectedInstrumentId = selectedInstrumentId;
      //call api to get events data
      let query = chartEngine.chart.query;
      if (query) {
        chartEngine.chart.query.selectedInstrumentId = selectedInstrumentId;
      }

      // }
    }, [selectedInstrumentId, chartEngine]);

    

    useEffect(() => {
      if (!chartEngine) return;
      // show more comparison
      // const unsubscribe = handleShowMoreShowLessStudyLegend(chartEngine, cqComparisonKeyWrapperRef);
      // return () => {
      //  typeof unsubscribe === 'function' && unsubscribe();
      // };
    }, [selectedPeerIds, selectedIndiciesIds, chartEngine]);

    useEffect(() => {
      return () => {
        if(!cqComparisonKeyWrapperRef.current) return;
        cqComparisonKeyWrapperRef.current.disconnect();
      };
    }, []);

    useEffect(() => {
      if(!chartEngine) return;
      const marketStatus = mainTickerData?.marketStatus;
      if(!marketStatus) return;
      if(marketStatus === 'Open') {
        chartEngine.layout.marketOpen = true;
      } else {
        chartEngine.layout.marketOpen = false;
      }
    }, [mainTickerData?.marketStatus, chartEngine]);

    /**
     * Catches up ChartEngine object and save it to component state.
     */
    const onChartInitialized = useCallback(
      ({ chartEngine, uiContext }) => {
        attachQuoteFeed({chartDataService, chartEngine, chartElementRef: ChartObjectContainer, store});

        chartEngine.localStorageChartKey = localStorageChartKey;
        chartEngine.customStorageKey = customStorageKey;
        chartEngine.tempStorageKey = tempStorageKey;
        setChartEngine(chartEngine);
        setUIChartContext(uiContext);
        chartEngine.reduxStore = {
          getState: store.getState
        };
      },
      [mainTickerId, chartEngine, store]
    );

    useEffect(() => {
      if (mainTickerId === null || chartEngine === null) {
        return;
      }
      const seriesSymbol = getInstrumentNameFromInstrumentId(mainTickerId, 'T');
      const defaultPeriod = getDefaultPeriod(settings, mainTickerData.marketStatus);
      if(!chartEngine.range?.selectedRange) {
        chartEngine.range = {
          selectedRange: defaultPeriod
        };
      }

      const spanSetting =
        CHART.RANGE.find(range => range.value.toUpperCase() === selectedRange.toUpperCase()) ||
        CHART.RANGE.find(range => range.value.toUpperCase() === (defaultPeriod));
      const selectedInstrumentSetting = settings.instruments.find(x => x.id === mainTickerId);
      const customTimeZone = selectedInstrumentSetting?.customTimeZone || selectedTicker?.timezoneIANA || settings.timeZone;
      chartEngine.setTimeZone(null, customTimeZone);
      const { periodicity,  multiplier, base } = chartEngine.ranges || spanSetting;

      const {interval, period, timeUnit} = chartEngine.range?.selectedRange === 'CUSTOM_RANGE' ? chartEngine.getPeriodicity() : periodicity;
      if(!isPopout) {
        const params = {
          span: {
            multiplier: multiplier,
            base: base
          },
          periodicity: {
            period: period,
            interval: interval,
            timeUnit: timeUnit
          },
          range: chartEngine.range?.selectedRange === 'CUSTOM_RANGE' ? chartEngine.range : null
        };

        const marketTimeZone = windowTimeZoneToIANA(mainTickerData.marketTimeZone);
        const {
          normalDailyClose,
          normalDailyOpen,
          lastUpdatedDate,
          currencyCode
        } = mainTickerData;
        let callback;
        if(base === 'today' && chartEngine.range.selectedRange !== 'CUSTOM_RANGE') {
          const { open, close } = MarketTime.factory(
            normalDailyOpen,
            normalDailyClose,
            marketTimeZone
          ).getOpenCloseInDate(new Date(selectedTicker?.lastUpdatedDate));
          params.span.base = 'day';
          if(params.range == null) params.range = {};
          params.range.dtLeft = open;
          params.range.dtRight = close;
          callback = () => setAtLeastMaxTicks(chartEngine, 100);
        }

        const currency = store.getState().currency.currency;
        const currencyText = getCurrencyText(currency);
        chartEngine.loadChart(
          //symbol
          {
            symbol: seriesSymbol || '',
            instrumentId: mainTickerId,
            type: 'T',
            currencyCode: currencyText || currencyCode,
            toCurrency: currency?.code,
            marketTimeZone,
            normalDailyClose,
            normalDailyOpen,
            lastUpdatedDate,
            last: mainTickerData.last,
            shareName: mainTickerData.shareName,
            marketAbbreviation: mainTickerData.marketAbbreviation,
            getCurrency: () => {
              return store.getState().currency.currency;
            }
          },
          //params
          params,
          () => {
            showEvents(chartEngine);
            const studies = getStudies(chartEngine);

            if (
              !settings.chart.excludeStudies.includes('VOLUME_UNDERLAY') &&
              settings.chart.defaultVolumeChart === 'VOLUME_UNDERLAY' &&
              !studies['vol undr']
            ) {
              CIQ.Studies.addStudy(/* stx */ chartEngine, /* type */ 'vol undr', /* inputs */ {}, /* outputs */ {});
            }

            if (
              !settings.chart.excludeStudies.includes('VOLUME_CHART') &&
              settings.chart.defaultVolumeChart === 'VOLUME_CHART' &&
              !studies['volume']
            ) {
              CIQ.Studies.addStudy(/* stx */ chartEngine, /* type */ 'volume', /* inputs */ {}, /* outputs */ {});
            }
            dispatch(toggleEnabledCurrencyOption(true));

            /* Show y-axis menu.
            To move left, move right, un-merge, merge y-axis */
            chartEngine.chart.yAxis.onAttach(chartEngine);
            typeof callback === 'function' && callback();
          }
          );
      }


      chartEngine.chart.calculateTradingDecimalPlaces = function () {
        return settings.format.decimalDigits;
      };

      const rootStyles = getComputedStyle(document.documentElement);
      const primaryColor = rootStyles.getPropertyValue('--color-primary');
      let colorMainChart = primaryColor;

      const instrumentColor = settings.instruments.find(x => x.id === mainTickerId).color;
      if(isValidColorHexCode(instrumentColor)){
        colorMainChart = instrumentColor;
      }

      mountainChart(chartEngine, colorMainChart);

      const rootEle = document.querySelector(':root');
      const colorBgTooltip = darker(colorMainChart);
      if(colorBgTooltip) {
        rootEle.style.setProperty('--colorTooltipChar', colorBgTooltip);
      }
      if(colorBgTooltip){
        rootEle.style.setProperty('--colorTooltipBorderChar', lighter(colorBgTooltip, 91));
      }
      const logoCompanyUrl = settings.companyLogo ?
        `url(${new URL(appSettings.customStylesheetUrlBase + '/' + settings.companyLogo.path, window.location.origin + '/' + appSettings.toolUrlBase).pathname})` : '';
      if(logoCompanyUrl) {
        rootEle.style.setProperty('--logoUrl', logoCompanyUrl);
      }

      if (isPopout) {
        var savedLayout = layout;
        var importOptions = {
          managePeriodicity: true,
          preserveTicksAndCandleWidth: false
        };

        if (savedLayout && chartEngine) {
          const layoutObj = JSON.parse(savedLayout);
          // use displayInitialized prevent chartiq draw during importLayout
          // error occur on pop out mobile.
          chartEngine.displayInitialized = false;
          const range = layoutObj.range ?? {};
          if(range.dtLeft && range.dtRight) {
            chartEngine.range.dtLeft = range.dtLeft;
            chartEngine.range.dtRight = range.dtRight;
          }
          chartEngine.importLayout(layoutObj, {...importOptions, cb: () => {
            chartEngine.displayInitialized = true;
            if (drawings && chartEngine) chartEngine.importDrawings(JSON.parse(drawings));
            if (preferences && chartEngine) chartEngine.importPreferences(JSON.parse(preferences));
          }});
          chartEngine.uiContext.advertised.Markers.implementation.initialize(layoutObj.CurrentEvent);
        } else {
          if (drawings && chartEngine) chartEngine.importDrawings(JSON.parse(drawings));
          if (preferences && chartEngine) chartEngine.importPreferences(JSON.parse(preferences));
        }
      }

      hideEvents(chartEngine);
    }, [selectedCurrency?.code, mainTickerId, chartEngine, mainTickerData.marketStatus]);

    useEffect(() => {
      if (!chartEngine) return;
      removeSericesChart();
      addSerieChart(selectedPeerIds, 'P');
    }, [selectedPeerIds, chartEngine, chartEngine?.chart?.period, chartEngine?.chart?.interval]);

    useEffect(() => {
      if (!chartEngine) return;
      removeSericesChart();
      addSerieChart(selectedIndiciesIds, 'I');
    }, [selectedIndiciesIds, chartEngine]);

    useDataRealTime({ mainTickerId, chartEngine });

    const onSeriesRemoved = useCallback(({ symbol, symbolObject }) => {
      if (symbolObject.isFirstLoad) return;
      if (symbolObject && symbolObject.instrumentId > 0 && symbolObject.type === 'P') {
        dispatch(deSelectPeer(symbolObject.instrumentId));
        // onRemoveSeriesPopout(symbolObject.instrumentId, symbolObject.type);
      }

      if (symbolObject && symbolObject.instrumentId > 0 && symbolObject.type === 'I') {
        dispatch(deSelectIndice(symbolObject.instrumentId));
        // onRemoveSeriesPopout(symbolObject.instrumentId, symbolObject.type);
      }
    }, []);

    function getChartState(chartEngine) {
      const layout = getLayout(chartEngine);
      var preferences = JSON.stringify(chartEngine.exportPreferences());
      var drawings = JSON.stringify(chartEngine.exportDrawings());
      return { layout, preferences, drawings };
    }

    function popoutFunc() {
      const isIFrame = window.top !== window.self;
      const peers = store.getState().peers.selectedPeerIds;
      const indices = store.getState().indices.selectedIndicesIds;
      const currentEvent = chartEngine.chart.CurrentEvent;
      const selectedRange = chartEngine.range.selectedRange;
      const { layout, preferences, drawings } = getChartState(chartEngine);

      const component = window.euroland.components.PopoutComponent({
        data: { peers, indices, mainTickerId, currentEvent, selectedRange, layout, preferences, drawings, selectedCurrency },
        dataCallBack: ({ type, payload }) => {
          switch (type) {
            case 'remove-peer':
              component.updateProps({
                data: { peers: peers.filter(p => p !== payload), indices, mainTickerId }
              });
              break;

            default:
              break;
          }
        },
        onClose: () => {
          // setMainChartLayoutAgain();
          // setCustomItemLocalStorageByProperty(customStorageKey, 'peers', '');
          // setCustomItemLocalStorageByProperty(customStorageKey, 'indices', '');
          if (!isIFrame && document.getElementById('middleLayout')) {
            document.getElementById('middleLayout').remove();
          }
        },
        onRendered: () => {

        }
      });

      if (isIFrame) {
        component.renderTo(window.parent, window.xprops.layout.middle, 'popup');
      } else {
        let middle = document.getElementById('middleLayout');
        if (!middle) {
          middle = document.createElement('div');
          middle.id = 'middleLayout';
          document.body.appendChild(middle);
        }

        component.renderTo(window.parent, '#middleLayout', 'popup');
      }
    }

    useEffect(() => {
      if (!chartEngine) return;

      // update
      const studies = getStudies(chartEngine);
      const volumeStudy = studies['volume'];
      if (volumeStudy?.study?.parameters) {
        volumeStudy.study.parameters.isBlindMode = isBlindMode;
      }

      // draw series
      for (let series in chartEngine.chart.series) {
        const serieId = chartEngine.chart.series[series]?.parameters?.symbolObject?.instrumentId;
        if (!serieId || !chartEngine.getRendererFromSeries(series)) continue;
        const pattern = getPattern(serieId);
        chartEngine.modifySeries(series, {pattern: pattern});
    }
    chartEngine.draw();

    }, [chartEngine, isBlindMode]);

    // const [colorBlindMode, setColorBlindMode] = useState(false);

    return (
      <div className="chart-wrapper" ref={ChartObjectContainer}>
        <ChartObject
          isPopout={isPopout}
          chartInitialized={onChartInitialized}
          onSeriesRemoved={onSeriesRemoved}
          config={config}
          additionalOptionsTypes={settings.chart.enabledAdditionalOptions}
          onPopUp={popoutFunc}
        />
      </div>
    );
  },
  arePropsEquals
);

function mapStateToProps(state, ownProps) {
  return {
    mainTickerId: state.tickers.selectedInstrumentId,
    colorBlindMode: ownProps.colorBlindMode
  };
}

export default connect(mapStateToProps)(Chart);
