import {useEffect, useState} from 'react';

function utils() {
  const current = new Date();
  const timeToNextMinus = () => (60 - current.getSeconds()) * 1000;
  const timeToNextHour = () => ((60 - current.getMinutes()) * 60 * 1000) + timeToNextMinus(); 
  return {
    timeToNextMinus,
    timeToNextHour
  };
}

/**
 * 
 * @param {"minus" | "hour"} each 
 * @param {boolean} [stop] 
 * @param {*} [debug] 
 * @returns 
 */
export default function useIntervalForceRender(each, stop = false, debug = () => {}) {
  const [key, setKey] = useState([]);

  useEffect(() => {
    if(stop) return;

    let timer;
    const handle = (root) => {
      const utilsTime = utils();
      timer = setTimeout(handle.bind(undefined, false), each === 'minus' ? utilsTime.timeToNextMinus() : utilsTime.timeToNextHour());

      if(root) return;
      setKey([]);
      debug();
    };

    handle(true);

    return () => clearTimeout(timer);
  }, [each, stop]);

  return key;
}