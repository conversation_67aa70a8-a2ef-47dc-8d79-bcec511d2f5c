﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace WatchListAPI.Entities.SharkDb
{
    [Table("InstrumentPrice")]
    public class InstrumentPrice
    {
        [Key]
        public int InstrumentId { get; set; }

        [Column(TypeName = "money")]
        public decimal? Bid { get; set; }

        [Column(TypeName = "money")]
        public decimal? Ask { get; set; }

        [Column(TypeName = "money")]
        public decimal? Open { get; set; }

        [Column(TypeName = "money")]
        public decimal? Last { get; set; }

        [Column(TypeName = "money")]
        public decimal? High { get; set; }

        [Column(TypeName = "money")]
        public decimal? Low { get; set; }

        public long? Volume { get; set; }

        [Column(TypeName = "money")]
        public decimal? Mid { get; set; }

        public DateTime? Date { get; set; }

        [Column(TypeName = "money")]
        public decimal? PrevClose { get; set; }

        // Computed column - not mapped directly for insert/update
        [NotMapped]
        public decimal Change => PrevClose != 0 ? (Last ?? 0) - (PrevClose ?? 0) : 0;

        public DateTime? LastRowChange { get; set; }

        [NotMapped]
        public decimal ChangePercentage => PrevClose != 0
            ? (decimal)(((double)((Last ?? 0) - (PrevClose ?? 0)) / (double)(PrevClose ?? 1)) * 100)
            : 0;

        [Column(TypeName = "money")]
        public decimal? TodayTurnover { get; set; }

        [Column(TypeName = "money")]
        public decimal? VWAP { get; set; }

        public int? BidSize { get; set; }

        public int? AskSize { get; set; }

        [Column(TypeName = "money")]
        public decimal? OfficialClose { get; set; }

        public DateTime? OfficialCloseDate { get; set; }
    }
}
