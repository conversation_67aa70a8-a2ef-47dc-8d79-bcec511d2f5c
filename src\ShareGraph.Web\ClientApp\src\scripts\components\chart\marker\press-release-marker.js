import { replace<PERSON><PERSON> } from '@euroland/libs';
import i18n from '../../../services/i18n';
import { formatShortDate } from '../../../helper';
import appConfig from '../../../services/app-config';
import {EVENT_TYPES} from '../../../common';
import {EventHandler, EventLoader, EventMarker, EventNode, MARKER_CLASS} from './marker';
import {fetchPressReleaseDataPaging} from '../../../services/commonService';
import { get } from 'es-toolkit/compat';
import dayjs from 'dayjs';


const isIFrame = window.self !== window.top;

/**
 * @typedef {{id: number, dateTime: string, title: string, messageTypeId: number}} PressReleaseData
 */


class PressReleaseEventMarker extends EventMarker {
  constructor(options) {
    super({...options, label: EVENT_TYPES.PRESSRELEASES});
    const { setting } = options;

    this.openPressReleaseAsAPopup = (setting.pressReleases.openAs || '').toLowerCase() === 'popup';
    this.contentEl.innerHTML = i18n.translate('pressReleaseAbbreviate');
    this.mainNode.classList.add('press-releases', 'pressRelease');
  }

  initEvents() {
    super.initEvents();
    this.eventCollection.listen(this.mainNode, 'click', (e) => {
      const tooltipElement = e.target?.closest?.('.' + MARKER_CLASS.TOOLTIP_ITEM);
      if(tooltipElement) {
        this.openPressReleaseDetail(tooltipElement);
        this.hiddenTooltip();
      }
    });
  }

  openPressReleaseDetail(element) {
    if (!this.openPressReleaseAsAPopup) {
      this.appendHyperlinkIfNeeded(element);
    } else {
      const id = parseInt(element.getAttribute('data-press-id'), 10);
      this.openPressReleaseDetailPopup(id);
    }
  }

  openPressReleaseDetailPopup(pressReleaseId) {
    const component = window.euroland.components.PressReleaseComponent({
      pressreleaseId: pressReleaseId,
      onClose: () => {
        if (!isIFrame && document.getElementById('middleLayout')) {
          document.getElementById('middleLayout').remove();
        }
      },
      onRendered: () => {
        console.log('Share Dialog has completed its full render');
      }
    });

    if (isIFrame) {
      component.renderTo(window.parent, window.xprops.layout.middle);
    } else {
      let middle = document.getElementById('middleLayout');
      if (!middle) {
        middle = document.createElement('div');
        middle.id = 'middleLayout';
        middle.classList.add('pr-dialog');
        document.body.appendChild(middle);
      }
      component.renderTo(window.parent, '#middleLayout');
    }
  }

  appendHyperlinkIfNeeded(element) {
    if (this.openPressReleaseAsAPopup) return;
    const id = parseInt(element.getAttribute('data-press-id'), 10);
    const newPageUrlPattern = this.setting.pressReleases.newPageUrlPattern;

    if (!newPageUrlPattern) {
      const pressDetailLink = `press-release/detail?companyCode=${this.setting.companyCode}&lang=${this.setting.language}&v=${this.setting.companyCodeVersion || ''}&ID=${id}`;
      window.open(pressDetailLink, '_blank');
    } else {
      const pressReleaseDetailUrlEncode = encodeURIComponent(
        window.location.origin + `/press-release/detail?companyCode=${this.setting.companyCode}&lang=${this.setting.language}&v=${this.setting.companyCodeVersion || ''}&ID=${id}`
      );
      const newPageObject = {
        ID: id,
        lang: this.setting.language,
        companycode: this.setting.companyCode,
        v: this.setting.companyCodeVersion,
        pressReleaseUrl: pressReleaseDetailUrlEncode
      };
      const newPageUrl = replaceKey(newPageUrlPattern, newPageObject);
      window.open(decodeURIComponent(newPageUrl), '_blank');
    }
  }
}

/**
 * @typedef {{selectedInstrumentId: number, isRT:boolean}} PressReleaseEventOptions
 */

export class PressReleaseEventLoader extends EventLoader {
  async fetch(cursor) {
    const result = await fetchPressReleaseDataPaging({
      ins: this.options.selectedInstrumentId,
      cursor,
      from: this.from,
      to: this.to,
      signal: this.abortController.signal,
      isRT: this.options.isRT
    });

    const pressReleases = result.data.company.pressReleases;

    const records = pressReleases?.edges?.map(item => ({ ...item.node, Date: new Date(item.node.dateTime), date: item.node.dateTime }));

    let endCursor;

    if(records && pressReleases.pageInfo.hasNextPage) endCursor = pressReleases.pageInfo.endCursor;

    return {
      data: records,
      endCursor
    };
  }
}

export class PressReleaseEventNode extends EventNode {
  createMarker(date, records) {
    return new PressReleaseEventMarker({
      setting: this.setting,
      stx: this.stx,
      template: this.template,
      eventCollection: this.eventCollection,
      date,
      records
    });
  }
}
export class PressReleaseEventHandler extends EventHandler {
  eventLoaderCreator(from, to) {
    return PressReleaseEventLoader.create(
      from,
      to,
      this.options,
      this.eventNode.onLoad.bind(this.eventNode)
    );
  }

  eventNodeCreator() {
    return new PressReleaseEventNode(
      this.stx,
      document.querySelector('#chartEventPrototypePr'),
      appConfig.get()
    );
  }
}

export const PressReleaseFactor = {
  _cache: {},
  /**
   *
   * @param {any} stx
   * @param {PressReleaseEventOptions} options
   * @returns {PressReleaseEventHandler}
   */
  get(stx, options) {
    const { selectedInstrumentId } = options;

    Object.entries(this._cache).map(([key, item]) => {
      if(key == selectedInstrumentId) return;
      if(!item.isSleep) { item.sleep(); }
    }); // sleep all

    if(!this._cache[selectedInstrumentId]) {
      this._cache[selectedInstrumentId] = new PressReleaseEventHandler(stx, options);
    }

    return this._cache[selectedInstrumentId];
  }
};

window.PressReleaseFactor = PressReleaseFactor;
