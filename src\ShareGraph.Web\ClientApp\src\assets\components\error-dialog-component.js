(function ErrorDialogComponentFactory(euroland) {
  const props = {
    message: {
      type: 'string',
      required: false
    },
    onConfirm: {
      type: 'function',
      required: true,
      default: function () {}
    },
    onClose: {
      type: 'function',
      required: true,
      default: function () {}
    }
  };

  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;

/**
 * Creates the ErrorDialogComponent.
 * @returns {euroland.components.ErrorDialogComponent}
 */
  euroland.createComponent('ErrorDialogComponent', {
    tag: 'error-dialog-component',
    url: baseUrl + 'error-dialog' + location.search,
    dimensions: {
      width: '297px !important',
      height: '156px !important'
    },
    template: {
      name: 'modal',
      ignoreBackdropClick: true,
      hideCloseButton: true
    },
    props: props
  });
})(window.euroland);
