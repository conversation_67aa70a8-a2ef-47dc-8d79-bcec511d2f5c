import { CIQ } from 'chartiq/js/components';

CIQ.Euroland.Studies.createStudy('vol undr', {
  name: 'Volume Underlay',
  customOptions: {
    multiple: false
  },
  underlay: null,
  overlay: true,
  range: '0 to max',
  yAxis: {
    ground: true,
    initialMarginTop: 0,
    position: 'none',
    heightFactor: 0.25
  },
  seriesFN: CIQ.Studies.createVolumeChart,
  calculateFN: function (...args) {
    CIQ.Studies.calculateVolume(...args);
  },
  inputs: {},
  outputs: { 'Up Volume': '#8cc176', 'Down Volume': '#b82c0c' },
  customRemoval: true,
  attributes: {
    panelName: { hidden: true }
  }
});

export { CIQ };
