import useSharePrice52Weeks from '../../customHooks/useSharePrice52Weeks';
import { dynamicSort } from '../../helper';
import i18n from '../../services/i18n';
import Table from '../commons/accessibilities/Table';

const Weeks52HighLow = () => {
  const [{ sharePrice52Weeks, configColumns }] = useSharePrice52Weeks();

  const columnsAvailable = [
    { columnDisplay: i18n.translate('sharesLabel'), fieldName: 'shareName' },
    { columnDisplay: i18n.translate('priceLabel'), fieldName: 'last' },
    { columnDisplay: i18n.translate('weeks52LowLabel'), fieldName: 'low52W' },
    { columnDisplay: i18n.translate('weeks52HighLabel'), fieldName: 'high52W' },
    { columnDisplay: i18n.translate('percentFrom52WLowLabel'), fieldName: 'percent52WLow' },
    { columnDisplay: i18n.translate('percentFrom52WHighLabel'), fieldName: 'percent52WHigh' }
  ];
  const columns = columnsAvailable
    .filter(p => configColumns.map(s => s.toUpperCase()).includes(p.fieldName.toUpperCase()))
    .map(item => ({
      ...item,
      order: configColumns.indexOf(item.fieldName.toLocaleUpperCase())
    }))
    .sort(dynamicSort('order'));
  return (
    <Table
      className="share-performance-table__52w"
      caption={i18n.translate('sharePrice52WeeksCaption')}
      columns={columns}
      data={sharePrice52Weeks}
    />
  );
};

export default Weeks52HighLow;
