import { CIQ } from 'chartiq/js/components';
import { appSettings } from '../../../appSettings';

CIQ.Euroland.Studies.calculateTypicalPrice	 = function (stx, sd) {
	var quotes = sd.chart.scrubbed;
	var period = sd.days;
	if (quotes.length < period + 1) {
		if (!sd.overlay) sd.error = true;
		return;
	}
	var name = sd.name;
	for (var p in sd.outputs) {
		name = p + ' ' + name;
	}
	var field = 'hlc/3';
	if (sd.type == 'Med Price') field = 'hl/2';
	else if (sd.type == 'Weighted Close') field = 'hlcc/4';

	var total = 0;
	if (sd.startFrom <= period) sd.startFrom = 0;
	for (var i = sd.startFrom; i < quotes.length; i++) {
		if (i && quotes[i - 1][name]) total = quotes[i - 1][name] * period;
		total += quotes[i][field];
		if (i >= period) {
			total -= quotes[i - period][field];
			quotes[i][name] = total / period;
		}
	}
};

// const settingObject = JSON.parse(localStorage.getItem('sg-settings')) || {
//   Chart: {}
// };

const studyName = CIQ.I18N.translate('Typical Price');

CIQ.Euroland.Studies.createStudy(studyName, {
  name: studyName,
  calculateFN: CIQ.Euroland.Studies.calculateTypicalPrice,
  inputs: { Period: 14, Overlay: false }
});

export { CIQ };
