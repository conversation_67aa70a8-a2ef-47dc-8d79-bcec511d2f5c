import produce from 'immer';

import {
  FETCH_ORDER_DEPTH_BEGIN,
  FETCH_ORDER_DEPTH_SUCCESS,
  FETCH_ORDER_DEPTH_FAILURE
} from '../actions/orderDepthAction';

const initialState = {
  orderDepthData: [],
  rowUpdated: '',
  loading: true,
  fetchError: null
};

const orderDepthReducer = produce((draft, action) => {
  switch (action.type) {
    case FETCH_ORDER_DEPTH_BEGIN:
      draft.loading = true;
      break;
    case FETCH_ORDER_DEPTH_SUCCESS:
      draft.loading = false;
      draft.orderDepthData = action.payload.orderDepths.marketDepth || [];
      draft.rowUpdated = action.payload.orderDepths.rowUpdated;
      break;
    case FETCH_ORDER_DEPTH_FAILURE:
      draft.loading = false;
      draft.fetchError = action.payload.error;
      break;
    default:
      break;
  }
}, initialState);

export default orderDepthReducer;
