<?xml version="1.0" encoding="utf-8"?>
<settings>
	<Layout>FULL</Layout>
	<TimeZone>Europe/Amsterdam</TimeZone>
	<UseLatinNumber>false</UseLatinNumber>
	<StreamUpdateDelay>500</StreamUpdateDelay>
	<Instruments>
		<Instrument>
			<Id>26515</Id>
			<Color>#7DCA48</Color>
			<Order>1</Order>
			<Default>true</Default>
			<Ticker>
				<en-GB>Carl B</en-GB>
				<ar-AE>CARL B</ar-AE>
			</Ticker>

			<TickerName>
				<en-GB>Carl B (DKK)</en-GB>
				<ar-AE>CARL B</ar-AE>
			</TickerName>

			<MarketAbbreviation>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</MarketAbbreviation>
			<IsRT>TRUE</IsRT>
		</Instrument>

		<Instrument>
			<Id>107277</Id>
			<Color>#04A851</Color>
			<Order>2</Order>
			<Default>true</Default>
			<TickerName>
				<en-GB>CARL B (COP)</en-GB>
				<ar-AE>CARL B</ar-AE>
			</TickerName>
			<MarketAbbreviation>
				<en-GB>COP</en-GB>
				<ar-AE>DKK</ar-AE>
			</MarketAbbreviation>
			<IsRT>TRUE</IsRT>
		</Instrument>


		<Instrument>
			<Id>7970003</Id>
			<Color>#04A851</Color>
			<Order>5</Order>
			<Default>true</Default>
			<Name>
				<en-GB>Carlsberg</en-GB>
				<ar-AE>Share 1 in arabic</ar-AE>
			</Name>
			<!-- <Ticker>
					<en-GB>Share 5</en-GB>
					<ar-AE>CARL B</ar-AE>
				</Ticker> -->
			<IsRT>FALSE</IsRT>
		</Instrument>

		<Instrument>
			<Id>5878004</Id>
			<Color>#0071BD</Color>
			<Order>6</Order>
			<Default>true</Default>
			<Name>
				<en-GB>Share 1 in english</en-GB>
				<ar-AE>Share 1 in arabic</ar-AE>
			</Name>
			<!-- <Ticker>
				<en-GB>Share 6</en-GB>
				<ar-AE>CARL B</ar-AE>
			</Ticker> -->
		</Instrument>

		<Instrument>
			<Id>125433</Id>
			<Color>#8BC34A</Color>
			<Order>7</Order>
			<Default>true</Default>
			<!-- <Ticker>
				<en-GB>Share 7</en-GB>
				<ar-AE>Share 1 in arabic </ar-AE>
			</Ticker> -->
		</Instrument>


	</Instruments>

	<Peers>
		<Enabled>true</Enabled>
		<Peer>
			<Id>9287</Id>
			<Color>#111E6C</Color>
			<Order>1</Order>
			<TickerName>
				<en-GB>Heineken 1</en-GB>
			</TickerName>
		</Peer>
		<Peer>
			<Id>10000</Id>
			<Color>#FF0000</Color>
			<Order>2</Order>
			<TickerName>
				<en-GB>In Bev</en-GB>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>32904</Id>
			<Color>#F90703</Color>
			<Order>3</Order>
			<TickerName>
				<en-GB>Diageo</en-GB>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>3541</Id>
			<Color>#005A73</Color>
			<Order>4</Order>
			<TickerName>
				<en-GB>Efes</en-GB>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>3562</Id>
			<Color>#C21E56</Color>
			<Order>5</Order>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
	</Peers>
	<Indices>
		<Enabled>true</Enabled>
		<!-- <Index>
      <Id>9900003</Id>
      <Color>#ff0000</Color>
      <Order>1</Order>
    </Index>
    <Index>
      <Id>8920003</Id>
      <Color>#00ff00</Color>
      <Order>2</Order>
       <TickerName>
	   		<en-GB>OMXC Consumer</en-GB>
	   </TickerName>
    </Index>
	<Index>
      <Id>6306004</Id>
      <Color>#0000ff</Color>
      <Order>3</Order>

    </Index> -->
		<Index>
			<Id>6346004</Id>
			<Color>#8A0303</Color>
			<Order>4</Order>

		</Index>
		<Index>
			<Id>2040003</Id>
			<Color>#4F7942</Color>
			<Order>5</Order>

		</Index>
	</Indices>

	<Ticker>
		<!-- Posible values: GRAPH, TABLE -->
		<EnabledFormat>GRAPH, TABLE</EnabledFormat>
		<!-- Posible values: SINGLE, MULTIPLE -->
		<TickerType>MULTIPLE</TickerType>
		<!-- Posible values: SINGLE_TICKER_1, SINGLE_TICKER_2, MULTIPLE_TICKER_1, MULTIPLE_TICKER_2,
		{CustomTemplate}  -->
		<!-- <GraphTickerTemplate>SINGLE_TICKER_1_WITH_DANGEROUS_HTML_TAGS</GraphTickerTemplate> -->
		<GraphTickerTemplate>MULTIPLE_TICKER_2</GraphTickerTemplate>
		<!-- <TableColumns>TABLE_TICKER_SINGLE, TABLE_TICKER_MULTIPLE</TableColumns> -->
		<TableTickerTemplate>TABLE_TICKER_MULTIPLE</TableTickerTemplate>
		<SlidesPerView>4</SlidesPerView>
		<!-- Possible values: FADE, TRANSFORM -->
		<TableAnimation>TRANSFORM</TableAnimation>
		<!-- Possible values: FADE, BLINK_PRICE, BLINK_MARKET -->
		<GraphAnimation>BLINK_PRICE</GraphAnimation>
	</Ticker>
	<Chart>
		<!-- Posible values: DYNAMIC_CALLOUT, TOOLTIP, HEADUPS
		Default value = DYNAMIC_CALLOUT  -->
		<DefaultTooltipType>DYNAMIC_CALLOUT</DefaultTooltipType>
		<!-- Posible values: CANDLE, LINE, VERTEX_LINE, STEP, MOUNTAIN, HISTOGRAM, BAR.
		Default value = MOUNTAIN  -->
		<DefaultChartType>MOUNTAIN</DefaultChartType>
		<!-- Posible values: 1D, 5D, 1M, 3M, 6M, YTD, 1Y, 3Y, 5Y, ALL, CUSTOM_RANGE. -->
		<EnabledPeriods>1D, 5D, 1M, 3M, 6M, YTD, 1Y, 3Y, 5Y, CUSTOM_RANGE</EnabledPeriods>
		<!--Default
		value = 1Y  -->
		<DefaultPeriod>1Y</DefaultPeriod>
		<!-- Posible values: EXCEL, PRINT, EXPORT, SHARE
		Empty for disable -->
		<EnabledAdditionalOptions>EXCEL, PRINT, EXPORT, SHARE</EnabledAdditionalOptions>
	</Chart>

	<ShareDetails>
		<!-- Options: True|False -->
		<Enabled>True</Enabled>
		<!-- Fields available: TIME,CURRENCY,MARKET,MARKET_STATUS ...-->
		<ShareDataItems>TIME,CURRENCY,MARKET,MARKET_STATUS,ISIN,SYMBOL,BID,BID_SIZE,ASK,ASK_SIZE</ShareDataItems>
		<!-- Options: True|False -->
		<DisplayOnSelectedTicker>True</DisplayOnSelectedTicker>
		<!-- Options: Grid|Flow -->
		<DisplayType>Grid</DisplayType>
		<!-- Options: True|False -->
		<PartialDisplay>True</PartialDisplay>
		<NumberItemsInCollapsedMode>8</NumberItemsInCollapsedMode>
	</ShareDetails>

	<Performance>
		<!-- Posible values: GRAPH, TABLE
		Empty for disable -->
		<EnabledFormat>TABLE, GRAPH</EnabledFormat>
		<!-- Posible values: SHARE_PRICE_DEVELOPMENT, SHARE_PRICE_DEVELOPMENT_BY_YEARS,
		52_WEEKS_HIGH_LOW
		Empty for disable -->
		<PerformanceType>SHARE_PRICE_DEVELOPMENT, 52_WEEKS_HIGH_LOW,
			SHARE_PRICE_DEVELOPMENT_BY_YEARS</PerformanceType>
	</Performance>
	<Format>
		<en-GB>
			<TickerDateTimeFormat>DD MMM, YYYY hh:mm A [(GMT] Z[)]</TickerDateTimeFormat>
			<ShortDate xml:space="preserve">MM/DD/YYYY</ShortDate>
			<DecimalDigits>2</DecimalDigits>
			<PercentDigits>2</PercentDigits>
			<!--DecimalSeparator:
			comma or dot-->
			<DecimalSeparator xml:space="preserve">.</DecimalSeparator>
			<!--ThousandsSeparator:
			comma, dot, space-->
			<ThousandsSeparator xml:space="preserve">,</ThousandsSeparator>
			<!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
			<NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
		</en-GB>
		<ar-AE>
			<TickerDateTimeFormat>DD MMM, YYYY hh:mm [(GMT] Z[)]</TickerDateTimeFormat>
			<ShortDate xml:space="preserve">dd/MM/yyyy</ShortDate>
			<DecimalDigits>7</DecimalDigits>
			<PercentDigits>2</PercentDigits>
			<!--DecimalSeparator:
			comma or dot-->
			<DecimalSeparator xml:space="preserve">,</DecimalSeparator>
			<!--ThousandsSeparator:
			comma, dot, space-->
			<ThousandsSeparator xml:space="preserve">.</ThousandsSeparator>
			<!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
			<NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
		</ar-AE>
	</Format>

</settings>