import { useDispatch, useSelector } from 'react-redux';
import { classNames } from '../../helper';
import { setBlindMode } from '../../actions/blindModeAction';
import i18n from '../../services/i18n';

const BlindMode = () => {
  const dispatch = useDispatch();
  const isBlindMode = useSelector(state => state.blindMode.isBlindMode);
  const enabledBlindMode = useSelector(state => state.blindMode.enabledBlindMode);

  const handleChangeColorBlindMode = () => {
    dispatch(setBlindMode());
  };

  if (!enabledBlindMode) return null;

  return <div>
    <button
      className={classNames('blind-mode-area', { active: isBlindMode })}
      onClick={handleChangeColorBlindMode}
    >
      <span className="fs fs-eye-plus blind-mode-area__icon"></span>
      <span className="blind-mode-area__label">{i18n.translate('colorDeficiency')}</span>
    </button>
  </div>;
};

export default BlindMode;
