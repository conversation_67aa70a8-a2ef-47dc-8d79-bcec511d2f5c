import { useContext, useLayoutEffect, useMemo, useRef } from 'react';
import { AppContext } from '../../../AppContext';
import { TICKER_SWITCH_TYPE } from '../../../common';
import { default as templateGraphDefault } from '../../../../markup/ticker/MULTIPLE_TICKER_1.html';
import { default as templateTableDefault } from '../../../../markup/ticker/TABLE_TICKER_SINGLE.html';
import {
  replaceKey,
  formatDateTime,
  convertNumber,
  classByValue,
  getCustomPhraseTicker,
  convertPercentDecimal,
  convertMinutesToString,
  i18nTranslate,
  marketStatusByValue,
  getOpenDateTimeByMinute,
  getCountDownMinutesOpenMarket,
  getTimeZoneDisplay,
  classNames,
  clickWithoutMove,
  translateStringFormat,
  convertChangePercentDecimal,
  formatShortDateAsUTC,
  formatNumberDisplay
} from '../../../helper';
import i18n from '../../../services/i18n';
import { useTreatLikeDom } from '../../../customHooks';
import {TIME_DELAY_SHOW_TICKER} from '../../../constant/common';
import {useDelay} from '../../../customHooks/useDelayUpdate';
import useWatchChange from '../useWatchChange';
import useDelayLoading from '../../../customHooks/useDelayLoading';
import useRefreshing from '../useRefreshing';
import {isValidColorHexCode} from '../../../utils/color';
import useIntervalForceRender from '../../../customHooks/useIntervalForceRender';
import { useFormatNumberByInstrument } from '../../../customHooks/useFormatNumberByInstrument';
import useTickerLabels from '../useTickerLabels';
import { useSelectedTicker } from '../../../customHooks/useTickers';

const MultipleTickerItem = ({ prevData, data: _data, isSelected, onItemClick = () => {}, tickerFormat }) => {
  const { formatNumberByInstrument, formatNumberChangeByInstrument } = useFormatNumberByInstrument();
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER );
  const fieldChanged = useWatchChange(data, ['bid', 'ask', 'last', 'open', 'high', 'low', 'volume']);
  const settings = useContext(AppContext);
  const selectedTicker = useSelectedTicker();
  const containerRef = useRef(null);
  const refreshing = useRefreshing(data);
  const [delayLoading] = useDelayLoading(refreshing);
  const forceRenderKey = useIntervalForceRender('minus', data.isRT);
  const { className, events, attr, ...domProps } = useTreatLikeDom();
  const { getLabelsByTemplate } = useTickerLabels();

  const {turnOverDisplay,marketCapDisplay,numberOfShareDisplay} = settings?.shareDetails;


  useLayoutEffect(() => {
    function _setBgMarketAbbreviation() {
      const tickerMarketAbbreviation = containerRef.current.querySelector('.ticker__market-abbreviation');
      if (!tickerMarketAbbreviation) return;
      const currentIns = settings.instruments.find(x => x.id === data.instrumentId);
      const color = currentIns.color;
      //const { marketAbbreviation, marketName } = data;
      // if (marketAbbreviation == null || !marketAbbreviation) {
      //   tickerMarketAbbreviation.textContent = marketName?.slice(0, 3);
      // }

      if (isValidColorHexCode(color) && tickerMarketAbbreviation.classList.contains('ticker__market-abbreviation--bg-color')) {
        tickerMarketAbbreviation.style.background = color;
      }
    }
    _setBgMarketAbbreviation();
  });

  const lastValue = useMemo(() => {
    if (!prevData) return;
    const prevItem = prevData.find(e => e.instrumentId == data.instrumentId);
    return data.last - prevItem.last;
  }, [data]);

  const template = useMemo(() => {
    function _getTemplateHtml() {
      if (tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE) {
        const templateTable = settings.ticker.tableTickerTemplate || templateTableDefault;
        const tableHTMLDOM = new DOMParser().parseFromString(templateTable, 'text/xml');
        const tableRowHTML =
          tableHTMLDOM.querySelector('.table__body-tr') &&
          tableHTMLDOM.querySelector('.table__body-tr').outerHTML;
        return tableRowHTML;
      }
      return settings.ticker.graphTickerTemplate || templateGraphDefault;
    }

    function _renderTemplate(item) {
      if (item) {
        const templateTable = settings.ticker.tableTickerTemplate;
        const tableRow = _getTemplateHtml();
        const labels = {
          ...getLabelsByTemplate(templateTable),
          ...getLabelsByTemplate(tableRow)
        };
        const dataTicker = _normalizeData(item);
        const dataTemplate = { ...dataTicker, ...labels };
        const content = replaceKey(tableRow, dataTemplate);
        if (tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE) return content;
        let templateHeading = replaceKey(templateTable, dataTemplate);
        let tableHTMLDOM = new DOMParser().parseFromString(templateHeading, 'text/xml');
        tableHTMLDOM.querySelector('.table__body-tr').remove();
        tableHTMLDOM.querySelector('.table__body').innerHTML = content;
        return tableHTMLDOM.querySelector('.table').outerHTML;
      }

      return '<h2> No data</h2>';
    }
    return _renderTemplate(data);
  }, [data, forceRenderKey]);

  function _normalizeData(item) {
    const data = { ...item };
    const instrumentId = item.instrumentId;
    const countDownMinutes = getCountDownMinutesOpenMarket(item);
    data.bid = formatNumberByInstrument(item.bid, instrumentId);
    data.ask = formatNumberByInstrument(item.ask, instrumentId);
    data.change = formatNumberChangeByInstrument(item.change, instrumentId);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.high = formatNumberByInstrument(Math.max(item.high, item.last), instrumentId);
    data.high52W = formatNumberByInstrument(Math.max(item.high52W, item.last), instrumentId);
    data.last = formatNumberByInstrument(item.last, instrumentId);
    data.low = formatNumberByInstrument(Math.min(item.low, item.last), instrumentId);
    data.low52W = formatNumberByInstrument(Math.min(item.low52W, item.last), instrumentId);
    data.open = formatNumberByInstrument(item.open, instrumentId);
    data.percent52W = convertPercentDecimal(item.percent52W);
    data.volume = convertNumber(item.volume);
    data.volumeChange = formatNumberChangeByInstrument(item.volumeChange, instrumentId);
    data.lastUpdatedDate = formatDateTime(item.lastUpdatedDate);
    data.timeToCloseMarket = `${convertMinutesToString(
      countDownMinutes,
      i18n.translate('hrs'),
      i18n.translate('mins')
    )} ${getTimeZoneDisplay(item.id, selectedTicker?.timezoneIANA)}`;
    data.dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, item.id, selectedTicker?.timezoneIANA);
    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';
    data.todayTurnover = formatNumberByInstrument(
          formatNumberDisplay(item.todayTurnover, turnOverDisplay), instrumentId
        );
    data.marketCap = formatNumberByInstrument(
      formatNumberDisplay(item.last*item.noShares, marketCapDisplay), instrumentId
    );
    data.noShares = convertNumber(
      formatNumberDisplay(item.noShares, numberOfShareDisplay),
      { decimalDigits: 0 }
    );
    data.lowYtdDate = formatShortDateAsUTC(item.lowYtdDate);
    data.highYtdDate = formatShortDateAsUTC(item.highYtdDate);
    data.highest52wDate = formatShortDateAsUTC(item.highest52wDate);
    data.lowest52wDate = formatShortDateAsUTC(item.lowest52wDate);
    data.allTimeHighDate = formatShortDateAsUTC(item.allTimeHighDate);
    data.allTimeLowDate = formatShortDateAsUTC(item.allTimeLowDate);

    data.allTimeHigh = formatNumberByInstrument(item.allTimeHigh, instrumentId);
    data.allTimeLow = formatNumberByInstrument(item.allTimeLow, instrumentId);
    data.lowYtd = formatNumberByInstrument(item.lowYtd, instrumentId);
    data.highYtd = formatNumberByInstrument(item.highYtd, instrumentId);
    data.percentYtd = convertChangePercentDecimal(item.percentYtd);
    data.totalTrades = convertNumber(item.totalTrades);
    data.prevClose = formatNumberByInstrument(item.prevClose, instrumentId);
    data.lotSize = convertNumber(item.lotSize);
    data.pE = formatNumberByInstrument(item.eps === 0 ? 0 : item.last / item.eps, instrumentId);
    data.averagePrice = formatNumberByInstrument(item.averagePrice, instrumentId);
    data.askSize = convertNumber(item.askSize);
    data.bidSize = convertNumber(item.bidSize);

    return getCustomPhraseTicker(data);
  }

  function _getLabel() {
    const singleLabels = [
      'w52RangeLabel',
      'volumeLabel',
      'bidAskLabel',
      'marketCloseLabel',
      'marketOpenedLabel',
      'openLabel',
      'highLabel',
      'lowLabel',
      'marketOpenInLabel',
      'sharesLabel',
      'lastLabel',
      'changePercentageLabel',
      'changeLabel',
      'marketWillOpenLabel',
      'marketWillCloseLabel',
      'relativeVolumelabel'
    ];

    return {
      dayRangeLabel: i18n.translate('rangeLabel'),
      ...i18nTranslate(singleLabels)
    };
  }

  function onClick(event) {
    // Keypresses other than Enter and Space
    if (event instanceof KeyboardEvent && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }
    event.preventDefault();
    onItemClick(data.instrumentId);
  }

  return (
    <div
      {...events({
        ...clickWithoutMove(onClick),
        onKeyDown: onClick
      })}
      data-change={fieldChanged.join(',')}

      {...domProps}
      {...className(
        classNames(
          'ticker__item',
          { selected: isSelected },
          classByValue(data.change, data.isRT) || 'indicator-neutral',
          `volume--${classByValue(data.volumeChange)}`,
          marketStatusByValue(data.marketStatus),
          {
            streaming: data.isRT,
            loading: delayLoading,
            'animate--increase': lastValue > 0,
            'animate--decrease': lastValue < 0,
            'animate--neutral': data.isRT ? false : (lastValue === 0) // do not add neutral when isRT = true
          }
        )
      )}
      ref={containerRef}
      dangerouslySetInnerHTML={{ __html: template }}
    ></div>
  );
};

export default MultipleTickerItem;
