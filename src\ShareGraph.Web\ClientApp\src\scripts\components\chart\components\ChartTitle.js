import { classNames } from '@euroland/libs';
import { useContext } from 'react';

import { CIQ } from '../../../components/chart/chartiq-import';
import ChartContext from '../../../context/ChartContext';
import { useSelectedTicker } from '../../../customHooks/useTickers';
import { formatDateInstrumentId, translateStringFormat } from '../../../helper';
import i18n from '../../../services/i18n';
import { CLOSE_STATUS_CHART } from '../custom-web-components/common';
import dayjs from '../../../utils/dayjs';
import appConfig from '../../../services/app-config';
import TickerName from '../../TickerName';
import Clock from '../../Clock';

const ChartTitle = ({ chartTitleData }) => {
  const {
    chart: [chartEngine]
  } = useContext(ChartContext);

  const setting = appConfig.get();
  const formatShortDate = setting.format.shortDate;
  const tickerDateTimeFormat = setting.format.tickerDateTimeFormat;
  const timeFormat = setting.format.timeFormat || 'HH:mm A CET';

  const {
    symbol,
    currentPrice,
    todaysChange,
    todaysChangePct,
    symbolObject,
    closeStatus
  } = chartTitleData;

  const {
    shareName,
    marketAbbreviation,
    instrumentId
  } = symbolObject;

  const selectedTicker = useSelectedTicker();
  const isOpen = selectedTicker.marketStatus === 'Open';
  const displayDate = selectedTicker.lastUpdatedDate;

  const dateTimeFormat = timeFormat;

  return (
    <div className="chart-title">
      <div className="chart-title__content">
        <h2 className="chart-title__symbol">{instrumentId && (<TickerName instrumentId={instrumentId} marketAbbreviation={marketAbbreviation} shareName={shareName} />)}</h2>
        <div className="chart-title__current-price-wrapper">
            <p className={classNames('chart-title__current-price', {
              'chart-title__invisible': !currentPrice
            })}>
              {closeStatus === CLOSE_STATUS_CHART.UP && <i className="fs fs-triangle-up" />}
              {closeStatus === CLOSE_STATUS_CHART.DOWN && <i className="fs fs-triangle-down" />}
              <span className="chart-title__current-price-value">{currentPrice || 'N/A'}</span>
              <span className="chart-title__currency">{symbolObject.currencyCode}</span>
            </p>

            <p className={classNames('chart-title__change-content', {
              'chart-title__invisible': !todaysChange
            })}>
              <span
                className={classNames('chart-title__today-change', {
                  'close-up': closeStatus === CLOSE_STATUS_CHART.UP,
                  'close-down': closeStatus === CLOSE_STATUS_CHART.DOWN
                })}
              >
                {todaysChange || 'N/A'}
              </span>
              &nbsp;
              {!!todaysChangePct && (
                <span
                  className={classNames('chart-title__today-change', {
                    'close-up': closeStatus === CLOSE_STATUS_CHART.UP,
                    'close-down': closeStatus === CLOSE_STATUS_CHART.DOWN
                  })}
                >
                  {translateStringFormat('todaysChangePct', [todaysChangePct])}
                </span>
              )}
            </p>
            {/* <p className='chart-title__clock'>
              <Clock />
            </p> */}
        </div>
      </div>
      <div className="chart-title__time-wrapper">
        {displayDate && currentPrice && (
          <span className="chart-title__as-of">
            {translateStringFormat('asOf', [
              formatDateInstrumentId(displayDate, selectedTicker.instrumentId, {
                timeZone: selectedTicker?.timezoneIANA || setting.timeZone,
                format: dateTimeFormat
              })
            ])}
          </span>
          
        )}
        {symbol && selectedTicker?.isRT && (
          <p className={classNames('chart-title__real-time', { close: !isOpen })}>
            {isOpen ? <i className="fs fs-checked-radio" /> : <i className="fs fs-close-radio" />}
            <span className="chart-title__real-time-label"> {i18n.translate('realTimeData')}</span>
          </p>
        )}
      </div>
    </div>
  );
};

export default ChartTitle;
