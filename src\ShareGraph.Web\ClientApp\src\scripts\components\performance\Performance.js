import { useState, useContext, useEffect, useRef} from 'react';
import { PERFORMANCE_SWITCH_TYPE, PERFORMANCE_SELECT_TYPE } from '../../common';
import { AppContext } from '../../AppContext';
import Switcher from '../Switcher';
import { SharePriceDevelopment } from './SharePriceDevelopment';
import { SharePriceDevelopmentByYears } from './SharePriceDevelopmentByYears';
import { Weeks52HighLow } from './52WeeksHighLow';
import { LAYOUT } from '../../common';
import i18n from '../../services/i18n';
import {  useDispatch } from 'react-redux';
import { fetch52Weeks } from '../../actions/52WeeksAction';
import { REFRESH_PERFORMANCE_TIME } from '../../constant/common';
import { Dropdown, Option } from '@euroland/dropdown-react';


export const Performance = () => {
    const settings = useContext(AppContext);
    const layout = settings.layout || LAYOUT.FULL;
    const dispatch = useDispatch();
    const [sPDByYearSetting, setSPDByYearSetting] = useState([]);
    const showEarliestYearFirstSPByYear = settings.performance.showEarliestYearFirstSPByYear;
    // set state which type is selecting
    const isDefaultFormat = (format) => {
        return settings.performance.enabledFormats.length > 0 && settings.performance.enabledFormats[0].toUpperCase() === format;
      };
    const listPerformanceTypes = settings.performance.performanceTypes;

    const refreshTime = settings.performance?.refreshSeconds ?? REFRESH_PERFORMANCE_TIME;

    const containerRef = useRef(null);

    const [performanceFormatTypes, setPerformanceFormatTypes] = useState([
        {
          type: PERFORMANCE_SWITCH_TYPE.GRAPH_TYPE,
          icon: 'fs-area-chart',
          isDefaultSelected: isDefaultFormat(PERFORMANCE_SWITCH_TYPE.GRAPH_TYPE),
          dataTooltip: i18n.translate('graphTooltipLabel'),
          text: i18n.translate('graphTooltipLabel'),
          order: settings.performance.enabledFormats?.findIndex(p => p.toUpperCase() === PERFORMANCE_SWITCH_TYPE.GRAPH_TYPE) || 0
        },
        {
          type: PERFORMANCE_SWITCH_TYPE.TABLE_TYPE,
          icon: 'fs-table',
          isDefaultSelected: isDefaultFormat(PERFORMANCE_SWITCH_TYPE.TABLE_TYPE),
          dataTooltip: i18n.translate('tableTooltipLabel'),
          text: i18n.translate('tableTooltipLabel'),
          order: settings.performance.enabledFormats?.findIndex(p => p.toUpperCase() === PERFORMANCE_SWITCH_TYPE.TABLE_TYPE) || 0
        }
      ].sort((a, b) => a.order - b.order));

    const [performanceFormat, setPerformanceFormat] = useState(performanceFormatTypes.find(t => t.isDefaultSelected).type);

    const handleSwitchType = (type) => {
        setPerformanceFormat(type);
      };

    const [performanceType, setPerformanceType] = useState(listPerformanceTypes.length > 0 && listPerformanceTypes[0]);
    const handleChangeType = (value) => {
        setPerformanceType(value);

        switch(performanceType){
            case PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT:
                break;
            case PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT_BY_YEARS:
                break;
            case PERFORMANCE_SELECT_TYPE.WEEKS_52_HIGH_LOW:
                dispatch(fetch52Weeks());
                break;
        }
    };

    const getPerformanceSelectTypeTranslation = type => {
        switch(type){
            case PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT:
                return i18n.translate('sharePriceDevelopment');
            case PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT_BY_YEARS:
                return i18n.translate('sharePriceDevelopmentByYears');
            case PERFORMANCE_SELECT_TYPE.WEEKS_52_HIGH_LOW:
                return i18n.translate('weeks52HighLow');
        }
    };

    useEffect(()=>{
        const instrumentSettings = settings.instruments.map(x => {return {id: x.id, color: x.color, order: x.order, type: 'T'};});
        const peersSettings = settings.peers.peers.map(x => {return {id: x.id, color: x.color, order: 10000 + x.order, type: 'P'};});
        const indicesSettings = settings.indices.indices.map(x => {return {id: x.id, color: x.color, order : 20000 + x.order, type: 'I'};});
        setSPDByYearSetting([...instrumentSettings, ...indicesSettings, ...peersSettings]);
    }, []);

    useEffect(() => {
        setPerformanceFormatTypes(performanceFormatTypes => performanceFormatTypes.map(item => {
            const type = item.type === PERFORMANCE_SWITCH_TYPE.GRAPH_TYPE? 'graph' : 'table';
            let id;
            switch(performanceType){
                case PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT:
                    id = `spd-${type}`;
                    break;
                case PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT_BY_YEARS:
                    id = `spdby-${type}`;
                    break;
                case PERFORMANCE_SELECT_TYPE.WEEKS_52_HIGH_LOW:
                    id = `52whl-${type}`;
                    break;
            }
            return {...item, id};
        }));
    }, [performanceType]);

    return (
      <>
        {/* <div className='performance--full'> */}
        {layout === LAYOUT.FULL && <h2 className='title-section performance-details__title'>{i18n.translate('performanceTab')}</h2>}
        {/* </div> */}
        <div className='performance--buttons' ref={containerRef}>
            <div className='performance__switch-options switcher__wrapper--v2'>
            {
                            settings.performance.enabledFormats.length > 1
                            && (
              <Switcher
                onClick={handleSwitchType}
                type={PERFORMANCE_SWITCH_TYPE.GRAPH}
                tabs={performanceFormatTypes}
                tabActive={performanceFormat}
                isButton={true}
                ariaLabel={i18n.translate('performanceViewTabs')}>
                            </Switcher>
            )}
            <Dropdown
              className="dropdown-list performance--dropdown"
              buttonClassName="dropdown-toggle"
              type={performanceType}
              value={performanceType}
              onOptionSelect={handleChangeType}
              icon={<span className="fs-caret-top"></span>}
            >
              {listPerformanceTypes.map((item, index) => {
                return (
                  <Option value={item} key={index}>
                    {getPerformanceSelectTypeTranslation(item)}
                  </Option>
                );
              })}
            </Dropdown>
          </div>

          {/* <select className={'performance--selection form-select'} onChange = {handleChangeType}>
                {
                    listPerformanceTypes.map((item, index)=> {
                        return <option
                            value ={item}
                            key={index}>{getPerformanceSelectTypeTranslation(item)}
                        </option>;
                    })

                }
            </select> */}
        </div>
        <div
            tabIndex={0}
            role='tabpanel'
            id={performanceFormat ===  PERFORMANCE_SWITCH_TYPE.GRAPH_TYPE ? performanceFormatTypes[0].id : performanceFormatTypes[1].id}
          >
          {performanceType === PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT &&
            sPDByYearSetting.length !== 0 && (
              <SharePriceDevelopment
                format={performanceFormat}
                sPDByYearSetting={sPDByYearSetting}
                showEarliestYearFirstSPByYear={showEarliestYearFirstSPByYear}
                refreshTime={refreshTime}
              />
            )}
          {performanceType ===
            PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT_BY_YEARS &&
            sPDByYearSetting.length !== 0 && (
              <SharePriceDevelopmentByYears
                sPDByYearSetting={sPDByYearSetting}
                format={performanceFormat}
                showEarliestYearFirstSPByYear={showEarliestYearFirstSPByYear}
                refreshTime={refreshTime}
              />
            )}
          {performanceType === PERFORMANCE_SELECT_TYPE.WEEKS_52_HIGH_LOW && (
            <Weeks52HighLow
              format={performanceFormat}
              refreshTime={refreshTime}
            />
          )}
        </div>
      </>
    );
};
