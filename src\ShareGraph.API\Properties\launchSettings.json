{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:2020", "sslPort": 44334}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "ShareGraph.API": {"commandName": "Project", "dotnetRunMessages": "true", "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:5003;http://localhost:50003", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}