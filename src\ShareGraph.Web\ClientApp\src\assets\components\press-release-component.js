(function PressReleaseComponentFactory(euroland) {
  const props = {
    pressreleaseId: {
      type: 'number',
      required: true
    }
  };

  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;

/**
 * Creates the PressReleaseComponent.
 * @returns {euroland.components.PressReleaseComponent}
 */
  euroland.createComponent('PressReleaseComponent', {
    tag: 'press-release-component',
    url: baseUrl + 'press-release' + location.search ,
    //new URL(location.pathname === '/' ? 'press-release' : location.pathname + '/press-release', location.origin) + location.search,
    dimensions: {
      width: '50%',
      height: '70%'
    },
    template: {
      name: 'modal',
      clickOverlayToClose: true
    },
    props: props
  });
})(window.euroland);
