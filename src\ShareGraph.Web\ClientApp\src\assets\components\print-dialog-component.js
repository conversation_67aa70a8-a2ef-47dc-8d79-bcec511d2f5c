(function PrintDialogComponentFactory(euroland) {
  const props = {
    onConfirm: {
      type: 'function',
      required: true,
      default: function (aggreed) {}
    }
  };

  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;

/**
 * Creates the PrintDialogComponent.
 * @returns {euroland.components.PrintDialogComponent}
 */
  euroland.createComponent('PrintDialogComponent', {
    tag: 'print-dialog-component',
    url: baseUrl + 'print-dialog' + location.search,
    dimensions: {
      width: '260px !important',
      height: '134px !important'
    },
    template: {
      name: 'modal',
      clickOverlayToClose: false
    },
    props: props
  });
})(window.euroland);
