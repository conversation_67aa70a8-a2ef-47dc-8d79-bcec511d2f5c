# Deployment process

This document shows how to host ShareGraph3 application on an IIS server.

This document covers the following subjects:

* Required databases, users, and access permissions
* Create Azure Blob Storage container 
* Install the .NET Core Hosting Bundle on Windows Server.
* Create an IIS site in IIS Manager
* Deploy ShareGraph3.

## Prerequisites
* [.NET Core 5.0](https://dotnet.microsoft.com/download/dotnet-core/5.0) or above
* Sql Server 2016 or above. 


## Required components
* [ShareGraph3 API]()
* [SData API]()
* Logging System

## Required databases, users, and access permissions.

Starting from ShareGraph3, we start to use EntityFramework as a replacement for ORM framework  [Euroland.NetCore.ToolsFramework.Data](https://gitlab.euroland.com/tools/tools-framework/-/tree/master/src%2FEuroland.NetCore.ToolsFramework.Data) to overcome the time consuming of development process. So we drop Stored Procedure for ShareGraph3 project.

### List of required databases
* **Shark**
* **News**

### Create an user for database accessing with permissions.

The EntityFramework needs an user has basic permissions (`READ`, `WRITE`, `EXECUTE`):

| DB name   |      Permissions      |
|----------|:-------------|
| **Shark** | READ, WRITE, EXECUTE |
| **News** | READ, WRITE, EXECUTE |

Once the db user is ready, update it to the application configuration as at section [Deploy ShareGraph3 application package](#deploy-shareGraph3-application-package)

## Create Azure Blob Storage Container

ShareGraph3 needs a storage to save the images which are uploaded by end-users when they use the Social Network Sharing feature. To support ShareGraph3 works across  multi-environments, we decided to use Azure Blob Storage service to do upload images to.

## Install the .NET Core Hosting Bundle

Install the  _.NET Core Hosting Bundle on the IIS server. The bundle installs the .NET Core Runtime, .NET Core Library, and the  [ASP.NET Core Module](https://docs.microsoft.com/en-us/aspnet/core/host-and-deploy/aspnet-core-module?view=aspnetcore-5.0). The module allows ASP.NET Core apps to run behind IIS.

Download the installer using the following link:
[Current .NET Core Hosting Bundle installer (direct download)](https://dotnet.microsoft.com/permalink/dotnetcore-current-windows-runtime-bundle-installer)

1.  Run the installer on the IIS server.
    
2.  Restart the server or execute  `net stop was /y`  followed by  `net start w3svc`  in a command shell.

## Create an IIS site in IIS Manager

I assume that we already have a site for **/tools/** solution, and ShareGraph3 is just like other Euroland's tool services located under the **/tools/** solution.

1. Add a new Application Pool with options:
	- *Name*: Enter appropriate name for the application pool
	- *NET CRL version*: **No Managed Code**
	-  *Managed pipeline mode*: **Integrated**
 
 2. Confirm the process model identity has the proper permissions
 If the default identity of the app pool (**Process Model** > **Identity**) is changed from `ApplicationPoolIdentity` to another identity, verify that the new identity has the required permissions to access the app's folder, database, and other required resources. For example, the app pool requires read and write access to folders where the app reads and writes files.

## Deploy ShareGraph3
### 1. Verify that required components are ready
* Logging System
* Databases & users
* ShareGraph3 API
* SData API

### 3. Download latest version of ShareGraph3



### 4. Update application settings like Db User, Azure Blob Storage account, logging to the file `appSettings.json` and `appSettings.[environment].json`
### 5. Verify whether application is running under the expected environment