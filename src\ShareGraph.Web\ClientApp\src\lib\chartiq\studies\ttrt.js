import { CIQ } from 'chartiq/js/components';
import { formatWithDateTime } from '../../../scripts/helper';
import appConfig from '../../../scripts/services/app-config';
import fetchApi from '../../../scripts/services/fetch-api';
import i18n from '../../../scripts/services/i18n';
import {fetchDividendData} from '../../../scripts/services/commonService';
import {appSettings} from '../../../appSettings';
import {getInstrumentSettingById} from '../../../scripts/utils/format-number';

let setting;

CIQ.Euroland.Studies.FactorDevidends = (dividends, data) => {
  let multiple = 1;
  let newDividend = dividends.map(x => {
    let dDate = new Date(x.date);
    let filterData = data.filter(y =>y.DT <= dDate);
    let close = filterData[filterData.length-1].Close;
    multiple*=x.dividend/close + 1;
    return {
      ...x, 
      close: close,
      factor:multiple
    };
  });
  return newDividend;
};

CIQ.Euroland.Studies.calculateTTRT = function (stx, sd) {
  setting = appConfig.get();
  
  const instrumentId = stx.chart.symbolObject.instrumentId;
  const isRT = getInstrumentSettingById(instrumentId)?.isRT;

  
  var quotes = sd.chart.scrubbed,
    dataLength = quotes.length;


  document.querySelector('cq-loader').show();
  if (document.querySelector('#loading-status')) {
    document.querySelector('#loading-status').innerHTML = i18n.translate('loading');
  }

  fetchDividendData(instrumentId, quotes[0].DT, quotes[dataLength - 1].DT, isRT).then(res => {
    const dividends = res.data.dividendEvents;

    // Attach raw dividend data to study descriptor (sd)
    sd.dividends = dividends;

    // Trigger series drawing with raw data
    stx.draw();

    document.querySelector('cq-loader').hide();
    if (document.querySelector('#loading-status')) {
      document.querySelector('#loading-status').innerHTML = i18n.translate('loadingComplete');
    }
  });
};


const studyName = CIQ.I18N.translate('Total Return');


CIQ.Euroland.Studies.createStudy(studyName, {
  name: studyName,
  overlay: true,
  calculateFN: CIQ.Euroland.Studies.calculateTTRT,
  seriesFN: function (stx, sd, quotes) {
    const removeEmptyQuote = quotes.filter(Boolean);
    const dividends = (sd.dividends || []).map(item => {
      return {...item, Date: new Date(item.date)};
    }).filter(item => {
      if(removeEmptyQuote.length > 0) {
        return (removeEmptyQuote[0].DT <= item.Date);
      }
      return false;
    });
    const name = 'TTRT ' + sd.name;
    const factor = CIQ.Euroland.Studies.FactorDevidends(dividends, removeEmptyQuote);
    for (let i = 0; i < removeEmptyQuote.length; i++) {
      let f = factor.filter(x=>new Date(x.date)<=removeEmptyQuote[i].DT);
      let k = f && f.length > 0 ? f[f.length-1].factor : 1;
      removeEmptyQuote[i][name] =  removeEmptyQuote[i].Close * k;
    }

    // Draw series as line
    CIQ.Studies.displaySeriesAsLine(stx, sd, removeEmptyQuote);
  },
  inputs: {},
  outputs: {
    'TTRT': '#0000FF'
  },
  parameters: {}
});

export { CIQ };
