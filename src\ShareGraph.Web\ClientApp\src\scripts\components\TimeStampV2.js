import { useEffect, useState } from 'react';
import useIntervalForceRender from '../customHooks/useIntervalForceRender';
import { convertMinutesToString, getCountDownMinutesOpenMarket } from '../helper';
import i18n from '../services/i18n';
import { usePrevious } from '../customHooks';
import Clock from './Clock';

export default function TimeStamp({ tickerData, fetchLoading }) {
  useIntervalForceRender('minus');
  const { marketName , marketStatus } = tickerData ?? {};

  const [shouldShowTimestamp, setShouldShowTimestamp] = useState(false);
  const previousLoading = usePrevious(fetchLoading);
  useEffect(() => {
    if(previousLoading && !fetchLoading && tickerData?.timezoneIANA) {
        setShouldShowTimestamp(true);
    }
  },[previousLoading, fetchLoading, tickerData?.timezoneIANA]);

  if (!tickerData || !tickerData.marketStatus) return null;

  const isClose = marketStatus === 'Close';

  // closing status
  const countDownMinutes = getCountDownMinutesOpenMarket(tickerData);
  // const dateTimeToMarketOpen = getOpenDateTimeByMinute(countDownMinutes, tickerData.id);

  // opening status
  const timeToCloseMarket = `${convertMinutesToString(
    countDownMinutes,
    i18n.translate('hrs'),
    i18n.translate('mins')
  )}`;

  const timeToOpenOrCloseStatus = isClose
    ? `${i18n.translate('marketWillOpenLabel')} ${timeToCloseMarket}`
    : `${i18n.translate('marketWillCloseLabel')} ${timeToCloseMarket}`;

  const timeToOpenOrCloseStatusString = `${timeToOpenOrCloseStatus}. `;



  return (
    <div className="time-stamp--v2">
      {isClose ? (
        <span className="market-name status-closed">
          <i className="fs fs-close-radio"></i>
          <span>&nbsp;{marketName}&nbsp;</span>
          <span className='status-closed__label'>{i18n.translate('marketCloseLabel')}</span>.
        </span>
      ) : (
        <span className="market-name status-opened">
          <i className="fs fs-checked-radio"></i>
          <span>&nbsp;{marketName}&nbsp;</span>
          <span className='status-opened__label'>{i18n.translate('marketOpenedLabel')}</span>.
        </span>
      )}
      &nbsp;
      <span className='time-open-close'>
        {timeToOpenOrCloseStatusString}
      </span>
      {shouldShowTimestamp && <span className='time-open-close__update'><Clock /></span>}
    </div>
  );
}
