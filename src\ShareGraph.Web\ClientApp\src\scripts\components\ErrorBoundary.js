import React from 'react';
import { connect } from 'react-redux';
import { clearErrorUI, hasErrorUI } from '../actions/errorUIActions';
import { openErrorDialog } from '../customHooks/useFetchErrors';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
    this.renderErrorRef = React.createRef();
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error) {
    if (!this.renderErrorRef.current) {
      setTimeout(() => {
        openErrorDialog('', () => this.props.clearErrorUI());
      }, 100);
      this.renderErrorRef.current = true;
    }
    this.props.hasErrorUI(error);
  }

  componentDidUpdate(prevProps, prevState) {
    if (!prevState.hasError || !!this.props.error) return;
    this.setState({ hasError: false });
    this.renderErrorRef.current = false;
  }

  render() {
    if (this.state.hasError) return null;

    return this.props.children;
  }
}

const mapStateToProps = state => ({
  error: state.errorUIs.error
});

const mapDispatchToProps = () => {
  return {
    hasErrorUI,
    clearErrorUI
  };
};

export default connect(mapStateToProps, mapDispatchToProps())(ErrorBoundary);
