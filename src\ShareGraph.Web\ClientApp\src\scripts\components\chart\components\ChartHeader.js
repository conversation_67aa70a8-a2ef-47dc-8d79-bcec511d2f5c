import useChartTitle from '../../../customHooks/useChartTitle';
import ChartShowRange from './ChartShowRange';
import ChartTitle from './ChartTitle';
import useComparisonWrapperStyle from '../../../customHooks/useComparisonWrapperStyle';

const ChartHeader = () => {
  const chartTitleData = useChartTitle();
  const { chartHeaderRef } = useComparisonWrapperStyle();
  return (
    <div className="chart-header" ref={chartHeaderRef}>
      <div className="chart-header__title-wrapper">
        <ChartTitle chartTitleData={chartTitleData} />
        <ChartShowRange />
      </div>
    </div>
  );
};

export default ChartHeader;
