import { useContext } from 'react';
import { AppContext } from '../../AppContext';
import { getLabelsTicker, i18nTranslate, translateStringFormat } from '../../helper';
import i18n from '../../services/i18n';
export default function useTickerLabels() {
  const settings = useContext(AppContext);

  const { turnOverDisplay, marketCapDisplay, numberOfShareDisplay } = settings?.shareDetails;

  const getDisplayLabel = (labels, key, displayValue) => {
    if (!labels.includes(key)) return {};
    const translateValue =  displayValue !== 'exact' ? i18n.translate(displayValue) : '';
    return {
      [key]: translateStringFormat(key, [translateValue])
    };
  };

  const getLabelsByTemplate = (template) => {
    const labels = getLabelsTicker(template);
    return {
      ...i18nTranslate(labels),
      dayRangeLabel: i18n.translate('rangeLabel'),
      caption: i18n.translate('shareDetails'),
      ...getDisplayLabel(labels, 'turnoverLabel', turnOverDisplay),
      ...getDisplayLabel(labels, 'marketCapLabel', marketCapDisplay),
      ...getDisplayLabel(labels, 'numberOfSharesLabel', numberOfShareDisplay)
    };
  };

  return {
    getLabelsByTemplate
  };

}
