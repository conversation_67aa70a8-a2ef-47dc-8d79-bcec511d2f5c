"Deploy Gamma":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:staging
  dependencies:
    - package-staging
  variables:
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_ENVIRONMENT: Gamma
    DEPLOY_SERVICE_URL: 'https://ee-v-gamma1.euroland.com:8172/msdeploy.axd'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - next
  environment:
    name: Gamma
    url: 'https://gamma.euroland.com/tools/sharegraph3/?companycode=dk-cbg'

"Deploy Staging":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
    - .prevent-fetching
  stage: deploy:staging
  dependencies:
    - package-staging
  variables:
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
    DEPLOY_ENVIRONMENT: Staging
    DEPLOY_SERVICE_URL_SERVER1: 'https://ee-v-webcat151.euroland.com:8172/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER2: 'https://ee-v-webcat161.euroland.com:8172/msdeploy.axd'
    DEPLOY_API_IIS_APP_PATH: 'staging-site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'staging-site/tools/sharegraph3'
  script:
    - |
      ECHO Deploying API app to ee-v-webcat161...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to ee-v-webcat151...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to ee-v-webcat161...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to ee-v-webcat151...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    refs:
      - next
  environment:
    name: Staging
    url: 'https://staging-gr.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'


"ReDeploy To Gamma":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: deploy:staging
  variables:
    PUBLISH_OUTPUT_DIR: 'bin/Release/net6.0/publish'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/publish'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/publish'
    DEPLOY_ENVIRONMENT: Gamma
    DEPLOY_SERVICE_URL: 'https://ee-v-gamma1.euroland.com:8172/msdeploy.axd'
    DEPLOY_API_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'Default Web Site/tools/sharegraph3'
  script:
    - 'call "./build/build.bat" --build-client --output-path "%PUBLISH_OUTPUT_DIR%"'
    - |
      ECHO Deploying API app...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%GAMMA_DEPLOY_USER%" --pwd "%GAMMA_DEPLOY_PSW%" --url "%DEPLOY_SERVICE_URL%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    - /^v[0-9]\.[0-9]+\.[0-9]+(?:\-next.[0-9]+)$/i
  when: manual
  environment:
    name: Gamma
    url: 'https://gamma.euroland.com/tools/sharegraph3/?companycode=dk-cbg'

"ReDeploy To Staging":
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: deploy:staging
  variables:
    PUBLISH_OUTPUT_DIR: 'bin/Release/net6.0/publish'
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/publish'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/publish'
    DEPLOY_ENVIRONMENT: Staging
    DEPLOY_SERVICE_URL_SERVER1: 'https://ee-v-webcat151.euroland.com:8172/msdeploy.axd'
    DEPLOY_SERVICE_URL_SERVER2: 'https://ee-v-webcat161.euroland.com:8172/msdeploy.axd'
    DEPLOY_API_IIS_APP_PATH: 'staging-site/tools/sharegraph3-api'
    DEPLOY_WEB_IIS_APP_PATH: 'staging-site/tools/sharegraph3'
  script:
    - 'call "./build/build.bat" --build-client --output-path "%PUBLISH_OUTPUT_DIR%"'
    - |
      ECHO Deploying API app to ee-v-webcat161...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying API app to ee-v-webcat151...
      call ".\build\msdeploy.bat" --source "%PUBLISH_API_PACKAGE_DIR%" --iisAppPath "%DEPLOY_API_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT%
    - |
      ECHO Deploying Web app to ee-v-webcat161...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER2%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
    - |
      ECHO Deploying Web app to ee-v-webcat151...
      call ".\build\msdeploy.bat" --source "%PUBLISH_WEB_PACKAGE_DIR%" --iisAppPath "%DEPLOY_WEB_IIS_APP_PATH%" --usr "%STAGING_DEPLOY_USER%" --pwd "%STAGING_DEPLOY_PWD%" --url "%DEPLOY_SERVICE_URL_SERVER1%" --env %DEPLOY_ENVIRONMENT% --exf "appSettings.FieldMapping.json"
  only:
    - /^v[0-9]\.[0-9]+\.[0-9]+(?:\-next.[0-9]+)$/i
  when: manual
  environment:
    name: Staging
    url: 'https://staging-gr.eurolandir.com/tools/sharegraph3/?companycode=dk-cbg'
