import dayjs from 'dayjs';

import { appSettings } from '../../../../appSettings';
import { formatShortDate } from '../../../helper';
import appConfig from '../../../services/app-config';
import cancelCreator from './cancelCreator';
import { CIQ } from '../chartiq-import';
import i18n from '../../../services/i18n';
import { VimeoVideo } from './entities/VimeoVideo';
import { HtmlVideo } from './entities/HtmlVideo';

let zIndex = 400;

export const VIDEO_TYPE = {
  VIMEO: 'VIMEO',
  HTML: 'HTML'
};

let createdVideoMarkers = {};
let cancellable = cancelCreator();

function getVideoType(type) {
  switch (type) {
    case 'AzureBlob':
      return VIDEO_TYPE.HTML;
    case 'Vimeo':
      return VIDEO_TYPE.VIMEO;
    default:
      break;
  }
}

export function createVideoMarkers(stx, isDynamicCallout, isHeadsUpFloating, type, cb) {
  isDynamicCallout = stx.layout.headsUp.dynamic;
  isHeadsUpFloating = stx.layout.headsUp.floating;
  let setting = appConfig.get();
  const data = stx.chart.videoData || [];

  const label = type;

  /**
   *
   * @param {*} stx
   * @param { String } label
   * @param { Array<Object> | Object } markerData
   * @returns Promise
   */
  function createSingleMaker(stx, label, markerData) {
    return new Promise((resolve, reject) => {
      try {
        const title = markerData.title || '';
        const pdfUrl = appSettings.webcastBlobContainer + '/' + markerData.transcriptUrl;
        const videoUrl =  appSettings.webcastBlobContainer + '/' +markerData.urls?.find(urlE => !!urlE)?.url;
        const videoType = getVideoType(markerData.defaultHost) || VIDEO_TYPE[setting.videoSetting?.videoType] || VIDEO_TYPE.VIMEO;
        const thumbnailUrl =  appSettings.webcastBlobContainer + '/' + markerData.thumbnailUrl;
        const datum = {
          x: markerData.Date,
          label: 'video',
          category: 'video',
          headline: title,
          pdfUrl,
          videoUrl,
          videoWidth: 340,
          videoType,
          videoId: markerData.vimeo?.vimeoId,
          appId: markerData.appId || '58479',
          thumbnailUrl
        };
        const marker = new CIQ.Marker({
          stx: stx,
          label: label,
          xPositioner: 'date',
          x: markerData.Date,
          node: new VideoMarkerNode(datum)
        });
        cancellable.markers.push(marker);
        resolve(marker);
      } catch (e) {
        reject(e);
      }
    });
  }

  function createChartMakers(datax = []) {
    if (!datax.length) {
      return Promise.resolve();
    }

    const newData = datax.filter(value => {
      const dateString = dayjs(value.Date).toISOString();
      return !createdVideoMarkers[dateString];
    });

    if (!newData.length) {
      return Promise.resolve();
    }

    return newData.reduce((prev, value) => {
      return prev.then(() => {
        return createSingleMaker(stx, label, value).then(marker => {
          if (cancellable.markers.length === datax.length) {
            stx.chart.container.querySelectorAll('.stx-marker-video-wrapper').forEach(el => {
              el.classList.add('active--marker');
            });

            stx.draw();
          }
          if (dayjs(value.Date).isSame(marker.params.x)) {
            const dateString = dayjs(value.Date).toISOString();
            createdVideoMarkers[dateString] = true;
          }
        });
      });
    }, Promise.resolve());
  }

  const { promise, cancel } = cancellable(createChartMakers, data);

  cancellable.onCancel = () => {
    createdVideoMarkers = {};

    while (cancellable.tasks.length) {
      cancellable.tasks.shift().cancel();
    }

    while (cancellable.markers.length) {
      cancellable.markers.shift().remove();
    }
    cancellable = cancelCreator();
  };

  return {
    cancel: () => {
      cancel();
    },
    promise
  };
}

function VideoMarkerNode({
  x,
  label,
  category,
  videoUrl,
  headline,
  pdfUrl,
  videoWidth,
  videoType,
  videoId,
  appId,
  thumbnailUrl
}) {
  const node = (this.node = document.createElement('div'));
  node.className = 'stx-marker square stx-marker-video-wrapper';
  if (category) node.classList.add(category);
  const visual = CIQ.newChild(node, 'div', 'stx-visual');
  visual.title = headline;
  CIQ.newChild(node, 'div', 'stx-stem');

  // node.addEventListener('click', nodeClicked);
  CIQ.safeClickTouch(node, nodeClicked);
  function nodeClicked({ target: el }) {
    let expandNode = node.querySelector('.stx-marker-video.stx-marker-expand');
    if (!expandNode) {
      expandNode = createVideoExpandNode({
        x,
        label,
        category,
        videoUrl,
        headline,
        pdfUrl,
        videoWidth,
        videoType,
        videoId,
        appId,
        thumbnailUrl
      });
      node.append(expandNode);
    }
    expandNode.style.zIndex = zIndex++;

    const isVisual = el === visual;
    const isClose = el.classList.contains('ciq-close');
    let isVisible = node.classList.contains('highlight');
    const videoPlayer = node.querySelector('.video-player');

    if (isVisual || isClose) {
      node.classList.toggle('highlight');
      CIQ.Marker.positionContentVerticalAndHorizontal(node, true);
      if (isVisible) {
        videoPlayer.playerVideo.pause();
      }
    }
    // autoplay video
    if (!node.firstClick) {
      node.firstClick = true;
      setTimeout(() => {
        videoPlayer.playerVideo.play();
      }, 100); // estimating time of 100 ms to be able to start video
    }
  }
}

function onlyOneVideoPlaying(videoPlayer) {
  Array.from(document.querySelectorAll('.video-player'))
    .filter(video => video !== videoPlayer)
    .forEach(video => {
      video.playerVideo.pause();
    });
}

// function stopAllVideoPlaying() {
//   Array.from(document.querySelectorAll('.video-player'))
//   .forEach(video => {
//     video.playerVideo.pause();
//   });
// }

CIQ.inheritsFrom(VideoMarkerNode, CIQ.Marker.NodeCreator, false);
// creates node for marker content
function createVideoExpandNode({
  videoUrl,
  headline,
  pdfUrl,
  videoWidth,
  x: date,
  videoType,
  videoId,
  appId,
  thumbnailUrl
}) {
  const expand = document.createElement('div');
  expand.className = 'stx-marker-video stx-marker-expand';
  const closeButton = document.createElement('div');
  closeButton.className = 'ciq-icon ciq-close';
  expand.appendChild(closeButton);
  const controllerVideo = getVideo({ type: videoType, videoId, title: headline, videoUrl, appId, thumbnailUrl });
  controllerVideo.container.classList.add('video-player');
  expand.appendChild(controllerVideo.container);
  const titleExpand = createTitleExpandNode({ headline, pdfUrl, videoWidth, date });
  expand.append(titleExpand);

  return expand;
}

function createTitleExpandNode({ headline, pdfUrl, date }) {
  const expand = document.createElement('div');
  expand.className = 'event-marker__item--info';
  const titleWrapper = document.createElement('p');
  titleWrapper.className = 'event-marker__item--title-wrapper';
  expand.appendChild(titleWrapper);

  const titleSpan = document.createElement('span');
  titleSpan.className = 'event-marker__item--title';
  titleSpan.innerHTML = headline;
  titleWrapper.appendChild(titleSpan);

  const linkTranscript = document.createElement('a');
  linkTranscript.className = 'event-marker__item--transcription';
  linkTranscript.title = i18n.translate('transcripts');
  // linkTranscript.setAttribute('href', pdfUrl);
  // linkTranscript.setAttribute('target', '_blank');
  titleWrapper.appendChild(linkTranscript);

  CIQ.safeClickTouch(linkTranscript, linkNodeClicked);
  function linkNodeClicked() {
    window.open(pdfUrl, '_blank');
  }

  const iconSpan = document.createElement('span');
  iconSpan.className = 'fs-transcription';
  linkTranscript.appendChild(iconSpan);

  const time = document.createElement('time');
  time.innerHTML = formatShortDate(date);
  expand.appendChild(time);

  return expand;
}

function getVideo({ type = VIDEO_TYPE.HTML, videoId, title, videoUrl, appId, thumbnailUrl }) {
  let controllerVideo = null;
  switch (type) {
    case VIDEO_TYPE.VIMEO:
      controllerVideo = new VimeoVideo({ videoId, title, appId, thumbnailUrl, callback: onlyOneVideoPlaying });
      break;
    case VIDEO_TYPE.HTML:
      controllerVideo = new HtmlVideo({ videoUrl, thumbnailUrl, callback: onlyOneVideoPlaying });
      break;
    default:
      break;
  }
  return controllerVideo;
}
