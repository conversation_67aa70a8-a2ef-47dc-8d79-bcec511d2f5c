using GraphQL.Client.Abstractions;
using GraphQL.Client.Http;
using System;
using Microsoft.Extensions.DependencyInjection;
using Euroland.FlipIT.ShareGraph.API.Extensions;
using GraphQL.Client.Serializer.Newtonsoft;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;

namespace Euroland.FlipIT.ShareGraph.API.Infrastructure
{
  public class DefaultGraphQLClientFactory : IGraphQLClientFactory
  {
    private readonly IServiceProvider _serviceProvider;
    public DefaultGraphQLClientFactory(IServiceProvider serviceProvider)
    {
      _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }
    public IGraphQLClient CreateGraphQLClient(Uri endpoint)
    {
      if (endpoint == null)
      {
        throw new ArgumentNullException(nameof(endpoint));
      }

      if (!endpoint.IsAbsoluteUri)
      {
        throw new ArgumentException("Uri of endpoint must be absolute");
      }

      using (var scoped = _serviceProvider.CreateScope())
      {
        var context = scoped.ServiceProvider.GetRequiredService<Microsoft.AspNetCore.Http.IHttpContextAccessor>().HttpContext;
        var request = context.Request;
        var uriBuilder = new System.UriBuilder(endpoint);
        var isPreviewParam = context.IsPreviewMode() ? "&isPreview" : "";
        uriBuilder.Query = $"companycode={request.Query["companycode"]}&lang={request.Query["lang"]}&v={request.Query["v"]}{isPreviewParam}";
        return new GraphQLHttpClient(new GraphQLHttpClientOptions
        {
          EndPoint = uriBuilder.Uri
        },
        new NewtonsoftJsonSerializer(
          new JsonSerializerSettings
          {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
          })
        );
      }
    }
  }
}
