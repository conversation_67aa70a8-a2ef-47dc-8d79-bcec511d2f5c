import { appSettings } from '../../../appSettings';
import appConfig from '../../services/app-config';
import createExtraButton from './createExtraButton';

const ExtraButton = () => {
  const config = appConfig.get();
  const additionalOptions = config.chart.enabledAdditionalOptions;
  return (
    <div className="additional-options" id="AdditionalOptions">
      <div className="option-buttons">
        {additionalOptions.map(createExtraButton)}
      </div>
    </div>
  );
};

export default ExtraButton;
