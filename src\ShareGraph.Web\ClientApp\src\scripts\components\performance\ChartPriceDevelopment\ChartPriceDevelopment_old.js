import React, { useEffect, useRef, useState, Fragment } from 'react';
import { useSelector, useStore } from 'react-redux';
import { CIQ } from '../chartiq-import';
import { convertPercentDecimal, dynamicSort, getDeviceType } from '../../../helper';
import { DEVICES } from '../../../common';
import Legend from '../ChartPrice/Legend';

const ChartPriceDevelopment = ({ datas}) => {

  const store = useStore();
  const containerRef = useRef(null);
  const eventRef = useRef(null);
  const selectedTickerId = useSelector(state => state.tickers.selectedInstrumentId);
  const [series, setSeries] = useState([]);
  const [legends, setLegends] = useState(() => {
    var legends = datas.map((x) => {
      return { ...x, isSelected: x.instrumentId === selectedTickerId };
    });
    return legends;
  });
  const [chartEngine, setChartEngine] = useState(null);

  const cleanup = ()=> {
    if (chartEngine) {
      chartEngine.destroy();
      chartEngine.draw = () => {};
    }
  };

  // handle event when click a legend
  const clickedLegend = (id) => {
    const state = store.getState();
    if(id === state.tickers.selectedInstrumentId) return;
    //Update legend
    var legendUpdates = legends.map((x) => {
      var isSelected = x.instrumentId === id ? !x.isSelected : false;
      isSelected = x.instrumentId ===  state.tickers.selectedInstrumentId ? true : isSelected;
      return {
        ...x,
        isSelected: isSelected
      };
    });
    setLegends(legendUpdates);
    //Show highlight in chart
    var dataLengend = legends.find((x) => x.instrumentId === id);
    var { symbol } = dataLengend;
  //const hightlighted = chartEngine.chart.seriesRenderers[symbol].seriesParams[0].highlight || false;
  //chartEngine.chart.seriesRenderers[symbol].seriesParams[0].highlight = !hightlighted;
  //set all series is not current selected legend to assume only once line can be highlight at the same time
    for (let serie in chartEngine.chart.seriesRenderers) {
      if (serie === symbol) {
        chartEngine.chart.seriesRenderers[serie].seriesParams[0].highlight = !chartEngine.chart.seriesRenderers[serie].seriesParams[0].highlight;
      }else{
        chartEngine.chart.seriesRenderers[serie].seriesParams[0].highlight = false;
      }
    }
    highlightCurrentLegend(chartEngine);
  };



  // get series highlighted
  const getSeriesHighlighted = (chartEngine) => {
    var seriesHighlighted = null;
    for (var n in chartEngine.chart.seriesRenderers) {
      var serieObject = chartEngine.chart.seriesRenderers[n];
      if (!serieObject.params.highlightable) continue; //might not be necessary to check this here
      for (var i = 0; i < serieObject.seriesParams.length; i++) {
        const serie = serieObject.seriesParams[i];
        if(serie.highlight){
          seriesHighlighted = serie;
        }
      }
    }
    return seriesHighlighted;
  };

  //high light serie
  const highlightCurrentLegend = (chartEngine) => {
    let haveHighlightSerie = false;
    for (var n in chartEngine.chart.seriesRenderers) {
      var r2 = chartEngine.chart.seriesRenderers[n];
      if (!r2.params.highlightable) continue; //might not be necessary to check this here
      for (var m2 = 0; m2 < r2.seriesParams.length; m2++) {
        const serie = r2.seriesParams[m2];
        //if(serie.id != )
        if (serie.highlight) {
          serie.opacity = 1;
          haveHighlightSerie = true;
          // remove all marker to draw new main chart
          CIQ.Marker.removeByLabel(chartEngine, 'B');
          CIQ.Marker.removeByLabel(chartEngine, 'A');
          // create new marker for each point
          for (var i = 0; i < serie.data.length; i++) {
            let newNode = eventRef.current.cloneNode(true);
            newNode.id = null;
            newNode.innerHTML =
              convertPercentDecimal(serie.data[i].Close) + '%';

            new CIQ.Marker({
              stx: chartEngine,
              xPositioner: 'date',
              yPositioner: 'value',
              x: serie.data[i].DT,
              y: serie.data[i].Close,
              label: 'B',
              node: newNode
            });

            if (i === serie.data.length - 1) {
              let newNode = eventRef.current.cloneNode(true);
              newNode.id = null;
              newNode.innerHTML = serie.symbol;
              newNode.classList.remove('myEvents');
              newNode.style.color = serie.color;
              newNode.classList.add('main-share-name');
              new CIQ.Marker({
                stx: chartEngine,
                xPositioner: 'date',
                yPositioner: 'value',
                x: serie.data[i].DT,
                y: serie.data[i].Close,
                label: 'B',
                node: newNode
              });
            }
          }
          chartEngine.draw();

        } else {
          serie.opacity = 0.2;
        }
      }
    }
    if (!haveHighlightSerie) {
      // remove all marker to draw new main chart
      CIQ.Marker.removeByLabel(chartEngine, 'B');
      highlightMainSerie(chartEngine);
    }
  };

  // get instance chart
  const getInstanceChart = ()=>{
    let stxx = new CIQ.ChartEngine({
      controls:{
        'mSticky': null
      },
      allowScroll: false,
      allowZoom: false,
      container: containerRef.current,
      maximumCandleWidth: 500,
      longHoldTime :0,
      layout: {
        'chartType': 'vertex_line',
        'periodicity': 1,
        'interval': 'day'
      },
      preferences: {
        'currentPriceLine': false,
        'whitespace': 0
      },
      chart: {
        yAxis: {
          width: 20, // remove y axis
          initialMarginTop: 30, // adjust top margin - useful on very narrow charts.
          initialMarginBottom: 20, // adjust top margin - useful on very narrow charts.
          position: 'left',
          drawCurrentPriceLabel: false,
          drawSeriesPriceLabels: false,
          idealTickSizePixels: 200,
          priceFormatter: function (stx, panel, price, decimalPlaces) {
            var convertedPrice;
            // add our logic here to convert 'price' to 'convertedPrice'
            return convertPercentDecimal(price) + '%';
          },
          setBackground: function(stx, params){
            params.color = 'none';
          }
        },
        xAxis: {
          displayGridLines: true,
          initialMarginLeft: 100,
          fitLeftToRight: true,
          fitTight: true,
          formatter: function (
            labelDate,
            gridType,
            timeUnit,
            timeUnitMultiplier,
            defaultText
          ) {
            const mainData = datas.find((x) => x.instrumentId === selectedTickerId);
            const data = mainData.data.find(x => x.DT.toString() === labelDate.toString());
            // return data ? data.longLabel : defaultText;
              if (getDeviceType() === DEVICES.MD || getDeviceType() === DEVICES.SM || getDeviceType() === DEVICES.XS) {
                return data ? data.shortLabel : defaultText;
              } else {
                return data ? data.longLabel : defaultText;
              }
          }
        }
      },
      manageTouchAndMouse: true, // disable chart interactivity
      xaxisHeight: 50 // remove x axis
    });
    stxx.append('mousemoveinner', function () {
      const state = store.getState();
      const seriesHighlighted = getSeriesHighlighted(this);
      const legendUpdate = legends.map((x) => {
        let isSelected =
          (seriesHighlighted && seriesHighlighted.insId === x.instrumentId) ||
          x.instrumentId === state.tickers.selectedInstrumentId;
        return { ...x, isSelected: isSelected };
      });
      setLegends(legendUpdate);
      highlightCurrentLegend(this);
    });

    return stxx;
  };


  // config chart and add data for main chart, add series
    const initialChart = ()=>{
    const mainData = datas.find(x => x.instrumentId === selectedTickerId);
    var legendUpdates = legends.map((x) => {
      var isSelected = x.instrumentId === selectedTickerId ? !x.isSelected : false;
      isSelected = x.instrumentId === selectedTickerId ? true : isSelected;
      return {
        ...x,
        isSelected: isSelected
      };
    });
    setLegends(legendUpdates);
    if (!chartEngine) return;
    chartEngine.setStyle('stx_line_chart', 'width', 3);
    chartEngine.setStyle('stx_line_chart', 'color', mainData.color);
    chartEngine.append('draw', function () {
      if (
        this.chart.dataSet &&
        this.chart.dataSet.length &&
        !this.chart.standaloneBars
      ) {
        var context = this.chart.context;
        var panel = this.chart.panel;
        for (var i = 0; i < this.chart.dataSet.length; i++) {
          var quote = this.chart.dataSet[i];
          //var currentQuote = this.currentQuote('Close');
          if (!quote) continue;
          var price = quote.Close;
          var x = this.pixelFromTick(quote.tick, this.chart);
          if (this.chart.lastTickOffset) x = x + this.chart.lastTickOffset;
          var y = this.pixelFromPrice(price, panel);
          this.startClip();
          context.beginPath();
          context.arc(x, y, 4, 0, Math.PI * 2, false);
          context.strokeStyle = 'black';
          context.fillStyle = 'white';

          context.fill();
          context.stroke();
          this.endClip();
        }
      }
    });

    //Now load a chart with its data

    let dataSorting = mainData.data.sort(dynamicSort('DT'));
    const changePercentList = mainData.changePercentList.length;
    const maxList = changePercentList + 1;
    const minList = changePercentList - 1;

    chartEngine.loadChart(
      mainData.symbol, {
      //masterData: mainData.data,
      masterData: dataSorting,
      stretchToFillScreen: true,
      range: {
        dtLeft: new Date(2020, 0, 1),
        dtRight: new Date(2020, 0, maxList + 1)
      }
      }
    );
    //add series
    series.forEach((serie, idx) => {
      let { symbol, color, data } = serie;
      let dataSorting = data.sort(dynamicSort('DT'));
      let setinstrumentId = serie.instrumentId;
      chartEngine.addSeries(symbol, { color: color, data: dataSorting, insId: setinstrumentId, width: 1, isComparison: false, shareYAxis: true, permanent: true, opacity: 0.2});

    });
    highlightMainSerie(chartEngine);
  };

 // show legend name and value to points in chart
  const highlightMainSerie = (chartEngine) => {
    const state = store.getState();
        // remove all marker to draw new main chart
    CIQ.Marker.removeByLabel(chartEngine, 'A');
    const mainData = datas.find((x) => x.instrumentId === state.tickers.selectedInstrumentId );
    // create new marker for each point
    for (var i = 0; i < chartEngine.masterData.length; i++) {
      let data = chartEngine.masterData[i];
      let newNode = eventRef.current.cloneNode(true);
      newNode.id = null;
      newNode.innerHTML = convertPercentDecimal(data.Close) + '%';

      new CIQ.Marker({
        stx: chartEngine,
        xPositioner: 'date',
        yPositioner: 'above_candle',
        x: data.DT,
        y: data.Close,
        label: 'A',
        node: newNode
      });
    }
    if(chartEngine.masterData && chartEngine.masterData.length > 0){

      let lastData = chartEngine.masterData[chartEngine.masterData.length - 1];
      let newNode = eventRef.current.cloneNode(true);
      newNode.id = null;
      newNode.innerHTML = mainData.tickerName;
      newNode.classList.remove('myEvents');
      newNode.style.color = mainData.color;
      newNode.classList.add('main-share-name');
      new CIQ.Marker({
        stx: chartEngine,
        xPositioner: 'date',
        yPositioner: 'below_candle',
        x: lastData.DT,
        y: lastData.Close,
        label: 'A',
        node: newNode
      });

    }

    chartEngine.draw();
  };

  useEffect(() => {
    const state = store.getState();
    setSeries(datas.filter(x => x.instrumentId !== state.tickers.selectedTickerId));
  }, [datas, selectedTickerId]);

  useEffect(() => {
    if (!chartEngine) {
      setChartEngine(getInstanceChart());
    }else{
      // need remove all series to draw chart again.
      let seriesRenders = chartEngine.chart.seriesRenderers;
      if(Object.keys(seriesRenders).length){
        for (let serie in seriesRenders) {
          chartEngine.removeSeriesRenderer(seriesRenders[serie]);
          chartEngine.removeSeries(chartEngine.chart.series[serie]);
        }
      }
    }

  }, [selectedTickerId]);

  useEffect(() => {
    initialChart();
  }, [chartEngine, selectedTickerId]);

  useEffect(()=>{return cleanup;}, []);


return (
  <>
    <div className='chart-price'>
      <cq-context ref={containerRef}></cq-context>
    </div>
    <div id='stxEventPrototype' className='myEvents' ref={eventRef}></div>
    <LegendWrapper
      initialData={legends}
      selectedTickerId = {selectedTickerId}
      clickedLegend={clickedLegend}
    ></LegendWrapper>
  </>
);
};

const LegendWrapper = ({ initialData,selectedTickerId, clickedLegend }) => {
  return (
    <>
      <div className='legends'>
        {initialData !== null &&
          initialData.sort(dynamicSort('order')).map((item) => {
            return (
              <Fragment key={item.instrumentId}>
                <Legend
                  instid={item.instrumentId}
                  label={item.tickerName}
                  clickedLegend={clickedLegend}
                  isSelected={item.isSelected}
                  color={item.color}
                  isMainLegend={selectedTickerId === item.instrumentId}
                />
              </Fragment>
            );
          })}
      </div>
    </>
  );
};
export default ChartPriceDevelopment;
