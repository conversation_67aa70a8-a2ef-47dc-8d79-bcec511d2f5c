using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Studies
{
  public class Studies
  {
    public Macd MACD { get; set; }
    public Rsi RSI { get; set; }
    public BollingerBands BollingerBands { get; set; }
    public IchimokuClouds IchimokuClouds { get; set; }
    public Stochastics Stochastics { get; set; }
    public TotalReturn TotalReturn { get; set; }
    public Adx ADX { get; set; }
    public Volume Volume { get; set; }
    public Volume VolumeUnderlay { get; set; }  
  }
}
