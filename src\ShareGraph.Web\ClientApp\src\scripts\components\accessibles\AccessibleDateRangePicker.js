import i18n from '../../services/i18n';
import { DatePicker } from './DatePicker';

const AccessibleDateRangePicker = ({ startDate, endDate, onChange, startingDate }) => {
  const onChangeRangeDate = (type, date) => {
    onChange(type, date);
  };
  return (
    <table className="table-select-date">
      <tbody>
        <tr>
          <td>
            <span id="selectstartdate" className="detailed-setting-form__title">
              {i18n.translate('selectStartDate')}
            </span>
          </td>
          <td>
            <DatePicker
              id="accessible__date-picker-from"
              value={startDate}
              minDate={startingDate}
              maxDate={endDate}
              onSelect={date => onChangeRangeDate('startDate', date)}
              className="detailed-setting-form__date-picker"
            />
          </td>
        </tr>
        <tr>
          <td>
            <span id="selectenddate" className="detailed-setting-form__title">
              {i18n.translate('selectEndDate')}
            </span>
          </td>
          <td>
            <DatePicker
              id="accessible__date-picker-to"
              value={endDate}
              minDate={startDate || startingDate}
              onSelect={date => onChangeRangeDate('endDate', date)}
              className="detailed-setting-form__date-picker"
            />
          </td>
        </tr>
      </tbody>
    </table>
  );
};

export default AccessibleDateRangePicker;
