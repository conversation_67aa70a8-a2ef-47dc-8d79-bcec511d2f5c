<div class="ticker__item-inner ticker__item-inner--multiple-tickers-1 ticker__item-inner--{instrumentId}">
  <div class="ticker__col">
    <p class="ticker__heading">
      <span class="ticker__name">{ticker}</span>
      <span class="ticker__market-abbreviation">({marketAbbreviation})</span>
    </p>
    <p class="ticker__price">
      <strong class="ticker__price-number">{last}</strong>
      <span class="ticker__currency-code">{currencyCodeStr}</span>
    </p>
    <p class="ticker__change">
      <i class="fs fs-triangle-up"></i>
      <i class="fs fs-triangle-down"></i>
      <span class="ticker__change-value">{change} ({changePercentage}%)</span>
    </p>
    <p class="status-closed">
      <i class="fs fs-close-radio"></i>
      <span class="market-name">{marketName}</span>
      <span class="market-status">{marketCloseLabel}.</span>
      <span class="market-openTime">{marketWillOpenLabel} {dateTimeToMarketOpen}</span>
    </p>
    <p class="status-opened">
      <i class="fs fs-checked-radio"></i>
      <span class="market-name">{marketName} {marketOpenedLabel}. {marketWillCloseLabel} {timeToCloseMarket}</span>
    </p>
    <!-- <p class="status-openIn">
      <i class="fs fs-checked-radio"></i>
      <span class="market-openTime">{marketName} {marketOpenInLabel} {dateTimeToMarketOpen}</span>
    </p> -->
  </div>
</div>
