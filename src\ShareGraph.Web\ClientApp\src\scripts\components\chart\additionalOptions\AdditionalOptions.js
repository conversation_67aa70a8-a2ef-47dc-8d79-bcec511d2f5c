import { useState, useContext } from 'react';
import { AppContext } from '../../../AppContext';
import i18n from '../../../services/i18n';
import ToolTip from '../../commons/ToolTip';

export const AdditionalOptions = ({ additionalOptionsTypes }) => {
  const additionalOptionsEnabled = () => {
    return additionalOptionsTypes.length > 0 && additionalOptionsTypes[0];
  };

  const ADDITIONAL_OPTIONS_BUTTON_TYPE = {
    EXCEL_TYPE: 'EXCEL',
    PRINT_TYPE: 'PRINT',
    EXPORT_TYPE: 'IMAGE',
    SHARE_TYPE: 'SHARE'
  };

  const additionalOptionsFormatTypes = [
    {
      type: ADDITIONAL_OPTIONS_BUTTON_TYPE.EXCEL_TYPE,
      icon: 'fs-excel',
      isDefaultSelected: ADDITIONAL_OPTIONS_BUTTON_TYPE.EXCEL_TYPE
    },
    {
      type: ADDITIONAL_OPTIONS_BUTTON_TYPE.PRINT_TYPE,
      icon: 'fs-print',
      isDefaultSelected: ADDITIONAL_OPTIONS_BUTTON_TYPE.PRINT_TYPE
    },
    {
      type: ADDITIONAL_OPTIONS_BUTTON_TYPE.EXPORT_TYPE,
      icon: 'fs-export',
      isDefaultSelected: ADDITIONAL_OPTIONS_BUTTON_TYPE.EXPORT_TYPE
    },
    {
      type: ADDITIONAL_OPTIONS_BUTTON_TYPE.SHARE_TYPE,
      icon: 'fs-share',
      isDefaultSelected: ADDITIONAL_OPTIONS_BUTTON_TYPE.SHARE_TYPE
    }
  ];

  const [additionalOptionsType, setAdditionalOptionsType] = useState(additionalOptionsTypes.length > 0 && additionalOptionsTypes[0]);

  const handleOptionsClickType = (item) => {
    setAdditionalOptionsType(item);
  };

  const getOptionsClickTypeTranslation = type => {
    switch (type) {
      case ADDITIONAL_OPTIONS_BUTTON_TYPE.EXCEL_TYPE:
        return i18n.translate('excelOption');
      case ADDITIONAL_OPTIONS_BUTTON_TYPE.PRINT_TYPE:
        return i18n.translate('printOption');
      case ADDITIONAL_OPTIONS_BUTTON_TYPE.EXPORT_TYPE:
        return i18n.translate('exportOption');
      case ADDITIONAL_OPTIONS_BUTTON_TYPE.SHARE_TYPE:
        return i18n.translate('shareOption');
    }
  };

  function openShareDialog() {

  }

  return (
    <>
      {
        additionalOptionsEnabled() &&
        <div className='option-buttons'>
          {
            additionalOptionsTypes.map((item, index) => {
              return (
                <button className={`tooltip-wrapper option-button option-button--${item.toLowerCase()}`} onClick={() => handleOptionsClickType(item)}
                  aria-label={item}
                  key={index}
                  type='button'>
                  <i className={`fs ${additionalOptionsFormatTypes[index].icon}`}></i>
                  <ToolTip>
                    {getOptionsClickTypeTranslation(item)}
                  </ToolTip>
                </button>
              );
            })
          }
        </div>
      }
    </>
  );
};
