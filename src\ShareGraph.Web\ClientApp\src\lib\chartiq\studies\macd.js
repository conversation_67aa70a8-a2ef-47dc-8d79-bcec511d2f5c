import { CIQ } from 'chartiq/js/components';
import { appSettings } from '../../../appSettings';

CIQ.Euroland.Studies.displayHistogramWithSeries = function (stx, sd, quotes) {
  //var panel = stx.panels[sd.panel];
  var underlay = 0.5;

  if (sd.underlay) {
    underlay = 0.3;
  }

  CIQ.Studies.createHistogram(stx, sd, quotes, false, underlay);
  CIQ.Studies.displaySeriesAsLine(stx, sd, quotes);
};

CIQ.Euroland.Studies.calculateMACD = function (stx, sd) {
  var quotes = sd.chart.scrubbed,
    inputs = sd.inputs,
    name = sd.name,
    period,
    dataLength,
    field,
    maType,
    signalMAType,
    i,
    quote,
    histogramField, // Used for histogram values #ref: https://documentation.chartiq.com/CIQ.Studies.html#.displayHistogramWithSeries
    macdField,
    macd1Field,
    macd2Field,
    signalField;

  if (!sd.macd1Days) {
    sd.macd1Days = parseFloat(inputs['Fast MA Period']);
  }

  if (!sd.macd2Days) {
    sd.macd2Days = parseFloat(inputs['Slow MA Period']);
  }

  if (!sd.signalDays) {
    sd.signalDays = parseFloat(inputs['Signal Period']);
  }

  if (!sd.days) {
    sd.days = Math.max(sd.macd1Days, sd.macd2Days, sd.signalDays);
  }

  dataLength = quotes.length;
  maType = inputs['Moving Average Type'];
  signalMAType = inputs['Signal MA Type'];

  macdField = 'MACD ' + name;
  macd1Field = '_MACD1 ' + name;
  macd2Field = '_MACD2 ' + name;
  signalField = 'Signal ' + name;

  if (dataLength < sd.days + 1) {
    if (!sd.overlay) sd.error = true;
    return;
  }

  field = sd.inputs.Field;
  if (!field || field === 'field') {
    field = 'Close';
  }

  if (!maType) {
    maType = 'exponential';
  }

  CIQ.Studies.MA(maType, sd.macd1Days, field, 0, '_MACD1', stx, sd);
  CIQ.Studies.MA(maType, sd.macd2Days, field, 0, '_MACD2', stx, sd);

  period = Math.max(sd.startFrom, sd.days - 1);

  for (i = period; i < dataLength; i++) {
    quote = quotes[i];
    if ((quote[macd1Field] || quote[macd1Field] === 0) && (quote[macd2Field] || quote[macd2Field] === 0)) {
      quote[macdField] = quote[macd1Field] - quote[macd2Field];
    }
  }

  if (!signalMAType) {
    signalMAType = 'exponential';
  }

  CIQ.Studies.MA(signalMAType, sd.signalDays, macdField, 0, 'Signal', stx, sd);

  histogramField = name + '_hist';

  for (i = period; i < dataLength; i++) {
    quote = quotes[i];

    if (!quote[signalField] && quote[signalField] !== 0) {
      continue;
    }

    quote[histogramField] = quote[macdField] - quote[signalField];
  }

  sd.outputMap[histogramField] = '';
};


const studyName = CIQ.I18N.translate('MACD');

CIQ.Euroland.Studies.createStudy(studyName, {
  name: studyName,
  calculateFN: CIQ.Euroland.Studies.calculateMACD,
  seriesFN: CIQ.Euroland.Studies.displayHistogramWithSeries,
  inputs: { 'Fast MA Period': 12, 'Slow MA Period': 26, 'Signal Period': 9 },
  outputs: { MACD: 'auto', Signal: '#FF0000', 'Increasing Bar': '#00DD00', 'Decreasing Bar': '#FF0000' }
});

export { CIQ };
