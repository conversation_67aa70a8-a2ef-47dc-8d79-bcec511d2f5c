/**
 * @description
 * Fetch ticker strategy
 * xml settings can enable some instrument realtime or not
 * that config make some case when we fetch ticker data
 *
 * xml settings can tell what instrument is enable realtime update
 * but that not enough
 * lastUpdatedDate tell when was updated price of the field open
 * when we load first data of instrument and lastUpdatedDate is not today
 * if we take close price on today and calc with open price last day
 * this action will make wrong change, percent change field
 *
 * The way we update data
 * instrument treat as realtime:
 *  - last price update from trade socket
 *  - bid, ask update from fetch loop
 *  - another field update from snapshot socket
 *
 * instrument not realtime:
 *  - all field update from fetch loop
 */

import { getTickerSetting, getTickersSetting } from '../configs/configuration-app';
import { mergeObjectWithDefinedProps } from '../utils/common';
import isSameDayWithLastTick from '../real-time/isSameDayWithLastTick';
import { extractUniquePlaceholders, getCurrencyText, getDataTicker } from '../helper';
import * as dayjs from 'dayjs';
import {client, clientRT} from '../services/graphql-client';
import {buildOptimizedTickerQuery, normalizeGraphqlData, resolveFieldDependencies, TICKER_FIELDS_CONFIG} from '../graphql-queries/tickerQuery';
import { trimAbbreviation } from '../utils';
import { get } from 'es-toolkit/compat';
import appConfig from '../services/app-config';
import { apiFieldKeyValue } from '../templateApiField';


export function getRealTimeAndNotInstruments(instrumentIds = []) {
  const instrumentsSetting = getTickersSetting();
  const realTimeIds = [];
  const notRealTimeIds = [];

  instrumentIds.forEach(insId => {
    const isRealTimeInstrument = instrumentsSetting[insId]?.isRT;
    if (isRealTimeInstrument) realTimeIds.push(insId);
    else notRealTimeIds.push(insId);
  });

  return {
    realTimeIds,
    notRealTimeIds
  };
}

/**
 *
 * @param {import('../graphql-queries/tickerQuery').Instrument} item
 */
function translateNewResToOldRes(item) {
  const shareName = trimAbbreviation(item.shareName);
  const isOpened = get(item, 'market.status.isOpened', false);
  const remainingTime = get(item, 'market.status.remainingTime', 0);
  const marketStatus = isOpened ? 'Open' : 'Close';
  const volumeHistories = get(item, 'volumeHistories.nodes', []).slice(1); // always remove first record.

  const countdownToTheOpeningBell = isOpened ? null : remainingTime;
  const countdownToTheClosingBell = isOpened ? remainingTime : null;


  const countdownToTheOpeningBellDate = countdownToTheOpeningBell ? dayjs()
  .startOf('minute')
  .add(countdownToTheOpeningBell, 'minute')
  .toISOString() : undefined;

  const countdownToTheClosingBellDate = countdownToTheClosingBell ? dayjs()
  .startOf('minute')
  .add(countdownToTheClosingBell, 'minute')
  .toISOString() : undefined;

  const tickerValues = Object.keys(TICKER_FIELDS_CONFIG).reduce((acc, field) => {
    if(!TICKER_FIELDS_CONFIG[field].selector) return acc;
    return {
      ...acc,
      [field]: TICKER_FIELDS_CONFIG[field].selector(item)
    };
  }, {});

  return {
    ...tickerValues,
    marketName: get(item, 'market.translation.value', ''),
    shareName,
    ticker: item.symbol,
    instrumentId: item.id,
    normalDailyOpen: get(item, 'market.openTimeLocal', '00:00'),
    normalDailyClose: get(item, 'market.closeTimeLocal', '23:59'),
    businessDaysStoT: get(item , 'market.businessDaysStoT'),
    marketTimeZone: get(item, 'market.timezoneName'),
    currencyCode: get(item, 'currency.code'),
    marketStatus,
    countdownToTheOpeningBell,
    countdownToTheOpeningBellDate,
    countdownToTheClosingBell,
    countdownToTheClosingBellDate,
    open: get(item , 'currentPrice.open'),
    lastUpdatedDate: get(item, 'currentPrice.date'),
    currency: get(item, 'currency.name'),
    bid: get(item , 'currentPrice.bid'),
    ask: get(item, 'currentPrice.ask'),
    startingDate: get(item, 'historicals.nodes[0].dateTime'),
    volume: get(item, 'currentPrice.volume'),
    officialClose: get(item, 'currentPrice.officialClose'),
    officialCloseDate: get(item, 'currentPrice.officialCloseDate'),
    marketAbbreviation: get(item, 'market.abbreviation'),
    prevClose: get(item, 'currentPrice.tickerData.prevClose'),
    last: get(item, 'currentPrice.tickerData.last'),
    change: get(item, 'currentPrice.tickerData.change'),
    changePercentage: get(item, 'currentPrice.tickerData.changePercentage'),
    low: get(item, 'currentPrice.low'),
    high: get(item, 'currentPrice.high'),
    percent52W: get(item, 'performance.changePercentage'),
    low52W: get(item, 'performance.lowest'),
    high52W: get(item, 'performance.highest'),
    volumeChange: get(item, 'currentPrice.volume') ? get(item, 'currentPrice.volume')/(volumeHistories.reduce((acc, item) => acc + item.volume, 0)/volumeHistories.length)  : 0,
    fetchedTime: new Date().toISOString()
  };
}

function getApiTickerFields () {
  let template = getTickerSetting().graphTickerTemplate;
  const tableTickerTemplate = getTickerSetting().tableTickerTemplate;
  if(tableTickerTemplate) {
    template += ' ' + tableTickerTemplate;
  }
  return  extractUniquePlaceholders(template, apiFieldKeyValue);
}

/**
 *
 * @param {string[]} instrumentIds
 * @param {string} [toCurrency]
 * @returns
 */
async function getTickersData(instrumentIds, toCurrency) {
  if(!instrumentIds.length) {
    return new Promise((_, reject) => {
      reject('Not any instrument id provided.');
    });
  }

  const instrumentsSetting = getTickersSetting();

  const rtIds = {
    adj: [],
    notAdj: [],
    isExist() {
      return this.adj.length > 0 || this.notAdj.length > 0;
    }
  };

  const notRtIds = {
    adj: [],
    notAdj: [],
    isExist() {
      return this.adj.length > 0 || this.notAdj.length > 0;
    }
  };

  for(const id of instrumentIds) {
    const instrumentSetting = instrumentsSetting[id];

    if(instrumentSetting.isRT) {
      if(instrumentSetting.enabledAdjustPrice) {
        rtIds.adj.push(id);
      } else {
        rtIds.notAdj.push(id);
      }
    } else {
      if(instrumentSetting.enabledAdjustPrice) {
        notRtIds.adj.push(id);
      } else {
        notRtIds.notAdj.push(id);
      }
    }
  }

  const promiseAll = [];

  const fieldRequire = getApiTickerFields();

  const fields = resolveFieldDependencies(fieldRequire);
  const queryString = buildOptimizedTickerQuery(fields);

  if(rtIds.notAdj.length > 0) promiseAll.push(
    clientRT.query(queryString, { ids: rtIds.notAdj, toCurrency, adjClose: false})
  );

  if(rtIds.adj.length > 0) promiseAll.push(
    clientRT.query(queryString, { ids: rtIds.adj, toCurrency, adjClose: true})
  );

  if(notRtIds.notAdj.length > 0) promiseAll.push(
    client.query(queryString, { ids: notRtIds.notAdj, toCurrency, adjClose: false})
  );

  if(notRtIds.adj.length > 0) promiseAll.push(
    client.query(queryString, { ids: notRtIds.adj, toCurrency, adjClose: true })
  );

  const data = (await Promise.all(promiseAll)).map(result => result?.data.instrumentByIds?.map(item => normalizeGraphqlData(fieldRequire, item)) ?? []).flat();

  return data;

  // const [realTimeData, notRealTimeData] = await Promise.all([
  //   rtIds.isExist() ? clientRT.query(TICKER_INIT_QUERY, { ids: rtIds.notAdj, toCurrency, adjIds: rtIds.adj }) : Promise.resolve(),
  //   notRtIds.isExist() ? client.query(TICKER_INIT_QUERY, { ids: notRtIds.notAdj, toCurrency, adjIds: notRtIds.adj }) : Promise.resolve()
  // ]);


  // const realtime = [
  //   ...realTimeData?.data.instrumentByIds?.map(translateNewResToOldRes) ?? [],
  //   ...realTimeData?.data.adjInstrumentByIds?.map(translateNewResToOldRes) ?? []
  // ];

  // const notRealTime = [
  //   ...notRealTimeData?.data.instrumentByIds?.map(translateNewResToOldRes) ?? [],
  //   ...notRealTimeData?.data.adjInstrumentByIds?.map(translateNewResToOldRes) ?? []
  // ];

  // return {
  //   data: {
  //     realtime,
  //     notRealTime
  //   }
  // };
}


/**
 *
 * @param {{
 *  additionalRealtimeFields: Array<string | number>,
 *  realtimeIds: Array<string | number>,
 *  notRealTimeIds: Array<string | number>, apiFields: string[],
 *  toCurrency: CurrencyOption['code']
 * }} param0
 */
async function fetchUpdateTickersApiNewQuery({
  additionalRealtimeFields,
  realtimeIds,
  notRealTimeIds,
  toCurrency
}) {
  if (
    !notRealTimeIds.length &&
    !realtimeIds.length &&
    !additionalRealtimeFields.length
  ) {
    return new Promise((_, reject) => {
      reject('Not any instrument id provided.');
    });
  }

  const instrumentsSetting = getTickersSetting();

  const realTime = {
    ids: realtimeIds.filter((id) => !instrumentsSetting[id].enabledAdjustPrice),
    adjIds: realtimeIds.filter(
      (id) => instrumentsSetting[id].enabledAdjustPrice
    )
  };

  const notRealTime = {
    ids: notRealTimeIds.filter(
      (id) => !instrumentsSetting[id].enabledAdjustPrice
    ),
    adjIds: notRealTimeIds.filter(
      (id) => instrumentsSetting[id].enabledAdjustPrice
    )
  };

  const promiseAll = [];

  const fieldRequire = getApiTickerFields();

  const fields = resolveFieldDependencies(fieldRequire);
  const queryString = buildOptimizedTickerQuery(fields, 'TickerUpdate');


  if (realTime.ids.length > 0 || additionalRealtimeFields.length > 0)
    promiseAll.push(
      clientRT.query(queryString, {
        ids: realTime.ids,
        adjClose: false,
        additionalRealtimeIds: additionalRealtimeFields,
        toCurrency
      })
    );

  if (realTime.adjIds.length > 0)
    promiseAll.push(
      clientRT.query(queryString, {
        ids: realTime.adjIds,
        adjClose: true,
        additionalRealtimeIds: [],
        toCurrency
      })
    );

  if (notRealTime.ids.length > 0)
    promiseAll.push(
      client.query(queryString, {
        ids: notRealTime.ids,
        adjClose: false,
        additionalRealtimeIds: [],
        toCurrency
      })
    );

  if (notRealTime.adjIds.length > 0)
    promiseAll.push(
      client.query(queryString, {
        ids: notRealTime.adjIds,
        adjClose: true,
        additionalRealtimeIds: [],
        toCurrency
      })
    );

  const results = await Promise.all(promiseAll);

  const instruments = results
    .map(
      (result) =>
        result?.data.instrumentByIds?.map((item) =>
          normalizeGraphqlData(fieldRequire, item)
        ) ?? []
    )
    .flat();

  const additionalRealtime =
    results?.[0].data?.additionalRealtime?.map((item) => ({
      instrumentId: item.id,
      officialClose: item.currentPrice.officialClose,
      officialCloseDate: item.currentPrice.officialCloseDate
    })) ?? [];

  return {
    instruments,
    additionalRealtime
  };
  // const [dataRealTime, dataNotRealTime] = await Promise.all([
  //   (realtimeIds.length || additionalRealtimeFields.length) ? clientRT.query(TICKER_UPDATE_QUERY, {
  //     ids: realtimeIds.filter(id => !instrumentsSetting[id].enabledAdjustPrice),
  //     adjIds: realtimeIds.filter(id => instrumentsSetting[id].enabledAdjustPrice),
  //     additionalRealtimeIds: additionalRealtimeFields,
  //     toCurrency
  //   }) : Promise.resolve(),
  //   notRealTimeIds.length ? client.query(TICKER_UPDATE_QUERY, {
  //     ids: notRealTimeIds.filter(id => !instrumentsSetting[id].enabledAdjustPrice),
  //     adjIds: notRealTimeIds.filter(id => instrumentsSetting[id].enabledAdjustPrice),
  //     additionalRealtimeIds: [],
  //     toCurrency
  //   }) : Promise.resolve()
  // ]);

  // const realtime = [
  //   ...dataRealTime?.data.instrumentByIds?.map(translateNewResToOldRes) ?? [],
  //   ...dataRealTime?.data.adjInstrumentByIds?.map(translateNewResToOldRes) ?? []
  // ];
  // const notRealTime = [
  //   ...dataNotRealTime?.data.instrumentByIds?.map(translateNewResToOldRes) ?? [],
  //   ...dataNotRealTime?.data.adjInstrumentByIds?.map(translateNewResToOldRes) ?? []
  // ];
  // return {
  //   data: {
  //     notRealTime,
  //     realtime,
  //     additionalRealtime: dataRealTime?.data.additionalRealtime?.map(item => ({
  //       instrumentId: item.id,
  //       officialClose: item.currentPrice.officialClose,
  //       officialCloseDate: item.currentPrice.officialCloseDate
  //     })) ?? []
  //   }
  // };
}

// Ticker Actions
export const FETCH_TICKERS_BEGIN = 'FETCH_TICKERS_BEGIN';
export const FETCH_TICKERS_SUCCESS = 'FETCH_TICKERS_SUCCESS';
export const FETCH_TICKERS_FAILURE = 'FETCH_TICKERS_FAILURE';
export const REFRESH_TICKER_BEGIN = 'REFRESH_TICKER_BEGIN';
export const REFRESH_TICKER_SUCCESS = 'REFRESH_TICKER_SUCCESS';
export const REFRESH_TICKER_FAILURE = 'REFRESH_TICKER_FAILURE';
export const UPDATE_TICKER_OFFICIAL_CLOSE = 'UPDATE_TICKER_OFFICIAL_CLOSE';
export const TICKER_SELECTED_CHANGE_SUCCESS = 'TICKER_SELECTED_CHANGE_SUCCESS';

export const fetchTickersBegin = () => ({
  type: FETCH_TICKERS_BEGIN
});

export const fetchTickersSuccess = (instruments = []) => {
  const instrumentsSetting = getTickersSetting();
  return (dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);

    return dispatch({
      type: FETCH_TICKERS_SUCCESS,
      payload: {
        instruments: instruments.map(ins =>
          mergeObjectWithDefinedProps(
            {
              ...ins,
              originalCurrency: ins.currencyCode
            },
            {
              ...instrumentsSetting[ins.instrumentId],
              currencyCode: selectedCurrencyText || instrumentsSetting[ins.instrumentId].currencyCode
            }
          )
        )
      }
    });
  };
};

export const fetchTickersFailure = (error) => {
  console.error(error);
  return ({
    type: FETCH_TICKERS_FAILURE,
    payload: { error }
  });
};

export const selectTickerSuccess = (instrumentId) => ({
  type: TICKER_SELECTED_CHANGE_SUCCESS,
  payload: { instrumentId }
});

export const refreshTickerBigin = () => ({
  type: REFRESH_TICKER_BEGIN
});

export const refreshTickerSucess = (instruments = []) => {
  const instrumentsSetting = getTickersSetting();
  return (dispatch, getState) => {
    const { currency } = getState().currency;
    const selectedCurrencyText = getCurrencyText(currency);
    return dispatch({
      type: REFRESH_TICKER_SUCCESS,
      payload: {
        instruments: instruments.map(ins =>
          mergeObjectWithDefinedProps(
            {
              ...ins,
              originalCurrency: ins.currencyCode
            },
            {
              ...instrumentsSetting[ins.instrumentId],
              currencyCode: selectedCurrencyText || instrumentsSetting[ins.instrumentId].currencyCode
            }
          )
        )
      }
    });
  };

};

export const refreshTickersFailure = (error) => ({
  type: REFRESH_TICKER_FAILURE,
  payload: { error }
});

export function fetchTickersPopout(instrumentIds = [], mainTickerId) {
  return async (dispatch, getState) => {
    dispatch(fetchTickersBegin());

    if (!instrumentIds || !instrumentIds.length) {
      const ids = getState().tickers.instruments.map((ins) => ins.instrumentId);
      instrumentIds = [...ids];
    }

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    try {
      const instruments = await getTickersData(instrumentIds, toCurrency);
      dispatch(fetchTickersSuccess(instruments));
      dispatch(mainTickerId);
    } catch (error) {
      console.log(error);
      dispatch(fetchTickersFailure(error));
    }
  };
}

export function fetchTickers(instrumentIds = []) {
  return async (dispatch, getState) => {
    dispatch(fetchTickersBegin());

    if (!instrumentIds || !instrumentIds.length) {
      const ids = getState().tickers.instruments.map((ins) => ins.instrumentId);
      instrumentIds = [...ids];
    }

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    try {
      const instruments = await getTickersData(instrumentIds, toCurrency);
      dispatch(fetchTickersSuccess(instruments));
      return instruments;
    } catch (error) {
      // console.log(error);
      dispatch(fetchTickersFailure(error));
    }
  };
}

/**
 *
 * @param {number} instrumentId
 * @param {import('redux').Store<any, import('redux').AnyAction>} store
 * @returns
 */
export function isInstrumentUpdateFromSocket (instrumentId, store) {
  const instrumentsSetting = getTickersSetting();
  const now = new Date().toISOString();
  return instrumentsSetting[instrumentId]?.isRT && isSameDayWithLastTick({
    stockId: instrumentId,
    date: now,
    store
  });
}

export function refreshTickers() {
  return async (dispatch, getState) => {
    const instrumentsSetting = getTickersSetting();
    const tickerIds = /** @type {number[]} */(getState().tickers.instruments.map((ins) => ins.instrumentId));
    const updateStrategy = /** @type {Record<string | number, 'fetch' | 'socket'>} */(getState().tickers.updateStrategy);

    const notRealTimeIds = tickerIds.filter(id => !instrumentsSetting[id].isRT);
    const realtimeIds = tickerIds.filter(id => instrumentsSetting[id].isRT && updateStrategy[id] !== 'socket');
    const additionalRealtimeFields = tickerIds.filter(id => instrumentsSetting[id].isRT && updateStrategy[id] === 'socket');

    dispatch(refreshTickerBigin());

    const { currency } = getState().currency;
    const toCurrency = currency?.code;

    try {
      const { instruments,  additionalRealtime } = await fetchUpdateTickersApiNewQuery({
        notRealTimeIds,
        realtimeIds,
        additionalRealtimeFields,
        toCurrency
      });
      dispatch(refreshTickerSucess(instruments));
      additionalRealtime.forEach(item => {
        const { instrumentId, officialClose, officialCloseDate } = item;
        if(officialClose == null || officialCloseDate == null) return;
        dispatch(updateOfficialClose(instrumentId, officialClose, officialCloseDate));
      });
    } catch (error) {
      console.log(error);
      dispatch(refreshTickersFailure(error));
    }
  };
}

export function selectTicker(instrumentId) {
  return (dispatch, getState) => {
    const state = getState().tickers;
    const instruments = state.instruments;
    const previousSelected = state.selectedInstrumentId;

    if (previousSelected === instrumentId) {
      return;
    }
    if (instruments.filter((ins) => ins.instrumentId === instrumentId).length) {
      dispatch(selectTickerSuccess(instrumentId));
    } else {
      // TODO: Notify out that no ticker matches with provided `${instrumentId}`
    }
  };
}

/**
 *
 * @param {number} officialClose
 * @param {string} officialCloseDate
 * @param {number} instrumentId
 * @returns
 */
export function updateOfficialClose(instrumentId, officialClose, officialCloseDate) {
  return {
    type: UPDATE_TICKER_OFFICIAL_CLOSE,
    payload: { officialClose, officialCloseDate, instrumentId }
  };
}
