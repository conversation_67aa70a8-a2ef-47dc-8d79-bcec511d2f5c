import { CIQ } from '../chartiq-import';

CIQ.Drawing.BaseTwoPoint.prototype.measure = function () {
  var stx = this.stx;

  if (this.p0 && this.p1) {
    stx.setMeasure(
      this.p0[1],
      this.p1[1],
      this.p0[0],
      this.p1[0],
      true,
      this.name
    );
    var mSticky = stx.controls.mSticky;
    var mStickyInterior =
      mSticky && mSticky.querySelector('.mStickyInterior');
    if (mStickyInterior) {
      var lines = [];
      lines.push(CIQ.capitalize(stx.translateIf(this.name)));
      if (this.getYValue)
        lines.push(this.field || stx.defaultPlotField || 'Close');
      lines.push(mStickyInterior.innerHTML);
      mStickyInterior.innerHTML = lines.join('<br>');
    }
  }
};
