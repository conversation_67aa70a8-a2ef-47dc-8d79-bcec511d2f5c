using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Studies
{
  public class IchimokuClouds
  {
    public string ConversionLine { get; set; }
    public string BaseLine { get; set; }
    public string LeadingSpanA { get; set; }
    public string LeadingSpanB { get; set; }
    public string LeadingSpan { get; set; }
  }
}
