import { useContext } from 'react';
import { AppContext } from '../AppContext';

export const useSharePriceOrder = () => {
  const settings = useContext(AppContext);

  const instrumentSettings = settings.instruments.map(x => ({ id: x.id, order: x.order }));
  const peersSettings = settings.peers.peers.map(x => ({ id: x.id, order: 10000 + x.order }));
  const indicesSettings = settings.indices.indices.map(x => ({ id: x.id, order: 20000 + x.order }));
  const sharePriceOrders = [...instrumentSettings, ...indicesSettings, ...peersSettings].reduce(
    (s, c) => ({ ...s, [c.id]: c.order }),
    {}
  );
  return { sharePriceOrders };
};
