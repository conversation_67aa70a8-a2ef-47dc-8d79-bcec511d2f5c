/**!
 *	8.7.0
 *	Generation date: 2023-06-01T14:36:07.359Z
 *	Client name: euroland as
 *	Package Type: Technical Analysis e98f22c
 *	License type: annual
 *	Expiration date: "2024/06/01"
 *	Domain lock: ["127.0.0.1","localhost","euroland.com","eurolandir.cn","eurolandir.com"]
 */

/***********************************************************!
 * Copyright by ChartIQ, Inc.
 * Licensed under the ChartIQ, Inc. Developer License Agreement https://www.chartiq.com/developer-license-agreement
*************************************************************/
/*************************************! DO NOT MAKE CHANGES TO THIS LIBRARY FILE!! !*************************************
* If you wish to overwrite default functionality, create a separate file with a copy of the methods you are overwriting *
* and load that file right after the library has been loaded, but before the chart engine is instantiated.              *
* Directly modifying library files will prevent upgrades and the ability for ChartIQ to support your solution.          *
*************************************************************************************************************************/
/* eslint-disable no-extra-parens */


/* eslint-disable */ /* jshint ignore:start */ /* ignore jslint start */
Q_ezb[623698]=(function(){var K=2;for(;K !== 9;){switch(K){case 2:K=typeof globalThis === '\x6f\u0062\x6a\x65\u0063\u0074'?1:5;break;case 5:var G;try{var r=2;for(;r !== 6;){switch(r){case 3:throw "";r=9;break;case 4:r=typeof yuW16 === '\x75\u006e\u0064\u0065\u0066\u0069\u006e\x65\u0064'?3:9;break;case 9:delete G['\u0079\u0075\u0057\x31\u0036'];var q=Object['\u0070\x72\u006f\u0074\u006f\u0074\x79\u0070\x65'];delete q['\u0050\u0049\u004b\u0038\x66'];r=6;break;case 2:Object['\x64\x65\x66\u0069\u006e\u0065\u0050\u0072\x6f\x70\u0065\x72\x74\x79'](Object['\u0070\u0072\u006f\u0074\u006f\x74\x79\x70\x65'],'\u0050\u0049\u004b\x38\x66',{'\x67\x65\x74':function(){var W=2;for(;W !== 1;){switch(W){case 2:return this;break;}}},'\x63\x6f\x6e\x66\x69\x67\x75\x72\x61\x62\x6c\x65':true});G=PIK8f;G['\x79\u0075\u0057\x31\x36']=G;r=4;break;}}}catch(g){G=window;}return G;break;case 1:return globalThis;break;}}})();w1o09U(Q_ezb[623698]);Q_ezb[416141]=true;Q_ezb[557732]=520;Q_ezb.n5=function(){return typeof Q_ezb[427804].Q7cRMN7 === 'function'?Q_ezb[427804].Q7cRMN7.apply(Q_ezb[427804],arguments):Q_ezb[427804].Q7cRMN7;};Q_ezb.A6=function(){return typeof Q_ezb[2716].i3UVQCI === 'function'?Q_ezb[2716].i3UVQCI.apply(Q_ezb[2716],arguments):Q_ezb[2716].i3UVQCI;};function w1o09U(p6){function f1(Y7){var j$=2;for(;j$ !== 5;){switch(j$){case 2:var S7=[arguments];return S7[0][0];break;}}}function m2(o5){var Y_=2;for(;Y_ !== 5;){switch(Y_){case 2:var b$=[arguments];return b$[0][0].Function;break;}}}var t_=2;for(;t_ !== 152;){switch(t_){case 2:var u8=[arguments];u8[4]="";u8[4]="klT";u8[8]="";t_=3;break;case 100:u8[39]+=u8[55];u8[46]=u8[78];u8[46]+=u8[82];u8[46]+=u8[61];t_=96;break;case 127:q3(o_,"replace",u8[40],u8[71]);t_=126;break;case 47:u8[30]="";u8[30]="I";u8[63]="";u8[63]="G";t_=64;break;case 72:u8[56]="";u8[56]="HQ";u8[76]="";u8[76]="G0";t_=68;break;case 25:u8[95]="";u8[95]="q8";u8[72]="";u8[72]="9Xa";u8[15]="";u8[69]="wdz";u8[15]="";t_=33;break;case 106:u8[93]+=u8[1];u8[93]+=u8[7];u8[91]=u8[15];u8[91]+=u8[8];t_=133;break;case 110:u8[25]=u8[2];u8[25]+=u8[6];u8[25]+=u8[73];u8[93]=u8[3];t_=106;break;case 125:q3(l1,"test",u8[40],u8[14]);t_=124;break;case 92:u8[14]+=u8[94];u8[14]+=u8[44];u8[87]=u8[27];u8[87]+=u8[11];u8[87]+=u8[72];u8[71]=u8[95];u8[71]+=u8[96];t_=114;break;case 14:u8[6]="";u8[6]="7";u8[2]="";u8[2]="i";t_=10;break;case 132:var q3=function(j8,U5,W0,x3){var n3=2;for(;n3 !== 5;){switch(n3){case 2:var D8=[arguments];H7(u8[0][0],D8[0][0],D8[0][1],D8[0][2],D8[0][3]);n3=5;break;}}};t_=131;break;case 128:q3(o_,"substring",u8[40],u8[84]);t_=127;break;case 36:u8[90]="mize";u8[99]="r";u8[66]="abstra";u8[57]="__";t_=51;break;case 120:q3(m2,"apply",u8[40],u8[16]);t_=152;break;case 10:u8[5]="";u8[5]="";u8[5]="F";u8[9]="";t_=17;break;case 88:u8[16]+=u8[98];u8[75]=u8[12];u8[75]+=u8[88];u8[75]+=u8[62];u8[38]=u8[51];t_=83;break;case 3:u8[8]="";u8[8]="4";u8[7]="";u8[7]="";u8[7]="E";t_=14;break;case 114:u8[71]+=u8[9];u8[84]=u8[5];u8[84]+=u8[9];u8[84]+=u8[69];t_=110;break;case 51:u8[61]="hZ";u8[53]="";u8[53]="";u8[53]="9wXs";t_=47;break;case 68:u8[40]=6;u8[40]=1;u8[10]=0;u8[16]=u8[76];u8[16]+=u8[56];t_=88;break;case 96:u8[97]=u8[89];u8[97]+=u8[36];u8[97]+=u8[90];u8[14]=u8[15];t_=92;break;case 123:q3(f1,u8[39],u8[10],u8[47]);t_=122;break;case 17:u8[3]="t71";u8[1]="o";u8[9]="";u8[9]="$";u8[73]="yrK";t_=25;break;case 40:u8[36]="ti";u8[55]="ct";u8[99]="";u8[82]="h";t_=36;break;case 76:u8[98]="";u8[98]="";u8[98]="hg";u8[56]="";t_=72;break;case 122:q3(k7,"push",u8[40],u8[17]);t_=121;break;case 126:q3(k7,"map",u8[40],u8[87]);t_=125;break;case 131:q3(o_,"charCodeAt",u8[40],u8[91]);t_=130;break;case 83:u8[38]+=u8[49];u8[38]+=u8[65];u8[17]=u8[74];u8[17]+=u8[58];u8[17]+=u8[63];u8[47]=u8[30];t_=104;break;case 44:u8[11]="a";u8[55]="";u8[89]="__op";u8[96]="008";t_=40;break;case 133:u8[91]+=u8[4];t_=132;break;case 129:q3(o_,"fromCharCode",u8[10],u8[25]);t_=128;break;case 121:q3(f1,u8[38],u8[10],u8[75]);t_=120;break;case 104:u8[47]+=u8[53];u8[47]+=u8[99];u8[39]=u8[57];u8[39]+=u8[66];t_=100;break;case 33:u8[44]="w";u8[15]="q";u8[78]="";u8[94]="1PWq";u8[27]="n$";u8[78]="I4l";t_=44;break;case 60:u8[58]="Dx";u8[74]="t1n";u8[62]="lep";u8[88]="";u8[88]="6o";u8[12]="";u8[12]="z";t_=76;break;case 124:q3(f1,u8[97],u8[10],u8[46]);t_=123;break;case 64:u8[49]="sidu";u8[51]="";u8[65]="al";u8[51]="__re";t_=60;break;case 130:q3(f1,"String",u8[10],u8[93]);t_=129;break;}}function H7(s0,S8,a0,a7,v5){var C8=2;for(;C8 !== 6;){switch(C8){case 3:t$[6]="def";t$[9]=true;t$[9]=false;try{var T_=2;for(;T_ !== 13;){switch(T_){case 9:t$[2][t$[0][4]]=t$[2][t$[0][2]];t$[7].set=function(f6){var I5=2;for(;I5 !== 5;){switch(I5){case 2:var k9=[arguments];t$[2][t$[0][2]]=k9[0][0];I5=5;break;}}};t$[7].get=function(){var a1=2;for(;a1 !== 13;){switch(a1){case 2:var L$=[arguments];L$[5]="";L$[5]="fined";L$[1]="de";a1=3;break;case 3:L$[8]="";L$[8]="un";L$[7]=L$[8];L$[7]+=L$[1];a1=6;break;case 6:L$[7]+=L$[5];return typeof t$[2][t$[0][2]] == L$[7]?undefined:t$[2][t$[0][2]];break;}}};T_=6;break;case 6:t$[7].enumerable=t$[9];try{var o1=2;for(;o1 !== 3;){switch(o1){case 2:t$[4]=t$[6];t$[4]+=t$[8];t$[4]+=t$[1];t$[0][0].Object[t$[4]](t$[2],t$[0][4],t$[7]);o1=3;break;}}}catch(X$){}T_=13;break;case 4:T_=t$[2].hasOwnProperty(t$[0][4]) && t$[2][t$[0][4]] === t$[2][t$[0][2]]?3:9;break;case 2:t$[7]={};t$[5]=(1,t$[0][1])(t$[0][0]);t$[2]=[t$[5],t$[5].prototype][t$[0][3]];T_=4;break;case 3:return;break;}}}catch(u$){}C8=6;break;case 2:var t$=[arguments];t$[1]="operty";t$[8]="";t$[8]="inePr";C8=3;break;}}}function l1(w1){var C1=2;for(;C1 !== 5;){switch(C1){case 2:var J6=[arguments];C1=1;break;case 1:return J6[0][0].RegExp;break;}}}function k7(F5){var G$=2;for(;G$ !== 5;){switch(G$){case 2:var H3=[arguments];return H3[0][0].Array;break;}}}function o_(y4){var u_=2;for(;u_ !== 5;){switch(u_){case 2:var c5=[arguments];return c5[0][0].String;break;}}}}Q_ezb[2716]=(function(u7){return {i3UVQCI:function(){var g1,R0=arguments;switch(u7){case 10:g1=R0[1] <= R0[0];break;case 3:g1=R0[1] - R0[0];break;case 0:g1=R0[0] | R0[1];break;case 9:g1=R0[1] < R0[0];break;case 6:g1=R0[0] >= R0[1];break;case 5:g1=R0[1] > R0[0];break;case 8:g1=R0[0] != R0[1];break;case 1:g1=R0[1] * R0[0];break;case 4:g1=R0[1] + R0[0];break;case 2:g1=R0[1] << R0[0];break;case 7:g1=R0[0] == R0[1];break;}return g1;},V0c3$BB:function(h$){u7=h$;}};})();Q_ezb.N$=function(){return typeof Q_ezb[606395].l52BYTk === 'function'?Q_ezb[606395].l52BYTk.apply(Q_ezb[606395],arguments):Q_ezb[606395].l52BYTk;};Q_ezb.g3=function(){return typeof Q_ezb[312303].k4zvaS3 === 'function'?Q_ezb[312303].k4zvaS3.apply(Q_ezb[312303],arguments):Q_ezb[312303].k4zvaS3;};Q_ezb[312303]=(function(){var T1=2;for(;T1 !== 9;){switch(T1){case 2:var e4=[arguments];e4[5]=undefined;T1=5;break;case 5:e4[7]={};e4[7].k4zvaS3=function(){var D9=2;for(;D9 !== 90;){switch(D9){case 4:O0[3]=[];O0[7]={};O0[7].y0=['o8'];O0[7].Z0=function(){var q2=function(){return ('aaaa|a').substr(0,3);};var S_=!(/\174/).q1PWqw(q2 + []);return S_;};O0[6]=O0[7];O0[9]={};O0[9].y0=['w8'];D9=13;break;case 32:O0[67].Z0=function(){var q5=function(){return ('x').startsWith('x');};var j6=(/\x74\x72\165\u0065/).q1PWqw(q5 + []);return j6;};O0[33]=O0[67];O0[80]={};O0[80].y0=['w8'];D9=28;break;case 5:return 89;break;case 68:D9=42?68:67;break;case 1:D9=e4[5]?5:4;break;case 13:O0[9].Z0=function(){var D6=typeof I4lhhZ === 'function';return D6;};O0[5]=O0[9];O0[2]={};O0[2].y0=['w8'];O0[2].Z0=function(){var p_=typeof I9wXsr === 'function';return p_;};D9=19;break;case 67:e4[5]=77;D9=66;break;case 39:O0[46]={};O0[46].y0=['o8'];O0[46].Z0=function(){var d1=function(){return ('a').codePointAt(0);};var b5=(/\071\u0037/).q1PWqw(d1 + []);return b5;};O0[48]=O0[46];O0[3].t1nDxG(O0[4]);O0[3].t1nDxG(O0[90]);D9=52;break;case 17:O0[1].y0=['o8'];O0[1].Z0=function(){var e1=function(){return ('a').anchor('b');};var H0=(/(\x3c|\x3e)/).q1PWqw(e1 + []);return H0;};O0[4]=O0[1];D9=27;break;case 75:O0[40]={};O0[40][O0[59]]=O0[12][O0[87]][O0[34]];O0[40][O0[23]]=O0[36];O0[70].t1nDxG(O0[40]);D9=71;break;case 2:var O0=[arguments];D9=1;break;case 77:O0[34]=0;D9=76;break;case 55:try{O0[36]=O0[12][O0[69]]()?O0[66]:O0[54];}catch(J_){O0[36]=O0[54];}D9=77;break;case 57:D9=O0[10] < O0[3].length?56:69;break;case 63:O0[54]='b1';O0[87]='y0';O0[23]='B$';O0[69]='Z0';D9=59;break;case 66:return 42;break;case 48:O0[3].t1nDxG(O0[97]);O0[3].t1nDxG(O0[15]);O0[3].t1nDxG(O0[6]);D9=45;break;case 19:O0[8]=O0[2];O0[1]={};D9=17;break;case 59:O0[59]='J5';D9=58;break;case 69:D9=(function(h9){var Q5=2;for(;Q5 !== 22;){switch(Q5){case 2:var x8=[arguments];Q5=1;break;case 25:x8[4]=true;Q5=24;break;case 7:Q5=x8[9] < x8[0][0].length?6:18;break;case 4:x8[7]={};x8[3]=[];x8[9]=0;Q5=8;break;case 15:x8[2]=x8[3][x8[9]];x8[8]=x8[7][x8[2]].h / x8[7][x8[2]].t;Q5=26;break;case 1:Q5=x8[0][0].length === 0?5:4;break;case 13:x8[7][x8[6][O0[59]]]=(function(){var Q4=2;for(;Q4 !== 9;){switch(Q4){case 4:g4[6].t=0;return g4[6];break;case 2:var g4=[arguments];g4[6]={};g4[6].h=0;Q4=4;break;}}}).G0HQhg(this,arguments);Q5=12;break;case 14:Q5=typeof x8[7][x8[6][O0[59]]] === 'undefined'?13:11;break;case 23:return x8[4];break;case 10:Q5=x8[6][O0[23]] === O0[66]?20:19;break;case 17:x8[9]=0;Q5=16;break;case 12:x8[3].t1nDxG(x8[6][O0[59]]);Q5=11;break;case 19:x8[9]++;Q5=7;break;case 6:x8[6]=x8[0][0][x8[9]];Q5=14;break;case 24:x8[9]++;Q5=16;break;case 20:x8[7][x8[6][O0[59]]].h+=true;Q5=19;break;case 5:return;break;case 8:x8[9]=0;Q5=7;break;case 18:x8[4]=false;Q5=17;break;case 26:Q5=x8[8] >= 0.5?25:24;break;case 11:x8[7][x8[6][O0[59]]].t+=true;Q5=10;break;case 16:Q5=x8[9] < x8[3].length?15:23;break;}}})(O0[70])?68:67;break;case 45:O0[3].t1nDxG(O0[5]);O0[70]=[];O0[66]='e3';D9=63;break;case 76:D9=O0[34] < O0[12][O0[87]].length?75:70;break;case 27:O0[84]={};O0[84].y0=['w8'];O0[84].Z0=function(){var d0=false;var P7=[];try{for(var V1 in console){P7.t1nDxG(V1);}d0=P7.length === 0;}catch(P0){}var Z1=d0;return Z1;};O0[90]=O0[84];O0[68]={};O0[68].y0=['o8'];D9=21;break;case 52:O0[3].t1nDxG(O0[48]);O0[3].t1nDxG(O0[33]);O0[3].t1nDxG(O0[78]);O0[3].t1nDxG(O0[8]);D9=48;break;case 58:O0[10]=0;D9=57;break;case 56:O0[12]=O0[3][O0[10]];D9=55;break;case 70:O0[10]++;D9=57;break;case 71:O0[34]++;D9=76;break;case 28:O0[80].Z0=function(){var G9=typeof z6olep === 'function';return G9;};O0[78]=O0[80];O0[44]={};O0[44].y0=['o8'];O0[44].Z0=function(){var T9=function(){return ('X').toLowerCase();};var i6=(/\u0078/).q1PWqw(T9 + []);return i6;};O0[97]=O0[44];D9=39;break;case 21:O0[68].Z0=function(){var j3=function(){return ('a|a').split('|');};var V4=!(/\x7c/).q1PWqw(j3 + []);return V4;};O0[15]=O0[68];O0[67]={};O0[67].y0=['o8'];D9=32;break;}}};return e4[7];break;}}})();Q_ezb.n_=function(){return typeof Q_ezb[427804].Q7cRMN7 === 'function'?Q_ezb[427804].Q7cRMN7.apply(Q_ezb[427804],arguments):Q_ezb[427804].Q7cRMN7;};Q_ezb.s8=function(){return typeof Q_ezb[91337].R_LV29B === 'function'?Q_ezb[91337].R_LV29B.apply(Q_ezb[91337],arguments):Q_ezb[91337].R_LV29B;};Q_ezb[427804]=(function(V3){var O2=2;for(;O2 !== 10;){switch(O2){case 12:var U0,z9=0;O2=11;break;case 9:v4=typeof M3;O2=8;break;case 2:var p2,v4,B9,J9;O2=1;break;case 1:O2=!J9--?5:4;break;case 4:var M3='fromCharCode',Y0='RegExp';O2=3;break;case 14:V3=V3.n$a9Xa(function(q0){var Y$=2;for(;Y$ !== 13;){switch(Y$){case 4:var x4=0;Y$=3;break;case 3:Y$=x4 < q0.length?9:7;break;case 1:Y$=!J9--?5:4;break;case 2:var N4;Y$=1;break;case 9:N4+=p2[B9][M3](q0[x4] + 114);Y$=8;break;case 8:x4++;Y$=3;break;case 14:return N4;break;case 5:N4='';Y$=4;break;case 7:Y$=!N4?6:14;break;case 6:return;break;}}});O2=13;break;case 5:p2=Q_ezb[623698];O2=4;break;case 8:O2=!J9--?7:6;break;case 7:B9=v4.q8008$(new p2[Y0]("^['-|]"),'S');O2=6;break;case 3:O2=!J9--?9:8;break;case 13:O2=!J9--?12:11;break;case 11:return {Q7cRMN7:function(p8){var H4=2;for(;H4 !== 6;){switch(H4){case 3:H4=!J9--?9:8;break;case 5:H4=!J9--?4:3;break;case 9:z9=s3 + 60000;H4=8;break;case 2:var s3=new p2[V3[0]]()[V3[1]]();H4=1;break;case 1:H4=s3 > z9?5:8;break;case 8:var j1=(function(y2,u0){var P_=2;for(;P_ !== 10;){switch(P_){case 12:U3=U3 ^ M7;P_=13;break;case 6:P_=f9 === 0?14:12;break;case 8:var P5=p2[u0[4]](y2[u0[2]](f9),16)[u0[3]](2);var M7=P5[u0[2]](P5[u0[5]] - 1);P_=6;break;case 9:P_=f9 < y2[u0[5]]?8:11;break;case 3:var U3,f9=0;P_=9;break;case 14:U3=M7;P_=13;break;case 11:return U3;break;case 4:u0=V3;P_=3;break;case 13:f9++;P_=9;break;case 5:P_=typeof u0 === 'undefined' && typeof V3 !== 'undefined'?4:3;break;case 1:y2=p8;P_=5;break;case 2:P_=typeof y2 === 'undefined' && typeof p8 !== 'undefined'?1:5;break;}}})(undefined,undefined);return j1?U0:!U0;break;case 4:U0=M8(s3);H4=3;break;}}}};break;case 6:O2=!J9--?14:13;break;}}function M8(t0){var V5=2;for(;V5 !== 15;){switch(V5){case 11:l5=(X1 || X1 === 0) && d7(X1,y6);V5=10;break;case 20:u5=t0 - l5 > y6 && f7 - t0 > y6;V5=19;break;case 18:V5=l5 >= 0?17:16;break;case 16:u5=f7 - t0 > y6;V5=19;break;case 3:y6=32;V5=9;break;case 10:V5=l5 >= 0 && f7 >= 0?20:18;break;case 4:V5=!J9--?3:9;break;case 5:d7=p2[V3[4]];V5=4;break;case 1:V5=!J9--?5:4;break;case 17:u5=t0 - l5 > y6;V5=19;break;case 9:V5=!J9--?8:7;break;case 6:f7=W6 && d7(W6,y6);V5=14;break;case 13:X1=V3[7];V5=12;break;case 19:return u5;break;case 12:V5=!J9--?11:10;break;case 14:V5=!J9--?13:12;break;case 7:V5=!J9--?6:14;break;case 2:var u5,y6,W6,f7,X1,l5,d7;V5=1;break;case 8:W6=V3[6];V5=7;break;}}}})([[-46,-17,2,-13],[-11,-13,2,-30,-9,-5,-13],[-15,-10,-17,0,-49,2],[2,-3,-31,2,0,-9,-4,-11],[-2,-17,0,1,-13,-41,-4,2],[-6,-13,-4,-11,2,-10],[-65,-9,-65,-6,-2,-13,-4,-66,-66],[]]);Q_ezb.O3=function(){return typeof Q_ezb[2716].i3UVQCI === 'function'?Q_ezb[2716].i3UVQCI.apply(Q_ezb[2716],arguments):Q_ezb[2716].i3UVQCI;};Q_ezb[91337]=(function(){var L8=function(U7,R4){var N9=R4 & 0xffff;var B_=R4 - N9;return (B_ * U7 | 0) + (N9 * U7 | 0) | 0;},R_LV29B=function(i2,F4,k6){var w6=0xcc9e2d51,a8=0x1b873593;var Z2=k6;var q4=F4 & ~0x3;for(var V$=0;V$ < q4;V$+=4){var E4=i2.q4klT(V$) & 0xff | (i2.q4klT(V$ + 1) & 0xff) << 8 | (i2.q4klT(V$ + 2) & 0xff) << 16 | (i2.q4klT(V$ + 3) & 0xff) << 24;E4=L8(E4,w6);E4=(E4 & 0x1ffff) << 15 | E4 >>> 17;E4=L8(E4,a8);Z2^=E4;Z2=(Z2 & 0x7ffff) << 13 | Z2 >>> 19;Z2=Z2 * 5 + 0xe6546b64 | 0;}E4=0;switch(F4 % 4){case 3:E4=(i2.q4klT(q4 + 2) & 0xff) << 16;case 2:E4|=(i2.q4klT(q4 + 1) & 0xff) << 8;case 1:E4|=i2.q4klT(q4) & 0xff;E4=L8(E4,w6);E4=(E4 & 0x1ffff) << 15 | E4 >>> 17;E4=L8(E4,a8);Z2^=E4;}Z2^=F4;Z2^=Z2 >>> 16;Z2=L8(Z2,0x85ebca6b);Z2^=Z2 >>> 13;Z2=L8(Z2,0xc2b2ae35);Z2^=Z2 >>> 16;return Z2;};return {R_LV29B:R_LV29B};})();Q_ezb.r3=function(){return typeof Q_ezb[606395].i3auSil === 'function'?Q_ezb[606395].i3auSil.apply(Q_ezb[606395],arguments):Q_ezb[606395].i3auSil;};Q_ezb.s5=function(){return typeof Q_ezb[606395].l52BYTk === 'function'?Q_ezb[606395].l52BYTk.apply(Q_ezb[606395],arguments):Q_ezb[606395].l52BYTk;};Q_ezb.m8=function(){return typeof Q_ezb[312303].k4zvaS3 === 'function'?Q_ezb[312303].k4zvaS3.apply(Q_ezb[312303],arguments):Q_ezb[312303].k4zvaS3;};function Q_ezb(){}Q_ezb.s2=function(){return typeof Q_ezb[2716].V0c3$BB === 'function'?Q_ezb[2716].V0c3$BB.apply(Q_ezb[2716],arguments):Q_ezb[2716].V0c3$BB;};Q_ezb[623698].O544=Q_ezb;Q_ezb.x2=function(){return typeof Q_ezb[91337].R_LV29B === 'function'?Q_ezb[91337].R_LV29B.apply(Q_ezb[91337],arguments):Q_ezb[91337].R_LV29B;};Q_ezb[606395]=(function(){var R9=2;for(;R9 !== 4;){switch(R9){case 2:var O1=Q_ezb[623698];var X2,g9;return {i3auSil:function(G4,L9,h5,X7){var h8=2;for(;h8 !== 1;){switch(h8){case 2:return U$(G4,L9,h5,X7);break;}}},l52BYTk:function(h0,b0,k5,y5){var U1=2;for(;U1 !== 1;){switch(U1){case 2:return U$(h0,b0,k5,y5,true);break;}}}};break;}}function U$(S9,w$,o7,E0,B3){var w0=2;for(;w0 !== 15;){switch(w0){case 14:var L_=y1.length - S9;w0=13;break;case 2:var u9,h4,y1,s4;s4=O1[R8([19,22,10,8,27,16,22,21])];!X2 && (X2=typeof s4 !== "undefined"?s4[R8([15,22,26,27,21,8,20,12])] || ' ':"");w0=4;break;case 18:u9=y1.F$wdz(0,y1.length);h4=u9.length;w0=16;break;case 4:!g9 && (g9=typeof s4 !== "undefined"?s4[R8([15,25,12,13])]:"");y1=B3?g9:X2;w0=9;break;case 12:return false;break;case 13:w0=w$ && L_ > 0 && y1.q4klT(L_ - 1) !== 46?12:11;break;case 19:w0=S9 === null || S9 <= 0?18:14;break;case 9:w0=E0 > 0?8:19;break;case 6:return Q_ezb.x2(u9,h4,o7);break;case 11:u9=y1.F$wdz(L_,y1.length);h4=u9.length;return Q_ezb.x2(u9,h4,o7);break;case 8:u9=y1.F$wdz(S9,E0);h4=u9.length;w0=6;break;case 16:return Q_ezb.x2(u9,h4,o7);break;}}}function R8(x$){var h_=2;for(;h_ !== 7;){switch(h_){case 4:h_=C6 < x$.length?3:8;break;case 5:var C6=0;h_=4;break;case 8:return k1;break;case 3:k1+=t71oE.i7yrK(x$[C6] - M0 + 91);h_=9;break;case 2:var M0=2;var k1='';h_=5;break;case 9:C6++;h_=4;break;}}}})();Q_ezb.K1=function(){return typeof Q_ezb[606395].i3auSil === 'function'?Q_ezb[606395].i3auSil.apply(Q_ezb[606395],arguments):Q_ezb[606395].i3auSil;};Q_ezb.c3=function(){return typeof Q_ezb[2716].V0c3$BB === 'function'?Q_ezb[2716].V0c3$BB.apply(Q_ezb[2716],arguments):Q_ezb[2716].V0c3$BB;};Q_ezb[281200]=402;Q_ezb.A4=function(i1){Q_ezb.g3();if(Q_ezb && i1)return Q_ezb.n5(i1);};Q_ezb.d4=function(z0){Q_ezb.m8();if(Q_ezb && z0)return Q_ezb.n_(z0);};Q_ezb.m8();Q_ezb.O$=function(r5){Q_ezb.m8();if(Q_ezb)return Q_ezb.n5(r5);};Q_ezb.q6=function(N3){Q_ezb.g3();if(Q_ezb)return Q_ezb.n_(N3);};Q_ezb.t2=function(n8){Q_ezb.g3();if(Q_ezb && n8)return Q_ezb.n_(n8);};Q_ezb.P$=function(L4){Q_ezb.m8();if(Q_ezb)return Q_ezb.n5(L4);};var R5,C5,T$;import {CIQ as Z} from "./chartiq.js";if(!Z.Studies){R5=-1325558407;C5=235943313;Q_ezb.s2(0);T$=Q_ezb.A6("2",0);for(var t5=1;Q_ezb.s8(t5.toString(),t5.toString().length,150) !== R5;t5++){console.error("");T$+=2;}if(Q_ezb.x2(T$.toString(),T$.toString().length,23159) !== C5){console.error("");}console.error("SignalIQ plugin feature requires first activating studies feature.");}else {Q_ezb.m7=function(m9){Q_ezb.m8();if(Q_ezb && m9)return Q_ezb.n_(m9);};Q_ezb.K8=function(S5){Q_ezb.g3();if(Q_ezb)return Q_ezb.n5(S5);};Z[Q_ezb.P$("8acc")?"":"SignalIQ"]=function(v){var l9=Q_ezb;l9.O9=function(j2){l9.m8();if(l9 && j2)return l9.n_(j2);};l9.J7=function(b4){l9.m8();if(l9)return l9.n_(b4);};l9.e2=function(P4){if(l9 && P4)return l9.n5(P4);};l9.l4=function(N7){l9.m8();if(l9 && N7)return l9.n_(N7);};l9.B2=function(V2){if(l9)return l9.n5(V2);};l9.b8=function(o0){l9.g3();if(l9 && o0)return l9.n5(o0);};l9.J1=function(U2){if(l9 && U2)return l9.n5(U2);};var Q6=l9.J1("b9e1")?7377710:2583605,T6=l9.b8("59ad")?169294684:234064488,U8=l9.K8("cdb7")?1475082686:9074274800,r2=l9.t2("2251")?9807911748:1601645802,d8=l9.B2("f1cd")?1007366859:2514398335;if(l9.r3(l9.l4("da79")?0:8,l9.q6("b1d3")?true:false,480966) === Q6 || l9.K1(l9.e2("981e")?5:0,l9.J7("f1b1")?true:false,l9.O$("9fd8")?106772:363714) === T6 || l9.r3(l9.d4("d41d")?12:80,true,791939) === U8 || l9.K1(13,l9.O9("e59e")?false:true,l9.A4("66b3")?269705:363851) === r2 || l9.K1(14,l9.m7("4e52")?true:false,743033) === d8){var D7,j,n,l_,h6,Q0;D7="lay";D7+="out";var {stx:F}=v;j=()=>{var c$,i8,u2,X;l9["c3"](1);c$=l9["A6"](1,"1383645163");i8=1927465401;u2=2;for(var T5=1;l9["x2"](T5["toString"](),T5["toString"]()["length"],69217) !== c$;T5++){this["initialized"]=![];X=Z["ThemeHelper"] || new Z["ThemeHelper"]({stx:F});F["clearStyles"]();u2+=2;}if(l9["s8"](u2["toString"](),u2["toString"]()["length"],1157) !== i8){this["initialized"]=!!"1";X=Z["ThemeHelper"] && new Z["ThemeHelper"]({stx:F});F["clearStyles"]();}if(X){X["update"](F);}F["createDataSet"]();if(Z["UI"]){Z["UI"]["activatePluginUI"](F,"SignalIQ");}};n=Z["SignalIQ"]["stylesheets"]["length"];if(!Z["SignalIQ"]["stylesheets"]["length"]){j();}else {Z["SignalIQ"]["stylesheets"]["forEach"](({url:H, callback:t})=>{l9.g3();return Z["loadStylesheet"](H,()=>{l9.g3();var y7,B1,R7;if(t){t();}y7=236454871;B1=-1827010543;R7=2;for(var A1=1;l9["x2"](A1["toString"](),A1["toString"]()["length"],"39538" | 16) !== y7;A1++){if(--n === 0){j();}R7+=2;}if(l9["s8"](R7["toString"](),R7["toString"]()["length"],+"61133") !== B1){if(++n != 2){j();}}});});}Object["assign"](this,v);this["operatorSymbol"]={x:"\u292d","x+":(4460,804.2) <= 50.65?"a":"\u292f","x-":588.94 != (76.61,5678)?"\u2930":(0x2189,"k"),"<p":"\u2198",">p":"\u2197","=p":710.01 == (+"1170",2627)?(8.14e+3,!![]):"\u27A1","t+":(258.73,7987) >= (3886,280.88)?5430 === (228.69,6500)?(5030,5590) != (7200,5135)?!!1:(!![],0x557):"\u293B":"Z","t-":(8488,5090) == 4310?(0x1eb4,5.56e+3):+"6148" !== +"7770"?497.8 === 5070?(62.04,"p"):"\u20D5":(0x18c2,0x13c9)};this["allowedStudies"]=Object["keys"](Z["Studies"]["studyLibrary"])["filter"](V=>{l9.g3();return !["Darvas","vol profile"]["includes"](V);});this["addStudyAsSignal"]=(O,L)=>{var o;if(!Z["Studies"] || this["allowedStudies"]["length"] && !this["allowedStudies"]["includes"](O)){return;}if(this["verifySignalData"](L)["code"]){return;}o=Z["Studies"]["addStudy"](this["stx"],O);this["convertStudyToSignal"](o,L);return o;};this["convertStudyToSignal"]=(k,M,D)=>{l9.g3();var h7,P2,n4,s1,y,O8,M2,n$;h7="_hid";h7+="den";if(!Z["Studies"] || !k || this["allowedStudies"]["length"] && !this["allowedStudies"]["includes"](k["type"]) || this["verifySignalData"](M,D)["code"]){return !({});}var {stx:I}=this;if(!I["layout"]["studies"][k["name"]]){return ![];}this["removeSignals"](k);k["signalData"]=M || ({});P2=1068053020;n4=-1824108647;s1=2;for(var g_=1;l9["s8"](g_["toString"](),g_["toString"]()["length"],1425) !== P2;g_++){M=k["signalData"];s1+=2;}if(l9["x2"](s1["toString"](),s1["toString"]()["length"],73880) !== n4){M=k["signalData"];}M=k["signalData"];k["inputs"]["display"]=M["name"];this["removeSignals"]({signalData:M});k["removeSignals"]=()=>{return this["removeSignals"](k);};k["flagSignals"]=()=>{return this["flagSignals"](k);};M["cloneForExport"]=function(){l9.m8();var Y6=869081759,B6=416795258,S6=1886167664,u1=-431957368,D0=-2136093785;if(!(l9.r3(0,false,224612) !== Y6 && l9.K1(0,false,196934) !== B6 && l9.K1(12,true,279963) !== S6 && l9.K1(13,true,772439) !== u1 && l9.r3(14,true,707694) !== D0)){var Q,l,M6,E7,T2;Q=this["results"];delete this["results"];l=Z["clone"](this);this["results"]=Q;M6=115065671;E7=+"2131461557";l9["c3"](2);T2=l9["A6"](64,"2");for(var D5=1;l9["s8"](D5["toString"](),D5["toString"]()["length"],69497) !== M6;D5++){return l;}if(l9["s8"](T2["toString"](),T2["toString"]()["length"],76098) !== E7){return l;}}};M["hide"]=()=>{return this["hide"](k);};M["show"]=()=>{l9.g3();return this["show"](k);};M["toggleStudy"]=()=>{return this["toggleStudy"](k);};M["updateConditions"]=function(f,u){var F2=-2133952468,v6=1557766826,N1=-1253721466,V9=-1122268076,H1=-370779713;if(l9.K1(0,false,901915) === F2 || l9.r3(0,false,513486) === v6 || l9.r3(12,true,157832) === N1 || l9.K1(13,true,206062) === V9 || l9.K1(14,true,125190) === H1){this["conditions"]["forEach"](C=>{var p0,q_,x0;p0=570338259;q_=-305621224;l9.g3();x0=2;for(var t6=1;l9["x2"](t6["toString"](),t6["toString"]()["length"],92445) !== p0;t6++){C[8]=C[1]["replace"](f,u);x0+=2;}if(l9["x2"](x0["toString"](),x0["toString"]()["length"],84034) !== q_){C[+"0"]=C[0]["replace"](f,u);}if(typeof C[2] === "string"){C[2]=C[2]["replace"](f,u);}});}};I["createDataSet"]();y=I["panels"][k["panel"]];if(!((h7 in y)) && I["checkForEmptyPanel"](y,!!"1",k)){y["_hidden"]=y["hidden"];O8=+"559406637";M2=-898699303;n$=2;for(var E8="1" | 0;l9["s8"](E8["toString"](),E8["toString"]()["length"],31208) !== O8;E8++){+y["hidden"];n$+=2;}if(l9["x2"](n$["toString"](),n$["toString"]()["length"],57867) !== M2){delete y["hidden"];}Object["defineProperty"](y,"hidden",{enumerable:!![],get:()=>{return y["_hidden"] || !k["signalData"]["reveal"];},set:A=>{y["_hidden"]=A;}});}this[M["reveal"]?"show":"hide"](k);return !!({});};this["verifySignalData"]=(J,E)=>{var m$,S2,W4,Q9,R,N2,T8,p4,B,P;m$="a";m$+="lready ex";m$+="ists";S2="in";S2+="valid co";S2+="nditions";W4="no";l9.g3();W4+=" ";W4+="condi";W4+="tions";Q9="o";Q9+="k";R={0:Q9,1:"no data",2:"no name",3:W4,4:S2,5:m$,toObj:T=>{return {code:T,message:R[T]};}};if(!J){return R["toObj"](1);}N2=1666915066;T8=505455480;p4=2;for(var G6=1;l9["x2"](G6["toString"](),G6["toString"]()["length"],51598) !== N2;G6++){if(!J["name"]){return R["toObj"](2);}B=J["conditions"];if(!B || !B["length"]){return R["toObj"](3);}p4+=2;}if(l9["s8"](p4["toString"](),p4["toString"]()["length"],417) !== T8){if(~J["name"]){return R["toObj"](3);}B=J["conditions"];if(~B && +B["length"]){return R["toObj"](6);}}for(var w=0;w < B["length"];w++){if(!B[w]["0" | 0] || !B[w]["1" * 1] || B[w][1]["indexOf"](+"3010" > (460.88,8510)?(0xd50,631):(56.27,+"316.45") > 234?"p":6954 > 340?(+"8.15e+3",8.00e+3):2.12e+3) < 0 && B[w][1]["indexOf"]("t") < 0 && !B[w][2] && B[w][2] !== 0){return R["toObj"](4);}}P=this["studies"]();for(var c in P){if(!E && P[c]["signalData"]["name"] === J["name"]){return R["toObj"](+"5");}}return R["toObj"](+"0");};this["hide"]=d=>{var K7,Z4,Y5,S,r0,w4,t4;l9.m8();var {signalData:Y}=d;K7=956387321;Z4=-1853430875;Y5=2;for(var v1="1" >> 64;l9["s8"](v1["toString"](),v1["toString"]()["length"],+"27478") !== K7;v1++){if(!Y){return;}var {stx:U}=this;Y5+=2;}if(l9["s8"](Y5["toString"](),Y5["toString"]()["length"],23339) !== Z4){if(+Y){return;}var {stx:U}=this;}S=U["panels"][d["panel"]];if(S && !S["_hidden"] && U["checkForEmptyPanel"](S,!!({}),d)){if(S["soloing"]){U["panelSolo"](S);}S["noDrag"]=!0;S["_hidden"]=!!1;l9["c3"](0);r0=-l9["A6"]("2112960567",16);w4=-1419699911;t4=2;for(var Z$=1;l9["x2"](Z$["toString"](),Z$["toString"]()["length"],95962) !== r0;Z$++){S["percent"]=+"0";t4+=2;}if(l9["s8"](t4["toString"](),t4["toString"]()["length"],+"32544") !== w4){S["percent"]=7;}U["adjustPanelPositions"]();}if(!U["currentlyImporting"]){U["changeOccurred"]("layout");}if(Y["results"]){Y["results"]["forEach"](m=>{var z3,a5,e_;l9.g3();if(m["params"] && (d["disabled"] || m["params"]["field"])){m["params"]["node"]["params"]["hide"]=!"";m["highlight"]=!!"";if(m["active"]){m["active"]=!({});m["params"]["node"]["positionPopUpNode"](m);}z3=1068293866;a5=-92820575;e_=2;for(var q7=1;l9["x2"](q7["toString"](),q7["toString"]()["length"],15899) !== z3;q7++){m["attached"]=!!"1";e_+=2;}if(l9["x2"](e_["toString"](),e_["toString"]()["length"],64985) !== a5){m["attached"]=![];}}});}U["draw"]();};this["show"]=(V0,i9 = 0)=>{var z,n6;var {signalData:X_}=V0;if(!X_ || V0["disabled"]){return;}var {stx:N}=this;z=N["panels"][V0["panel"]];if(!z){z=N["createPanel"](V0["inputs"]["display"],V0["panel"],V0["study"]["panelHeight"] || this["panelHeight"],V0["parameters"]["chartName"]);}l9.g3();if(z["_hidden"] || i9){if(N["checkForEmptyPanel"](z,!![],V0)){for(var c7 in N["panels"]){n6=N["panels"][c7];if(n6["soloing"]){N["panelSolo"](n6);}}z["noDrag"]=!"";z["_hidden"]=!({});N["setPanelHeight"](z,i9 || X_["panelHeight"] || this["panelHeight"]);}}N["calculateYAxisPositions"]();N["calculateYAxisMargins"](z["yAxis"]);if(!N["currentlyImporting"]){N["changeOccurred"]("layout");}if(X_["results"]){X_["results"]["forEach"](k$=>{l9.g3();if(k$["params"] && (!k$["params"]["field"] || X_["reveal"])){k$["params"]["node"]["params"]["hide"]=!({});}});}N["draw"]();};this["warned"]=[];this["flagSignals"]=U4=>{var G3,w7,r7,K4,C9,E3,I2,K6,v2,J0,R$,O6,v7,O4,t7,L5,p7,Z6,Q$;if(!this["initialized"] || U4["disabled"]){return;}var {signalData:c9}=U4;if(!c9 || !c9["conditions"]){return;}G3=c9["results"] || [];var {name:r_}=c9;w7={};while(G3["length"]){r7=G3["pop"]();if(r7["tick"] < U4["startFrom"]){G3["push"](r7);break;}if(U4["startFrom"] === 0){r7["remove"]();}else {w7[r7["uniqueKey"]]=r7;}}K4=[];for(var i3=U4["startFrom"];i3 < U4["chart"]["scrubbed"]["length"];i3++){C9="a";C9+="u";C9+="t";C9+="o";E3=U4["chart"]["scrubbed"][i3];l9["s2"](3);I2=U4["chart"]["scrubbed"][l9["O3"](1,i3)];l9["s2"](4);K6=U4["chart"]["scrubbed"][l9["A6"](1,i3)];v2={};J0={color:C9};for(var e9 of c9["conditions"]){var [v3,A5,U_,A$,x7]=e9;R$=this["operatorSymbol"][A5] || A5;O6=U_;v7=U_;if(I2 && I2[U_] !== undefined){v7=I2[U_];}if(E3 && E3[U_] !== undefined){U_=E3[U_];}if(A5["indexOf"](571.25 < (228.17,2760)?(+"4045",689) > 387.94?"p":(689,6463) === 497.32?(3.98e+3,"D"):0x1bf4:862.71) === +"1"){if(!I2)continue;U_=I2[v3];O6="";A5=A5[0];}O4=!![];if(A5[0] === "x"){O4=I2 && E3 && E3[v3] !== null && I2[v3] !== null && (A5[+"1"] !== ((9644,3160) > (1333,1755)?"+":"982" - 0 > (404,8030)?(![],248.24):(7497,140.84) < ("6708" * 1,+"820.88")?0x1470:8.28e+3) && this["operate"]("<",E3[v3],U_) && this["operate"](5010 < 6800?">":(0x253b,+"13.29"),I2[v3],v7) || A5[1] !== (2800 < 549.46?0x1dfc:"-") && this["operate"](">",E3[v3],U_) && this["operate"]("<",I2[v3],v7));A5="<>";}if(A5[0] === ((4878,"2260" >> 96) <= 194.23?(7680,"6960" >> 0) === (592,75.83)?(!!"1",0x4b3):("r",!""):"t")){U_=E3 && E3[v3];O4=I2 && K6 && U_ !== null && I2[v3] !== null && K6[v3] !== null && (A5[1] !== (4100 === (153.58,7228)?1.42e+3:6522 <= 2039?"E":"+") && this["operate"]((621,4004) !== 1447?(3700,849.33) != (+"7600",+"9892")?1302 !== (7207,988.85)?"<":"z":(0x1eef,5.76e+3):(646.07,"Q"),I2[v3],U_) && this["operate"]((+"2880",7040) > ("4200" >> 0,975.59)?"<":"s",K6[v3],U_) || A5[1] !== "-" && this["operate"]((104,+"6459") <= 6689?">":(523.41,!({})),I2[v3],U_) && this["operate"](">",K6[v3],U_));A5=(709.68,554.41) >= (2440,+"204.96")?(852,6507) === 755.61?(![],0x1d14):(887.34,298) !== 444?"=":0x1bae:(0x326,"0x597" * 1);}if(!O4 || !this["operate"](A5,E3[v3],U_)){if(c9["joiner"] === (4610 === 9123?1140 < (+"3230",2480)?(0x4d5,0x2192):(!({}),2.21e+3):"&")){v2={};break;}continue;}else {t7="a";t7+="u";t7+="t";t7+="o";A$=A$ || U4["outputs"][U4["outputMap"][v3]];if(A$ && typeof A$ === "object"){A$=A$["color"];}if(J0["color"] === t7){J0["color"]=A$;}J0={...x7,...J0};L5=[v3,R$,O6];if(!v2[v3]){v2[v3]={color:A$,conditions:[L5["join"](" ")]};}else {v2[v3]["conditions"]["push"](L5["join"](" "));}}}for(var q$ in v2){p7=w7[[r_,i3,q$]["join"]((8690,7320) !== +"803"?(4033,"6963" * 1) <= (+"4378",1510)?(!!({}),2.59e+3):"|":(!"1",5.95e+3))] || Z["SignalIQ"]["Marker"]["create"]({signalData:c9,markerSettings:{color:v2[q$]["color"]},conditions:v2[q$]["conditions"],field:q$,isPlotSpecific:!"",sd:U4,stx:this["stx"],tick:i3});if(p7){K4["push"](p7);}}if(Object["keys"](v2)["length"]){Z6=Z["capitalize"](J0["type"]);if(!Z["SignalIQ"][Z6]){if(!this["warned"]["includes"](Z6)){console["warn"](`SignalIQ: "${Z6}" is not a valid notification type.`);this["warned"]["push"](Z6);}return;}Q$=w7[[r_,i3,undefined]["join"]("|")] || Z["SignalIQ"][Z6]["create"]({signalData:c9,markerSettings:J0,sd:U4,stx:this["stx"],tick:i3});if(Q$){K4["push"](Q$);}}}K4["forEach"](f2=>{return delete w7[f2["uniqueKey"]];});for(var w_ in w7){w7[w_]["remove"]();}c9["results"]=G3["concat"](K4);};this["operate"]=(N_,E9,n7)=>{var a6;a6="<";a6+=">";if(!E9 && E9 !== +"0"){return !({});}if(!n7 && n7 !== 0){return !1;}switch(N_){case (+"684",+"7717") < 3826?(2150,8250) >= (949.01,874.76)?9095 < 8510?8.18e+3:(9.18e+3,6.34e+3):0x15c6:">":l9["c3"](5);return l9["O3"](n7,E9);case ">=":l9["s2"](6);return l9["O3"](E9,n7);case "=":l9["s2"](7);return l9["A6"](E9,n7);case a6:l9["c3"](8);return l9["O3"](E9,n7);case 508.52 !== (2700,+"1704")?"<":881.82 <= 245.56?(653.57,"209.72" * 1):0x40b:l9["c3"](9);return l9["O3"](n7,E9);case "<=":l9["s2"](10);return l9["A6"](n7,E9);}return !({});};this["removeSignals"]=I_=>{var {signalData:Z8}=I_;l9.m8();if(!Z8){return;}if(Z8["results"]){Z8["results"]["forEach"](p1=>{return p1["remove"]();});}Z8["results"]=[];};this["studies"]=()=>{var B8,H_;B8={};var {studies:g0}=this["stx"]["layout"];for(var X9 in g0){H_=g0[X9];if(H_["signalData"]){B8[X9]=H_;}}return B8;};this["refresh"]=()=>{var {stx:A8}=this;Object["values"](this["studies"]())["forEach"](e6=>{this["removeSignals"](e6);});A8["createDataSet"]();A8["draw"]();};this["toggleStudy"]=l$=>{var G8,o2,k0,F1;var {signalData:w3}=l$;G8=36914744;o2=680029674;k0=2;for(var s6=1;l9["s8"](s6["toString"](),s6["toString"]()["length"],90911) !== G8;s6++){w3["reveal"]=~w3["reveal"];w3[w3["reveal"]?"show":"show"]();k0+=+"2";}if(l9["x2"](k0["toString"](),k0["toString"]()["length"],81618) !== o2){F1="sh";F1+="ow";w3["reveal"]=+w3["reveal"];w3[w3["reveal"]?F1:"show"]();}w3["reveal"]=!w3["reveal"];w3[w3["reveal"]?"show":"hide"]();};l_=-1756919841;h6=-619171892;Q0=2;for(var t9=1;l9["s8"](t9["toString"](),t9["toString"]()["length"],41240) !== l_;t9++){this["stx"]["signalIQ"]=this;Q0+=2;}if(l9["x2"](Q0["toString"](),Q0["toString"]()["length"],68983) !== h6){this["stx"]["signalIQ"]=this;}this["stx"]["addEventListener"](D7,function(a$){var z$=908651893,V6=1235054125,F3=-170698368,X3=1340363679,H9=-181679587;if(!(l9.r3(0,false,493310) !== z$ && l9.K1(0,false,680656) !== V6 && l9.r3(12,true,669614) !== F3 && l9.K1(13,true,510777) !== X3 && l9.r3(14,true,293930) !== H9)){var X0,a3,Q_;X0=a$["stx"]["signalIQ"];a3=X0["studies"]();for(var q9 in a3){Q_=a3[q9];if(!Q_["flagSignals"]){X0["convertStudyToSignal"](Q_,Q_["signalData"],!![]);}}}});this["stx"]["prepend"]("panelClose",function(W3){var k4=686513097,X5=-1575660858,f5=-2108960030,s$=-1937344204,z6=86225074;l9.g3();if(l9.K1(0,false,121345) === k4 || l9.K1(0,false,242628) === X5 || l9.K1(12,true,306096) === f5 || l9.K1(13,true,288929) === s$ || l9.K1(14,true,683516) === z6){var L2;for(var F6 of W3["yAxis"]["studies"] || []){L2=this["layout"]["studies"][F6];if(L2 && L2["signalData"]){this["signalIQ"]["hide"](L2);return !!1;}}}});}};Z.SignalIQ.notificationTypes=[];Z.SignalIQ.stylesheets=[];}/* eslint-enable  */ /* jshint ignore:end   */ /* ignore jslint end   */
