using Euroland.FlipIT.ShareGraph.API.Entities.Configurations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class Instrument
  {
    public int Id { get; set; }

    public bool Default { get; set; }

    public string Color { get; set; }

    public int Order { get; set; }

    public string TickerName { get; set; }

    public string ShareName { get; set; }

    public string Ticker { get; set; }

    public string CurrencyCode { get; set; }

    public string MarketName { get; set; }

    public string MarketAbbreviation { get; set; }

    public PressRelease PressRelease { get; set; }

    public bool IsRT { get; set; }

    public string Symbol { get; set; }

    public string CustomTimeZone { get; set; }

    public int? DecimalDigits { get; set; }

    public int? PercentDigits { get; set; }

    public bool? EnabledAdjustPrice { get; set; } = false;
  }
}
