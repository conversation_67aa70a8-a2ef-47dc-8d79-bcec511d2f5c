using AutoMapper;
using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Studies;
using Euroland.FlipIT.ShareGraph.API.Extensions;
using System.Collections.Generic;

namespace Euroland.FlipIT.ShareGraph.API.Mappers
{
  public class ConfigurationMapperProfile : Profile
  {
    public ConfigurationMapperProfile()
    {
      // PerformanceConfig to Performance
      CreateMap<PerformanceConfig, Performance>(MemberList.Destination)
          .ForMember(dest => dest.EnabledFormats, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledFormat) ? new List<string>() : ol.EnabledFormat.ToListItem()))
          .ForMember(dest => dest.PerformanceTypes, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.PerformanceType) ? new List<string>() : ol.PerformanceType.ToListItem()))
          .ForMember(dest => dest.Enable52WTableColumns, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.Enable52WTableColumn) ? new List<string>() : ol.Enable52WTableColumn.ToListItem()))
          .ForMember(dest => dest.EnableSharePriceDevelopmentColumns, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnableSharePriceDevelopmentColumn) ? new List<string>() : ol.EnableSharePriceDevelopmentColumn.ToListItem()))
          .ForMember(dest => dest.NumberOfYearSPByYear, conf => conf.MapFrom(ol =>
              ol.NumberOfYearSPByYear))
          .ForMember(dest => dest.ShowEarliestYearFirstSPByYear, conf => conf.MapFrom(ol =>
              ol.ShowEarliestYearFirstSPByYear))
          .ForMember(dest => dest.ExcludeIds, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.ExcludeIds) ? new List<int>() : ol.ExcludeIds.ToIntListItem()))
          ;

      // ChartConfig to Chart
      CreateMap<ChartConfig, Chart>(MemberList.Destination)
          .ForMember(dest => dest.Studies, conf => conf.MapFrom(ol => new Studies()))
          .ForMember(dest => dest.ExcelDownloadOption, conf => conf.MapFrom(ol => new ExcelDownloadOption()))
          .ForMember(dest => dest.DefaultTooltipType, conf => conf.MapFrom(ol => ol.DefaultTooltipType))
          .ForMember(dest => dest.DefaultChartType, conf => conf.MapFrom(ol => ol.DefaultChartType))
          .ForMember(dest => dest.EnabledChartTypes, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledChartTypes) ? new List<string>() : ol.EnabledChartTypes.ToListItem()))
          .ForMember(dest => dest.ExcludeStudies, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.ExcludeStudies) ? new List<string>() : ol.ExcludeStudies.ToListItem()))
          .ForMember(dest => dest.EnabledEvents, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledEvents) ? new List<string>() : ol.EnabledEvents.ToListItem()))
          .ForMember(dest => dest.DefaultEvents, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.DefaultEvents) ? new List<string>() : ol.DefaultEvents.ToListItem()))
          .ForMember(dest => dest.EnabledChartPreferences, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledChartPreferences) ? new List<string>() : ol.EnabledChartPreferences.ToListItem()))
          .ForMember(dest => dest.EnabledYAxisPreferences, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledYAxisPreferences) ? new List<string>() : ol.EnabledYAxisPreferences.ToListItem()))
          .ForMember(dest => dest.DefaultPeriod, conf => conf.MapFrom(ol => ol.DefaultPeriod))
          .ForMember(dest => dest.IsStripedBackground, conf => conf.MapFrom(ol => ol.IsStripedBackground))
          .ForMember(dest => dest.HideChartTitle, conf => conf.MapFrom(ol => ol.HideChartTitle))
          .ForMember(dest => dest.EnabledDefaultVolumeChart, conf => conf.MapFrom(ol => ol.EnabledDefaultVolumeChart))
          .ForMember(dest => dest.EnabledHoverEvent, conf => conf.MapFrom(ol => ol.EnabledHoverEvent))
          .ForMember(dest => dest.EnabledPeriods, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledPeriods) ? new List<string>() : ol.EnabledPeriods.ToListItem()))
          .ForMember(dest => dest.EnabledAdditionalOptions, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledAdditionalOptions) ? new List<string>() : ol.EnabledAdditionalOptions.ToListItem()))
          .ForMember(dest => dest.EnabledSocialMedias, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledSocialMedias) ? new List<string>() : ol.EnabledSocialMedias.ToListItem()))
         .ForMember(dest => dest.DefaultVolumeChart, conf => conf.MapFrom(ol => ol.DefaultVolumeChart))
         .ForMember(dest => dest.BlinkingPricePointer, conf => conf.MapFrom(ol => new BlinkingPricePointer()));

      // ShareDetailsConfig to ShareDetails
      CreateMap<ShareDetailsConfig, ShareDetails>(MemberList.Destination)
          .ForMember(dest => dest.Enabled, conf => conf.MapFrom(ol => ol.Enabled))
          .ForMember(dest => dest.DisplayOnSelectedTicker, conf => conf.MapFrom(ol => ol.DisplayOnSelectedTicker))
          .ForMember(dest => dest.DisplayType, conf => conf.MapFrom(ol => ol.DisplayType))
          .ForMember(dest => dest.PartialDisplay, conf => conf.MapFrom(ol => ol.PartialDisplay))
          .ForMember(dest => dest.NumberItemsInCollapsedMode, conf => conf.MapFrom(ol => ol.NumberItemsInCollapsedMode))
          .ForMember(dest => dest.NumberItemsInPrinting, conf => conf.MapFrom(ol => ol.NumberItemsInPrinting))
          .ForMember(dest => dest.ShareDataItems, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.ShareDataItems) ? new List<string>() : ol.ShareDataItems.ToListItem()))
          .ForMember(dest => dest.Print, conf => conf.MapFrom(ol => ol.Print))
          .ForMember(dest => dest.NumberOfShareDisplay, conf => conf.MapFrom(ol =>
            string.IsNullOrEmpty(ol.NumberOfShareDisplay) ? "exact" : ol.NumberOfShareDisplay.ToDisplayFormat()))
          .ForMember(dest => dest.TurnOverDisplay, conf => conf.MapFrom(ol =>
            string.IsNullOrEmpty(ol.TurnOverDisplay) ? "exact" : ol.TurnOverDisplay.ToDisplayFormat()))
          .ForMember(dest => dest.MarketCapDisplay, conf => conf.MapFrom(ol =>
            string.IsNullOrEmpty(ol.MarketCapDisplay) ? "exact" : ol.MarketCapDisplay.ToDisplayFormat()));

      // TickerConfig to Ticker
      CreateMap<TickerConfig, Ticker>(MemberList.Destination)
          .ForMember(dest => dest.EnabledFormat, conf => conf.MapFrom(ol =>
              string.IsNullOrEmpty(ol.EnabledFormat) ? new List<string>() : ol.EnabledFormat.ToListItem()))
          .ForMember(dest => dest.TickerType, conf => conf.MapFrom(ol => ol.TickerType.Trim().ToUpper()))
          .ForMember(dest => dest.GraphTickerType, conf => conf.MapFrom(ol => ol.GraphTickerTemplate))
          .ForMember(dest => dest.TableTickerType, conf => conf.MapFrom(ol => ol.TableTickerTemplate))
          .ForMember(dest => dest.SlidesPerView, conf => conf.MapFrom(ol => ol.SlidesPerView))
          .ForMember(dest => dest.GraphAnimation, conf => conf.MapFrom(ol => ol.GraphAnimation))
          .ForMember(dest => dest.TableAnimation, conf => conf.MapFrom(ol => ol.TableAnimation));
    }
  }
}
