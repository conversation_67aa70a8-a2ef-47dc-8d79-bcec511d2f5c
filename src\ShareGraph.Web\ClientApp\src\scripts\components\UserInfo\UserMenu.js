import { useState, useRef, useEffect } from "react";
import { useAuth } from "../../customHooks/useAuth";
import PromodeModal from "../../pages/promode/promode-manager-modal/PromodeModal";
import { classNames } from "../../helper";

const UserMenu = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const userInfoRef = useRef(null);
  const menuRef = useRef(null);
  const auth = useAuth();

  const user = auth.user;
  const [isModalOpen, setIsModalOpen] = useState(false);

  const menuItems = [
    { label: "Promode", action: () => handlePromode() },
    { label: "Logout", action: () => handleLogout() }
  ];

  const toggleMenu = () => {
    if (!isMenuOpen) {
      setIsMenuOpen(true);
      setFocusedIndex(-1); // Don't auto-focus when opening with mouse
      // Focus will be handled by useEffect
    } else {
      setIsMenuOpen(false);
      setFocusedIndex(-1);
    }
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setFocusedIndex(-1);
  };

  const handlePromode = () => {
    handleOpenModal();
    closeMenu();
  };

  const handleLogout = () => {
    const logout = window.EurolandAppContext?.command("logout");
    logout?.();
    closeMenu();
  };

  const handleKeyDown = (event) => {
    if (!isMenuOpen) {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        setIsMenuOpen(true);
        setFocusedIndex(0); // Set focus to first item when opening with keyboard
      }
      return;
    }

    switch (event.key) {
      case "Escape":
        event.preventDefault();
        closeMenu();
        userInfoRef.current?.focus();
        break;
      case "ArrowDown":
        event.preventDefault();
        setFocusedIndex((prev) => (prev < menuItems.length - 1 ? prev + 1 : 0));
        break;
      case "ArrowUp":
        event.preventDefault();
        setFocusedIndex((prev) => (prev > 0 ? prev - 1 : menuItems.length - 1));
        break;
      case "Enter":
      case " ":
        event.preventDefault();
        if (focusedIndex >= 0) {
          menuItems[focusedIndex].action();
        }
        break;
      default:
        break;
    }
  };

  const handleMenuItemClick = (action) => {
    action();
  };

  const handleMenuItemKeyDown = (event, action, index) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      action();
    }
  };

    // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userInfoRef.current && !userInfoRef.current.contains(event.target)) {
        closeMenu();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus management for keyboard navigation
  useEffect(() => {
    if (isMenuOpen && menuRef.current) {
      if (focusedIndex >= 0) {
        const menuItems = menuRef.current.querySelectorAll(
          ".user-info__menu-item"
        );
        if (menuItems[focusedIndex]) {
          menuItems[focusedIndex].focus();
        }
      } else {
        // Focus the menu container when no specific item is focused
        menuRef.current.focus();
      }
    }
  }, [focusedIndex, isMenuOpen]);

  // Set menu width equal to trigger button width
  useEffect(() => {
    if (isMenuOpen && menuRef.current && userInfoRef.current) {
      const triggerWidth = userInfoRef.current.offsetWidth;
      menuRef.current.style.minWidth = `${triggerWidth}px`;
    }
  }, [isMenuOpen]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const getInitials = (userName) => {
    if (!userName || typeof userName !== 'string') {
      return '';
    }

    const words = userName.trim().split(' ').filter(word => word.length > 0);

    if (words.length === 0) {
      return '';
    }

    if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    }

    return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
  };

  return (
    <>
      <div className="user-info" ref={userInfoRef}>
        <button
          className="user-info__trigger"
          onClick={toggleMenu}
          onKeyDown={handleKeyDown}
          aria-expanded={isMenuOpen}
          aria-haspopup="true"
          aria-label={`User menu for ${user.name}`}
          type="button"
        >
          <div className="user-info__avatar">
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="user-info__avatar-image"
              />
            ) : (
              <span className="user-info__avatar-initials">
                {getInitials(user.name)}
              </span>
            )}
          </div>
          <div className="user-info__name-info">
            <span className="user-info__name">{user.name}</span>

            <span className={classNames("fs-caret-top user-info__arrow", {
              "open": isMenuOpen
            })}></span>
          </div>

        </button>

        {isMenuOpen && (
          <div
            className="user-info__menu"
            ref={menuRef}
            role="menu"
            aria-label="User menu"
            onKeyDown={handleKeyDown}
            tabIndex={-1}
          >
            {menuItems.map((item, index) => (
              <button
                key={item.label}
                className={`user-info__menu-item ${
                  focusedIndex === index ? "user-info__menu-item--focused" : ""
                }`}
                onClick={() => handleMenuItemClick(item.action)}
                onKeyDown={(e) => handleMenuItemKeyDown(e, item.action, index)}
                onMouseEnter={() => setFocusedIndex(index)}
                role="menuitem"
                tabIndex={-1}
                type="button"
              >
                {item.label}
              </button>
            ))}
          </div>
        )}
      </div>

      <PromodeModal isOpen={isModalOpen} onClose={handleCloseModal} />

    </>
  );
};

export default UserMenu;
