import produce from 'immer';

import {
  <PERSON>ETCH_HISTORICAL_BEGIN,
  FETCH_HISTORICAL_FAILURE,
  FETCH_HISTORICAL_SUCCESS
} from '../../actions/accessibles/historicalActions';
import HistoricalData from '../../entities/HistoricalData';

const initialState = {
  instruments: [],
  loading: true,
  refreshing: false,
  fetchError: null,
  refreshError: null
};

const historicalDataReducer = produce((draft, action) => {
  switch (action.type) {
    case FETCH_HISTORICAL_BEGIN:
      draft.loading = true;
      break;
    case FETCH_HISTORICAL_SUCCESS:
      draft.loading = false;
      draft.instruments = action.payload.data.map(d => new HistoricalData(d));
      break;
    case FETCH_HISTORICAL_FAILURE:
      draft.loading = false;
      draft.fetchError = action.payload.error;
      break;
    default:
      break;
  }
}, initialState);

export default historicalDataReducer;
