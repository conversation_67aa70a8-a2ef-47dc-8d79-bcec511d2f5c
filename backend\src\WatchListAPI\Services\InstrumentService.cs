﻿using WatchListAPI.Common;
using WatchListAPI.DTOs;
using WatchListAPI.Infrastructure;
using WatchListAPI.Infrastructure.UnitOfWorks;

namespace WatchListAPI.Services
{
    public interface IInstrumentService
    {
        Task<PagingResult<InstrumentDto>> GetInstrumentsAsync(PagingRequestBaseDto input);
    }
    public class InstrumentService : IInstrumentService
    {
        private readonly IEurolandIDProfileUnitOfWork _eurolandIDProfileUnitOfWork;
        public InstrumentService(IEurolandIDProfileUnitOfWork eurolandIDProfileUnitOfWork)
        {
            _eurolandIDProfileUnitOfWork = eurolandIDProfileUnitOfWork;
        }

        public async Task<PagingResult<InstrumentDto>> GetInstrumentsAsync(PagingRequestBaseDto input)
        {
            return await _eurolandIDProfileUnitOfWork.IntrumentRepository.GetInstrumentsAsync(input);
        }
    }
}
