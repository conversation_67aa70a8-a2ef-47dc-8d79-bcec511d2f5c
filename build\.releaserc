{"branches": ["master", {"name": "next", "prerelease": true}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "../CHANGELOG.md"}], ["@semantic-release/exec", {"analyzeCommitsCmd": "node ./release.js -o \"../src/Version.props\" -v ${lastRelease.version}", "verifyReleaseCmd": "node ./release.js -o \"../src/Version.props\" -v ${nextRelease.version} -r \"${nextRelease.notes}\""}], ["@semantic-release/git", {"assets": ["src/Version.props", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/gitlab", {"gitlabUrl": "https://gitlab.euroland.com", "assets": ["../src/Version.props", "../CHANGELOG.md", "../doc/**"]}]]}