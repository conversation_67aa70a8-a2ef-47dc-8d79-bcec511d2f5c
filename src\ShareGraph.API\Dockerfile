FROM mcr.microsoft.com/dotnet/aspnet:5.0-focal AS base
WORKDIR /app
EXPOSE 5003
EXPOSE 5001

ENV ASPNETCORE_URLS=http://+:80
#ENV ASPNETCORE_ENVIRONMENT=Docker

# Creates a non-root user with an explicit UID and adds permission to access the /app folder
# For more info, please refer to https://aka.ms/vscode-docker-dotnet-configure-containers
#RUN adduser -u 5678 --disabled-password --gecos "" appuser && chown -R appuser /app
#USER appuser

#RUN apt-get install -y cifs-utils

FROM mcr.microsoft.com/dotnet/sdk:5.0-focal AS build
WORKDIR /src

# It's important to keep lines from here down to "COPY . ." identical in all Dockerfiles
# to take advantage of <PERSON><PERSON>'s build cache, to speed up local container builds
COPY ["src/ShareGraph.Web/ShareGraph.App.csproj", "src/ShareGraph.Web/"]
COPY ["src/ShareGraph.UnitTests/ShareGraph.UnitTests.csproj", "src/ShareGraph.UnitTests/"]
COPY ["src/ShareGraph.API/ShareGraph.API.csproj", "src/ShareGraph.API/"]

COPY ["./Directory.Build.props", "./"]
COPY ["./FlipIT.ShareGraph.sln", "./"]
COPY ["./NuGet.config", "./"]

RUN dotnet restore "FlipIT.ShareGraph.sln"

COPY . .

WORKDIR "/src/src/ShareGraph.API"
RUN dotnet build "ShareGraph.API.csproj" --no-restore -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ShareGraph.API.csproj" --no-restore -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN ls
ENTRYPOINT ["dotnet", "Euroland.FlipIT.ShareGraph.API.dll"]
