using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.ShareGraph.API.Types
{
    public class ConfigurationType : ObjectType<Configuration>
    {
        protected override void Configure(IObjectTypeDescriptor<Configuration> descriptor)
        {
            descriptor
                .Field(t => t.Setting)
                .ResolveWith<ConfigurationResolver>(c => c.GetSettings(default!));
        }
    }
}
