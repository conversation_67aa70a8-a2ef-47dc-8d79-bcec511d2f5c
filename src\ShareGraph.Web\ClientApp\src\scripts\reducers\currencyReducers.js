import produce from 'immer';
import { ENABLED_CURRENCY_OPTION, SWITCH_CURRENCY, UPDATE_CURRENCY_RATE_BEGIN, UPDATE_CURRENCY_RATE_FAILURE, UPDATE_CURRENCY_RATE_SUCCESS } from '../actions/currencyAction';

/**
 * @typedef {{text?: string, code?: string, decimalDigits?: number }} CurrencyOption
 * @typedef {{date: string, value: number }} CurrencyRate
 * @typedef {{
 *  loading: boolean, 
 *  error: object | null
 * }} GetCurrencyRate
 */
// currentRate?: number
const initState = {
  currency: /** @type{CurrencyOption | undefined }*/(undefined),
  currencyRate: /** @type{{ [code: string]: CurrencyRate }}*/({}),
  getCurrencyRate: /** @type{ GetCurrencyRate }*/({loading: false, error: null}),
  enabledCurrencyOption: /** @type{ Boolean }*/(true)
};

export default function createCurrencyReducers() {
  return function currencyReducers(state = initState, action) {
    const payload = action.payload;
    switch (action.type) {
      case SWITCH_CURRENCY:
        return produce(state, draft => {
          const selected = payload.currencyCode;
          draft.currency = selected;
        });
      case UPDATE_CURRENCY_RATE_BEGIN:
        return produce(state, draft => {
          draft.getCurrencyRate.loading = true;
          draft.getCurrencyRate.error = null;
        });
      case UPDATE_CURRENCY_RATE_SUCCESS:
        return produce(state, draft => {
          payload.currencyRates.forEach(({baseCurrencyCode, currencyRate}) => {
            draft.currencyRate[baseCurrencyCode] = currencyRate;
          });
          draft.getCurrencyRate.loading = false;
        });
      case UPDATE_CURRENCY_RATE_FAILURE:
        return produce(state, draft => {
          draft.getCurrencyRate.loading = false;
          draft.getCurrencyRate.error = payload.error;
        });
      case ENABLED_CURRENCY_OPTION:
        return produce(state, draft => {
          draft.enabledCurrencyOption = payload.enabledCurrencyOption;
        });
        
      default:
        return state;
    }
  };
}