import { CIQ } from 'chartiq/js/standard';
import { appSettings } from '../../../../appSettings';
import { setDefaultOutputForStudies } from './setDefaultOutputForStudies';
import { TIME_DELAY_SHOW_TICKER } from '../../../constant/common';

export default function onChartReady({shareGraphSettings, config}, stx) {
  const { defaultPeriod } = shareGraphSettings.chart;
  stx.chart.xAxis.fitLeftToRight = true;
  //get language code by region code
  let currentRegionCode = appSettings.language || 'en-GB';
  var languagesCode = Object.keys(CIQ.I18N.langConversionMap);
  var currentLanguageCode = 'en';
  languagesCode.forEach((languageCode) => {
    if (CIQ.I18N.langConversionMap[languageCode] === currentRegionCode) {
      currentLanguageCode = languageCode;
    }
  });
  CIQ.I18N.localize(stx, currentLanguageCode);
  stx.bypassRightClick = {
    series: true,
    study: true,
    drawing: false
  };
  stx.preferences = {
    ...stx.preferences,
    labels: true,
    currentPriceLine: true,
    whitespace: 100
  };

  stx.chart.yAxis.drawCurrentPriceLabel = true;
  stx.chart.xAxis.initialMarginBottom = 200;
  setDefaultOutputForStudies(stx);
  stx.chart.CurrentEvent = config.currentEvent || '';
  /*
  stx.range = {
    selectedRange: config.selectedRange || defaultPeriod || '1Y'
  };*/ 
  stx.streamParameters.maxWait = TIME_DELAY_SHOW_TICKER;
  stx.streamParameters.maxTicks = 1000;
  stx.axisBorders = false;

  stx.chart.xAxis.displayGridLines = false;
  stx.chart.yAxis.displayGridLines = false;
  stx.chart.yAxis.position =
    getComputedStyle(document.body).getPropertyValue(
      '--chartiq-yaxis-position'
    ) || stx.chart.yAxis.position;
  stx.yaxisLabelStyle = 'roundRect';
  stx.layout.stripedBackground = Boolean(
    shareGraphSettings.chart.isStripedBackground
  );

  // new CIQ.Animation(stx, { tension: 0.3, ticksFromEdgeOfScreen: 10 });
  stx.mainSeriesRenderer.supportsAnimation = false;
  // stx.chart.yAxis.displayBorder = false;

  stx.insideChart = false;
}
