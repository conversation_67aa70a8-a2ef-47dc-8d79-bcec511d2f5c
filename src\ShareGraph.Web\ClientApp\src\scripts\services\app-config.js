const appConfig = (function () {
  function AppConfig() {
    this._settings = {};
  }

  AppConfig.prototype.set = function (settings) {
    this._settings = settings;
  };

  /**
   *
   * @returns {import("../../../types/configuration-app").IConfigurationSetting}
   */
  AppConfig.prototype.get = function () {
    return this._settings;
  };

  return new AppConfig();
})();

export default appConfig;

