# Mount ting windows file share to the Linux docker image

Starting from ShareGraph3, we will start to deploy tools, API(s) to Linux docker image. And sometimes we may come accross scenarios where out tool interact with network file share (UNC path - windows) to read or writing something to a file. The problem is that when the dockerized application needs accessing to a file will not work because container may not have access to that file share. In order to access it, the network file share need to be mounted to the docker container. It can be done through a docker-compose file.

To mount the file share, you need to create a volume with CIFS and local driver and for that, you need following things

```YML
volumes:
  setting_storage:
    driver: local
    driver_opts:
      type: cifs
      o: username=docker,password=123@abc,rw #username=<username>,password=<password>,rw,domain=<domain_name>
      device: "\\\\**********\\wwwroot\\tools\\FlipIT.ShareGraph.API\\Config"
```

Here, setting_storage is the volume name and driver to mention where you want to create.
Next map the created volume to a local path(where you wanted to be on your container, if you wanted it to be on the new directory, just provide the name of it, it will create the directory and map to it) under services volumes section.

The complete docker-compose file:

```YML
volumes:
  setting_storage:
    driver: local
    driver_opts:
      type: cifs
      o: username=docker,password=123@abc,rw
      device: "\\\\**********\\wwwroot\\tools\\FlipIT.ShareGraph.API\\Config"
  general_setting_storage:
    driver: local
    driver_opts:
      type: cifs
      o: username=docker,password=123@abc,rw
      device: "\\\\**********\\wwwroot\\tools\\Config"

services:
  sharegraph3:
    image: sharegraph/web
    cap_add:
      - SYS_ADMIN
    environment:
      - ASPNETCORE_ENVIRONMENT=Docker
      - ShareGraphApiURL=http://localhost:5003/graphql
      - SDataApiUrl=http://localhost:5005/graphql
      - IntegrationScriptUrl=https://dev.vn.euroland.com/tools/common/integration.js
      #- BlobAttachmentContainerURL=https://portalvhds1fxb0jchzgjph.blob.core.windows.net/press-releases-attachments
    build:
      context: .
      dockerfile: src/ShareGraph.Web/Dockerfile
    volumes:
      - setting_storage:/app/Config
      - general_setting_storage:/app/GeneralConfig
    ports:
      - 50002:80
  sharegraph-api:
    image: sharegraph/api
    cap_add:
      - SYS_ADMIN
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - GeneralSettingsPath=./GeneralConfig,
      - ToolSettingsPath=./Config
    build:
      context: .
      dockerfile: src/ShareGraph.API/Dockerfile
    volumes:
      - setting_storage:/app/Config
      - general_setting_storage:/app/GeneralConfig
    ports:
      - 5003:80
```
