import i18n from '../../services/i18n';
import useSharePricePerformance from '../../customHooks/useSharePricePerformance';
import {
  convertChangePercentDecimal,
  convertNumber,
  formatDateTimeAccessibility,
  getMarketStatusLabel,
  getOpenCloseTimeMarketDisplay,
  stringifyQuery,
  translateStringFormat
} from '../../helper';
import { useDispatch, useSelector } from 'react-redux';
import { appSettings } from '../../../appSettings';
import { Link } from 'react-router-dom';
import { formatNumberChangeByInstrument } from '../../utils/format-number';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import {useChangeQuoteCurrency, useSelectedCurrency} from '../../customHooks/useSelectedCurrency';
import { fetchTickers } from '../../actions';
import { getAllInstrumentsSetting } from '../../configs/configuration-app';
import { useSelectedTicker } from '../../customHooks/useTickers';

const SharePricePerformance = () => {
  const dispatch = useDispatch();
  const { formatNumberByInstrument } = useFormatNumberByInstrument();
  const [{ selectedSharePrice }] = useSharePricePerformance();
  const selectedTicker = useSelectedTicker();
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
  const isRT = getAllInstrumentsSetting()[selectedInstrumentId]?.isRT ?? false;
  const selectedCurrency = useSelectedCurrency();
  const getDownloadExcelLink = () => {
    const { companyCode, language, companyCodeVersion } = appSettings;

    return {
      pathname: '/download/dailydata',
      search: stringifyQuery({
        companycode: companyCode,
        lang: language,
        v: companyCodeVersion,
        selectInstrumentId: selectedInstrumentId,
        isRT: isRT,
        toCurrency: selectedCurrency?.code??''
      })
    };
  };

  useChangeQuoteCurrency(() => {
    dispatch(fetchTickers());
  });

  return (
    <section className="share-price-today">
      <h2 className="share-price-today__title">{i18n.translate('sharePriceTitle')}</h2>
      <p className="share-price-today__datetime">
        {formatDateTimeAccessibility(selectedSharePrice.lastUpdatedDate)}
      </p>

      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('openPrice', [
            formatNumberByInstrument(selectedSharePrice.open, selectedInstrumentId),
            (typeof selectedSharePrice.open === 'number' || selectedSharePrice.open) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />

      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('lastPrice', [
            formatNumberByInstrument(selectedSharePrice.last, selectedInstrumentId),
            (typeof selectedSharePrice.last === 'number' || selectedSharePrice.last) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('changeInCurrency', [
            formatNumberChangeByInstrument(selectedSharePrice.change, selectedInstrumentId),
            (typeof selectedSharePrice.change === 'number' || selectedSharePrice.change) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('changeInPercentage', [
            convertChangePercentDecimal(selectedSharePrice.changePercentage)
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('highestPrice', [
            formatNumberByInstrument(selectedSharePrice.high, selectedInstrumentId),
            (typeof selectedSharePrice.high === 'number' || selectedSharePrice.high) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('lowestPrice', [
            formatNumberByInstrument(selectedSharePrice.low, selectedInstrumentId),
            (typeof selectedSharePrice.low === 'number' || selectedSharePrice.low) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('sharesTraded', [
            convertNumber(selectedSharePrice.volume),
            (typeof selectedSharePrice.volume === 'number' || selectedSharePrice.volume) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('bidPrice', [
            formatNumberByInstrument(selectedSharePrice.bid, selectedInstrumentId),
            (typeof selectedSharePrice.bid === 'number' || selectedSharePrice.bid) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />
      <p
        className="share-price-today__price"
        dangerouslySetInnerHTML={{
          __html: translateStringFormat('askPrice', [
            formatNumberByInstrument(selectedSharePrice.ask, selectedInstrumentId),
            (typeof selectedSharePrice.ask === 'number' || selectedSharePrice.ask) ? selectedSharePrice.currencyCode : ''
          ])
        }}
      />

      <p className="share-price-today__price">
        {`${selectedSharePrice.marketName} ${getMarketStatusLabel(
          selectedSharePrice
        )}. ${getOpenCloseTimeMarketDisplay(selectedSharePrice, selectedTicker?.timezoneIANA)}`}
      </p>

      <p className="share-price-today__dataDelayed">{translateStringFormat('dataDelayed', [15])}</p>
      <Link className="share-price-today__excelDownload" target="_blank" to={getDownloadExcelLink()}>
        {i18n.translate('excelDownload')}
      </Link>
    </section>
  );
};

export default SharePricePerformance;
