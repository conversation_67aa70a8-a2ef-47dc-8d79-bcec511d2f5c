import produce from 'immer';
import { SET_BLIND_MODE } from '../actions/blindModeAction';

export default function blindModeReducer({enabled = false, defaultSelected= false} = {}) {
  const initState = {
    isBlindMode: defaultSelected,
    enabledBlindMode: enabled
  };

  /**
   * @param {typeof initState} state
   * @param {{type: string, payload: boolean}} action
   */
  function reducer(state = initState, action) {
    switch(action.type) {
      case SET_BLIND_MODE: {
        return produce(state, draft => {
          draft.isBlindMode = !state.isBlindMode;
        });
      }
      default: {
        return state;
      }
    }
  }

  return reducer;
}
