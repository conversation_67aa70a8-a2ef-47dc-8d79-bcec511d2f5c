import { CIQ } from '../chartiq-import';
import { cqdElementStyle, cqdialogsTop } from './mutation_variable';

// Access the class definition of the web component.
const CQDialog = CIQ.UI.components('cq-dialog')[0].classDefinition;
// Access the class definition of the web component.
//xtend the web component class.
const centerFunc = CQDialog.prototype.center;
CQDialog.prototype.center = function () {
  const isStickyDialog =
    Object.prototype.hasOwnProperty.call(this, 'params') &&
    this.params.stx.container.closest('cq-context').getAttribute('cq-sticky-dialog') === 'true';
  if (!isStickyDialog) {
    centerFunc.call(this);
    return;
  }
  let top = 50;
  let left = 50;
  let offset = 100;
  var chart = this.params.stx.container.closest('.graph');
  const addtionalHeight = 0;
  var h = chart.offsetHeight + addtionalHeight;
  var w = chart.offsetWidth;
  top = chart.offsetTop + (h - this.offsetHeight) / 2;
  left = chart.offsetLeft + (w - this.offsetWidth) / 2;
  var elementStyle = {
    top: top + 'px',
    left: left + 'px',
    position: 'absolute',
    width: this.offsetWidth + 'px'
  };
  var cqPicker = document.querySelector('cq-color-picker');
  var yTop;
  cqPicker.style.left = left + this.offsetWidth + 'px';
  cqdElementStyle.value = { left: left + this.offsetWidth, height: this.offsetHeight, top: top };
  cqdialogsTop.value = cqdElementStyle.height + cqdElementStyle.top - 166;
  if (cqPicker.offsetTop > cqdialogsTop.value) yTop = cqdialogsTop.value;
  cqPicker.style.top = yTop + 'px';
  Object.assign(this.style, elementStyle);
};

const stxContextMenuFunc = CQDialog.prototype.stxContextMenu;
CQDialog.prototype.stxContextMenu = function() {
  stxContextMenuFunc.call(this);
  this.style.position = 'absolute';

  if(this.getAttribute('cq-drawing-context') !== 'true') {
    this.style.left = parseFloat(this.style.left) + window.scrollX + 'px';
    this.style.top = parseFloat(this.style.top) + window.scrollY + 'px';
    return;
  }
  const rect = this.context.topNode.getBoundingClientRect();
  this.style.left = parseFloat(this.style.left) - rect.x + 'px';
  this.style.top = parseFloat(this.style.top) - rect.y + 'px';
};

/**
 * Renders the title on the dialog, if applicable
 *
 * @return {HTMLElement} Element containing the title.
 * @tsmember WebComponents.Dialog
 * @private
 */
CQDialog.prototype._renderTitleElement = function() {
  const title = this.getAttribute('cq-title') || '';
  const titleId = this.getAttribute('aria-labelledby');
  let titleEl = this.querySelector('#' + titleId);

  if (!title && titleEl) titleEl.remove();
  else if (title) {
    if (!titleEl) {
      titleEl = document.createElement('h3');
      titleEl.id = titleId;
      this.prepend(titleEl);
    }

    if (this.context)
      CIQ.makeTranslatableElement(titleEl, this.context.stx, title);
    else titleEl.innerText = title;
  }

  if (titleEl) {
    let sibling = titleEl.nextSibling;
    const hasDesc = sibling && sibling.matches('p[id]');
    const description = this.getAttribute('cq-description') || '';
    if (!description) {
      if (hasDesc) sibling.remove();
    } else {
      if (!hasDesc) {
        sibling = document.createElement('p');
        // Add the description directly after the title
        titleEl.parentElement.insertBefore(sibling, titleEl.nextSibling);
      }
      sibling.id = this.getAttribute('aria-describedby');
      if (this.context)
        CIQ.makeTranslatableElement(sibling, this.context.stx, description);
      else sibling.innerHTML = description;
    }
  }
  return titleEl;
};