import { CIQ } from 'chartiq/js/components';
import { appSettings } from '../../../appSettings';

CIQ.Euroland.Studies.calculateBollinger = function (stx, sd) {
  var field = sd.inputs.Field;
  if (!field || field == 'field') field = 'Close';

  CIQ.Studies.MA(
    sd.inputs['Moving Average Type'],
    sd.days,
    field,
    0,
    '_MA',
    stx,
    sd
  );

  sd.std = new CIQ.Studies.StudyDescriptor(sd.name, 'STD Dev', sd.panel);
  sd.std.chart = sd.chart;
  sd.std.startFrom = sd.startFrom;
  sd.std.days = sd.days;
  sd.std.inputs = {
    Field: field,
    'Standard Deviations': 1,
    Type: sd.inputs['Moving Average Type']
  };
  sd.std.outputs = { '_STD Dev': null };
  CIQ.Studies.calculateStandardDeviation(stx, sd.std);

  CIQ.Studies.calculateGenericEnvelope(
    stx,
    sd,
    sd.inputs['Standard Deviations'],
    '_MA ' + sd.name,
    '_STD Dev ' + sd.name
  );
  if (sd.type == 'Boll %b') sd.zoneOutput = '%b';
};

const studyName = CIQ.I18N.translate('Bollinger Bands');
CIQ.Euroland.Studies.studyLibrary = CIQ.extend(CIQ.Studies.studyLibrary, {
  [studyName]: {
    name: studyName,
    overlay: true,
    calculateFN: CIQ.Euroland.Studies.calculateBollinger,
    seriesFN: CIQ.Studies.displayChannel,
    inputs: {
      Period: 20,
      //Field: 'field',
      'Standard Deviations': 2,
      'Moving Average Type': 'ma',
      'Channel Fill': true
    },
    outputs: {
      'Bollinger Bands Top': 'auto',
      'Bollinger Bands Median': 'auto',
      'Bollinger Bands Bottom': 'auto'
    },
    attributes: { 'Standard Deviations': { min: 0.1, step: 0.1 } }
  },
  // 'bollinger_%b': {
  //   name: 'Bollinger %b',
  //   calculateFN: CIQ.Euroland.Studies.calculateBollinger,
  //   inputs: {
  //     Period: 20,
  //     Field: 'field',
  //     'Standard Deviations': 2,
  //     'Moving Average Type': 'ma'
  //   },
  //   outputs: { '%b': 'auto' },
  //   parameters: {
  //     init: {
  //       studyOverZonesEnabled: true,
  //       studyOverBoughtValue: 100,
  //       studyOverBoughtColor: 'auto',
  //       studyOverSoldValue: 0,
  //       studyOverSoldColor: 'auto'
  //     }
  //   },
  //   attributes: { 'Standard Deviations': { min: 0.1, step: '0.1' * 1 } }
  // },
  // 'bollinger_bw': {
  //   name: 'Bollinger Bandwidth',
  //   calculateFN: CIQ.Euroland.Studies.calculateBollinger,
  //   inputs: {
  //     Period: 20,
  //     Field: 'field',
  //     'Standard Deviations': 2,
  //     'Moving Average Type': 'ma'
  //   },
  //   outputs: { Bandwidth: 'auto' },
  //   attributes: { 'Standard Deviations': { min: 0.1, step: 0.1 } }
  // }
});

export { CIQ };
