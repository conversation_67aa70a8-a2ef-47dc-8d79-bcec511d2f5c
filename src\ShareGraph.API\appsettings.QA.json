{"Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**********", "port": "514", "appName": "FlipIT.ShareGraph.API", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "FlipIT.ShareGraph.API"}}, "GeneralSettingsPath": "..\\Config", "ToolSettingsPath": "Config", "OpifexToolCompanySettingsPath": "..\\..\\tools\\Opifex2-API\\wwwroot\\ShareGraph3\\Config\\Company", "OpifexGeneralSettingDirectory": "..\\..\\tools\\Opifex2-API\\wwwroot\\Config\\Company", "OpifexPreviewEnabled": true, "ConnectionStrings": {"TranslationDbContext": "Server=**********;Database=Shark;User=uShark;PWD=**********;TrustServerCertificate=true;Application Name=ShareGraph.API;"}, "InternalSDataApiUrl": "http://localhost:88/tools/flipit-sdata-api/graphql"}