import { useLayoutEffect, useState } from 'react';
import { dynamicSort } from '../helper';

const useOrderedInstruments = ({ data, settings }) => {
  const [orderedData, setOrderedData] = useState([]);

  useLayoutEffect(() => {
    const arrMapping = (data || [])
      .map(x => {
        return {
          ...x,
          order: settings.find(y => y.id === x.instrumentId).order
        };
      })
      .sort(dynamicSort('order'));

    setOrderedData(arrMapping);
  }, [data]);

  return [{ orderedData }];
};

export default useOrderedInstruments;
