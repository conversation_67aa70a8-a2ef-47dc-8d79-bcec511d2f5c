using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
    /// <summary>
    /// Configuration for share ticker
    /// </summary>
    public class Ticker
    {
        public IEnumerable<string> EnabledFormat { get; set; }
        public string TickerType { get; set; }
        public string GraphTickerType { get; set; } // typeof template
        public string GraphTickerTemplate { get; set; } // html content
        public string TableTickerType { get; set; } // typeof template
        public string TableTickerTemplate { get; set; } // html content
        public int SlidesPerView {get; set;}
        public string GraphAnimation {get; set;}
        public string TableAnimation {get; set;}
    }

    public class TickerConfig
    {
        public string EnabledFormat { get; set; }
        public string TickerType { get; set; }
        public string GraphTickerTemplate { get; set; }
        public string TableTickerTemplate { get; set; }
        public int SlidesPerView {get; set;}
        public string GraphAnimation {get; set;}
        public string TableAnimation {get; set;}
    }
}
