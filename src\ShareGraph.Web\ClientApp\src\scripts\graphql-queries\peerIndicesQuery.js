import {gql} from './utils';

export const PEER_INDICES_QUERY = gql(/* GraphQL */`query PeerIndices(
  $ids: [Int!]!
  $adjClose: Boolean
  $additionalRealtimeIds: [Int!]!
  $toCurrency: String
) {
  instrumentByIds(
    ids: $ids
    exchangeCurrency: $toCurrency
    adjClose: $adjClose
  ) {
    ...PeerIndicesData
  }

  additionalRealtime: instrumentByIds(
    ids: $additionalRealtimeIds
    exchangeCurrency: $toCurrency
  ) {
    id
    currentPrice {
      officialClose
      officialCloseDate
    }
  }
}

fragment PeerIndicesData on Instrument {
  id
  shareName
  symbol
  market {
    timezoneName
    abbreviation
    openTimeLocal
    closeTimeLocal
    translation {
      value
    }
    status {
      isOpened
      remainingTime
    }
  }
  currency {
    code
    name
    symbol
  }
  currentPrice {
    open
    prevClose
    volume
    officialClose
    officialCloseDate
    lastRowChange
    last
    changePercentage
    low
    change
    date
  }
}
`);