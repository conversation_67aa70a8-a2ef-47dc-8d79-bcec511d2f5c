import { useSelector } from 'react-redux';
import { Skeleton } from '../Skeleton';
import { Peer } from './Peer';
import { Indices } from './Indices';
import { SWITCHER_TYPE, COMPARISON_SWITCH_TYPE } from '../../common';
import Switcher from '../Switcher';
import useComparison from '../../customHooks/useComparison';

export const Comparison = () => {
  const { comparisonType, handleSwitchFormat, enableComparisonTypes, isType } = useComparison();
  const fetchLoadingPeer = useSelector(state => state.peers.loading);
  const fetchLoadingIndices = useSelector(state => state.indices.loading);

  return (
    <div className='comparison'>
        <Switcher
          onClick={handleSwitchFormat}
          type={SWITCHER_TYPE.COMPARISON}
          tabs={enableComparisonTypes} 
          tabActive={comparisonType}
          isButton={false}
        />
        {(fetchLoadingPeer && isType(COMPARISON_SWITCH_TYPE.PEER_TYPE)) && <Skeleton />}
        {(fetchLoadingIndices && isType(COMPARISON_SWITCH_TYPE.INDICES_TYPE)) && <Skeleton />}
        
        {
          !fetchLoadingPeer && isType(COMPARISON_SWITCH_TYPE.PEER_TYPE) && 
          <div id="peers" className="peers">
            <Peer />
          </div>
        }
        {
          !fetchLoadingIndices && isType(COMPARISON_SWITCH_TYPE.INDICES_TYPE) && 
          <div id="indices" className="indices"><Indices /></div>
        }
        
    </div>
  );
};
