using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using HotChocolate;

namespace Euroland.FlipIT.ShareGraph.API.Resolvers
{
  public class BlinkingPricePointerResolver
  {
    public bool GetEnabled([Service] ISetting _settings)
    {
      var section = _settings.GetChild($"Chart:BlinkingPricePointer");
      if (section.Exists())
      {
        return section.GetValue<bool>("Enabled", false);
      }

      return false;
    }
  }
}
