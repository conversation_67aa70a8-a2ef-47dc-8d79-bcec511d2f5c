import { useEffect, useLayoutEffect, useRef } from 'react';
import useResize, { UserAgent } from '../customHooks/useResize';

const withScrollSwipe = Component => {
  return props => {
    const wrapperRef = useRef();
    const scrollSwipeRef = useRef();
    const isTouchable = useRef(false);
    const { width } = useResize();

    useOnClickOutsideElement(wrapperRef, () => {
      if (!isTouchable.current) return;
      hasScrollSwipe();
    });

    const hasScrollSwipe = () => {
      if (!scrollSwipeRef.current || !wrapperRef.current) return;
      wrapperRef.current.style.setProperty('position', 'relative');
      scrollSwipeRef.current.style.setProperty('position', 'absolute');
      scrollSwipeRef.current.style.setProperty('left', '0');
      scrollSwipeRef.current.style.setProperty('right', '0');
      scrollSwipeRef.current.style.setProperty('z-index', '9999');
      scrollSwipeRef.current.style.setProperty('height', '100%');
    };

    const hideScrollSwipe = () => {
      if (!scrollSwipeRef.current || !wrapperRef.current) return;
      wrapperRef.current.style.setProperty('position', '');
      scrollSwipeRef.current.style.setProperty('z-index', '-1');
      scrollSwipeRef.current.style.setProperty('position', '');
      scrollSwipeRef.current.style.setProperty('height', '');
    };

    useLayoutEffect(() => {
      isTouchable.current = UserAgent.isMobile || width < 850;
      if (isTouchable.current) {
        hasScrollSwipe();
      }
    }, [width]);

    useEffect(() => {
      if (!wrapperRef.current) return;
      wrapperRef.current.addEventListener('click', () => {
        if (!isTouchable.current) return;
        hideScrollSwipe();
      });
    }, [wrapperRef.current, scrollSwipeRef.current]);

    if (UserAgent.isMobile || width < 850) {
      return (
        <div ref={wrapperRef}>
          <div ref={scrollSwipeRef} />
          <Component {...props} />
        </div>
      );
    }

    return <Component {...props} />;
  };
};

/**
 *
 * @param {{current: import('react').ReactNode}} ref
 * @param {() => void} callback
 */
const useOnClickOutsideElement = (ref, callback = () => {}) => {
  const positionElements = useRef({ top: 0, right: 0, bottom: 0, left: 0 });
  const setPositionElemnts = container => {
    const { left, top, bottom, right } = container.getBoundingClientRect();
    positionElements.current.top = top + window.scrollY;
    positionElements.current.left = left + window.scrollX;
    positionElements.current.bottom = bottom + window.scrollY;
    positionElements.current.right = right + window.scrollX;
  };
  useEffect(() => {
    const container = ref?.current;
    if (!container) return;

    setPositionElemnts(container);
    const handleScroll = () => {
      setPositionElemnts(container);
    };

    const handleExecuteCallback = (event, callbck) => {
      if (
        event.pageY < positionElements.current.top ||
        event.pageY > positionElements.current.bottom ||
        event.pageX > positionElements.current.right ||
        event.pageX < positionElements.current.left
      ) {
        callbck();
      }
    };
    const hanldeClick = event => {
      handleExecuteCallback(event, callback);
    };
    const hanldeTouchStart = e => {
      const event = e.touches?.[0] || {};
      handleExecuteCallback(event, callback);
    };
    document.addEventListener('scroll', handleScroll);
    document.addEventListener('mousedown', hanldeClick);
    document.addEventListener('touchstart', hanldeTouchStart);

    return () => {
      document.removeEventListener('scroll', handleScroll);
      document.removeEventListener('mousedown', hanldeClick);
      document.removeEventListener('touchstart', hanldeTouchStart);
    };
  }, [ref?.current]);
};

export default withScrollSwipe;
