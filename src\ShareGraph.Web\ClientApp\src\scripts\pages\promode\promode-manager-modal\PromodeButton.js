import { useState } from "react";
import "./PromodeButton.scss";
import PromodeModal from "./PromodeModal";

export default function PromodeButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <button className="promode-button" onClick={handleOpenModal}>
        Open Promode
      </button>

      <PromodeModal isOpen={isModalOpen} onClose={handleCloseModal} />
    </>
  );
}
