import { useId, useRef, useState } from 'react';
import { safeInterval } from '../../utils';
import { useEffect, useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import classNames from 'classnames';

import { SWITCHER_TYPE, TEMPLATE_TICKER, TICKER_SWITCH_TYPE } from '../../common';
import { fetchTickers, refreshTickers } from '../../actions/tickerActions';
import { SingleTicker } from './SingleTicker';
import { MultipleTicker } from './MultipleTicker';
import Switcher from '../Switcher';
import { AppContext } from '../../AppContext';
import { getContainerTickerByType, getLabelsTicker } from '../../helper';
import { Skeleton } from '../Skeleton';
import i18n from '../../services/i18n';
import { useLazyActiveInstrument } from '../../customHooks';
import useStocksRealtime from '../../real-time/useStockRealtime';
import { updateStock} from '../../actions';
import useStockSnapshot from '../../real-time/useStockSnapshot';
import TimeStamp from '../TimeStampV2';
import { updateDefaultDateTime } from '../../utils/dayjs/formatDate';
import useAppSelector from '../../customHooks/useAppSelector';
import { useChangeQuoteCurrency, useTriggerUpdateRateCurrency } from '../../customHooks/useSelectedCurrency';
import CurrencySelection from '../CurrencySelection';
import appConfig from '../../services/app-config';

export const Ticker = ({ tickerType, isPrint, onSwitchTickerType = () => {} }) => {
  const settings = useContext(AppContext);
  const enabledInstruments = settings.instruments; //.filter(e => e.enabled);
  const { lazySelectedInstrument, setLazySelectedInstrument} = useLazyActiveInstrument();

  const uniqueId = useId();
  const graphTypeId = `${uniqueId}-graph_type`;
  const tableTypeId = `${uniqueId}-table_type`;

  const setDefaultTickerFormat = format => {
    return (
      settings.ticker.enabledFormat.length > 0 && settings.ticker.enabledFormat[0].toUpperCase() === format
    );
  };
  const tickerFormatTypes = [
    {
      type: TICKER_SWITCH_TYPE.GRAPH_TYPE,
      icon: 'fs-graph',
      isDefaultSelected: setDefaultTickerFormat(TICKER_SWITCH_TYPE.GRAPH_TYPE),
      dataTooltip: i18n.translate('graphTooltipLabel'),
      text: i18n.translate('graphTooltipLabel'),
      id: graphTypeId
    },
    {
      type: TICKER_SWITCH_TYPE.TABLE_TYPE,
      icon: 'fs-table',
      isDefaultSelected: setDefaultTickerFormat(TICKER_SWITCH_TYPE.TABLE_TYPE),
      dataTooltip: i18n.translate('tableTooltipLabel'),
      text: i18n.translate('tableTooltipLabel'),
      id: tableTypeId
    }
  ];
  const [tickerFormat, setTickerFormat] = useState(tickerFormatTypes.find(t => t.isDefaultSelected).type);
  const tickerContainerRef = useRef();
  const REFRESH_INTERVAL = settings.tickerRefreshSeconds ? settings.tickerRefreshSeconds * 1000 : 10000;
  const isSingleTicker = tickerType.toLowerCase() === TEMPLATE_TICKER.SINGLE;

  const tickerData = useSelector(state => state.tickers.instruments);
  const fetchLoading = useSelector(state => state.tickers.loading);
  const updateStrategy = useAppSelector(state => state.tickers.updateStrategy);
  const dispatch = useDispatch();

  const selectedTicker = isSingleTicker ? tickerData[0] : tickerData.find(x => x.instrumentId === lazySelectedInstrument);

  useEffect(() => {
    const cancelInterval = safeInterval(() => dispatch(refreshTickers()), REFRESH_INTERVAL);
    return function () {
      cancelInterval.cancel();
    };
  }, [dispatch]);

  useEffect(()=>{
    if(!lazySelectedInstrument|| fetchLoading) return;
    const instrumentSetting = settings.instruments.find(x=>x.id === lazySelectedInstrument);
    let timeZone = instrumentSetting?.customTimeZone || selectedTicker?.timezoneIANA || settings.timeZone;
    if(timeZone){
      updateDefaultDateTime({
        timeZone: timeZone
      });
    }
  }, [lazySelectedInstrument, settings.instruments, selectedTicker?.timezoneIANA, fetchLoading]);

  useChangeQuoteCurrency(() => {
    dispatch(fetchTickers());
  });

  useTriggerUpdateRateCurrency(() => {
    dispatch(refreshTickers());
  });

  useStocksRealtime(function(data) {
    if(updateStrategy[data.id] !== 'socket') return;
    dispatch(updateStock(data.id, { close: data.price, date: data.date}));
  }, settings.instruments.map(item => item.id));

  useStockSnapshot(function (data) {
    if(updateStrategy[data.id] !== 'socket') {
      if(!(data?.open)) return;
    }
    dispatch(updateStock(data.id, data));
  }, settings.instruments.map(item => item.id));

  const onTickerSelectedChange = instrumentId => {
    if (instrumentId) {
      setLazySelectedInstrument(instrumentId);
    }
  };

  const handleSwitchType = type => {
    setTickerFormat(type);
    if(typeof onSwitchTickerType === 'function') {
      onSwitchTickerType(type);
    }
  };

  const getHeightTableTicker = () => {
    return tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE
      ? { height: `${(enabledInstruments.length + 1) * 55 + 57}px` }
      : {};
  };


  return (
    <>
      <div className="switcher__wrapper switcher__wrapper--v2">
        <TimeStamp tickerData={selectedTicker} fetchLoading={fetchLoading} />
        <div className="switcher__wrapper--currency">
          {!isPrint && settings.currencies.enabled && <CurrencySelection />}
          {settings.ticker.enabledFormat.length > 1 && (
            <Switcher
              className="switcher__ticker"
              onClick={handleSwitchType}
              type={SWITCHER_TYPE.TICKER}
              tabs={tickerFormatTypes}
              tabActive={tickerFormat}
              isButton={true}
              ariaLabel={i18n.translate('tickerViewTabs')}
            />
          )}
        </div>
      </div>
      <div className="tab-contents">
        <div
          className={classNames(
            `ticker ticker--${tickerType.toLowerCase()} ticker--${getContainerTickerByType(
              tickerFormat
            )}`
          )}
          ref={tickerContainerRef}
          role='tabpanel'
          id={tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE ? graphTypeId : tableTypeId}
          tabIndex={0}
        >
          {fetchLoading && <Skeleton style={getHeightTableTicker()} />}

          {!fetchLoading && isSingleTicker && tickerData && (
            <SingleTicker data={tickerData} tickerFormat={tickerFormat} />
          )}

          {!fetchLoading && !isSingleTicker && (
            <MultipleTicker
              onTickerSelected={onTickerSelectedChange}
              data={tickerData}
              selectedInstrumentId={lazySelectedInstrument}
              isPrint={isPrint}
              tickerFormat={tickerFormat}
            />
          )}
        </div>
      </div>
    </>
  );
};


export const getTickerLabels = ()=>{
  const graphTickerTemplate =  appConfig.get().ticker.graphTickerTemplate||'';
  const tableTickerTemplate =  appConfig.get().ticker.tableTickerTemplate||'';

  const graphFields = getLabelsTicker(graphTickerTemplate);
  const tableFields = getLabelsTicker(tableTickerTemplate);

  return  [...new Set([...graphFields, ...tableFields])];
};