import { memo, useEffect, useId, useMemo, useRef, useState } from 'react';
import { classNames } from '../../helper';

function ToolTip({ id, right, timeout = 2000, children, ...rest }) {
  const tooltipRef = useRef();
  const [isShowTooltip, setIsShowTooltip] = useState(false);

  const uniqueId = useId();
  const uniqueTooltipId = useMemo(() => id || uniqueId, [id, uniqueId]);

  useEffect(() => {
    const tooltipElement = tooltipRef.current;
    if (!tooltipElement) return;

    const getTooltipParentElement = () => {
      const parentElement = tooltipElement.parentElement;

      const isParentOfTooltip = parentElement &&  parentElement.classList.contains('tooltip-wrapper');
      return isParentOfTooltip ? parentElement : undefined;
    };

    const tooltipParentElement = getTooltipParentElement();
    if (!tooltipParentElement) return;

    const addAriaLabelByToParent = () => {
      const hasAriaLabelOrLabelBy =
        !tooltipParentElement.hasAttribute('aria-label') &&
        !tooltipParentElement.hasAttribute('aria-labelledby');

       if (hasAriaLabelOrLabelBy) {
        tooltipParentElement.setAttribute('aria-labelledby', uniqueTooltipId);
       }
    };

    // add label to container
    addAriaLabelByToParent();

    let timeoutPopup;

    const openTooltip = () => {
      setIsShowTooltip(true);
      tooltipParentElement.classList.add('showPopup');
      closeAfterTimeout();
    };

    const closeAfterTimeout = () => {
      if (timeoutPopup) clearTimeout(timeoutPopup);
      timeoutPopup = setTimeout(() => {
        closeTooltip();
      }, timeout);
    };

    const closeTooltip = () => {
      tooltipParentElement.classList.remove('showPopup');
      setIsShowTooltip(false);
    };

    const handleMouseEnterFn = () => {
      openTooltip();
    };
    const handleMouseLeaveFn = () => {
      closeTooltip();
    };

    const handleFocusFn = () => {
      openTooltip();
    };

    const handleBlurFn = () => {
      closeTooltip();
    };

    const handleKeyDownFn = (event) => {
      if (event.key === 'Escape' && isShowTooltip) {
        closeTooltip();
      }
    };

    tooltipParentElement.addEventListener('mouseenter', handleMouseEnterFn);
    tooltipParentElement.addEventListener('mouseleave', handleMouseLeaveFn);
    tooltipParentElement.addEventListener('focus', handleFocusFn);
    tooltipParentElement.addEventListener('blur', handleBlurFn);
    document.addEventListener('keydown', handleKeyDownFn);

    return () => {
      tooltipParentElement.removeEventListener('mouseenter', handleMouseEnterFn);
      tooltipParentElement.removeEventListener('mouseleave', handleMouseLeaveFn);
      tooltipParentElement.removeEventListener('focus', handleFocusFn);
      tooltipParentElement.removeEventListener('blur', handleBlurFn);
      document.removeEventListener('keydown', handleKeyDownFn);
    };
  }, [timeout, isShowTooltip, uniqueTooltipId]);

  return (
    <span {...rest} ref={tooltipRef} className={classNames('tooltip', { right: right })}>
      <span className="tooltip__content" id={uniqueTooltipId}>{children}</span>
    </span>
  );
}

export default memo(ToolTip);
