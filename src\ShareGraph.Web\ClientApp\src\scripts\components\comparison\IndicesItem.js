import { useRef, useContext, useLayoutEffect, useMemo, useCallback } from 'react';

import { debounceLast } from '../../utils';
import { AppContext } from '../../AppContext';
import {
  replaceKey,
  convertNumberDecimal,
  classNames,
  getCustomPhraseIndices,
  convertPercentDecimal,
  marketStatusByValue,
  clickWithoutMove,
  translateStringFormat,
  formatChangeNumber,
  convertChangePercentDecimal
} from '../../helper';
import { usePrevious } from '../../customHooks';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import {useDelay} from '../../customHooks/useDelayUpdate';

export const IndicesItem = ({ data: _data, isSelected, onItemClick = () => {} }) => {
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER);
  const settings = useContext(AppContext);
  const containerRef = useRef(null);
  const { change, marketStatus } = data;
  const prevData = usePrevious(data);

  const lastValue = useMemo(() => {
    if (!prevData) return;
    return data.last - prevData.last;
  }, [data]);

  //
  useLayoutEffect(() => {
    _changeStyle();
  });

  function _changeStyle() {
    _setIndicesSelectedBorderColor();
  }
  // add class indicator-up or indicator-down base on change value
  // and add class base on market status

  //  set border color when indices selected
  function _setIndicesSelectedBorderColor() {
    if (isSelected) {
      const currentIns = settings.indices.indices.find(x => x.id === data.instrumentId);
      const color = currentIns.color || '#E3E3E3';
      containerRef.current.querySelector('.custom-checkbox').style.color = color;
      containerRef.current.style.borderColor = color;
    } else {
      containerRef.current.style.borderColor = '#E3E3E3';
    }
  }

  function _getTemplateHtml() {
    return `<div class="indices__heading comparison__heading">
                  <div class="indices__name-icon comparison__name-icon">
                    <i class="fs fs-checked-checkbox custom-checkbox"></i>
                    <span class="fs-checkbox unchecked-checkbox"><span class="path1"></span><span class="path2"></span></span> 
                    <span class="indices__name comparison__name">{tickerName}</span>                    
                  </div>
                    <div class="indices__last-price comparison__last-price-wrapper">
                      <span class="indices__last-price comparison__last-price">{last}</span>
                      <span class="indices__last-price comparison__currency">{currencyCodeStr}</span>
                    </div>
                </div>
                <p class="indices__change comparison__change">       
                    <i class="fs fs-triangle-up"></i>
                    <i class="fs fs-triangle-down"></i>   
                    <span class="indices__change-value comparison__change-value">{change} ({changePercentage}%)</span>
                </p>`;
  }

  function _renderTemplate(item) {
    if (item) {
      const tableRow = _getTemplateHtml();
      //const labels = _getLabel();
      const dataIndices = _normalizeData(item);
      const dataTemplate = { ...dataIndices }; //, ...labels };
      const content = replaceKey(tableRow, dataTemplate);
      return content;
    }

    return '<h2> No data</h2>';
  }
  // show number with format from setting
  // and get custom phrase for indices name
  function _normalizeData(item) {
    const data = { ...item };
    data.change = formatChangeNumber(item.change);
    data.changePercentage = convertChangePercentDecimal(item.changePercentage);
    data.last = convertNumberDecimal(item.last);
    data.currencyCodeStr = data.currencyCode ? translateStringFormat('currency', [data.currencyCode]) : '';

    return getCustomPhraseIndices(data);
  }

  // function _getLabel() {
  //   const singleLabels = [
  //     'w52RangeLabel',
  //     'volumeLabel',
  //     'bidAskLabel',
  //     'marketCloseLabel',
  //     'marketOpenedLabel',
  //     'openLabel',
  //     'highLabel',
  //     'lowLabel',
  //     'marketOpenInLabel',
  //     'sharesLabel',
  //     'lastLabel',
  //     'changePercentageLabel',
  //     'changeLabel'
  //   ];

  //   return {
  //     dayRangeLabel: i18n.translate('rangeLabel'),
  //     ...i18nTranslate(singleLabels)
  //   };
  // }

  const onClick = useCallback(debounceLast(function (event) {
    // Keypresses other than Enter and Space
    if (event.nativeEvent instanceof KeyboardEvent && event.key !== 'Enter' && event.key !== ' ') {
      return;
    }
    event.preventDefault();
    onItemClick(data.instrumentId);
  }, 200), [data.instrumentId]);

  return (
    <div
      {...clickWithoutMove(onClick)}
      onKeyDown={onClick}
      tabIndex="0"
      role="button"
      aria-pressed={isSelected}
      className={classNames(
        {
          'indicator-up': change > 0,
          'indicator-down': change < 0,
          'indicator-neutral': change === 0,
          selected: isSelected,
          'animate--increase': lastValue > 0,
          'animate--decrease': lastValue < 0,
          'animate--neutral': lastValue === 0
        },
        'indices__item comparison__item',
        marketStatusByValue(marketStatus)
      )}
      ref={containerRef}
      // style={{borderColor:borderColor}}
      dangerouslySetInnerHTML={{ __html: _renderTemplate(data) }}
    ></div>
  );
};
