const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { SubresourceIntegrityPlugin } = require('webpack-subresource-integrity');
const Dotenv = require('dotenv-webpack');
const paths = require('./paths');
const { resolve } = require('path');

module.exports = {
  mode: 'production',
  devtool: 'cheap-module-source-map',
  stats: {
    //https://github.com/webpack-contrib/mini-css-extract-plugin/issues/138
    children: false,
    assets: false,
    warnings: false
  },
  output: {
    path: paths.outputPath,
    filename: 'js/[name]-[contenthash:8].js',
    chunkFilename: 'js/[name]-[contenthash:8].js',
    assetModuleFilename: 'assets/[name]-[contenthash:8][ext]',
    crossOriginLoading: 'anonymous'
    //filename: 'js/[name].js',
    //chunkFilename: 'js/[name].js'
    //library: 'euroland',
    //libraryTarget: 'umd'
  },
  plugins: [
    new CleanWebpackPlugin(),
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      filename: 'css/[name]-[contenthash:8].css',
      chunkFilename: 'css/[id]-[contenthash:8].css'
    }),
    new HtmlWebpackPlugin({
      template: resolve(__dirname, '../', '../', 'index.html'),
      filename: resolve(__dirname, '../', '../', 'dist', 'index.html'),
      inject: 'body',
      chunks: ['main'],
      cache: false
    }),
    new Dotenv({
      path: paths.envProdPath, // Path to .env.production file
      expand: true
    }),
    new Dotenv({
      path: paths.envPath, // Path to .env file
      expand: true
    }),
    new SubresourceIntegrityPlugin({
      enabled: true,
      hashFuncNames: ['sha384']
    })
  ],
  optimization: {
    runtimeChunk: false,
    moduleIds: 'hashed',
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendor: {
          priority: -10,
          test: /[\\/]node_modules[\\/]/,
          name: 'vendor'
        },
        "chartiq": {
          priority: -8,
          test: /[\\/]node_modules[\\/]chartiq[\\/]/,
          name: 'chartiq'
        },
        realtimelibe: {
          priority: -8,
          test: /[\\/]node_modules[\\/](?:[@]azure[\\/])/,
          name: 'realtimelib'
        }
      }
    },
    minimizer: [new TerserPlugin({
      minify: TerserPlugin.uglifyJsMinify, // To fix escape unicode characters with default Terser.
      terserOptions: {
        /**
         * fix variable name caused issue in mac safari and mobile safari
         * @link https://github.com/mishoo/UglifyJS/issues/5776
         */
        webkit: true
      }
    })]
  }
};
