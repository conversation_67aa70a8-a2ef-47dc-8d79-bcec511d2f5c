import i18n from '../../../services/i18n';
import ToolTip from '../../commons/ToolTip';
import { useMemo, useRef } from 'react';
import appConfig from '../../../services/app-config';
import { TEMPLATE_TICKER } from '../../../common';
import { toElSvg, toImage, toSvg } from '../../../lib/htmlToImage';
import { chartLoading } from '../ChartHelper';

export const showSelectedMultipleTickerTable = (shareGraphDom) => {
  const multipleTickerTableDom = shareGraphDom.querySelector('.ticker--table_ticker_multiple');
  if(!multipleTickerTableDom) return;
  const selectedTickerRowDom = multipleTickerTableDom.querySelector('.table__body-tr.selected');
  const tickerRowDoms = multipleTickerTableDom.querySelectorAll('.table__body-tr');
  tickerRowDoms.forEach(tickerRowDom => {
    tickerRowDom.style.display = 'none';
  });
  selectedTickerRowDom.style.display = 'table-row';
  return () => {
    tickerRowDoms.forEach(tickerRowDom => {
      tickerRowDom.style.display = 'table-row';
    });
  };
};

export const getTickerDom = ({shareGraphDom, tickerType}) => {
  if(!tickerType) return {};
  const isMultipleTicker = tickerType.trim().toLowerCase() === TEMPLATE_TICKER.MULTIPLE;
  let selectorTicker = isMultipleTicker ? '.tickerLayout .ticker--multiple .ticker__item.selected > *' : '.tickerLayout .tab-contents .ticker__inner > *';
  const tickerTableDom = shareGraphDom.querySelector('.ticker--table_ticker_multiple') || shareGraphDom.querySelector('.ticker--table_ticker_single');

  return {
    isMultipleTicker,
    tickerTableDom, 
    tickerDom: tickerTableDom || shareGraphDom.querySelector(selectorTicker)
  };
};

export function DownloadImageButton() {  
  const exportBtnRef = useRef(null);
  const shareGraphSettings = appConfig.get();
  const tickerType = shareGraphSettings.ticker.tickerType;
  const loadingChart = useMemo(() => {
    return chartLoading.self();
  }, []);
  const filter = (node)=>{
    const exclusionClasses = ['switcher', 'additional-options', 'spin-loader'];
    
    if(node instanceof HTMLElement) {
      const styleList = getComputedStyle(node);

      if(node.nodeName === 'CQ-LOADER') return false;

      if(styleList.display === 'none') return false;
      if(styleList.visibility === 'hidden') return false;
      if(styleList.opacity === '0') return false;
      
    }

    return !exclusionClasses.some(classname => {
      if (node.classList) {
        return node.classList.contains(classname);
      }
    });
  };

  function exportImage() {
    const shareGraphDom = document.querySelector('.share-graph');
    if(!shareGraphDom) throw new Error('Graph dom do not exist');
    let graphDom = document.querySelector('.graph');
    const isRtl = window.appSettings?.isRtl;
    
    if(loadingChart.isLoading()) return;
    
    let multipleSelectedTicker = null;
    const undoShowSelectedMultipleTickerTable = showSelectedMultipleTickerTable(shareGraphDom);
    const { tickerDom, tickerTableDom, isMultipleTicker} = getTickerDom({ shareGraphDom, tickerType });

    if (isMultipleTicker) {
      multipleSelectedTicker = shareGraphDom.querySelector('.tickerLayout .ticker--multiple .ticker__item.selected');
      multipleSelectedTicker?.classList?.add('selected--print');
    }
  
    const tickerHeight = tickerDom.clientHeight;
    const tickerWidth = tickerDom.clientWidth;
    let screenShotWrapper = document.createElement('div');
    
    screenShotWrapper.classList.add('screenShot');

    loadingChart.show();
    // export chart
    const canvas1 = toImage(graphDom, {backgroundColor:'#ffffff', filter: filter, avoidBlockPage: true})
    .then(canvas=> ({canvas: canvas, key: 'graph'}))
    .catch((err)=>{
      console.log('Error export graph: ' + err);
      loadingChart.hide();
    });
    const canvas2 = toImage(tickerDom, {
      backgroundColor: '#ffffff',
      width: Math.ceil(tickerDom.getBoundingClientRect().width) // The library is not correctly retrieving the width of the element. For instance, if the width is 100.44, the library will round it to 100. Sometimes, this causes a break in the UI as the image does not display correctly.
    })
      .then((canvas) => {
        if (isMultipleTicker) {
          multipleSelectedTicker?.classList?.remove('selected--print');
        }
        return { canvas: canvas, key: 'ticker' };
      })
      .catch((err) => {
        console.log('Error export ticker: ' + err);
        loadingChart.hide();
        multipleSelectedTicker?.classList?.remove('selected--print');
      })
      .finally(() => {
        typeof undoShowSelectedMultipleTickerTable === 'function' &&
          undoShowSelectedMultipleTickerTable();
      });
  
    const waitLoadImage = values => new Promise((resolve) => setTimeout(() => resolve(values)));
    Promise.all([canvas1, canvas2]).then(waitLoadImage).then((values) => {
      const tickerCanvas = values.find(x => x.key === 'ticker').canvas;
      const graphCanvas = values.find(x => x.key === 'graph').canvas;
      const canvasWrapper = document.createElement('canvas');
      const paddingTop = 230;
      const paddingLeft = 230;
      const gap = 30;
        canvasWrapper.width = graphCanvas.width + paddingLeft;
        canvasWrapper.height = tickerHeight + graphCanvas.height + gap + paddingTop;
        var canvasWrapperContext = canvasWrapper.getContext('2d');
        let tickerCanvasX = isRtl ? canvasWrapper.width - paddingLeft/2 - tickerWidth : paddingLeft/2;
        let tickerCanvasY = paddingTop/2;
        let tickerCanvasWidth = tickerWidth; 

        if(tickerTableDom || tickerCanvas.width > graphCanvas.width){
          tickerCanvasX = paddingLeft/2;
          tickerCanvasWidth = graphCanvas.width;
        }
        canvasWrapperContext.fillStyle = '#fff';
        canvasWrapperContext.fillRect(0, 0 , canvasWrapper.width, canvasWrapper.height);
        canvasWrapperContext.drawImage(tickerCanvas, tickerCanvasX, tickerCanvasY, tickerCanvasWidth, tickerHeight);
        canvasWrapperContext.drawImage(graphCanvas, paddingLeft/2, tickerHeight + gap + paddingTop/2);
        let link = document.createElement('a');

        link.download = (i18n.translate('imageDownloadFileName') || 'sharegraph').replace(/\W/gm, '') + '.png';
        link.href = canvasWrapper.toDataURL();
        link.click();
        loadingChart.hide();
              
        setTimeout(() => {
          link.remove();
          canvasWrapper.remove();
        }, 0);
    });
  }

  return (
    <button ref={exportBtnRef} className="tooltip-wrapper option-button option-button--export"
      aria-labelledby="downloadToImageBtn"
      onClick={exportImage}
      >
      <i aria-hidden="true" className='fs fs-icon-image'></i>
      <ToolTip aria-hidden="true" id="downloadToImageBtn">{ i18n.translate('exportOption') }</ToolTip>
    </button>
  );
}
