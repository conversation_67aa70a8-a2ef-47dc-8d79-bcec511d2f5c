const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackTagsPlugin = require('html-webpack-tags-plugin');
const assets = require('./assets');
const paths = require('./paths');
const rules = require('./rules');
const pkg = require('../../package.json');
const { resolve, relative } = require('path');

const contextPath = resolve(__dirname, '../', '../', 'src');

const formatVersion = (version) => {
  return version.replace(/[^\d]+/g, '_');
};

module.exports = {
  target: ['web', 'es5'],
  context: paths.contextPath,
  entry: {
    fetchPolyfill: 'whatwg-fetch',
    main: paths.entryPath
  },
  module: {
    rules
  },
  resolve: {
    modules: [
      'src',
      'node_modules'
    ],
    extensions: ['.js'],
    fallback: { 'crypto': false }
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: resolve(__dirname, '../', '../', 'src/assets/'),
          to({ context, absoluteFilename }) {
            return `assets/${relative(context, absoluteFilename)}`;
          }
        }
      ]
    }),
    new HtmlWebpackTagsPlugin({
      links: [...assets.links],
      scripts: [...assets.scripts],
      append: false,
      usePublicPath: false,
      useHash: true,
    })
  ]
};
