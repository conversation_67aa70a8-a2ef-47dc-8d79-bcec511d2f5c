import i18n from '../../../services/i18n';
import appConfig from '../../../services/app-config';
import {EVENT_TYPES} from '../../../common';
import {EventHandler, EventLoader, EventMarker, EventNode} from './marker';
import {client, clientRT} from '../../../services/graphql-client';
import {DIVIDEND_EVENT_PAGING_QUERY} from '../../../graphql-queries/dividendEventsQuery';
import { CIQ } from '../chartiq-import';
import {convertNumberDecimal} from '../../../helper';

class DividendEventMarker extends EventMarker {
  constructor(options) {
    super({...options, label: EVENT_TYPES.DIVIDEND});
    this.contentEl.innerHTML = i18n.translate('dividendAbbreviate');
    this.mainNode.classList.add(EVENT_TYPES.DIVIDEND);
  }

  initializeTooltipContent() {
    super.initializeTooltipContent();
    this.tooltipInnerEl = this.tooltipEl;
    this.tooltipItemEl = this.mainNode.querySelector('.item');
  }
}

/**
 * @typedef {{isRT:boolean, selectedInstrumentId: number}} DividendEventOptions
 */

export class DividendEventLoader extends EventLoader {
  async fetch(cursor) {
    const result = await (this.options.isRT ? clientRT : client).query(DIVIDEND_EVENT_PAGING_QUERY, { 
      id: this.options.selectedInstrumentId, 
      fromDate: this.from, 
      toDate: this.to,
      cursor
    }, { fetchOptions: { signal: this.abortController.signal }});

    const dividends = result.data?.instrumentById.dividends;
    const hasNextPage = dividends?.pageInfo.hasNextPage;
    let endCursor;
    const records = dividends?.edges.map(item => ({
      Date: new Date(item.node.exDate), 
      date: item.node.exDate, 
      title: `${CIQ.I18N.translate('Dividends')}: ${convertNumberDecimal(item.node.grossDivAdj)} ${item.node.currency}` 
    }));

    if(dividends && hasNextPage) endCursor = dividends.pageInfo.endCursor;
    
    return {
      data: records,
      endCursor
    };
  }
}

export class DividendEventNode extends EventNode {
  createMarker(date, records) {
    return new DividendEventMarker({ 
      setting: this.setting,
      stx: this.stx, 
      template: this.template, 
      eventCollection: this.eventCollection, 
      date,
      records
    });
  }
}
export class DividendEventHandler extends EventHandler {
  eventLoaderCreator(from, to) {
    return DividendEventLoader.create(
      from, 
      to,
      this.options,
      this.eventNode.onLoad.bind(this.eventNode)
    );
  }

  eventNodeCreator() {
    return new DividendEventNode(
      this.stx, 
      document.querySelector('#chartEventPrototype'), 
      appConfig.get()
    );
  }
}

export const DividendFactor = {
  _cache: {},
  /**
   * 
   * @param {any} stx 
   * @param {DividendEventOptions} options 
   * @returns {DividendEventHandler}
   */
  get(stx, options) {
    const { selectedInstrumentId } = options;
    
    Object.entries(this._cache).map(([key, item]) => {
      if(key == selectedInstrumentId) return;
      if(!item.isSleep) { item.sleep(); }
    }); // sleep all

    if(!this._cache[selectedInstrumentId]) {
      this._cache[selectedInstrumentId] = new DividendEventHandler(stx, options);
    }
    
    return this._cache[selectedInstrumentId];
  }
};