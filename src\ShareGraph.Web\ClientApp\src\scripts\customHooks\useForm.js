import { useRef, useState } from 'react';

class FormStore {
  constructor({ initialValues, forceUpdate = () => {} }) {
    this.forceUpdate = forceUpdate;
    this.formState = initialValues;
    this.setFieldValue = this.setFieldValue.bind(this);
    this.setFieldsValue = this.setFieldsValue.bind(this);
    this.resetFields = this.resetFields.bind(this);
  }
  setFieldValue(filename, data) {
    this.formState[filename] = data;
    this.forceUpdate();
  }
  setFieldsValue(data = {}) {
    for (const key in data) {
      this.formState[key] = data[key];
    }
    this.forceUpdate();
  }
  resetFields() {
    this.formState = {};
    this.forceUpdate();
  }

  getFormStore() {
    return {
      formState: this.formState,
      setFieldsValue: this.setFieldsValue,
      setFieldValue: this.setFieldValue,
      resetFields: this.resetFields
    };
  }
}

const useForm = (initialValues = {}) => {
  const formRef = useRef();
  const [, forceUpdate] = useState({});
  if (!formRef.current) {
    const formStore = new FormStore({
      initialValues,
      forceUpdate: () => forceUpdate({})
    });
    formRef.current = formStore.getFormStore();
  }
  return formRef.current;
};

export default useForm;
