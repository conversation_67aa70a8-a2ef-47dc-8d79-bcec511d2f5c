using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using HotChocolate;

namespace Euroland.FlipIT.ShareGraph.API.Resolvers
{
  public class ExcelDownloadOptionResolver
  {
    public bool GetIncludedTotalReturn([Service] ISetting _settings)
    {
      var section = _settings.GetChild($"Chart:ExcelDownloadOption");
      if (section.Exists())
      {
        return section.GetValue<bool>("IncludedTotalReturn", false);
      }

      return false;
    }

    public bool GetIncludedSelectedPeersAndIndicies([Service] ISetting _settings)
    {
      var section = _settings.GetChild($"Chart:ExcelDownloadOption");
      if (section.Exists())
      {
        return section.GetValue<bool>("IncludedSelectedPeersAndIndicies", false);
      }

      return false;
    }
  }
}
