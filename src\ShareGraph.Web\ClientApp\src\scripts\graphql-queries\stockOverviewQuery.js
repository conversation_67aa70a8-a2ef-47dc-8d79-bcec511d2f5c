import {gql} from './utils';

export const STOCK_OVERVIEW_QUERY = gql(/* GraphQL */`query StockOverview (
  $instrumentId: Int!
  $period: Periods!
  $from: DateTime
  $to: DateTime
  $toCurrency: String
) {
  stockOverview(
    instrumentId: $instrumentId
    period: $period
    from: $from
    to: $to
    toCurrency: $toCurrency
  ) {
    firstPrice
    lastPrice
    change
    changePercentage
    highestPrice
    highestPriceDate
    lowestPrice
    lowestPriceDate
    totalVolume
    highestVolumeDate
    lowestVolumeDate
    totalReturn
  }
}
`);