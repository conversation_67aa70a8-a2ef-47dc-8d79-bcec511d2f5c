export * from './tickerActions';
export * from './shareDetailsActions';

/**
 * @typedef StockData
 * @prop {number} close
 * @prop {number} open
 * @prop {number} bid
 * @prop {number} ask
 * @prop {string} date
 * @prop {number} high
 * @prop {number} low
 * @prop {number} volume
 */

/**
 * @typedef UpdateStockPayload
 * @prop { number } instrumentId
 * @prop { Partial<StockData> } data
 */

export const UPDATE_STOCK_DATA = 'UPDATE_STOCK_DATA';

/**
 * 
 * @param { number } instrumentId 
 * @param { Partial<StockData> } data 
 * @returns 
 */
export const updateStock = (instrumentId, data) => {
  const cloneData = {...data};

  // remove nullish field
  for(const key of Object.keys(cloneData)) {
    // we use == because we want to check null or undefined
    if(cloneData[key] == null) delete cloneData[key];
  }
  return {
    type: UPDATE_STOCK_DATA,
    payload: /** @type { UpdateStockPayload } */({
      instrumentId,
      data: cloneData
    })
  };
};
