import {gql} from './utils';

export const GET_52_WEEKS_QUERY = gql(/* GraphQL */`query FiftyTwoWeeks($ids: [Int!]!, $toCurrency: String, $adjClose: Boolean) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    id
    shareName
    currentPrice {
      last
    }
    _52W: performance(period: FIFTY_TWO_WEEKS) {
      highest
      lowest
    }
    currency {
      code
      name
    }
    market {
      abbreviation
    }
  }
}
`);