import {useEffect} from 'react';

export default function useAutoFocusToPopup () {
  useEffect(() => {
    if(window?.xprops !== undefined) {
      window.focus();
    }
    const handleEscape = e => {
      if(e.code === 'Escape') {
        window?.xprops.close();
      }
    };

    document.addEventListener('keyup', handleEscape);
    return () => document.removeEventListener('keyup', handleEscape);
  }, []);

}