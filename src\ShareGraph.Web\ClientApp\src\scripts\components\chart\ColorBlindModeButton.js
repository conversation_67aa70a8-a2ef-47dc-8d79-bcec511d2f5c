import i18n from '../../services/i18n';
import {uniqueID} from '../../utils/util';
export const ColorBlindModeButton = ({onChange}) => {
  const inputId = uniqueID();
    return    (
        <div className='colorvision-selection'>
            <label htmlFor={inputId}>{i18n.translate('colorVisionDeficiencyMode')}</label>
            <span className='switch'>
                <input type='checkbox' id={inputId} onChange={onChange} className='toggleswitch'></input>
                <div class='toggle-mode slider round' ></div>
            </span>

        </div>
    );
};
