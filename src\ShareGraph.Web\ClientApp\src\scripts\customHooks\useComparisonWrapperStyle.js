import { useLayoutEffect, useRef } from 'react';

const useComparisonWrapperStyle = () => {
  const chartHeaderRef = useRef();

  useLayoutEffect(() => {
    const chartHeaderDom = chartHeaderRef.current;
    const comparisonWrapperDom = document.querySelector('#comparison .switcher');
    if (!chartHeaderDom || !comparisonWrapperDom) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const chartHeaderHeight = entry.target.clientHeight;
        const comparisonWrapperHeight = comparisonWrapperDom.clientHeight;
        const marginTop = chartHeaderHeight - comparisonWrapperHeight;
        comparisonWrapperDom.style.setProperty('margin-top', marginTop + 'px');
      }
    });
    resizeObserver.observe(chartHeaderDom);
    return () => {
      resizeObserver.unobserve(chartHeaderDom);
    };
  }, []);

  return { chartHeaderRef };
};

export default useComparisonWrapperStyle;
