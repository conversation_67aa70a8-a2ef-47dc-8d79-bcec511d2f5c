/**!
 *	9.3.0
 *	Generation date: 2025-06-05T15:44:23.178Z
 *	Package Type: Core alacarte
 *	Build descriptor: 13d0ed304
 *	Client name: euroland as
 *	Expiration date: "2026/06/01"
 *	Domain lock: ["127.0.0.1","localhost","euroland.com","eurolandir.cn","eurolandir.com"]
 *	License type: annual
 *	Features: TypeScript Definitions
 */

/***********************************************************!
 * Copyright © 2025 S&P Global All rights reserved
*************************************************************/
/*************************************! DO NOT MAKE CHANGES TO THIS LIBRARY FILE!! !*************************************
* If you wish to overwrite default functionality, create a separate file with a copy of the methods you are overwriting *
* and load that file right after the library has been loaded, but before the chart engine is instantiated.              *
* Directly modifying library files will prevent upgrades and the ability for ChartIQ to support your solution.          *
*************************************************************************************************************************/

/*************************************************************************!
* Please note that manually changing the domain list or expiration dates  *
*                                                                         *
* >>>>>>>>>>>>>>>>>>>>>>>>>>>>>> WILL NOT <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< *
*                                                                         *
*   modify the library locking mechanism. Any changes must be requested   *
*                          directly from ChartIQ.                         *
***************************************************************************/

/* eslint-disable */ /* jshint ignore:start */ /* ignore jslint start */
(function(){
	const license = Object.defineProperties({
		domains: ["^127\\.0\\.0\\.1$","^(?:.+\\.)?localhost$","^(?:.+\\.)?euroland\\.com$","^(?:.+\\.)?eurolandir\\.cn$","^(?:.+\\.)?eurolandir\\.com$"],
		filesystem: false,
		licenseExpiration: "2026/06/01",
		trialExpiration: undefined,
		version: '9.3.0'
	}, {
		daysUntilExpired: {
			get: function(){return Math.round((this.expirationDate - new Date()) / 86400000)}
		},
		expirationDate: {
			get: function(){return new Date(this.licenseExpiration || this.trialExpiration)}
		},
		gracePeriodDate: {
			get: function(){
				const gracePeriodDate = new Date(this.expirationDate);
				if(!this.isTrial){
					gracePeriodDate.setDate(gracePeriodDate.getDate() + 30);
				}
				return gracePeriodDate;
			}
		},
		isDateLocked: {
			get: function(){return this.licenseExpiration || this.trialExpiration}
		},
		isDomainLocked: {
			get: function(){return this.domains && this.domains.length > 0;}
		},
		isFileSystemLocked: {
			get: function(){return this.filesystem !== undefined}
		},
		isExpired: {
			get: function(){return new Date() > this.expirationDate}
		},
		isGracePeriodExpired: {
			get: function(){return new Date() > this.gracePeriodDate}
		},
		isTrial: {
			get: function(){return this.trialExpiration !== undefined}
		},
		isValidDomain: {
			value: function(domain){return this.domains.some((pattern) => new RegExp(pattern).test(domain))}
		}
	});
	if(license.isDateLocked){
		if(license.isExpired){
			console.error('ChartIQ: this license has expired!');
			if(license.isTrial){
				alert('ChartIQ: this license has expired!');
			}
			if(license.isGracePeriodExpired){
				throw new Error('ChartIQ: this license has expired!');
			}
		}else if(license.isTrial && license.daysUntilExpired < 3){
			alert("This ChartIQ trial license expires in " + license.daysUntilExpired + " days!");
			console.log("WARNING: This ChartIQ trial license expires in " + license.daysUntilExpired + " days!");
		}
	}

	if(typeof document !== 'undefined'){
		if(license.isFileSystemLocked && location.protocol === 'file:'){
			return;
		}
		const hostname = new URL(location.href).hostname;
		if(license.isDomainLocked && !license.isValidDomain(hostname)){
			alert("ChartIQ ERROR: Not licensed for domain " + hostname);
			console.error("ChartIQ ERROR: Not licensed for domain " + hostname);
		}
	}

	if(license.version === 'alpha'){
		alert('ChartIQ: This is an internal PRE-PRODUCTION release--not for external use!');
	}
})();
/* eslint-enable  */ /* jshint ignore:end   */ /* ignore jslint end   */

/* eslint-disable no-extra-parens */


/* eslint-disable */ /* jshint ignore:start */ /* ignore jslint start */
v.g=(function(){var j=2;for(;j !== 9;){switch(j){case 1:return globalThis;break;case 5:var Y;try{var x=2;for(;x !== 6;){switch(x){case 9:delete Y['\u006d\u0062\u006d\x4b\x74'];var J=Object['\x70\x72\x6f\x74\x6f\x74\u0079\x70\u0065'];delete J['\x75\u0047\x57\x4e\x73'];x=6;break;case 3:throw "";x=9;break;case 4:x=typeof mbmKt === '\x75\u006e\u0064\u0065\u0066\x69\x6e\u0065\x64'?3:9;break;case 2:Object['\u0064\u0065\x66\u0069\x6e\x65\x50\u0072\x6f\u0070\x65\x72\x74\x79'](Object['\u0070\u0072\x6f\u0074\u006f\x74\u0079\x70\u0065'],'\u0075\u0047\x57\u004e\u0073',{'\x67\x65\x74':function(){return this;},'\x63\x6f\x6e\x66\x69\x67\x75\x72\x61\x62\x6c\x65':true});Y=uGWNs;Y['\u006d\u0062\x6d\u004b\u0074']=Y;x=4;break;}}}catch(A){Y=window;}return Y;break;case 2:j=typeof globalThis === '\x6f\x62\u006a\u0065\x63\x74'?1:5;break;}}})();v.Z1nyb=b;U4(v.g);v.K2=(function(){var u=2;for(;u !== 4;){switch(u){case 2:var O=v;var L={i3sPVt7:(function(l){var w=2;for(;w !== 18;){switch(w){case 8:w=B < t.length?7:12;break;case 7:w=n === l.length?6:14;break;case 14:m+=o(p(B) ^ M(n));w=13;break;case 9:var B=0,n=0;w=8;break;case 6:n=0;w=14;break;case 12:m=v.r(m,'"');var N=0;var V=function(I){var T=2;for(;T !== 35;){switch(T){case 17:N+=1;T=16;break;case 11:v.R(v.Z(),m,v.y(v.y(m,-7,7),0,6));T=4;break;case 7:T=N === 2 && I === 5?6:13;break;case 9:N+=1;T=8;break;case 27:N+=1;T=26;break;case 4:return N;break;case 14:v.R(v.Z(),m,v.y(v.y(m,-10,10),0,8));T=4;break;case 5:v.R(v.Z(),m,v.y(v.y(m,-4,4),0,3));T=4;break;case 23:v.R(v.Z(),m,v.y(v.y(m,-6,6),0,4));T=4;break;case 25:T=N === 7 && I === 2?24:22;break;case 6:N+=1;T=14;break;case 3:T=N === 1 && I === 3?9:7;break;case 22:L.i3sPVt7=K;T=21;break;case 1:N+=1;T=5;break;case 12:N+=1;T=11;break;case 26:v.R(v.Z(),m,v.y(v.y(m,-6,6),0,4));T=4;break;case 2:T=N === 0 && I === 2?1:3;break;case 21:return K(I);break;case 20:N+=1;T=19;break;case 8:v.R(v.Z(),m,v.y(v.y(m,-8,8),0,7));T=4;break;case 10:T=N === 4 && I === 2?20:18;break;case 24:N+=1;T=23;break;case 18:T=N === 5 && I === 3?17:15;break;case 16:v.R(v.Z(),m,v.y(v.y(m,-4,4),0,3));T=4;break;case 13:T=N === 3 && I === 2?12:10;break;case 19:v.R(v.Z(),m,v.y(v.y(m,-5,5),0,4));T=4;break;case 15:T=N === 6 && I === 0?27:25;break;}}};var K=function(W){var F=2;for(;F !== 1;){switch(F){case 2:return m[W];break;}}};return V;break;case 13:(B++,n++);w=8;break;case 2:var Q=function(d){var h=2;for(;h !== 11;){switch(h){case 4:var q=0;h=3;break;case 2:var C=v.E();var s=v.H();var S=[];h=4;break;case 12:return U;break;case 13:h=!U?6:12;break;case 9:S[q]=C(d[q] + 89);h=8;break;case 8:q++;h=3;break;case 3:h=q < d.length?9:7;break;case 7:var k,U;h=6;break;case 14:U=O[k];h=13;break;case 6:k=v.D(v.f(S,function(){var P=2;for(;P !== 1;){switch(P){case 2:return 0.5 - s();break;}}}),'');h=14;break;}}};var m='',t=v.a6()(Q([32,1,9,21,-40])());var o=v.E();var p=v.P$().bind(t);var M=v.P$().bind(l);w=9;break;}}})('3LB!FF')};return L;break;}}})();v.F$=function(){return typeof v.K2.i3sPVt7 === 'function'?v.K2.i3sPVt7.apply(v.K2,arguments):v.K2.i3sPVt7;};v.e2=function(){return typeof v.K2.i3sPVt7 === 'function'?v.K2.i3sPVt7.apply(v.K2,arguments):v.K2.i3sPVt7;};var u7=2;for(;u7 !== 13;){switch(u7){case 2:u7=v.e2(2) == v.e2(3)?1:5;break;case 1:v.B9=54;u7=5;break;case 3:u7=v.e2(2) >= 76?9:8;break;case 5:u7=v.e2(5) > v.e2(2)?4:3;break;case 8:u7=v.F$(3) <= 37?7:6;break;case 7:v.x3=69;u7=6;break;case 9:v.p4=22;u7=8;break;case 14:v.K_=79;u7=13;break;case 6:u7=v.F$(0) === v.e2(2)?14:13;break;case 4:v.H3=58;u7=3;break;}}function v(){}v.m0=function(){return typeof v.G5.z1nLARl === 'function'?v.G5.z1nLARl.apply(v.G5,arguments):v.G5.z1nLARl;};v.Z$=function(){return typeof v.z3.W_XgRoC === 'function'?v.z3.W_XgRoC.apply(v.z3,arguments):v.z3.W_XgRoC;};v.V2=function(){return typeof v.T9.P8Eqih7 === 'function'?v.T9.P8Eqih7.apply(v.T9,arguments):v.T9.P8Eqih7;};v.K0=function(){return typeof v.m_.u3dt9DB === 'function'?v.m_.u3dt9DB.apply(v.m_,arguments):v.m_.u3dt9DB;};v.z3=(function(q4){var G4=2;for(;G4 !== 10;){switch(G4){case 2:var W8,T3,P5,c_;G4=1;break;case 4:var A1='fromCharCode',W0='RegExp';G4=3;break;case 14:q4=v.x7(q4,function(w9){var h3=2;for(;h3 !== 13;){switch(h3){case 7:h3=!E$?6:14;break;case 4:var g9=0;h3=3;break;case 2:var E$;h3=1;break;case 14:return E$;break;case 6:return;break;case 1:h3=!c_--?5:4;break;case 3:h3=g9 < w9.length?9:7;break;case 9:E$+=W8[P5][A1](w9[g9] + 115);h3=8;break;case 5:E$='';h3=4;break;case 8:g9++;h3=3;break;}}});G4=13;break;case 1:G4=!c_--?5:4;break;case 7:P5=v.s$(T3,new W8[W0]("^['-|]"),'S');G4=6;break;case 13:G4=!c_--?12:11;break;case 11:return {W_XgRoC:function(g8){var q_=2;for(;q_ !== 6;){switch(q_){case 2:var D3=new W8[q4[0]]()[q4[1]]();q_=1;break;case 8:var Q$=(function(a5,N5){var K9=2;for(;K9 !== 10;){switch(K9){case 14:O8=Q_;K9=13;break;case 11:return O8;break;case 1:a5=g8;K9=5;break;case 5:K9=typeof N5 === 'undefined' && typeof q4 !== 'undefined'?4:3;break;case 8:var r8=W8[N5[4]](a5[N5[2]](Y1),16)[N5[3]](2);var Q_=r8[N5[2]](r8[N5[5]] - 1);K9=6;break;case 13:Y1++;K9=9;break;case 4:N5=q4;K9=3;break;case 3:var O8,Y1=0;K9=9;break;case 12:O8=O8 ^ Q_;K9=13;break;case 6:K9=Y1 === 0?14:12;break;case 2:K9=typeof a5 === 'undefined' && typeof g8 !== 'undefined'?1:5;break;case 9:K9=Y1 < a5[N5[5]]?8:11;break;}}})(undefined,undefined);return Q$?q3:!q3;break;case 9:f0=D3 + 60000;q_=8;break;case 4:q3=A4(D3);q_=3;break;case 1:q_=D3 > f0?5:8;break;case 5:q_=!c_--?4:3;break;case 3:q_=!c_--?9:8;break;}}}};break;case 6:G4=!c_--?14:13;break;case 8:G4=!c_--?7:6;break;case 12:var q3,f0=0,g$;G4=11;break;case 3:G4=!c_--?9:8;break;case 5:W8=v.g;G4=4;break;case 9:T3=typeof A1;G4=8;break;}}function A4(n_){var n0=2;for(;n0 !== 25;){switch(n0){case 4:n0=!c_--?3:9;break;case 2:var j9,l8,S7,V9,D6,I9,P0;n0=1;break;case 9:n0=!c_--?8:7;break;case 11:I9=(D6 || D6 === 0) && P0(D6,l8);n0=10;break;case 18:j9=false;n0=17;break;case 26:g$='j-002-00003';n0=16;break;case 19:n0=I9 >= 0 && n_ - I9 <= l8?18:15;break;case 27:j9=false;n0=26;break;case 5:P0=W8[q4[4]];n0=4;break;case 17:g$='j-002-00005';n0=16;break;case 10:n0=!c_--?20:19;break;case 1:n0=!c_--?5:4;break;case 14:n0=!c_--?13:12;break;case 13:D6=q4[7];n0=12;break;case 6:V9=S7 && P0(S7,l8);n0=14;break;case 3:l8=31;n0=9;break;case 12:n0=!c_--?11:10;break;case 7:n0=!c_--?6:14;break;case 8:S7=q4[6];n0=7;break;case 20:j9=true;n0=19;break;case 16:return j9;break;case 15:n0=V9 >= 0 && V9 - n_ <= l8?27:16;break;}}}})([[-47,-18,1,-14],[-12,-14,1,-31,-10,-6,-14],[-16,-11,-18,-1,-50,1],[1,-4,-32,1,-1,-10,-5,-12],[-3,-18,-1,0,-14,-42,-5,1],[-7,-14,-5,-12,1,-11],[-65,-65,-4,-2,-15,-59,-61,-4,-13],[]]);v.I$=function(){return typeof v.m_.C_lxEql === 'function'?v.m_.C_lxEql.apply(v.m_,arguments):v.m_.C_lxEql;};v.X2=function(){return typeof v.z3.W_XgRoC === 'function'?v.z3.W_XgRoC.apply(v.z3,arguments):v.z3.W_XgRoC;};v.T9=(function(C8){return {o1OoTIV:function(){var I7,A6=arguments;switch(C8){case 1:I7=A6[0] * A6[1];break;case 3:I7=-A6[0] * A6[2] + A6[1];break;case 6:I7=A6[0] << A6[1];break;case 0:I7=A6[0] | A6[1];break;case 5:I7=A6[1] * A6[0] + A6[2];break;case 2:I7=-A6[1] + A6[0];break;case 4:I7=(-A6[0] + A6[1]) / A6[3] * A6[4] - A6[2];break;}return I7;},P8Eqih7:function(F3){C8=F3;}};})();v.m_=(function(){function Y_(q9){var d$=2;for(;d$ !== 7;){switch(d$){case 5:var J9=0;d$=4;break;case 4:d$=J9 < q9.length?3:8;break;case 9:J9++;d$=4;break;case 3:k3+=v.E()(q9[J9] - k8 + 91);d$=9;break;case 8:return k3;break;case 2:var k8=5;var k3='';d$=5;break;}}}function v7(X1,r6,N9,a1,V3){var t2=2;for(;t2 !== 15;){switch(t2){case 16:return y$.m0(P_,l0,N9);break;case 3:R_=V3?O_:N1;t2=9;break;case 2:var P_,l0,R_,Y8;!Y8 && (Y8=y5[Y_([22,25,13,11,30,19,25,24])]);!N1 && (N1=typeof Y8 !== "undefined"?Y8[Y_([18,25,29,30,24,11,23,15])] || ' ':"");!O_ && (O_=typeof Y8 !== "undefined"?Y8[Y_([18,28,15,16])]:"");t2=3;break;case 18:P_=v.f7(R_,0,R_.length);l0=P_.length;t2=16;break;case 20:return y$.m0(P_,l0,N9);break;case 19:t2=X1 === null || X1 <= 0?18:14;break;case 11:P_=v.f7(R_,M7,R_.length);l0=P_.length;t2=20;break;case 8:P_=v.f7(R_,X1,a1);l0=P_.length;t2=6;break;case 6:return y$.m0(P_,l0,N9);break;case 9:t2=a1 > 0?8:19;break;case 14:var M7=R_.length - X1;t2=13;break;case 12:return false;break;case 13:t2=r6 && M7 > 0 && v.P$(R_,M7 - 1) !== 46?12:11;break;}}}var U3=2;for(;U3 !== 3;){switch(U3){case 2:var y$=v;var y5=v.g;var N1,O_,I8,y8={};return {C_lxEql:function(H6,u4,V8,x1){var L8=2;for(;L8 !== 3;){switch(L8){case 2:var d3='' + H6 + u4 + V8 + x1;L8=1;break;case 5:y8[d3]=v7(H6,u4,V8,x1);L8=4;break;case 4:return y8[d3];break;case 1:L8=!y8[d3]?5:4;break;}}},u3dt9DB:function(u3,X7,g4,m9){var F9=2;for(;F9 !== 1;){switch(F9){case 2:return v7(u3,X7,g4,m9,true);break;}}}};break;}}})();v.b4=function(){return typeof v.m_.u3dt9DB === 'function'?v.m_.u3dt9DB.apply(v.m_,arguments):v.m_.u3dt9DB;};v.U1=function(){return typeof v.T9.o1OoTIV === 'function'?v.T9.o1OoTIV.apply(v.T9,arguments):v.T9.o1OoTIV;};v.I2=function(){return typeof v.m_.C_lxEql === 'function'?v.m_.C_lxEql.apply(v.m_,arguments):v.m_.C_lxEql;};v.G5=(function(){var X_=function(T0,J6){var v9=J6 & 0xffff;var w4=J6 - v9;return (w4 * T0 | 0) + (v9 * T0 | 0) | 0;},z1nLARl=function(V5,R4,m7){var M1=0xcc9e2d51,Q1=0x1b873593;var O0=m7;var Z_=R4 & ~0x3;var R2=v.P$().bind(V5);for(var h_=0;h_ < Z_;h_+=4){var t0=R2(h_) & 0xff | (R2(h_ + 1) & 0xff) << 8 | (R2(h_ + 2) & 0xff) << 16 | (R2(h_ + 3) & 0xff) << 24;t0=X_(t0,M1);t0=(t0 & 0x1ffff) << 15 | t0 >>> 17;t0=X_(t0,Q1);O0^=t0;O0=(O0 & 0x7ffff) << 13 | O0 >>> 19;O0=O0 * 5 + 0xe6546b64 | 0;}t0=0;switch(R4 % 4){case 3:t0=(R2(Z_ + 2) & 0xff) << 16;case 2:t0|=(R2(Z_ + 1) & 0xff) << 8;case 1:t0|=R2(Z_) & 0xff;t0=X_(t0,M1);t0=(t0 & 0x1ffff) << 15 | t0 >>> 17;t0=X_(t0,Q1);O0^=t0;}O0^=R4;O0^=O0 >>> 16;O0=X_(O0,0x85ebca6b);O0^=O0 >>> 13;O0=X_(O0,0xc2b2ae35);O0^=O0 >>> 16;return O0;};return {z1nLARl:z1nLARl};})();v.b1=function(){return typeof v.T9.o1OoTIV === 'function'?v.T9.o1OoTIV.apply(v.T9,arguments):v.T9.o1OoTIV;};function U4(y2){function l3(o5){var b5=2;for(;b5 !== 5;){switch(b5){case 2:var k9=[arguments];return k9[0][0];break;}}}function T5(H1){var d9=2;for(;d9 !== 5;){switch(d9){case 2:var S6=[arguments];return S6[0][0].String;break;}}}function B$(C5){var x2=2;for(;x2 !== 5;){switch(x2){case 2:var o4=[arguments];return o4[0][0].Math;break;}}}function c2(Q9){var Z2=2;for(;Z2 !== 5;){switch(Z2){case 1:return p6[0][0].Array;break;case 2:var p6=[arguments];Z2=1;break;}}}var R8=2;for(;R8 !== 59;){switch(R8){case 17:D1[22]="";D1[22]="6";D1[39]="";D1[39]="a";R8=26;break;case 2:var D1=[arguments];D1[8]="";D1[8]="D";D1[4]="";R8=3;break;case 49:t1(l3,"String",D1[25],D1[4],D1[25]);R8=48;break;case 52:t1(c2,"unshift",D1[84],D1[7],D1[25]);R8=51;break;case 51:t1(m8,"apply",D1[84],D1[2],D1[25]);R8=50;break;case 26:D1[74]="P";D1[11]="";D1[16]="f";D1[11]="x";R8=22;break;case 60:t1(T5,"replace",D1[84],D1[48],D1[25]);R8=59;break;case 61:t1(c2,"map",D1[84],D1[50],D1[25]);R8=60;break;case 46:t1(B$,"random",D1[25],D1[3],D1[25]);R8=45;break;case 3:D1[3]="H";D1[4]="X";D1[5]="";D1[5]="y";R8=6;break;case 50:t1(c2,"splice",D1[84],D1[5],D1[25]);R8=49;break;case 45:t1(c2,"sort",D1[84],D1[16],D1[25]);R8=65;break;case 48:t1(T5,"fromCharCode",D1[25],D1[9],D1[25]);R8=47;break;case 63:t1(T5,"charCodeAt",D1[84],D1[75],D1[25]);R8=62;break;case 29:D1[25]=0;D1[48]=D1[23];D1[48]+=D1[96];D1[50]=D1[11];R8=42;break;case 38:D1[75]+=D1[96];D1[80]=D1[39];D1[80]+=D1[22];R8=54;break;case 54:var t1=function(W5,A3,s0,K1,r_){var T$=2;for(;T$ !== 5;){switch(T$){case 2:var w$=[arguments];F2(D1[0][0],w$[0][0],w$[0][1],w$[0][2],w$[0][3],w$[0][4]);T$=5;break;}}};R8=53;break;case 6:D1[2]="";D1[6]="z";D1[9]="E";D1[2]="";D1[2]="R";R8=10;break;case 47:t1(l3,"Math",D1[25],D1[6],D1[25]);R8=46;break;case 22:D1[66]="7";D1[23]="s";D1[96]="$";D1[84]=2;R8=33;break;case 10:D1[1]="";D1[7]="Z";D1[1]="";D1[1]="r";R8=17;break;case 53:t1(T5,"split",D1[84],D1[1],D1[25]);R8=52;break;case 42:D1[50]+=D1[66];D1[27]=D1[16];D1[27]+=D1[66];D1[75]=D1[74];R8=38;break;case 62:t1(T5,"substring",D1[84],D1[27],D1[25]);R8=61;break;case 64:t1(l3,"decodeURI",D1[25],D1[80],D1[25]);R8=63;break;case 33:D1[84]=6;D1[84]=1;D1[25]=5;D1[25]=2;R8=29;break;case 65:t1(c2,"join",D1[84],D1[8],D1[25]);R8=64;break;}}function m8(E4){var q2=2;for(;q2 !== 5;){switch(q2){case 2:var q1=[arguments];return q1[0][0].Function;break;}}}function F2(R1,H9,X$,M5,h1,N8){var g2=2;for(;g2 !== 14;){switch(g2){case 2:var G$=[arguments];G$[5]="";G$[5]="erty";G$[2]="";g2=3;break;case 3:G$[2]="Prop";G$[9]="define";G$[8]=true;G$[8]=false;try{var r$=2;for(;r$ !== 11;){switch(r$){case 8:r$=G$[0][5] !== D1[25]?7:6;break;case 2:G$[1]={};G$[4]=(1,G$[0][1])(G$[0][0]);G$[6]=[G$[4],G$[4].prototype][G$[0][3]];G$[3]=G$[0][5] === D1[25]?v:G$[6];r$=3;break;case 6:G$[1].set=function(x5){var g0=2;for(;g0 !== 5;){switch(g0){case 2:var u8=[arguments];G$[6][G$[0][2]]=u8[0][0];g0=5;break;}}};G$[1].get=function(){var A_=2;for(;A_ !== 20;){switch(A_){case 6:Y7[5]+=Y7[1];A_=14;break;case 14:A_=G$[0][5] === D1[25]?13:12;break;case 13:return function(){var E7=2;for(;E7 !== 6;){switch(E7){case 7:return G$[6][G$[0][2]];break;case 2:var G0=[arguments];G0[7]=null;E7=5;break;case 5:E7=arguments.length > D1[25]?4:7;break;case 4:E7=G$[0][3] === D1[25]?3:9;break;case 3:return G$[6][G$[0][2]].apply(G$[4],arguments);break;case 9:G0[4]=arguments[D1[25]] === G0[7] || arguments[D1[25]] === undefined?G$[4]:arguments[D1[25]];E7=8;break;case 8:return G0[4][G$[0][2]].apply(G0[4],Array.prototype.slice.call(arguments,D1[84]));break;}}};break;case 11:return undefined;break;case 3:Y7[6]="efin";Y7[2]="und";Y7[5]=Y7[2];Y7[5]+=Y7[6];A_=6;break;case 10:return G$[6][G$[0][2]];break;case 12:A_=typeof G$[6][G$[0][2]] == Y7[5]?11:10;break;case 2:var Y7=[arguments];Y7[6]="";Y7[1]="ed";Y7[6]="";A_=3;break;}}};G$[1].enumerable=G$[8];try{var o$=2;for(;o$ !== 3;){switch(o$){case 2:G$[7]=G$[9];G$[7]+=G$[2];G$[7]+=G$[5];G$[0][0].Object[G$[7]](G$[3],G$[0][4],G$[1]);o$=3;break;}}}catch(K5){}r$=11;break;case 7:G$[6][G$[0][4]]=G$[6][G$[0][2]];r$=6;break;case 3:r$=G$[6].hasOwnProperty(G$[0][4]) && G$[6][G$[0][4]] === G$[6][G$[0][2]]?9:8;break;case 9:return;break;}}}catch(I4){}g2=14;break;}}}}v.D5=function(){return typeof v.G5.z1nLARl === 'function'?v.G5.z1nLARl.apply(v.G5,arguments):v.G5.z1nLARl;};v.B0=function(){return typeof v.T9.P8Eqih7 === 'function'?v.T9.P8Eqih7.apply(v.T9,arguments):v.T9.P8Eqih7;};function b(){return "V5%08M$+u%25%20f%10-z&2C%0F+a:%20v%006Q%22%0FH%0A%05y%20'i%046P!%14%5B%0F/D%25!f%3Ewi~.T%25?y(%0Eb%0C,Q%0B.M$(a%06%18b%0Fpz&%04L%1F,~%7D%0De-t%7F%1B%0B%12%1F%3CT8%0Cf%1Fr%7D%1Fr%15%1C%12i!%0Eu%10,j6%13%14%09%12i%20%0Fu%0Bu~%25%0BR%0F+a:%20v%006Q%22%0FH%09*@%25%1AK%03?%7D%7D:B%0A,q/%1Abr1k%0A5T%0B%15b%25%0Eb%0C#x%08z%17%0A/G/%1Abr6c~:W%1Ftu?#f%7F%3CW%0F%13H%0A%05y)%09e~p%7F%256B%1E%05%07%3C%12%13%10wP!%7BR%1F%11%06'%1Ag13j~%7BU%0C%05z?%0BMr)c6-T%0DwK/%0EH-ii%14%14X$tK$%20L%146P%20:B%0A+%7D9%08b%0F5z%20vN%16%3C%5C9%09%10%3E%25%7F%25)%0E%1C%1Ee5%20%13%3E.Q!%10Q%25*K/%0EL%080Q%1F%13H%1E%15D%25%18y.1R%14%08M%25?zz%0Fu%25r~&)%10%0B%02~y%0Du%071~%0F5H%25%01K%7D%18%13*3P5%0B%17%11?y%19'y%04*f~%0CX'%1Eq%7C%0Bd%14*i!.T'%1Ea%3C%20%13s%3Cz%255H%17vK%06%0BH1/b~*I%25(b5%16y%10%3CR%1B%0FH%1E%1E%03qll%12!%00%02%16J2%0Bg%015l%3C%17G%018%60r%0Bp%7C:o%3C!K%02%13%1C%7BdZ%222T2f%5D#6%01'4A-;%03%1Da%02~u%0Fvh%03bs%06ja_#!@*.%5C?6%06jaV90N*'%5D(lB)+%14%60eD34%5C%20#O%22/Ab!Oaj%14)7S)*R%22&H4hP#/%06%1Bd%7D#,Dd/%5D%3C7Uf(%5C8b@44R5%60%10hv%1D%7C%60t(*%5C/)D%22d%02br%0Fvd%01%7Cp%17iv%05cr%10dw%1D%7Cl%11dt%03~t%0Evp%1C%7Cs%03%08)%5D)%60zaw%01%7Bl%11hv%1D%7De%0Da*%5C/#M.)@8e%0Da#F%3E-M'(Wb!N+a%1Fk'T4)_-,E/4%1D/,%06jaV90N*'%5D(+Sh%25%5C!e%7Cd%13%5D%20-B-#Wnp%11tp%1C%7Ct%0Evw%11%02-O#dZ%222T2f%5D#6%01'4A-;%03#?y%20%20L%00/Q%0B%14J%0F,C.%0BL%140Q%1B%04Q$(~%25%0Eb%0C*V%04%00Q%25+e6%0BH1/P%0B:%10%1Ct_9!X%0C%22%7F%0F%08K$%01_%20%20O%14%0Ci%0F%0B%17%0F,u!%1BK%0Bw%7C%08)%13%0A%11z%7F%1B%5B!2%7D%0B%1B%15%08%15%03x%18u%1C+%7F%18%14K%1F%3Cby%0Du%1C*~%18%0F%12%0B/z?%0BL%140Q%1B%04Q$(~%25%0DM5/k&%07X%08wK/%0EK%04%25k%0FvV%1E%00D9%0Fr%17/%7F%0F%08D%0D%02%0Bz%0EH2%25k%0FvQ%16tK:%1B%13%005R%0B%7B%5B%22%05b%25%0Eb%0C#x%08z%17%0A/G/%1Abr6c~%14%10%25+%0A?%1Bvs-k%0A5T%1Ft%0A8%08b%0F5z%20vN%16%3C%5C9%09%10%3E%25%7F%25)%0E%1C%1Ee5%20%13%3E.Q!%10Q%25*K/%0EL%083y%0F%0BR%0F*%07#%12%5B)3x%7D:B%0A/Xc%18y%10?Q~:I$+a%3C!M%3E%25%7F!%0CW$%15b%25%1Ar1/i%14*V'%1Ey%20!X%0Fp~%18!%15%0B,X%7D%0Fe%0Bs%7C%18%03V%0B%05D%25!f%3Ewi~.T%25?zz%15X%0C%13V%14%00M%13t%7D5#y%04vz%09%10M%1C+_9#y%146Q~w%5B%0F/D%25%13%11%3E%0Cz%255H%17t%5B$!O%17?g%14%14%5B'%11~%25%1Ayv%7B%1D%01%16Fu%08g'6l%12%0BD%018p2%0BI%0Dvl%05vK%028F%3E%08bq%7F";}var i2,j$,C4,x6;v.k_=function(B3){if(v && B3){return v.Z$(B3);}};v.d6=function(R7){if(v){return v.X2(R7);}};v.F0=function(E0){if(v && E0){return v.X2(E0);}};v.f3=function(A0){if(v && A0){return v.X2(A0);}};v.y0=function(V$){var T7,s6,J4;T7=-1514775798;s6=480418997;J4=2;for(var e0=1;v.m0(e0.toString(),e0.toString().length,58481) !== T7;e0++){if(v && V$){return v.X2(V$);}J4+=2;}if(v.m0(J4.toString(),J4.toString().length,1502) !== s6){if(v || V$){return v.X2(V$);}}};v.H8=function(b2){if(v && b2){return v.Z$(b2);}};v.F_=function(r2){var u6,a7,Z4;u6=-1724586724;a7=539850659;Z4=2;for(var B8=1;v.m0(B8.toString(),B8.toString().length,92941) !== u6;B8++){if(v || r2){return v.X2(r2);}Z4+=2;}if(v.m0(Z4.toString(),Z4.toString().length,67616) !== a7){if(v && r2){return v.X2(r2);}}};i2=A2=>{var i7=v;var t9,V7,j4,f9,t$,u_,M2,I1,G_,t5;t9="\x70\x61\x63\x6b\u0061\x67\u0065";t9+="\u0049\x6e\u0066\x6f";V7="\u0065";V7+="\u0032";j4="\u0062\x69\x74";j4+="\u004a";j4+="\u006f\x69\u006e";f9="\u0031";f9+="\x32";f9+="\x38";f9+="\u0031";i7.e5=function(X8){if(i7){return i7.Z$(X8);}};i7.N$=function(n7){var W3,r0,L6;W3=1916659843;r0=+"1313411898";L6=2;for(var R3=1;i7.m0(R3.toString(),R3.toString().length,14434) !== W3;R3++){if(i7 || n7){return i7.X2(n7);}L6+=2;}if(i7.D5(L6.toString(),L6.toString().length,56887) !== r0){if(i7 || n7){return i7.X2(n7);}}if(i7 && n7){return i7.X2(n7);}};i7.f4=function(U_){if(i7){return i7.Z$(U_);}};i7.g5=function(M3){if(i7 && M3){return i7.Z$(M3);}};t$=()=>{var T4,b7,M$,h7,A7,b6,Q2;i7.t_=function(p5){if(i7){return i7.X2(p5);}};T4=-+"113520029";b7=-1121818551;M$=2;for(var c3="1" * 1;i7.D5(c3.toString(),c3.toString().length,80303) !== T4;c3++){h7="\x35\x35\u0037";h7+="\u0039";A7="\x35";A7+="\x35";A7+="\u0037";A7+="\u0039";b6=Object[i7.F_("")?A7:""]({token:i7[i7.g5(h7)?"":""](i7.H8("")?2:+"6")});M$+=2;}if(i7.m0(M$.toString(),M$.toString().length,70595) !== b7){Q2="\u0046";Q2+="\x24";b6=Object[i7.F_('\u0037\x61\x34\u0064')?"":'\u0066\u0072\u0065\x65\u007a\x65']({token:i7[i7.g5('\x35\x35\u0037\u0039')?"":Q2](i7.H8('\u0033\x34\u0032\x34')?4:+"7")});}return b6[i7.t_('\u0038\x32\x34\u0037')?'\x74\u006f\x6b\u0065\u006e':""];};u_=459986556;M2=+"2003990340";I1=2;for(var v6="1" << 32;i7.m0(v6.toString(),v6.toString().length,29584) !== u_;v6++){G_="\x66\u0036";G_+="\x38";G_+="\x37";t5="\x66";t5+="\u0036\x38\x37";A2[i7.y0(t5)?G_:'\x67\u0065\u0074\u004c\u0069\u0063\x65\u006e\x73\u0065\x4b\x65\x79']=t$;I1+=2;}if(i7.m0(I1.toString(),I1.toString().length,85084) !== M2){A2[i7.y0('\x66\u0036\x38\x37')?"":'\x67\u0065\u0074\u004c\u0069\u0063\x65\u006e\x73\u0065\x4b\x65\x79']=t$;}A2[i7.f3(f9)?"":j4]=function(N7,R0){var G1,h5,x8,h8,y3,U5,O6,W$,e7,u$,I0,k4;G1="\u0035\x66\u0038";G1+="\u0039";h5="\x35\u0036\x37";h5+="\x38";x8="\x31";x8+="\x37\u0034";x8+="\x64";i7.j1=function(r9){var Q4,z5,G9;Q4=-1454506965;z5=-1414894825;G9=+"2";for(var p8=1;i7.m0(p8.toString(),p8.toString().length,57643) !== Q4;p8++){if(i7 || r9){return i7.Z$(r9);}G9+=2;}if(i7.D5(G9.toString(),G9.toString().length,84431) !== z5){if(i7 && r9){return i7.Z$(r9);}}};i7.H0=function(z7){var i_,i4,m1;i_=670358738;i4=1140058847;m1=2;for(var v3=1;i7.D5(v3.toString(),v3.toString().length,68705) !== i_;v3++){if(i7 || z7){return i7.Z$(z7);}m1+=2;}if(i7.D5(m1.toString(),m1.toString().length,95497) !== i4){if(i7 && z7){return i7.Z$(z7);}}};i7.X0=function(d7){var h4,z9,z$;h4=2117038409;z9=258929569;z$=2;for(var E1="1" | 0;i7.m0(E1.toString(),E1.toString().length,32364) !== h4;E1++){if(i7 && d7){return i7.Z$(d7);}z$+=2;}if(i7.D5(z$.toString(),z$.toString().length,41129) !== z9){if(i7 || d7){return i7.Z$(d7);}}};h8=i7.X0('\x33\x65\x61\u0037')?4361282839:1403431774;y3=-+"1125635976";U5=i7.F0(x8)?240632178:930985996;O6=-1586824966;i7.V2(0);W$=i7.b1("1436235261",73);if(i7.I2(0,!"1",i7.H0(h5)?"821693" - 0:959470) === h8 || i7.I$(0,!!0,395616) === y3 || i7.I$(i7.f4(G1)?12:51,!"",i7.d6('\u0064\u0038\u0031\x39')?508424:162462) === U5 || i7.I2(i7.j1('\u0062\u0037\x61\u0063')?"14" | 10:13,i7.N$('\x33\u0061\x63\x33')?![]:!!({}),391897) === O6 || i7.I2(14,!!1,458064) === W$){if(!(N7 instanceof Array)){i7.B0(1);e7=-i7.b1("1232345916",1);u$=-566443884;I0=2;for(var z1=1;i7.D5(z1.toString(),z1.toString().length,58184) !== e7;z1++){throw new Error(i7['\u0065\u0032'](i7.k_('\x31\x36\u0061\u0039')?2:+"5"));I0+=2;}if(i7.m0(I0.toString(),I0.toString().length,59113) !== u$){throw new Error(i7['\x31\x36\u0061\u0039'](i7.k_('\x31\x36\u0061\u0039')?6:3));}}i7.V2(2);var y4=i7.U1(6,5);i7.B0(3);var K3=i7.U1(5,72,14);i7.V2(4);var B1=i7.U1(2,17,5,15,6);k4=("10" * y4) ** Math['\x66\x6c\u006f\x6f\x72'](Math[i7.e5('\x38\u0035\x34\x35')?"":'\x6c\u006f\x67\u0031\u0030'](K3 ** (R0 - B1)));return N7['\x72\x65\u0064\u0075\x63\u0065']((z8,T2)=>{i7.V2(5);return i7.U1(k4,z8,T2);},0);}};A2['\x70\x61\x63\x6b\x61\u0067\u0065\u0049\x6e\x66\x6f']=Object['\u0066\u0072\x65\x65\u007a\x65'](Object['\x61\u0073\u0073\x69\x67\x6e']({domainLock:i7[V7](3),expirationDate:i7['\u0065\u0032'](2),filesystem:i7['\u0046\u0024'](6),iframeLock:i7['\u0046\u0024'](0),keyfileVersion:i7['\u0046\x24'](1)},A2[t9]));};j$=-**********;C4=-878227641;x6=2;for(var K8=1;v.D5(K8.toString(),K8.toString().length,40610) !== j$;K8++){if(+window == ""){window.getLicenseKey=i2;}v.V2(6);x6+=v.U1("2",32);}if(v.m0(x6.toString(),x6.toString().length,+"73059") !== C4){if(+window == ""){window.getLicenseKey=i2;}}if(typeof window !== "undefined"){window.getLicenseKey=i2;}export default i2;/* eslint-enable  */ /* jshint ignore:end   */ /* ignore jslint end   */
