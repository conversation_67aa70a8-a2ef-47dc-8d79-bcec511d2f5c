import { useEffect, useRef, useState } from 'react';

const useHorizontalScroll = ({ loading }) => {
  const tableRef = useRef(null);
  const outerRef = useRef(null);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const [scrollWidth, setScrollWidth] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.onresize = handleResize; // Assign the handleResize function directly to onresize

    return () => {
      window.onresize = null; // Clean up the onresize event to avoid memory leaks
    };
  }, [screenWidth]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const newScrollWidth = entry.target.scrollWidth;
        setScrollWidth(newScrollWidth);
      }
    });

    const tableElement = tableRef.current;
    const outerElement = outerRef.current;
    if (tableElement && outerElement) {
      resizeObserver.observe(tableElement);
      const hasHorizontalScrollbar = scrollWidth > outerElement.clientWidth;
      setHasHorizontalScroll(hasHorizontalScrollbar);
    }
  }, [screenWidth, loading, scrollWidth]);

  return { tableRef, outerRef, hasHorizontalScroll };
};

export default useHorizontalScroll;
