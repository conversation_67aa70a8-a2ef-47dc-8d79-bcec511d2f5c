import {gql} from './utils';

export const EARNING_EVENTS_QUERY = gql(/* GraphQL */`query EarningEvents($companyCode: String!, $fromDate: DateTime!, $toDate: DateTime!) {
  company(code: $companyCode) {
    fcEventsByTypes(
      fcEventTypeNames: "Results"
      where: {
        dateTime: {
          gte: $fromDate
          lt: $toDate
        }
      }
      order: { dateTime: ASC }
      first: 99999999
    ) {
      nodes {
        eventName
        dateTime
      }
    }
  }
}
`);

export const EARNING_EVENTS_PAGING_QUERY = gql(/* GraphQL */`query EarningEventsPaging(
  $companyCode: String!
  $fromDate: DateTime!
  $toDate: DateTime!
  $cursor: String
  $cultureName: String
) {
  company(code: $companyCode) {
    fcEventsByTypes(
      cultureName: $cultureName
      fcEventTypeNames: "Results"
      where: { dateTime: { gte: $fromDate, lt: $toDate } }
      order: { dateTime: DESC }
      first: 100
      after: $cursor
    ) {
      edges {
        node {
          eventName
          dateTime
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}
`);