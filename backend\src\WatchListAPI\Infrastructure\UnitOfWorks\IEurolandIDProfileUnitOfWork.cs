﻿using WatchListAPI.Infrastructure.Repositories;

namespace WatchListAPI.Infrastructure.UnitOfWorks
{
    public interface IEurolandIDProfileUnitOfWork : IUnitOfWorkBase<EurolandIDProfileDbContext>
    {
        IWatchListRepository WatchListRepository { get; }
        IWatchListDetailRepository WatchListDetailRepository { get; }
        IInstrumentRepository IntrumentRepository { get; }
    }

    public class EurolandIDProfileUnitOfWork(EurolandIDProfileDbContext dbContext,
                        IWatchListRepository watchListRepository,
                        IWatchListDetailRepository watchListDetailRepository,
                        IInstrumentRepository instrumentRepository
                        )
                : UnitOfWorkBase<EurolandIDProfileDbContext>(dbContext), IEurolandIDProfileUnitOfWork
    {
        public IWatchListRepository WatchListRepository => watchListRepository;
        public IWatchListDetailRepository WatchListDetailRepository => watchListDetailRepository;
        public IInstrumentRepository IntrumentRepository => instrumentRepository;
    }
}
