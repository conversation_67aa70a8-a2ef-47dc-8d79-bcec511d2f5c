using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using HotChocolate;

namespace Euroland.FlipIT.ShareGraph.API.Resolvers
{
  public class ConfigurationResolver
  {
    public Setting GetSettings([Service] ISetting _settings)
    {
      var styleURI = _settings.GetChild("StyleURI")?.Value;

      var settings = new Setting()
      {
        Layout = _settings.GetChild("Layout")?.Value,
        TimeZone = _settings.GetChild("TimeZone")?.Value,
        UseLatinNumber = bool.Parse(_settings.GetChild($"UseLatinNumber")?.Value ?? "true"),
        StyleURI = string.IsNullOrEmpty(styleURI) ? null : styleURI,
        StreamUpdateDelay = int.TryParse(_settings.GetChild("StreamUpdateDelay")?.Value, out int streamUpdateDelay) ? streamUpdateDelay : null,
        GoogleTagEnabled = bool.Parse(_settings.GetChild($"googleTagEnabled")?.Value ?? "false"),
        TickerRefreshSeconds = int.TryParse(_settings.GetChild("TickerRefreshSeconds")?.Value, out int tickerRefreshSeconds) ? tickerRefreshSeconds : null,
        UsePrevCloseDayForMcap = bool.TryParse(_settings.GetChild("UsePrevCloseDayForMcap")?.Value, out bool usePrevCloseDayForMcap) && usePrevCloseDayForMcap,
      };

      return settings;
    }
  }
}
