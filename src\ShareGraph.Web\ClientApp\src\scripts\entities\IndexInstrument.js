import { getCustomPhraseIndices } from '../helper';

export default class IndexInstrument {
  constructor(params = {}) {
    Object.assign(this, params);
    
    this.indexInstrumentData = getCustomPhraseIndices(params);
    this.title = this.indexInstrumentData?.tickerName || params.shareName || '';
    this.id = params.instrumentId ? `${params.instrumentId}_i` : '';
    this.value = params.instrumentId ? `${params.instrumentId}_i` : '';
  }
}
