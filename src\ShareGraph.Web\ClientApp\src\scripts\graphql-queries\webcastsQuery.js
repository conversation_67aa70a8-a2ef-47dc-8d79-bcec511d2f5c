import {gql} from './utils';

export const WEBCASTS_QUERY = gql(/* GraphQL */`query Webcasts ($companyCode: String!, $from: DateTime!, $to: DateTime) {
  webcasts(
    companyCode: $companyCode,
    from:$from,
    to: $to,
    )
    {
      id
      date
      title
      defaultHost
      transcriptUrl
      thumbnailUrl
      urls{
        url
        videoId
        hostType
      }
      vimeo {
        vimeoId
        hostType
      }
  }
}`);