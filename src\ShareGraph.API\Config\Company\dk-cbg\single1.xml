<?xml version="1.0" encoding="utf-8" ?>
<settings>
	<Layout>FULL</Layout>
	<TimeZone>Africa/Cairo</TimeZone>
	<UseLatinNumber>false</UseLatinNumber>
	
	<Instruments>
		<Instrument>
			<Id>32865</Id>
			<Color>#7DCA48</Color>
			<Order>3</Order>
			<Default>false</Default>
			<TickerName>
				<en-GB>CARL B (COP)</en-GB>
				<ar-AE>CARL B (COP).</ar-AE>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
			<MarketName>
				<en-GB>Copenhagen</en-GB>
				<ar-AE>Copenhagen</ar-AE>
			</MarketName>
			<MarketAbbreviation>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</MarketAbbreviation>
		</Instrument>
	</Instruments>

	<Ticker>
		<!-- Posible values: GRAPH, TABLE -->
		<EnabledFormat>GRAPH, TABLE</EnabledFormat>
		<!-- Posible values: SINGLE, MULTIPLE -->
		<TickerType>SINGLE</TickerType>
		<!-- Posible values: SINGLE_TICKER_1, SINGLE_TICKER_2, MULTIPLE_TICKER_1, MULTIPLE_TICKER_2, {CustomTemplate}  -->
		<!-- <GraphTickerTemplate>SINGLE_TICKER_1_WITH_DANGEROUS_HTML_TAGS</GraphTickerTemplate> -->
		<GraphTickerTemplate>SINGLE_TICKER_1</GraphTickerTemplate>
		<!-- <TableColumns>TABLE_TICKER_SINGLE, TABLE_TICKER_MULTIPLE</TableColumns> -->
		<TableTickerTemplate>TABLE_TICKER_SINGLE</TableTickerTemplate>
		<!-- Possible values: FADE, TRANSFORM -->
		<TableAnimation>FADE</TableAnimation>
		<!-- Possible values: FADE, BLINK_PRICE, BLINK_MARKET -->
		<GraphAnimation>BLINK_PRICE</GraphAnimation>
	</Ticker>

	<Peers>
		<Enabled>true</Enabled>
		<Peer>
			<Id>990010</Id>
			<Color>#04A851</Color>
			<Order>4</Order>
			<!-- <TickerName>
				<en-GB>CARL A (COP).Ticker</en-GB>
				<ar-AE>CARL B (COP).</ar-AE>
			</TickerName> -->
		</Peer>
		<Peer>
		<Id>18612</Id>
		<Color>#04A851</Color>
		<Order>1</Order>
		<TickerName>
			<en-GB>CARL A (COP).</en-GB>
			<ar-AE>CARL B (COP).</ar-AE>
		</TickerName>
		<CurrencyCode>
			<en-GB>DKK</en-GB>
			<ar-AE>DKK</ar-AE>
		</CurrencyCode>
		</Peer>
	</Peers>

	<ShareDetails>
		<!-- Options: True|False -->
		<Enabled>True</Enabled>
		<!-- Fields available: TIME,CURRENCY,MARKET,MARKET_STATUS ...-->
		<ShareDataItems>TIME,CURRENCY,MARKET,MARKET_STATUS,ISIN,SYMBOL,BID,BID_SIZE,ASK,ASK_SIZE</ShareDataItems>
		<!-- Options: True|False -->
		<DisplayOnSelectedTicker>True</DisplayOnSelectedTicker>
		<!-- Options: Grid|Flow -->
		<DisplayType>Grid</DisplayType>
		<!-- Options: True|False -->
		<PartialDisplay>True</PartialDisplay>
   </ShareDetails>
	
	<Format>
		<en-GB>
			<TickerDateTimeFormat>MMM DD, YYYY [|] hh:mm [UTC]Z</TickerDateTimeFormat>
			<ShortDate xml:space="preserve">DD/MM/YYYY</ShortDate>
			<DecimalDigits>2</DecimalDigits>
			<PercentDigits>2</PercentDigits>
			<!--DecimalSeparator: comma or dot-->
			<DecimalSeparator xml:space="preserve">.</DecimalSeparator>
			<!--ThousandsSeparator: comma, dot, space-->
			<ThousandsSeparator xml:space="preserve">.</ThousandsSeparator>
			<!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
			<NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
		</en-GB>
		<ar-AE>
			<TickerDateTimeFormat>DD MMM, YYYY hh:mm [(GMT] Z[)]</TickerDateTimeFormat>
			<ShortDate xml:space="preserve">DD/MM/YYYY</ShortDate>
			<DecimalDigits>7</DecimalDigits>
			<PercentDigits>2</PercentDigits>
			<!--DecimalSeparator: comma or dot-->
			<DecimalSeparator xml:space="preserve">,</DecimalSeparator>
			<!--ThousandsSeparator: comma, dot, space-->
			<ThousandsSeparator xml:space="preserve">.</ThousandsSeparator>
			<!--NegativeNumberFormat
				Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
				Possible values: (n), -n, - n, n-, n -
				Default value: -n-->
			<NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
		</ar-AE>
	</Format>

	<CustomPhrases>
		<rangeLabel>
			<en-GB>
				EN Range Label 
			</en-GB>
			<fi-FI>
				FI Range Label
			</fi-FI>
			<sv-SE>
				SE Range Label
			</sv-SE>
		</rangeLabel>
	</CustomPhrases>

</settings>
