import fetchApi from './fetch-api';
import { appSettings } from '../../appSettings';
import { isArray } from '../utils';
import appConfig from '../services/app-config';
import { EVENT_TYPES } from '../common';
import { CIQ } from '../../scripts/components/chart/chartiq-import';
import { chartLoading } from '../components/chart/ChartHelper';
import {MarketTime, MarketTimeInDate} from './market';
import dayjs from 'dayjs';
import {client, clientRT} from './graphql-client';
import {CHART_HISTORY_QUERY} from '../graphql-queries/chartHistoryQuery';
import {getAllInstrumentsSetting} from '../configs/configuration-app';

const cloneDate = date => {
  if (!date || Object.prototype.toString.call(date) !== '[object Date]') {
    return '';
  }

  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds(),
    date.getMilliseconds()
  );
};

/**
 * @typedef { 'second' | 'minute' | 'day' | 'week' | 'month' } TimeUnit
 */

/**
 * 
 * @param {number} period 
 * @param {TimeUnit} timeUnit
 * @param {Date | 'string'} date 
 * @returns { Date }
 */
function startOf(period, timeUnit, date) {
  const cloneDate = new Date(date);
  switch(timeUnit) {
    case 'second': {
      const seconds = cloneDate.getSeconds();
      cloneDate.setSeconds(seconds - (seconds % period), 0);
      return cloneDate;
    }
    case 'minute': {
      const minutes = cloneDate.getMinutes();
      cloneDate.setMinutes(minutes - (minutes % period), 0, 0);
      return cloneDate;
    }
    case 'day': {
      const hours = cloneDate.getHours();
      cloneDate.setHours(hours - (hours % period), 0, 0);
      return cloneDate;
    }
    case 'week': {
      const days = cloneDate.getDate();
      cloneDate.setDate(days - (days % (period * 7)), 0, 0);
      return cloneDate;
    }
  }
}

export default class ChartDataService {
  constructor(settings = {}) {
    this.settings = settings;
  }

  /**
   *
   * @param {{
   * instrumentId: string,
   * fromDate: Date,
   * toDate: Date,
   * period: number,
   * timeUnit: TimeUnit,
   * toCurrency?: string,
   * isIntraday?: boolean,
   * isRT?: boolean,
   * abortController: AbortController
   * }} param0
   */
  async newQuerySchema({
    instrumentId,
    fromDate,
    toDate,
    period,
    timeUnit,
    toCurrency,
    isIntraday,
    isRT,
    abortController
  }) {
    let timeIntervalGrouping = 1;
    switch(timeUnit) {
      case 'second':
        timeIntervalGrouping = period;
        break;
      case 'minute': {
        timeIntervalGrouping = 60 * period;
        break;
      }
    }

    let fromDateHistory = dayjs(fromDate).utc(true).startOf('day');
    let toDateHistory = dayjs(toDate).utc(true).startOf('day');
    const afterOneDay = toDateHistory.subtract(1, 'day');
    if(afterOneDay.isBefore(fromDateHistory)) {
      fromDateHistory = afterOneDay;
    }

    const first = isIntraday && timeIntervalGrouping == 1 ? null : 9999999;
    const last = isIntraday && timeIntervalGrouping == 1 ? 3600 : null;

    const result = await (isRT ? clientRT.query : client.query)(CHART_HISTORY_QUERY, {
      first,
      last,
      id: instrumentId,
      isIntraday,
      toCurrency,
      toDate,
      fromDate,
      timeIntervalGrouping,
      fromDateHistory: fromDateHistory.toISOString(),
      toDateHistory: toDateHistory.toISOString(),
      adjClose: getAllInstrumentsSetting()[instrumentId].enabledAdjustPrice
    }, { fetchOptions: {
      _signal: abortController?.signal
    }});

    const historicals = result.data.instrumentById.historicals.nodes;
    const intraday = result.data.instrumentById?.intraday?.nodes || [];

    historicals.forEach(item => {
      item.date = item.dateTime.slice(0, 10);
    });
    intraday.forEach(item => {
      item.date = item.dateTime.slice(0, 10);
    });

    const data = {
      data: {
        instrumentDailyHistorical: isIntraday ? historicals : [],
        instrumentHistorical: isIntraday ? intraday || [] : historicals
      }
    };
    
    return data;
  }

  /**
   *
   * @param {Array<Number>} instruments Array of instrumentId.
   * @param {TimeUnit} timeUnit
   * @param {number} period
   * @param {Date} from
   * @param {Date} to
   * @param {Record<string, any>} symbolObject
   * @returns {Promise}
   */
  async query(instruments, timeUnit, period, from, to, session, extended, isIntraday, symbolObject, stx) {
    const realTimeId = this.settings.realTimeId;
    const url = new URL(appSettings.sDataApiUrl);
    url.searchParams.append('session', session);
    url.searchParams.append('extended', extended);
    from = typeof from === 'string' ? new Date(from) : from;
    to = typeof to === 'string' ? new Date(to) : to;

    const now = dayjs().startOf('minute');
    const isFuture = now.isBefore(to) || now.isSame(to);

    // let fromDate = from ? (isIntraday ? formatWithDateTime(from): CIQ.yyyymmdd(from)) : '';
    // let toDate = to ? (isIntraday ? formatWithDateTime(lastOf(to, 'minus')): formatWithDateTime(lastOf(to, 'day'))) : '';
    let toCurrency = symbolObject.toCurrency;
    
    if(typeof symbolObject.getCurrency === 'function') {
      toCurrency = symbolObject.getCurrency()?.code;
    }
    
    const instrumentId = instruments[0];

    const result = this.newQuerySchema({
      url: url.href, 
      fromDate: from, 
      toDate: to, 
      instrumentId, 
      period, 
      timeUnit,
      isIntraday, 
      toCurrency,
      isRT: !!realTimeId[instrumentId],
      abortController: symbolObject.getAbortController?.()
    }).then(response => {
      if (response.errors && response.errors.length) {
        throw Error(response.errors);
      } else {
        const state = stx.reduxStore.getState();
        const currentPrice = [
          ...state.tickers.instruments ?? [],
          ...state.peers.instruments ?? [],
          ...state.indices.instruments ?? []
        ].find(item => item.instrumentId === instrumentId);

        return this.translate(response, isIntraday, symbolObject, currentPrice, period, isFuture);
      }
    });

    return result;
  }

  /**
   *  Called by the library to request an array of initial data for for a particular instrument/symbol
   */
  fetchInitialData(symbol, suggestedStartDate, suggestedEndDate, params, callback) {
    const marketTime = MarketTime.factory(
      params.symbolObject.normalDailyOpen, 
      params.symbolObject.normalDailyClose, 
      params.symbolObject.marketTimeZone
    );

    const start = dayjs(marketTime.marketDefine.getPreviousOpen());
    if(start.isBefore(dayjs(suggestedStartDate))) {
      suggestedStartDate = start.toDate();
    }
    
    const ins = params.symbolObject.instrumentId;
    const isInstraday = !CIQ.ChartEngine.isDailyInterval(params.interval);
    const selectedInstrumentId = params.symbolObject.type === 'T' ? params.symbolObject.instrumentId : params.symbolObject.selectedInstrumentId;

    const stx = params.stx;


    //only hide pr for UI loading
    if(stx.chart && stx.chart.CurrentEvent === EVENT_TYPES.PRESSRELEASES) {
      stx.chart.container.querySelectorAll('.event-marker').forEach((el)=>{
        el.classList.remove('active');
      });
    }

    chartLoading.show('fetchData-'+symbol);

    stx.chart.query = {
      startDate: suggestedStartDate,
      endDate: suggestedEndDate,
      instrumentId: ins,
      selectedInstrumentId: selectedInstrumentId,
      isRTSelectedInstrumentId: !!this.settings.realTimeId[selectedInstrumentId],
      type: 'fetchInitial'
    };

    this.query(
      [ins],
      params.interval,
      params.period,
      suggestedStartDate,
      suggestedEndDate,
      params.quoteDriverID,
      params.extended ? 1 : 0,
      isInstraday,
      params.symbolObject,
      stx
    )
      .then(data => {
        if(!data?.length) {
          stx.changeOccurred('layout');
        }
        callback({
          quotes: data
        });
      })
      .catch(err => {
        if (isArray(err)) {
          err = err.map(e => e.message).join(' -- ');
        } else {
          err = err.message;
        }
        callback({ error: err });
      })
      .finally(() => {
        //loader.hide();
        chartLoading.hide('fetchData-'+symbol);
      });
  }

  /**
   * Called by the library to request a data update - the most recent data not yet loaded into the chart
   */
  fetchUpdateData(symbol, startDate, params, callback) {
    const isMainSeries = params.type === 'T';
    const masterData = params.stx.masterData;
    const refineNumberTicks = 5;
    const refineTick = masterData?.length > refineNumberTicks ? masterData[masterData.length - refineNumberTicks] : undefined;
    if(refineTick) {
      if(refineTick.DT.getTime() < startDate.getTime()) startDate = refineTick.DT;
    }

    const ins = params.symbolObject.instrumentId;
    const isInstraday = !CIQ.ChartEngine.isDailyInterval(params.interval);

    const stx = params.stx;

    const endDate = stx.convertToDataZone(new Date());

    this.query([ins], params.interval, params.period, startDate, endDate, params.quoteDriverID, params.extended ? 1 : 0, isInstraday, params.symbolObject, stx)
      .then(data => {
        if(refineTick) {
          let refineNewTicks = data;
          if(data.length > refineNumberTicks) {
            refineNewTicks = data.slice(0, refineNumberTicks);
          }

          params.stx.updateChartData(refineNewTicks, null, {
            noCreateDataSet: true,
            noCleanupDates: true,
            allowReplaceOHL: true,
            ...(!isMainSeries ? {secondarySeries: symbol } : {})
          });
        }

        callback({
          quotes: data
        });
      })
      .catch(err => {
        if (isArray(err)) {
          err = err.map(e => e.message).join(' -- ');
        } else {
          err = err.message;
        }
        callback({ error: err });
      }).finally(() => {
        // chartLoading.hide('fetchData-'+symbol);
      });
  }

  /**
   * Called by the library to add more data into the chart in response to user interactions; such as zooming and scrolling
   */
  fetchPaginationData(symbol, suggestedStartDate, endDate, params, callback) {
    if(!params.future) {
      const start = dayjs(endDate).add(-7,'day');
      if(start.isBefore(dayjs(suggestedStartDate))) {
        suggestedStartDate = start.toDate();
      }
    }

    const ins = params.symbolObject.instrumentId;
    const isInstraday = !CIQ.ChartEngine.isDailyInterval(params.interval);
    const stx = params.stx;

    chartLoading.show('fetchData-'+symbol);
    const perviousQuery = stx.chart.query;
    //update start date to query in Event. that was used to get data for Event (in marker.js)
    stx.chart.query = {
      ...stx.chart.query,
      startDate: suggestedStartDate < perviousQuery.startDate ? suggestedStartDate : perviousQuery.startDate
      //endDate: endDate
    };


    stx.chart.query.type = 'fetchPagination';
    this.query(
      [ins],
      params.interval,
      params.period,
      suggestedStartDate,
      endDate,
      params.quoteDriverID,
      params.extended ? 1 : 0,
      isInstraday,
      params.symbolObject,
      stx
    )
      .then(data => {
        const result = {
          quotes: data,
          // moreAvailable: moreAvailable,
          //https://documentation.chartiq.com/quotefeed.html#~dataCallback
          //upToDate: endDate.getTime() > Date.now()
          upToDate: true
        };

        if(data.length === 0) {
          result.moreAvailable = false;
        }
        callback(result);
      })
      .catch(err => {
        if (isArray(err)) {
          err = err.map(e => e.message).join(' -- ');
        } else {
          err = err.message;
        }
        callback({ error: err });
      }).finally(() => {
        chartLoading.hide('fetchData-'+symbol);
      });
  }

  /**
   *  @param {{
   * feeddata: Array<any>,
   * ohlcDaily: Array<any>,
   * symbolObject: Record<string, any>,
   * period: number,
   * timeUnit: TimeUnit,
   * currentPrice: {
   *  date: string,
   *  last: string
   * }
   * }} param0
   */
  removeIntraExtraHour ({feeddata, ohlcDaily, symbolObject, currentPrice, period, timeUnit, isFuture}) {
    try {
      const {
        normalDailyOpen,
        normalDailyClose,
        marketTimeZone
      } = symbolObject;

      const marketTime = MarketTime.factory(
        normalDailyOpen.replace(/(:00)$/, ''),
        normalDailyClose.replace(/(:00)$/, ''),
        marketTimeZone
      );

      if(isFuture && currentPrice && feeddata.length > 0) {
        const marketTimeToday = new MarketTimeInDate(marketTime, new Date(currentPrice.lastUpdatedDate));
        if(marketTimeToday.isOpenInDay(new Date(currentPrice.lastUpdatedDate))) {
          const currentPriceDate = dayjs(startOf(period, timeUnit, currentPrice.lastUpdatedDate));
          
          let i = feeddata.length;
  
          while(currentPriceDate.isBefore(feeddata[i - 1].dateTime) && i > 0) { i--; }
          
          const newData = feeddata.slice(0, i);
          const lastPoint = newData[newData.length - 1];
          if(currentPriceDate.isAfter(lastPoint.dateTime)) {
            newData.push({
              close: currentPrice.last,
              high: currentPrice.last,
              low: currentPrice.last,
              open: currentPrice.last,
              volume: 0,
              dateTime: currentPriceDate.toISOString(),
              instrumentId: lastPoint.instrumentId
            });
          } else {
            lastPoint.high = Math.max(lastPoint.high, currentPrice.last);
            lastPoint.low = Math.min(lastPoint.low, currentPrice.last);
            lastPoint.close = currentPrice.last;
          }
  
          feeddata = newData;
        }
      }
      

      const ohlcDailyIndexed = MarketTime.indexByDate(ohlcDaily.concat({
        close: symbolObject.last,
        dateTime: symbolObject.lastUpdatedDate
      }), i => i.dateTime);
      const result = feeddata.reduce((group, history, index) => {
        let lastGroup = group.length > 0 ? group[group.length - 1] : undefined;
        const historyDate = new Date(history.dateTime);
        if(!lastGroup || !lastGroup.timeInDate.isSameDay(historyDate)) {
          lastGroup = {
            open: index,
            nextOpen: index,
            close: index,
            extra: undefined,
            timeInDate: new MarketTimeInDate(marketTime, historyDate)
          };
          group.push(lastGroup);
        }

        lastGroup.nextOpen++;

        if(lastGroup.timeInDate.isOpenInDay(historyDate)) {
          lastGroup.close++;
        } else if(lastGroup.extra === undefined) {
          const ohlc = ohlcDailyIndexed[lastGroup.timeInDate.dateName];
          if(ohlc && ohlc.close === history.close) {
            lastGroup.extra = lastGroup.nextOpen - lastGroup.close;
          }
        }
        return group;
      }, []).map(item => {
        return feeddata.slice(item.open, item.close + (item.extra ?? 0));
      }).flat();

      return result;
    } catch (e) {
      console.error(e);
      throw e;
    }
  }

  /**
   * Translate data server response into ChartIQ's required format.
   * @param {Object} response Data that server responses
   * @returns {Object} Object that understandable by ChartIQ
   * @param {Record<string, any>} symbolObject
   * @param {{lastUpdatedDate: string, last: number}} [currentPrice]
   * @param {number} period
   * @param {TimeUnit} timeUnit
   * @param {boolean} isFuture

   */
  translate(response, isIntraday, symbolObject, currentPrice, period, timeUnit, isFuture) {
    var feeddata = response.data.instrumentHistorical;

    if(isIntraday) {
      feeddata = this.removeIntraExtraHour({
        feeddata,
        ohlcDaily: response.data.instrumentDailyHistorical, 
        symbolObject,
        currentPrice,
        period,
        timeUnit,
        isFuture
      });
    }

    var newQuotes = [];
    for (var i = 0; i < feeddata.length; i++) {
      var newQuote = {};
      //newQuote.Date = new Date(feeddata[i].date).toISOString(); // DT is a string in ISO format, make it a Date instance

      //newQuote.DT = feeddata[i].date; // DT is a string in ISO format, make it a Date instance
      if(isIntraday) {
        newQuote.DT = feeddata[i].dateTime;
      }else{
        newQuote.Date = feeddata[i].date + 'T00:00:00';
      }
      // Ref: https://documentation.chartiq.com/tutorial-Dates%20and%20Time%20Zones.html#toc2__anchor
      newQuote.Open = feeddata[i].open;
      newQuote.High = feeddata[i].high;
      newQuote.Low = feeddata[i].low;
      newQuote.Close = feeddata[i].close;
      newQuote.Volume = feeddata[i].volume;
      newQuote.instrumentId = feeddata[i].instrumentId;
      newQuotes.push(newQuote);
    }
    return newQuotes;
  }
}
