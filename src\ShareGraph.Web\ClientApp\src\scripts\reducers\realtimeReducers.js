import produce from 'immer';
import {STREAM_SNAPSHOT, STREAM_TRADES} from '../actions/realtimeAction';
/**
 * @typedef RealtimeLastStock
 * @prop {number} id
 * @prop {number} volume
 * @prop {number} price
 * @prop {string} date
 */

const initState = {
  // stocks: /** @type {Record<number, RealtimeLastStock[]>} */({})
  trades: /** @type {Record<number, RealtimeLastStock>} */({}),
  snapshot: {}
};

export default function realtimeReducer() {
  /**
   * 
   * @param {typeof initState} state 
   * @param {{type: string, payload: RealtimeLastStock}} action 
   */
  function reducer(state = initState, action) {
    switch(action.type) {
      case STREAM_TRADES: {
        return produce(state, draft => {
          draft.trades[action.payload.id] = action.payload;
        });
      }
      case STREAM_SNAPSHOT: {
        return produce(state, draft => {
          draft.snapshot[action.payload.id] = action.payload;
        });
      }
      default: {
        return state;
      }
    }
  }

  return reducer;
}