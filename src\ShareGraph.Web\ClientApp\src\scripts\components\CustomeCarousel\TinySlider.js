import { Children, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { tns } from '../../../lib/tiny-slider';

import useResize from '../../customHooks/useResize';
import { classNames } from '../../helper';
import withScrollSwipe from '../../HOCs/withScrollSwipe';

const Carousel = ({ children, settings = {}, forceSingleRow = false, itemNumber, isRtl = false }) => {
  const containerRef = useRef(null);
  const wrapperRef = useRef(null);
  const sliderRef = useRef(null);
  const [isSingleRow, setIsSingleRow] = useState(true);
  const count = Children.count(children);

  useLayoutEffect(() => {
    const container = containerRef.current;
    const carouselStyles = getComputedStyle(wrapperRef.current);
    let carouselItemWidth = parseInt(carouselStyles.getPropertyValue('--carouselItemWidth'));
    const carouselGutter = parseInt(carouselStyles.getPropertyValue('--carouselGutter'));
    const carouselOffsetWidth = wrapperRef.current.offsetWidth;
    setIsSingleRow(carouselOffsetWidth > 768);
    if (carouselOffsetWidth < 445) {
      carouselItemWidth = undefined;
    }
    if (!sliderRef.current) {
      sliderRef.current = tns({
        container,
        gutter: carouselGutter,
        fixedWidth: itemNumber ? undefined : carouselItemWidth,
        items: itemNumber ? itemNumber : undefined,
        nav: false,
        mouseDrag: true,
        loop: false,
        slideBy: itemNumber ? Math.floor(itemNumber): 'page',
        controlsContainer: false,
        controlsPosition: 'bottom',
        preventScrollOnTouch: 'auto',
        swipeAngle: 30,
        textDirection: isRtl ? 'rtl' : 'ltr',
        ...settings
      });
    }
  }, [count]);

  useEffect(
    () => () => {
      if (sliderRef.current && sliderRef.current.destroy) {
        sliderRef.current.destroy();
      }
    },
    []
  );

  return (
    <div
      ref={wrapperRef}
      className={classNames('tiny-slider__container', {
        'tiny-slider__single-row': forceSingleRow || isSingleRow
      })}
    >
      <div ref={containerRef}>{children}</div>
    </div>
  );
};

const CarouselWrapper = ({ children, settings = {}, ...rest }) => {
  const count = Children.count(children);
  const { width } = useResize();
  return (
    <Carousel settings={settings} key={`${count}-${width}`} {...rest}>
      {children}
    </Carousel>
  );
};

export default CarouselWrapper;
