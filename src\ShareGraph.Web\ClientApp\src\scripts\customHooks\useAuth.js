import { useEffect, useState } from "react";


export const useAuth = () => {
  const [auth, setAuth] = useState(window.EurolandAppContext?.command('authState') || {});

  useEffect(() => {
    const handleAuthChange = () => {
      setAuth(window.EurolandAppContext?.command('authState'));
    };

    window.EurolandAppContext?.on('authChanged', handleAuthChange);

    return () => {
      window.EurolandAppContext?.off('authChanged', handleAuthChange);
    };
  }, []);

  return auth;
};
