import { CIQ } from '../chartiq-import';
import { drawHighLowLine } from './common';

// Overide chartScale


CIQ.UI.Layout.prototype.getChangeChartScale = function (node, chartScale) {
  var stx = this.context.stx,
    className = this.params.activeClassName;
  var listener = function (obj) {
    if (obj.value == chartScale) node.classList.add(className);
    else node.classList.remove(className);
  };
  this.addObserver({ base: stx, path: 'layout.chartScale', listener });
};

CIQ.UI.Layout.prototype.setChangeChartScale = function (node, chartScale) {
  var stx = this.context.stx;
  var layoutScale = stx.layout.chartScale;
  if (layoutScale == chartScale) {
    stx.setChartScale(null);
  } else {
    stx.setChartScale(chartScale);
  }
};

// Overide chartScale

CIQ.UI.Layout.prototype.getChangeChartScale = function (node, chartScale) {
  var stx = this.context.stx,
    className = this.params.activeClassName;
  var listener = function (obj) {
    if (obj.value == chartScale) node.classList.add(className);
    else node.classList.remove(className);
  };
  this.addObserver({ base: stx, path: 'layout.chartScale', listener });
};

CIQ.UI.Layout.prototype.setChangeChartScale = function (node, chartScale) {
  var stx = this.context.stx;
  var layoutScale = stx.layout.chartScale;
  if (layoutScale == chartScale) {
    stx.setChartScale(null);
  } else {
    stx.setChartScale(chartScale);
  }
};

// High/Low lines
CIQ.UI.Layout.prototype.getHighLowLine = function (node, e) {
  const className = this.params.activeClassName;
  var listener = function (params) {
    if(!params) return;
    const stx = params.obj;
    if (params.value) {
      node.classList.add(className);
    }else {
      node.classList.remove(className);
    }
    drawHighLowLine.call(stx);
  };
  this.addObserver({ base: this.context.stx.layout, path: 'showHighLowLine', listener });
};

CIQ.UI.Layout.prototype.setHighLowLine = function (e) {
  var stx = this.context.stx,
    className = this.params.activeClassName,
    node = e.node;

  stx.layout.showHighLowLine = !stx.layout.showHighLowLine;
  const cqChartTitleDOM = stx.container.querySelector('cq-chart-title');
  if (stx.layout.showHighLowLine) {
    node.classList.add(className);
    cqChartTitleDOM && (cqChartTitleDOM.style.display = 'none');
  } else {
    cqChartTitleDOM && (cqChartTitleDOM.style.display = 'block');
    node.classList.remove(className);
    if (stx.highLabel) {
      CIQ.Marker.removeByLabel(stx, 'H');
      stx.highLabel = null;
    }
    if (stx.lowLabel) {
      CIQ.Marker.removeByLabel(stx, 'L');
      stx.lowLabel = null;
    }
  }
  drawHighLowLine.call(stx);
};

CIQ.ChartEngine.prototype.append('draw', drawHighLowLine);

/**
 *
 * @param {HTMLElement} node
 * @param {HTMLElement} parentNode
 */
CIQ.UI.Layout.prototype.getStripeBackground = function (node, parentNode) {
  const { stx: base } = this.context;

  const listener = ({ value }) => {
    if(value) {
      node.classList.add('ciq-active');
    } else {
      node.classList.remove('ciq-active');
    }
  };

  listener({value: base.layout.stripedBackground });
  this.addObserver({ base, path: 'layout.stripedBackground', listener });
};

/**
 *
 * @param {{node: HTMLElement, e: Event, params: { parent: HTMLElement }}} param0
 */
CIQ.UI.Layout.prototype.setStripeBackground = function ({node, e, params: { parent }}) {
  const { stx: base } = this.context;
  base.layout.stripedBackground = !base.layout.stripedBackground;
  base.draw();
};

/**
 *
 * @param {HTMLElement} node
 */
CIQ.UI.Layout.prototype.chartTypeCus = function (node) {
	const { stx: base, config: { menuChartStyle } } = this.context;
  // ciq-no-icon-text, ciq-menu-icon, ciq-menu-text
  const defaultText = node.getAttribute('ciq-no-icon-text') ?? '';
  const textEl = node.querySelector('[ciq-menu-text]') ?? node;
  const iconEl = node.querySelector('[ciq-menu-icon]');
  const getMenuItemActive = (chartType) => {
    const menuActive = node.closest('cq-menu')?.querySelector(`[stxsetget="Layout.ChartType('${chartType}')"]`);

    const icon = menuActive?.querySelector('[ciq-menu-icon]')?.classList.toString();
    const text = menuActive?.querySelector('[ciq-menu-text]')?.textContent;

    return {icon, text};
  };

  if(iconEl) {
    node.classList.add('ciq-menu-icon');
  }

  textEl.innerHTML = defaultText;

	const listener = ({ obj: { chartType } }) => {
    // node.innerHTML = chartType;
    const { icon, text } = getMenuItemActive(chartType);
    iconEl?.setAttribute('class', icon ?? '');
    textEl.innerHTML = text ?? '';
	};
	this.addObserver({ base, path: 'layout.chartType', listener });
};

CIQ.ChartEngine.prototype.append('drawXAxis', function (chart, sortedAxisRepresentation) {
  if(!this.layout.stripedBackground) return;
	// Only do alternating shading if the x-axis at the bottom.
	// Otherwise more code is needed to shade the panels under the axis without shading over the axis itself.
	if (!this.xAxisAsFooter) return;
	var panel = chart.panel;
	if (panel.name != 'chart') return; // Skip study panels

  const axisRepresentation = [...sortedAxisRepresentation];

  axisRepresentation.sort((a,b) => {
    return a.left - b.left;
  });

  const backgroundColor = this.canvasStyle('stx_strip_background')?.backgroundColor;

  if(!backgroundColor) return;
	// set your alternate color. Add transparency so the grid lines are visible
	// this.chart.context.fillStyle = 'rgba(249, 249, 255, 1)';
	this.chart.context.fillStyle = backgroundColor;
	var ctx = /** @type{ CanvasRenderingContext2D } */(this.chart.context);
	var obj;

	// find the bottom of the last y-axis on the chart.
	// we need to know the y-axis because otherwise the bottom of the chart will be past the x-axis and we don't want that.

	// subtract 1 because whichPanel is designed for displaying the x-axis also
	var wPanel = this.whichPanel(this.chart.canvasHeight - 1);
	if (!wPanel) return; // happens if window height increases during resize
	var yAxis = wPanel.yAxis;
	var bottom = yAxis.bottom;
	var top = 0;

	var prevRight = -1;
	var nextBoundaryLeft = Number.MAX_VALUE;

	var edges = [];
	var edgesInterator = 0;

	for (var nb = 0; nb < axisRepresentation.length; nb++) {
		if (axisRepresentation[nb].grid == 'boundary') {
			nextBoundaryLeft = axisRepresentation[nb].left;
			break;
		}
	}

	for (var i = 0; i < axisRepresentation.length; i++) {
		obj = axisRepresentation[i];
		// Check for overlap
		if (i == nb) {
			for (nb++; nb < axisRepresentation.length; nb++) {
				if (axisRepresentation[nb].grid == 'boundary') {
					nextBoundaryLeft = axisRepresentation[nb].left;
					break;
				}
			}
			if (nb >= axisRepresentation.length) {
				// no more boundaries
				nb = -1;
				nextBoundaryLeft = Number.MAX_VALUE;
			}
			if (prevRight > -1) {
				if (obj.left < prevRight) continue;
			}
		} else {
			if (prevRight > -1) {
				if (obj.left < prevRight) continue;
			}
			if (obj.right > nextBoundaryLeft) continue;
		}
		prevRight = obj.right;
		if (Math.floor(obj.unpaddedRight) <= this.chart.right) {
			// you may add more code here to have the shading follow the tick instead of
			// always using the first grid line as the staring point which can cause some
			// flickering and unnatural shifts of the alternating colors.
			if (!edges[edgesInterator]) edges[edgesInterator] = { left: obj.hz };
			else {
				edges[edgesInterator].right = obj.hz - edges[edgesInterator].left;
				edgesInterator++;
			}
		}
	}

	if (edges[edgesInterator] && edges[edgesInterator].left && !edges[edgesInterator].right) {
		edges[edgesInterator].right = this.chart.right - edges[edgesInterator].left;
		edgesInterator++;
	}

  for (var j = 0; j < edgesInterator; j++) {
    if (edges[j].left && edges[j].right) {
      if (this.chart.left > edges[j].left) {
        const diff = this.chart.left - edges[j].left;
        const width = edges[j].right - diff;
        if (width <= 0) continue;
        ctx.fillRect(this.chart.left, top, width, bottom);
      } else {
        ctx.fillRect(edges[j].left, top, edges[j].right, bottom);
      }
    }
  }
});

CIQ.ChartEngine.prototype.append('createXAxis', function() {
  var wPanel = this.whichPanel(this.chart.canvasHeight - 1);
	if (!wPanel) return; // happens if window height increases during resize
	var yAxis = wPanel.yAxis;
	var bottom = yAxis.bottom;
  var ctx = /** @type{ CanvasRenderingContext2D } */(this.chart.context);
  ctx.fillStyle = this.canvasStyle('stx_xaxis')['borderColor'];
  ctx.fillRect(0, bottom, this.chart.canvasWidth, 1);
});

CIQ.ChartEngine.prototype.append('undisplayCrosshairs', function () {
  const sphareCanvas = this.chart?.sphareCanvas;

  if(sphareCanvas) {
    sphareCanvas.style.display = 'none';
  }
});
CIQ.ChartEngine.prototype.append('positionCrosshairsAtPointer', function () {
  const chartEngine = this;
  var chart = chartEngine.chart;
  const chartTypeAllow = ['line', 'mountain'];
  //only draw sphere icon in case: chart type is line or moutain and dynamic callout is not selected
  const sphareCanvas = chart.sphareCanvas;
  if(!sphareCanvas) return;
  CIQ.clearCanvas(sphareCanvas, chartEngine);
  sphareCanvas.style.display = 'none';
  if (chartEngine.overYAxis) return;
  if(chartEngine.activeDrawing) return;
  if(chartEngine.layout.headsUp.dynamic) return;
  if(!chartTypeAllow.includes(chartEngine.layout.chartType)) return;

  var tick = chartEngine.tickFromPixel(chartEngine.cx);

  let isPercentView = chartEngine.layout.chartScale === 'percent';
  var quote = chart.dataSet[tick];
  if (!quote || (isPercentView && !quote.transform)) return;

  const quoteClose = isPercentView ? quote.transform.Close : quote.Close;
  var x = chartEngine.pixelFromTick(tick);

  if (quoteClose >= chartEngine.chart.yAxis.low) {
    //avoid state does not updated
    var y = chartEngine.pixelFromPrice(quote.Close);

    drawSphere(sphareCanvas.context, x, y, chartEngine.chart.lineStyle.color);
  }

  Object.entries(chartEngine.chart.series).forEach(([serieName, serieConfig]) => {
    let serieInfo = isPercentView ? quote.transform[serieName] : quote[serieName];
    if (serieInfo) {
      if (serieInfo.Close < chartEngine.chart.yAxis.low) return;

      let y1 = isPercentView
        ? chartEngine.pixelFromTransformedValue(serieInfo.Close)
        : chartEngine.pixelFromPrice(serieInfo.Close);
      const color = serieConfig.parameters.color;

      drawSphere(sphareCanvas.context, x, y1, color);
    }
  });

  sphareCanvas.style.display = 'block';
});


const drawSphere = (ctx, x, y, color) => {
  ctx.beginPath();
  ctx.lineWidth = 1;
  ctx.arc(x, y, 4, 0, 2 * Math.PI, false);
  ctx.strokeStyle = color;
  ctx.fillStyle = color;
  ctx.fill();
  ctx.stroke();
  ctx.closePath();
};

CIQ.UI.Layout.prototype.setInterval = function (
  obj,
  periodicity,
  interval,
  timeUnit
) {
  var self = this;
  const stx = this.context.stx;
  if (self.context.loader) self.context.loader.show();
  self.context.stx.setPeriodicity(
      { period: periodicity, interval: interval, timeUnit: timeUnit },
      function () {
          if (self.context.loader) self.context.loader.hide();
          stx.home();
          stx.fillScreen();
      }
  );
};

/**
 *
 * @param {import("chartiq/js/componentUI").CIQ.ChartEngine} stx
 * @param {{selector: HTMLElement}} param1
 */
CIQ.UI.Layout.prototype.showPeriodicity = function (stx, params) {
  var prefix = 'Interval';
	var text = '';
	var { period, interval, timeUnit } = stx.getPeriodicity();
	period *= interval;
	text = period;
	if (timeUnit == 'day') {
		text += 'D';
	} else if (timeUnit == 'week') {
		text += 'W';
	} else if (timeUnit == 'month') {
		text += 'M';
	} else if (timeUnit == 'second') {
		text += 's';
	} else if (timeUnit == 'millisecond') {
		text += 'ms';
	} else if (period >= 60 && period % 15 === 0) {
		text = period / 60 + 'H';
	} else if (timeUnit == 'tick') {
		text = (period > 1 ? period : '') + 'T';
	} else {
		text += period > 1 ? 'Mins' : 'Min';
	}

	params.selector.innerHTML = '';
	params.selector.appendChild(CIQ.translatableTextNode(stx, prefix));
	params.selector.appendChild(document.createTextNode(' '));
	params.selector.appendChild(CIQ.translatableTextNode(stx, text));
};
