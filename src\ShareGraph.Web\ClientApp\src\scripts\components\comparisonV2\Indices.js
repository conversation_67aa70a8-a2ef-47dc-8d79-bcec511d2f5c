import { useContext } from 'react';
import { useDispatch, useStore } from 'react-redux';

import { AppContext } from '../../AppContext';
import PeerIndicesItem from './PeerIndicesItem';
import useStocksRealtime from '../../real-time/useStockRealtime';
import {updateStock} from '../../actions';
import isSameDayWithLastTick from '../../real-time/isSameDayWithLastTick';
import useOrderedInstruments from '../../customHooks/useOrderedInstruments';
import useIndices from '../../customHooks/useIndices';
import { getCustomPhraseIndices } from '../../helper';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';
import { fetchIndices } from '../../actions/indicesActions';

export const Indices = () => {
  const [{selectedIndicesIds, data, onIndicesSelectedChange, indicesContainerRef}] = useIndices();
  const settings = useContext(AppContext);
  const store = useStore();
  const tickerAnimation = settings.indices.animation || 'fade';
  const dispatch = useDispatch();

  useChangeQuoteCurrency(() => dispatch(fetchIndices()));

  const [{ orderedData }] = useOrderedInstruments({
    data,
    settings: settings.indices.indices
  });

  useStocksRealtime(function(data) {
    if(!isSameDayWithLastTick({stockId: data.id, date: data.date, store })) return;
    dispatch(updateStock(data.id, { close: data.price, date: data.date }));
  }, settings.indices.indices.map(item => item.id));

  return (
    <div
      ref={indicesContainerRef}
      className={`comparison-v2__inner  comparison-v2__animation--${tickerAnimation.toLowerCase()}`}
    >
      {orderedData.map((item, index) => {
        const currentIns = settings.indices.indices.find(x => x.id === item.instrumentId);
        const settingColorIns = currentIns.color;

        return (
          <PeerIndicesItem
            isSelected={selectedIndicesIds.includes(item.instrumentId)}
            onItemClick={onIndicesSelectedChange}
            data={getCustomPhraseIndices(item)}
            settingColor={settingColorIns}
            key={index}
          />
        );
      })}
    </div>
  );
};
