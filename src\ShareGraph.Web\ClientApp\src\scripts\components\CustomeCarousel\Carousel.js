import React, { forwardRef, useLayoutEffect, useRef, useState } from 'react';
import { useSwipeable } from 'react-swipeable';
import classNames from 'classnames';

import { Wrapper, CarouselContainer, CarouselSlot, SlideButton, PREV, NEXT } from './CarouselComponents';
import useResize from '../../customHooks/useResize';

const Carousel = forwardRef((props, ref) => {
  const getOrder = ({ index, pos, numItems }) => {
    return index - pos < 0 ? numItems - Math.abs(index - pos) : index - pos;
  };
  const numItems = React.Children.count(props.children);
  //const initialState = { pos: numItems - 1, sliding: false, dir: NEXT };
  const initialState = { pos: 0, sliding: false, dir: NEXT };
  const [state, dispatch] = React.useReducer(reducer, initialState);
  const [slidesPerView, setSlidesPerView] = useState();
  const [isSlider, setIsSlider] = useState(numItems > 1);
  const [carouselItemWidth, setCarouselItemWidth] = useState();
  const [actionBtnWidth, setActionBtnWidth] = useState(0);
  const { width } = useResize();

  useLayoutEffect(() => {
    const carouselStyles = getComputedStyle(ref.current);
    const carouselItemW = Number(carouselStyles.getPropertyValue('--carouselItemWidth').replace('px', ''));
    setCarouselItemWidth(carouselItemW);
    const carouselItemSpaceW = Number(carouselStyles.getPropertyValue('--carouselItemSpace').replace('px', ''));
    const carouselWidth = ref.current.offsetWidth;
    const slidesPerV = Math.floor((carouselWidth + carouselItemSpaceW) / (carouselItemSpaceW + carouselItemW));
    setSlidesPerView(slidesPerV);
    setIsSlider(numItems > 1);
    setActionBtnWidth(slidesPerV * carouselItemW + carouselItemSpaceW * (slidesPerV - 1));
  }, [state, width, numItems]);

  const slide = dir => {
    if (dir === PREV && state.pos === 0) {
      return;
    }
    if (dir === NEXT && state.pos == numItems - 1) {
      return;
    }
    dispatch({ type: dir, numItems });
    setTimeout(() => {
      dispatch({ type: 'stopSliding' });
    }, 50);
  };
  const handlers = useSwipeable({
    onSwipedLeft: () => slide(NEXT),
    onSwipedRight: () => slide(PREV),
    preventDefaultTouchmoveEvent: true,
    trackMouse: true
  });
  return (
    <div className="carousel" {...handlers} ref={ref}>
      <Wrapper>
        <CarouselContainer
          dir={state.dir}
          sliding={state.sliding}
          slidesPerView={slidesPerView}
          isSlider={isSlider}
          carouselItemWidth={carouselItemWidth}
        >
          {React.Children.map(props.children, (child, index) => (
            <CarouselSlot
              key={index}
              slidesPerView={slidesPerView}
              carouselItemWidth={carouselItemWidth}
              order={getOrder({ index: index, pos: state.pos, numItems })}
              //order={index}
            >
              {child}
            </CarouselSlot>
          ))}
        </CarouselContainer>
      </Wrapper>
      {isSlider && (
        <div className="actions-wrapper" style={{ width: actionBtnWidth }}>
          <SlideButton
            onClick={() => slide(PREV)}
            className={classNames('carousel__btn--prev', { disable: state.pos == 0 })}
          >
            <i className="fs fs-caret-left"></i>
          </SlideButton>
          <SlideButton
            onClick={() => slide(NEXT)}
            className={classNames('carousel__btn--next', {
              disable: state.pos == numItems - 1
            })}
          >
            <i className="fs fs-caret-right"></i>
          </SlideButton>
        </div>
      )}
    </div>
  );
});

function reducer(state, { type, numItems, ...rest }) {
  switch (type) {
    case 'reset':
      return { numItems, ...rest };
    case PREV:
      return {
        ...state,
        dir: PREV,
        sliding: true,
        pos: state.pos - 1
      };
    case NEXT:
      return {
        ...state,
        dir: NEXT,
        sliding: true,
        pos: state.pos + 1
      };
    case 'stopSliding':
      return { ...state, sliding: false };
    default:
      return state;
  }
}

export default Carousel;
