﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WatchListAPI.DTOs;
using WatchListAPI.Services;

namespace WatchListAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class WatchListController : ControllerBase
    {
        private readonly IWatchListService _watchListService;
        public WatchListController(IWatchListService watchListService)
        {
            _watchListService = watchListService;
        }

        [HttpGet("find/{id}")]
        public async Task<IActionResult> GetWatchListAsync(Guid id)
        {
            var response = await _watchListService.GetWatchListAsync(id);
            return Ok(response);
        }

        [HttpGet("find-all")]
        public async Task<IActionResult> GetAllWatchListAsync([FromQuery] string? keyword)
        {
            var response = await _watchListService.GetAllWatchListAsync(keyword);
            return Ok(response);
        }

        [HttpPost("add")]
        public async Task<IActionResult> AddWatchListAsync(AddWatchListDto input)
        {
            var response = await _watchListService.AddWatchListAsync(input);
            return Ok(response);
        }

        [HttpPut("update")]
        public async Task<IActionResult> UpdateWatchListAsync(UpdateWatchListDto input)
        {
            var response = await _watchListService.UpdateWatchListAsync(input);
            return Ok(response);
        }

        [HttpDelete("delete/{id}")]
        public async Task<IActionResult> DeleteWatchListAsync(Guid id)
        {
            var response = await _watchListService.DeleteWatchListAsync(id);
            return Ok(response);
        }

        [HttpPost("add-detail")]
        public async Task<IActionResult> AddWatchListDetailAsync(AddWatchListDetailDto input)
        {
            await _watchListService.AddWatchListDetailAsync(input);
            return Ok();
        }

        [HttpDelete("remove-detail")]
        public async Task<IActionResult> DeleteWatchListDetailAsync([FromQuery] DeleteWatchListDetailDto input)
        {
            await _watchListService.DeleteWatchListDetailAsync(input);
            return Ok();
        }
    }
}
