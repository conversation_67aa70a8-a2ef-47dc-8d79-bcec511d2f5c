import produce from 'immer';

import {
  <PERSON><PERSON><PERSON>_PEERS_BEGIN,
  FETCH_PEERS_SUCCESS,
  FETCH_PEERS_FAILURE,
  REFRESH_PEERS_BEGIN,
  REFRESH_PEERS_SUCCESS,
  REFRESH_PEERS_FAILURE,
  PEERS_SELECTED_CHANGE_SUCCESS,
  PEERS_SELECTED_CHANGE,
  PEERS_DESELECTED_CHANGE,
  UPDATE_PEERS_OFFICIAL_CLOSE
} from '../actions/peerActions';
import {calcPriceChange} from '../utils';
import {UPDATE_STOCK_DATA} from '../actions';
import dayjs from '../utils/dayjs';
import {getLoadStrategy} from '../helper';

const initialState = {
  instruments: [],
  updateStrategy: /** @type {import('../../../types/configuration-app').IUpdateStrategy} */({}),
  selectedPeerIds: [],
  loading: true,
  refreshing: false,
  fetchError: null,
  refreshError: null
};

export default function createPeerReducer(instrumentIds = []) {  
  initialState.instruments = instrumentIds.map((i) => {
    return {
      instrumentId: i
    };
  });

  initialState.updateStrategy = instrumentIds.reduce((acc, i) => {
    acc[i] = 'fetch';
    return acc;
  }, {});
  
  return function peerReducer(state = initialState, action) {
    switch (action.type) {
      case FETCH_PEERS_BEGIN:
        return produce(state, draft => {
          draft.loading = true;
          draft.fetchError = null;
        });
      case FETCH_PEERS_SUCCESS:
        return produce(state, draft => {
          draft.loading = false;
          const instruments = action.payload.instruments;
          draft.instruments = instruments;
          instruments.forEach(item => {
            draft.updateStrategy[item.id] = getLoadStrategy(item);
          });
        });
      case FETCH_PEERS_FAILURE:
        return produce(state, draft => {
          draft.loading = false;
          draft.fetchError = action.payload.error;
        });
      case PEERS_SELECTED_CHANGE_SUCCESS:
        return produce(state, draft => { 
          if(Array.isArray(action.payload.instrumentId)) {
            draft.selectedPeerIds = [...draft.selectedPeerIds, ...action.payload.instrumentId];
            return;
          }
          if(draft.selectedPeerIds.includes(action.payload.instrumentId)){
            draft.selectedPeerIds.splice(draft.selectedPeerIds.indexOf(action.payload.instrumentId), 1);
          }else{
            draft.selectedPeerIds = [...draft.selectedPeerIds, action.payload.instrumentId];
          }
        });
      case REFRESH_PEERS_BEGIN:
        return produce(state, draft => {
          draft.refreshing = true;
          draft.fetchError = null;
        });
      case UPDATE_PEERS_OFFICIAL_CLOSE:
        return produce(state, draft => {
          const { instrumentId, officialClose, officialCloseDate } = action.payload;
          draft.instruments.forEach(instrument => {
            if(instrument.instrumentId !== instrumentId) return;
            instrument.officialClose = officialClose;
            instrument.officialCloseDate = officialCloseDate;

            draft.updateStrategy[instrumentId] = getLoadStrategy(instrument);
          });
        });
      case REFRESH_PEERS_SUCCESS:
        return produce(state, draft => {
          draft.refreshing = false;
          for (const intrument of action.payload.instruments) {
            draft.updateStrategy[intrument.id] = getLoadStrategy(intrument);
            const index = draft.instruments.findIndex(i => i.id === intrument.id);
            if (index !== -1) {
              draft.instruments[index] = intrument;
            } else {
              draft.instruments.push(intrument);
            }
          }
        });
      case REFRESH_PEERS_FAILURE:
        return produce(state, draft => {
          draft.refreshing = false;
          draft.refreshError = action.payload.error;
        });
      case PEERS_SELECTED_CHANGE:
        return produce(state, draft => {
          draft.selectedPeerIds = [...draft.selectedPeerIds, action.payload.instrumentId];
        });
      case PEERS_DESELECTED_CHANGE:
        return produce(state, draft => {
          draft.selectedPeerIds = draft.selectedPeerIds.filter(ins => ins !== action.payload.instrumentId);
        });
      case UPDATE_STOCK_DATA:
        return produce(state, draft => {
          const payload = /** @type { import('../actions').UpdateStockPayload } */(action.payload);
          const close = payload.data?.close;
          const date = payload.data.date;
          if(!date) throw new Error('date not found');
          if(!dayjs(draft.lastUpdatedDate).isSame(date, 'day')) {
            console.log('because same date then we dont update close price');
            return;
          }
          if(close === undefined) return;
          draft.instruments.forEach(item => {
            if(item.instrumentId !== action.payload.instrumentId) return;
            const newData = calcPriceChange(item.open, close);

            item.last = newData.last;
            item.change = newData.change;
            item.changePercentage = newData.changePercentage;
          });
        });
      default:
        return state;
    }
  };
}

export const peerSelector = state => state.peers.instruments.reduce((s, ins) => {
  s[ins.instrumentId] = ins;
  return s;
}, {});