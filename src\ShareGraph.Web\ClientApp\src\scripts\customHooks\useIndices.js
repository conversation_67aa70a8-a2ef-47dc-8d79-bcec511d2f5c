import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { fetchIndices, selectIndices } from '../actions/indicesActions';
import IndexInstrument from '../entities/IndexInstrument';

const useIndices = () => {
  const instruments = useSelector(state => state.indices.instruments);
  const selectedIndicesIds = useSelector(state => state.indices.selectedIndicesIds);
  const fetchLoadingIndices = useSelector(state => state.indices.loading);
  const dispatch = useDispatch();
  const indicesContainerRef = useRef();
  const refreshing = useSelector(state => state.indices.refreshing);

  useEffect(() => {
    if (!refreshing) return;

    if (indicesContainerRef.current) {
      const indicesContainer = indicesContainerRef.current;
      setTimeout(() => {
        indicesContainer.classList.add('loading');
        setTimeout(() => {
          indicesContainer.classList.remove('loading');
        }, 1000);
      }, 50);
    }
  }, [indicesContainerRef, refreshing]);

  useEffect(() => {
    if ((instruments || []).some(instrument => !!instrument.shareName)) return;
    dispatch(fetchIndices());
  }, []);

  // event when indices select
  const onIndicesSelectedChange = instrumentId => {
    if (instrumentId) {
      dispatch(selectIndices(instrumentId));
    }
  };

  return [
    {
      selectedIndex: new IndexInstrument(
        instruments.find(instrument => instrument.instrumentId === selectedIndicesIds)
      ),
      indices: instruments.map(instrument => new IndexInstrument(instrument)),
      fetchLoadingIndices,
      selectedIndicesIds,
      indicesContainerRef,
      onIndicesSelectedChange,
      data: instruments
    }
  ];
};

export default useIndices;
