import {gql} from './utils';

export const DIVIDEND_EVENT_QUERY  = gql(/* GraphQL */`query DividendEvents($id: Int!, $fromDate: DateTime!, $toDate: DateTime!) {
  instrumentById(id: $id) {
    dividends(where: {exDate: { gte: $fromDate, lt: $toDate}}, first: 99999999) {
      nodes {
        exDate
        grossDivAdj
        currency
      }
    }
  }
}
`);

export const DIVIDEND_EVENT_PAGING_QUERY  = gql(/* GraphQL */`query DividendEventsPaging(
  $id: Int!
  $fromDate: DateTime!
  $toDate: DateTime!
  $cursor: String
) {
  instrumentById(id: $id) {
    dividends(
      where: { exDate: { gte: $fromDate, lt: $toDate } }
      first: 100
      after: $cursor
    ) {
      edges {
        node {
          exDate
          grossDivAdj
          currency
        }
      }

      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}
`);

