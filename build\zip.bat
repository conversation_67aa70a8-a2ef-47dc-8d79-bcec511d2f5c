@ECHO OFF

:: usage:
:: build.bat <[options]>
::
:: options:
::  --sln <value> -> path to project or solution file. Default empty.
::  --build-client -> build client app along with server side. Default  `true`

SET ROOT=%CD%
ECHO Current working DIR: %ROOT%

:: build.bat --sln soltion_or_project_file

setlocal

:parse
    if "%~1"=="" GOTO endparse
    if "%~1"=="--output" ( set "_output=%~2" )
    if "%~1"=="--input" ( set "_input=%~2" )
    if "%~1"=="--unzip" ( set "_HasUnzip=true" )
    shift
    GOTO parse
:endparse

if "%_HasUnzip%"=="" ( set "_HasUnzip=false" )

if "%_input%"=="" (  echo Must provide input DIR/zip file for uncompressing )
if "%_input%"=="" ( exit /b 1)

if "%_output%"=="" if "%_HasUnzip%"=="false" ( echo Must provide output DIR for compressing )
if "%_output%"=="" if "%_HasUnzip%"=="false" ( exit /b 1)

SET "_input=%ROOT%\%_input%"

if not exist "%_input%" ( echo Path not found %_input% )
if not exist "%_input%" ( exit /b 1 )

if "%_HasUnzip%"=="true" ( goto uncompress )
if "%_HasUnzip%"=="false" ( goto compress )

:compress
SET "_output=%ROOT%\%_output%"
call "./build/7za.exe" a -y "%_output%" "%_input%"
goto end

:uncompress
call "./build/7za.exe" x -y -o"%_output%" "%_input%"

:end
endlocal
