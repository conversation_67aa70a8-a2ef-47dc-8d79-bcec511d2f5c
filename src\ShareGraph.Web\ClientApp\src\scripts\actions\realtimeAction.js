import { getCurrentCurrencyRateById } from '../helper';

export const STREAM_TRADES = 'STREAM_TRADES';
export const STREAM_SNAPSHOT = 'STREAM_SNAPSHOT';
/**
 * 
 * @typedef SnapshotData
 * @prop { number } id
 * @prop { number } bid
 * @prop { number } bidSize
 * @prop { number } ask
 * @prop { number } askSize
 * @prop { string } date
 * @prop { number } high
 * @prop { number } last
 * @prop { number } low
 * @prop { number } open
 * @prop { number } volume
 */

export function streamTradesAction({ id, volume, price, date}) {
  return (dispatch, getState) => {
    const state = getState();
    const currentCurrencyRate = getCurrentCurrencyRateById(id, state)?.value || 1;
    const convertPrice = currentCurrencyRate * price;

    
    return dispatch({
      type: STREAM_TRADES,
      payload: {
        id,
        volume,
        price: convertPrice,
        rawPrice: price,
        date
      }
    });
  };
}

/**
 * 
 * @param {Partial<SnapshotData>} data 
 * @returns 
 */
export function streamSnapshotAction(data) {
  return (dispatch, getState) => {
    const state = getState();
    const currentCurrencyRate = getCurrentCurrencyRateById(data.id, state)?.value || 1;
    if(data.bid) {
      data.rawBid = data.bid;
      data.bid = currentCurrencyRate * data.bid;
    }
    if(data.ask) {
      data.rawAsk = data.ask;
      data.ask = currentCurrencyRate * data.ask;
    }
    if(data.high) {
      data.rawHigh = data.high;
      data.high = currentCurrencyRate * data.high;
    }
    if(data.last) {
      data.rawLast = data.last;
      data.last = currentCurrencyRate * data.last;
    }
    if(data.low) {
      data.rawLow = data.low;
      data.low = currentCurrencyRate * data.low;
    }
    if(data.open) {
      data.rawOpen = data.open;
      data.open = currentCurrencyRate * data.open;
    }
    return dispatch({
      type: STREAM_SNAPSHOT,
      payload: data
    });
  };
 
}

