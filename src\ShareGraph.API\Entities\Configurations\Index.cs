using System;
using System.Collections.Generic;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class Index
  {
    public int Id { get; set; }

    public string Color { get; set; }

    public int Order { get; set; }

    public string TickerName { get; set; }

    public string Ticker { get; set; }

    public string CurrencyCode { get; set; }

    public bool IsRT { get; set; }

    public string? Symbol { get; set; }

    public int? DecimalDigits { get; set; }

    public int? PercentDigits { get; set; }
    
    public string MarketAbbreviation { get; set; }

    public bool? EnabledAdjustPrice { get; set; } = false;

    public string? ShareName { get; set; }

  }

  public class IndicesConfig
  {
    public bool Enabled { get; set; }
    public string Animation { get; set; }
    public IEnumerable<Index> Indices { get; set; }
  }
}
