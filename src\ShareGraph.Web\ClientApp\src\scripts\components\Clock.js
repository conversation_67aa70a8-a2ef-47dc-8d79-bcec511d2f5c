import React, { useState, useEffect, useContext } from 'react';
import { AppContext } from '../AppContext';
import { formatDateFn } from '../utils/dayjs/formatDate';
import i18n from '../services/i18n';

export default function Clock() {
  const [formattedTime, setFormattedTime] = useState('');
  const settings = useContext(AppContext);

  useEffect(() => {
    const timer = setInterval(() => {
      const formatted = formatDateFn(new Date(), settings.format.longDate);
      setFormattedTime(formatted);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <>
      <span className="clock-label">{i18n.translate('localTime')} </span>
      <span className="clock">{formattedTime}</span>
    </>
  );
}