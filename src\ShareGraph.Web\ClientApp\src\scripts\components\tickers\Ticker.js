import { useRef, useState } from 'react';
import { useEffect, useContext } from 'react';
import { useDispatch } from 'react-redux';
import classNames from 'classnames';

import { SWITCHER_TYPE, TEMPLATE_TICKER, TICKER_SWITCH_TYPE } from '../../common';
import { SingleTicker } from './SingleTicker';
import { MultipleTicker } from './MultipleTicker';

import Switcher from '../Switcher';
import { AppContext } from '../../AppContext';
import { getContainerTickerByType } from '../../helper';
import { Skeleton } from '../Skeleton';
import i18n from '../../services/i18n';
import { useLazyActiveInstrument } from '../../customHooks';
import useStocksRealtime from '../../real-time/useStockRealtime';
import {refreshTickers, updateStock} from '../../actions';
import useStockSnapshot from '../../real-time/useStockSnapshot';
import useAppSelector from '../../customHooks/useAppSelector';
import {safeInterval} from '../../utils';

export const Ticker = ({ tickerType, isPrint }) => {
  const settings = useContext(AppContext);
  const enabledInstruments = settings.instruments; //.filter(e => e.enabled);
  const {lazySelectedInstrument, setLazySelectedInstrument} = useLazyActiveInstrument();
  const setDefaultTickerFormat = format => {
    return (
      settings.ticker.enabledFormat.length > 0 && settings.ticker.enabledFormat[0].toUpperCase() === format
    );
  };
  const tickerFormatTypes = [
    {
      type: TICKER_SWITCH_TYPE.GRAPH_TYPE,
      icon: 'fs-graph',
      isDefaultSelected: setDefaultTickerFormat(TICKER_SWITCH_TYPE.GRAPH_TYPE),
      dataTooltip: i18n.translate('graphTooltipLabel')
    },
    {
      type: TICKER_SWITCH_TYPE.TABLE_TYPE,
      icon: 'fs-table',
      isDefaultSelected: setDefaultTickerFormat(TICKER_SWITCH_TYPE.TABLE_TYPE),
      dataTooltip: i18n.translate('tableTooltipLabel')
    }
  ];
  const [tickerFormat, setTickerFormat] = useState(tickerFormatTypes.find(t => t.isDefaultSelected).type);
  const tickerContainerRef = useRef();
  const REFRESH_INTERVAL = settings.tickerRefreshSeconds ? settings.tickerRefreshSeconds * 1000 : 10000;
  const isSingleTicker = tickerType.toLowerCase() === TEMPLATE_TICKER.SINGLE;
  const tickerData = useAppSelector(state => state.tickers.instruments);
  const updateStrategy = useAppSelector(state => state.tickers.updateStrategy);
  const fetchLoading = useAppSelector(state => state.tickers.loading);
  const dispatch = useDispatch();

  useEffect(() => {
    const cancelInterval = safeInterval(() => dispatch(refreshTickers()), REFRESH_INTERVAL);
    return function () {
      cancelInterval.cancel();
    };
  }, [dispatch]);

  useStocksRealtime(function(data) {
    if(updateStrategy[data.id] !== 'socket') return;
    dispatch(updateStock(data.id, { close: data.price, date: data.date}));
  }, settings.instruments.map(item => item.id));

  useStockSnapshot(function (data) {
    if(updateStrategy[data.id] !== 'socket') {
      if(!('open' in data)) return;
    }
    dispatch(updateStock(data.id, data));
  }, settings.instruments.map(item => item.id));
    
  const onTickerSelectedChange = instrumentId => {
    if (instrumentId) {
      setLazySelectedInstrument(instrumentId);
    }
  };

  const handleSwitchType = type => {
    setTickerFormat(type);
  };

  const getHeightTableTicker = () => {
    return tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE
      ? { height: `${(enabledInstruments.length + 1) * 55 + 57}px` }
      : {};
  };

  return (
    <>
      <div className="switcher__wrapper">
        {settings.ticker.enabledFormat.length > 1 && (
          <Switcher
            className="switcher__ticker"
            onClick={handleSwitchType}
            type={SWITCHER_TYPE.TICKER}
            tabs={tickerFormatTypes}
            tabActive={tickerFormat}
            isButton={true}
          ></Switcher>
        )}
      </div>
      <div className="tab-contents">
        <div
          className={classNames(
            `ticker ticker--${tickerType.toLowerCase()} ticker--${getContainerTickerByType(tickerFormat)}`
          )}
          ref={tickerContainerRef}
        >
          {fetchLoading && <Skeleton style={getHeightTableTicker()} />}

          {!fetchLoading && isSingleTicker && tickerData && <SingleTicker data={tickerData} tickerFormat={tickerFormat} />}

          {!fetchLoading && !isSingleTicker && (
            <MultipleTicker
              onTickerSelected={onTickerSelectedChange}
              data={tickerData}
              selectedInstrumentId={lazySelectedInstrument}
              isPrint={isPrint}
              tickerFormat={tickerFormat}
            />
          )}
        </div>
      </div>
    </>
  );
};
