{"ConnectionStrings": {"EurolandIDProfileConnection": "Server=**********;Database=EurolandIDProfiles;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Syslog", "Serilog.Sinks.File"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**********", "port": "514", "appName": "Tools.UserPreferences", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "Tools.UserPreferences"}}, "AllowedHosts": "*", "AllowedOrigins": ["https://localhost:5173", "http://localhost:5173", "https://************"], "Authentication": {"RequireHttpsMetadata": true, "Authority": "https://dev.vn.euroland.com/auth/realms/irservices", "ValidIssuer": "https://dev.vn.euroland.com/auth/realms/irservices", "ValidAudiences": ["account", "share-graph-pro"], "ClockSkewSeconds": 0}}