import { useEffect, useState } from 'react';
import { appSettings } from '../../appSettings';
import {getCompany} from '../services/commonService';

/**
 *
 * @returns {{companyName: string}}
 */
const useCompany = () => {
  const [company, setCompany] = useState({});

  useEffect(() => {
    const companyCode = appSettings.companyCode;
    getCompany(companyCode)
      .then(data => {
        const companyData = data?.data?.company;
        if (!companyData) {
          console.log('🚀 ~ Not found company:', companyCode);
          return;
        }
        setCompany(companyData);
      })
      .catch(err => console.log(err));
  }, []);

  return company;
};

export default useCompany;
