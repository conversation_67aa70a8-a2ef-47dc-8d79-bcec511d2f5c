
import ChartPrice from './ChartPrice/ChartPrice';
import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useState } from 'react';
import { fetchSharePriceDevelopmentByYears } from '../../actions/sharePriceDevelopmentByYearsAction';
import { convertChangePercentDecimal, convertNumberDecimal, convertNumberInString, dynamicSort, formatChangeNumber, getCustomShareNameByInstrumentId } from '../../helper';
import i18n from '../../services/i18n';
import { Skeleton } from '../Skeleton';
import { appSettings } from '../../../appSettings';
import { TableV2 } from '../commons/tableV2/Table';
import TickerName, {getTickerName} from '../TickerName';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';


export const SharePriceDevelopmentByYears = ({ sPDByYearSetting, format, showEarliestYearFirstSPByYear, refreshTime }) => {
  const fetchLoading = useSelector(state => state.performanceByYear.loading);
  const dispatch = useDispatch();
  const sPDByYearData = useSelector(state => state.performanceByYear.instruments);
  const [datasGraph, setDatasGraph] = useState([]);
  const [datasTable, setDatasTable] = useState([]);
  const [tableHeadings, setTableHeading] = useState([]);
  const [indicatorColumns, setIndicatorColumns] = useState([]);
  let currentYear = new Date().getFullYear();
  const _normalizeData = (item) => {
    let data = { ...item };
    item.changePercentage ? data.changePercentage = convertNumberDecimal(item.changePercentage) : 0;
    data = { ...data };
    return data;
  };
  const _formatDataGraph = (datas) => {
    if (datas.length === 0 || sPDByYearSetting.length === 0) return [];

    const years = datas[0].yearlyPerformances.map(x => x.year);
    const minYear = Math.min(...years) - 1;
    return datas.map(item => {
      let { shareName, instrumentId, marketAbbreviation } = item;
      const numberYearlyPerformance = item.yearlyPerformances.length;
      const data = item.yearlyPerformances.map((x, idx) => {
        let close = x.changePercentage ? x.changePercentage : 0;
        let { year } = x;
        let yearLabel = '';
        let day = year - minYear + 1;
        if(day < 10) {
          day = `0${day}`;
        }

        if (showEarliestYearFirstSPByYear) {
          const reverseItem = item.yearlyPerformances[ numberYearlyPerformance - 1 - idx];
          close = reverseItem.changePercentage;
           //current year check and added in Asterisk
          yearLabel = reverseItem.year == currentYear ? reverseItem.year + '*' : reverseItem.year.toString();
        }else {
          yearLabel = year == currentYear ? year + '*' : year.toString();
        }
        return { DT: new Date(`2020-01-${day}`), Close: close, year: year, shareName,instrumentId: item.instrumentId, yearLabel: yearLabel, marketAbbreviation };
      });
      const itemSetting = sPDByYearSetting.find(x => x.id === instrumentId);
      const colorSetting = itemSetting.color || 'gray';
      const order = sPDByYearSetting.find(x => x.id === instrumentId).order;
      return { ...item, shareName, order: order, color: colorSetting, symbol: shareName, data: data, marketAbbreviation };
    });
  };

  const _formatDataTable = (datas) => {
    let isCurrentYear = false;
    const datasTable = datas.map(item => {
      let yearsItem = {};
      item.yearlyPerformances.forEach((y, idx) => {
        const { year, changePercentage } = y;
        yearsItem = { ...yearsItem, [year]: [{ value: changePercentage || 0, display: convertChangePercentDecimal(changePercentage || 0) + '%' }]};
      });
      const order = sPDByYearSetting.find(x=>x.id === item.instrumentId).order;
      const shareName = item.shareName;
      return {
        shareName: [{ value: shareName, display: <TickerName  marketAbbreviation={item.marketAbbreviation} shareName={item.shareName} instrumentId={item.instrumentId} />}],
        order: order,
        ...yearsItem
      };
    });

    let tableHeaders = [{ columnDisplay: i18n.translate('instrumentLabel') || 'Instrument', fieldName: 'shareName' }];

    let yearHeaders = Object.keys(datasTable[0]).filter(x => x !== 'shareName' && x !=='order').map(x => {
      if (x !== 'shareName') {
        const keyTranslation = x + 'Label';
        var y;
            if(x == currentYear){
              y = x+'*';
              isCurrentYear = true;
            } else {
              y = x;
            }
        return {
          columnDisplay: i18n.translate(keyTranslation) || convertNumberInString(y), fieldName: +x, isCurrentYear: isCurrentYear
        };
      }
    });

    const yearHeadersSorting =  showEarliestYearFirstSPByYear ? yearHeaders.sort(dynamicSort('fieldName')).reverse() : yearHeaders.sort(dynamicSort('fieldName'));
    tableHeaders = [...tableHeaders, ...yearHeadersSorting];
    setTableHeading(tableHeaders);
    // set indicator columns
    setIndicatorColumns(yearHeaders.map(x => x.fieldName));

    return datasTable;
  };

  useEffect(() => {
    function fetchData() {
      dispatch(fetchSharePriceDevelopmentByYears());
    }
    fetchData();
    const timer = setInterval(() => {
      fetchData();
    }, refreshTime * 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [dispatch, refreshTime]);

  useChangeQuoteCurrency(() => {
    dispatch(fetchSharePriceDevelopmentByYears());
  });

  useEffect(() => {
    if (!fetchLoading) {
      const dataTable = _formatDataTable(sPDByYearData);
      // const dataSorting = showEarliestYearFirstSPByYear ? dataTable.sort(dynamicSort('order')) : dataTable.sort(dynamicSort('order')).reverse();
      const dataSorting = dataTable.sort(dynamicSort('order'));
      format === 'GRAPH' ?
        setDatasGraph(_formatDataGraph(sPDByYearData)) : setDatasTable(dataSorting);
    }
  }, [sPDByYearData, format]);
//Current year checking and footer text set
const tableFooter = tableHeadings.filter((x)=>{
                    if(x.isCurrentYear == true){
                      return x;
                    }
                  });
//footer text assign
var tableFooterText;
  if(tableFooter[0] !== undefined && tableFooter[0].isCurrentYear == true){
    tableFooterText =  i18n.translate('sPDYeartableFooter');
  } else {
    tableFooterText =  '';
  }

return (
    <>
      { fetchLoading && <Skeleton style={{height: '500px'}} ></Skeleton>}
      {!fetchLoading && format === 'GRAPH' && datasGraph.length !== 0 &&
        <div id='spdby-graph' role='tabpanel' aria-labelledby='tab-spdby-graph' tabIndex={-1} hidden={format !== 'GRAPH'} className='spdby-graph'>
          <ChartPrice datas={datasGraph} showEarliestYearFirstSPByYear={showEarliestYearFirstSPByYear} footer={tableFooterText} />
        </div>
      }
      {!fetchLoading && format === 'TABLE' &&
        <div id='spdby-table' role='tabpanel' aria-labelledby='tab-spdby-table' tabIndex={-1} hidden={format !== 'TABLE'} className='table-responsive'>
          <TableV2
            datas={datasTable}
            headings={tableHeadings}
            indicatorColumns={indicatorColumns}
            footer={tableFooterText}
            caption={i18n.translate('sharePriceDevelopmentByYearsCaption')}
            />
          </div>
        }
      {/* {!fetchLoading && format === 'TABLE' && <div className='table-responsive'><Table className='performance--table  performanceByYear' datas={datasTable} headings={tableHeadings} indicatorColumns={indicatorColumns} footer={tableFooterText} /></div>} */}
    </>
  );
};
