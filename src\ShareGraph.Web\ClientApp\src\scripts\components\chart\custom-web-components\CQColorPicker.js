import { CIQ } from '../chartiq-import';
import { cqdElementStyle, cqdialogsTop } from './mutation_variable';
var timeoutEffect;
const CQColorPicker = CIQ.UI.components('cq-color-picker')[0].classDefinition;

CQColorPicker.prototype.display = function (activator) {
  var node = CIQ.UI.$(activator.node)[0];
  var positionOfNode = node.getBoundingClientRect();
  this.node.css({ top: '0px', left: '0px' });
  var positionOfColorPicker = this.parentNode.getBoundingClientRect();
  var oneSwatchDims = CIQ.elementDimensions(node, { border: true });
  var x = positionOfNode.left - positionOfColorPicker.left + oneSwatchDims.width + 10;
  var y = positionOfNode.top - positionOfColorPicker.top + 15;

  this.cqOverrides.children(':not(template)').remove();
  var context = activator.context || this.context || CIQ.UI.getMyContext(this);
  this.uiManager = context.uiManager;

  var closure = function (self, override) {
    return function () {
      self.pickColor(override);
    };
  };

  var overrideHeight = 0;
  if (activator.overrides && this.template.length) {
    var n;
    for (var i = 0; i < activator.overrides.length; i++) {
      var override = activator.overrides[i];
      n = CIQ.UI.makeFromTemplate(this.template, true);
      if (context.stx) override = context.stx.translateIf(override);
      n.text(override);
      CIQ.UI.stxtap(n[0], closure(this, override));
    }
    overrideHeight = CIQ.elementDimensions(n[0]).height;
  }

  // ensure color picker doesn't go off right edge of screen
  var dims = CIQ.elementDimensions(this, { border: true });
  var doc = this.ownerDocument || document;
  var docWidth = CIQ.guaranteedSize(doc).width;
  var w = dims.width || oneSwatchDims.width * this.colors.children()[0].children.length;
  if (x + w > docWidth) x = docWidth - w - 20; // 20 for a little whitespace and padding

  // or bottom of screen
  var docHeight = CIQ.guaranteedSize(doc).height;
  var h = dims.height || oneSwatchDims.height * this.colors.children().length + overrideHeight;
  //custom center the color picker
  var ciqheight = document.querySelector('.graph');

  // if (y + h > docHeight) y = docHeight - h - 30;

  x = cqdElementStyle.value.left;
  cqdialogsTop.value = cqdElementStyle.value.height + cqdElementStyle.value.top - 166;

  if (y > cqdialogsTop.value) y = cqdialogsTop.value;

  this.setAttribute('class', 'display-effect');
  this.node.css({ left: x + 'px', top: y + 'px' });
  timeoutEffect = setTimeout(function () {
    var cqColorPic = document.querySelector('cq-color-picker');
    cqColorPic.removeAttribute('class');
  }, 750);

  if (!this.classList.contains('stxMenuActive')) {
    this.open({ context: context }); // Manually activate the color picker
  } else {
    if (context.e) context.e.stopPropagation(); // Otherwise the color picker is closed when you swap back and forth between fill and line swatches on the toolbar
  }
  if (this.keyboardNavigation) this.keyboardNavigation.highlightAlign();
};
clearTimeout(timeoutEffect);
