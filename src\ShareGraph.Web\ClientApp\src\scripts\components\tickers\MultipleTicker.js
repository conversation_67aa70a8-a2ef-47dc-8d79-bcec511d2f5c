import { useRef, useContext, useLayoutEffect, useEffect, useState, useCallback } from 'react';
import { TinySliderNav, SlideItem } from '@euroland/react';

import { AppContext } from '../../AppContext';
import {
  formatDateTime,
  calculateNumberSliderPerView,
  dynamicSort
} from '../../helper';
import { TICKER_SWITCH_TYPE } from '../../common';
import { usePrevious } from '../../customHooks';
import TableTicker from './TableTicker';
import MultipleTickerItem from './MultipleTicker/MultipleTickerItem';
import useResize from '../../customHooks/useResize';
import TimeStamp from '../TimeStamp';
import { classNames } from '@euroland/libs';
import {useDelay} from '../../customHooks/useDelayUpdate';
import {TIME_DELAY_SHOW_TICKER} from '../../constant/common';
import useWatchChange from './useWatchChange';

export const MultipleTicker = ({ data: _data, onTickerSelected = () => {}, tickerFormat, isPrint = false, selectedInstrumentId }) => {
  const data = useDelay(_data, TIME_DELAY_SHOW_TICKER);
  
  const prevData = usePrevious(data);
  const settings = useContext(AppContext);
  const [slidesPerView, setSlidesPerView] = useState(settings.ticker.slidesPerView || 4);
  const { width } = useResize();
  const tinySliderRef = useRef();

  const containerRef = useRef(null);

  const tickerSelected = data.find(x => x.instrumentId === selectedInstrumentId);
 
  useLayoutEffect(() => {
    const carouselStyles = getComputedStyle(document.querySelector('.ticker--multiple'));
    const itemNumber = Number(carouselStyles.getPropertyValue('--slidesPerView'));
    setSlidesPerView(itemNumber);
  }, [tickerFormat, width]);

  const tickerAnimation =
    tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE
      ? settings.ticker.graphAnimation || 'fade'
      : settings.ticker.tableAnimation || 'fade';

  const [dataOrdered, setDataOrdered] = useState();

  let activeOrder = dataOrdered?.findIndex(instrument => instrument.instrumentId === selectedInstrumentId);
  activeOrder = activeOrder === undefined || activeOrder === -1 ? 0 : activeOrder;

  useLayoutEffect(() => {
    setDataOrdered(_sortDataTicker);
  }, [data, _sortDataTicker]);

  // order data by order setting in xml
  const _sortDataTicker = useCallback(() => {
    return data
      .map(x => {
        return {
          ...x,
          order: settings.instruments.find(y => y.id === x.instrumentId).order
        };
      })
      .sort(dynamicSort('order'));
  }, [data, settings.instruments]);

  // useLayoutEffect(() => {
  //   //add class to specific style for ticker time
  //   if (
  //     data.length > calculateNumberSliderPerView(slidesPerView) &&
  //     tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE
  //   ) {
  //     containerRef.current.closest('.ticker').classList.add('ticker--slider');
  //   }
  // });
  return (
    <>
      {tickerFormat === TICKER_SWITCH_TYPE.GRAPH_TYPE && (
        <>
          <time className="ticker__time">{formatDateTime(tickerSelected.lastUpdatedDate)}</time>
          <div ref={containerRef} className={`ticker__inner ticker__inner--${tickerAnimation.toLowerCase()}`}>
            {/* {dataOrdered.length <= calculateNumberSliderPerView(slidesPerView) || isPrint ? ( */}
            { isPrint ? (
              <div className="ticker__inner--list">
                {(dataOrdered || []).map((item, index) => (
                  <MultipleTickerItem
                    prevData={prevData}
                    tickerFormat={tickerFormat}
                    isSelected={item.instrumentId === selectedInstrumentId}
                    onItemClick={onTickerSelected}
                    key={index}
                    data={item}
                  />
                ))}
              </div>
            ) : (
              <TinySliderNav ref={tinySliderRef} isRtl={window.appSettings.isRtl} settings={{gutter: 0}} activeOrder={activeOrder}>
                { (dataOrdered || []).map((item, index) => {
                  const active = item.instrumentId === selectedInstrumentId;
                  return (
                    <SlideItem
                      key={index}
                      className={classNames('tiny-slider__item', { active })}
                      onClick={() => onTickerSelected(item.instrumentId)}
                      sliderIndex={index}
                      role="option"
                      aria-selected={active}
                    >
                      <MultipleTickerItem
                        prevData={prevData}
                        tickerFormat={tickerFormat}
                        isSelected={active}
                        // onItemClick={onTickerSelected}
                        key={index}
                        data={item}
                      />
                    </SlideItem>
                  );
                })}
              </TinySliderNav>

            )}
          </div>
        </>
      )}
      {tickerFormat === TICKER_SWITCH_TYPE.TABLE_TYPE && (
        <>
          <TimeStamp tickerData={tickerSelected} />
          <TableTicker className="ticker-table" onItemClick={onTickerSelected} data={dataOrdered} tickerAnimation={tickerAnimation} />
        </>
      )}
    </>
  );
};
