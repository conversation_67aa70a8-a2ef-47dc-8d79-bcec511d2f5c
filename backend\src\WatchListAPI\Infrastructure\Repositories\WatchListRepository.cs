﻿using Microsoft.EntityFrameworkCore;
using WatchListAPI.DTOs;
using WatchListAPI.Entities;
using static Microsoft.Extensions.Logging.EventSource.LoggingEventSource;

namespace WatchListAPI.Infrastructure.Repositories
{
    public interface IWatchListRepository : IRepositoryBase<EurolandIDProfileDbContext, WatchList, Guid>
    {
        Task<WatchList?> Find(Guid id, string username);
        void AddWatchListDetail(AddWatchListDetailDto input);
        IQueryable<WatchList> GetAllWatchList(string username, string? keyword);
        Task<int> CountWatchListByUsername(string username);
    }

    public class WatchListRepository : RepositoryBase<EurolandIDProfileDbContext, WatchList, Guid>, IWatchListRepository
    {
        public WatchListRepository(EurolandIDProfileDbContext context) : base(context)
        {
        }

        public void AddWatchListDetail(AddWatchListDetailDto input)
        {
            var entites = input.InstrumentIds.Select(e => new WatchListDetail
            {
                Id = Guid.NewGuid(),
                WatchListId = input.WatchListId!.Value,
                InstrumentId = e,
            });
            _dbContext.Set<WatchListDetail>().AddRange(entites);
        }

        public async Task<int> CountWatchListByUsername(string username)
        {
            return await _dbContext.Set<WatchList>().CountAsync(e => e.Username == username);

        }

        public async Task<WatchList?> Find(Guid id, string username)
        {
            return await _dbContext.Set<WatchList>()
                .FirstOrDefaultAsync(e => e.Username == username && e.Id == id);
        }

        public IQueryable<WatchList> GetAllWatchList(string username, string? keyword)
        {
            return _dbContext.Set<WatchList>().AsNoTracking()
                .Where(e => e.Username == username
                    && (keyword == null || e.Name.ToLower().Contains(keyword.Trim().ToLower()))
                );
        }
    }
}
