{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-webapp", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/ShareGraph.API/ShareGraph.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"type": "docker-build", "label": "docker-build: release", "dependsOn": ["build"], "dockerBuild": {"tag": "sharegraph:latest", "dockerfile": "${workspaceFolder}/src/ShareGraph.Web/Dockerfile", "context": "${workspaceFolder}", "pull": true}, "netCore": {"appProject": "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj"}}, {"type": "docker-build", "label": "docker-build:sharegraph-web:debug", "dependsOn": ["build-webapp"], "dockerBuild": {"tag": "sharegraph:dev", "target": "base", "dockerfile": "${workspaceFolder}/src/ShareGraph.Web/Dockerfile", "context": "${workspaceFolder}", "pull": true}, "netCore": {"appProject": "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj", "configureSsl": true}}, {"type": "docker-build", "label": "docker-build:sharegraph-api:debug", "dependsOn": ["build-api"], "dockerBuild": {"tag": "sharegraph:dev", "target": "base", "dockerfile": "${workspaceFolder}/src/ShareGraph.API/Dockerfile", "context": "${workspaceFolder}", "pull": true}, "netCore": {"appProject": "${workspaceFolder}/src/ShareGraph.API/ShareGraph.API.csproj", "configureSsl": true}}, {"type": "docker-run", "label": "docker-run:sharegraph-web:debug", "dependsOn": ["docker-build:sharegraph-web:debug"], "dockerRun": {"env": {"ASPNETCORE_ENVIRONMENT": "<PERSON>er"}}, "netCore": {"appProject": "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj", "enableDebugging": true, "configureSsl": true}}, {"type": "docker-run", "label": "docker-run:sharegraph-api:debug", "dependsOn": ["docker-build:sharegraph-api:debug"], "dockerRun": {"env": {"ASPNETCORE_URLS": "http://+:5003", "ASPNETCORE_ENVIRONMENT": "<PERSON>er"}}, "netCore": {"appProject": "${workspaceFolder}/src/ShareGraph.API/ShareGraph.API.csproj", "enableDebugging": true, "configureSsl": true}}, {"type": "docker-run", "label": "docker-run: release", "dependsOn": ["docker-build: release"], "dockerRun": {}, "netCore": {"appProject": "${workspaceFolder}/src/ShareGraph.Web/ShareGraph.App.csproj"}}]}