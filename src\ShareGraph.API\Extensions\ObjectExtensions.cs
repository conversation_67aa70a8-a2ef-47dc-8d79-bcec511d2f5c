using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;

namespace Euroland.FlipIT.ShareGraph.API.Extensions
{
  public static class ObjectExtensions
  {
    public static T ToObject<T>(this IDictionary<string, string> source)
    where T : class, new()
    {
      if (source.Any())
      {
        var someObject = new T();
        var someObjectType = someObject.GetType();

        foreach (var item in source)
        {
          try
          {
            var propTypeInfo = someObjectType.GetProperty(item.Key, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
            if (propTypeInfo != null)
            {
              var propType = propTypeInfo.PropertyType;
              var typeConverter = TypeDescriptor.GetConverter(propType);

              var value = typeConverter.ConvertFromString(item.Value);

              someObjectType
                          .GetProperty(item.Key, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance)
                          .SetValue(someObject, value, null);
            }
          }
          catch (Exception)
          {
            // while setting the property value catch any issue
            // ignore the property and go ahead...
          }
        }

        return someObject;
      }
      return null;
    }

    public static IEnumerable<string> ToListItem(this string str)
    {
      return str.ToUpper().Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                .Distinct();
    }

    public static IEnumerable<int> ToIntListItem(this string str)
    {
      string[] parts = str.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
      List<int> intList = new List<int>();
      foreach (string part in parts)
      {
        if (int.TryParse(part, out int number))
        {
          intList.Add(number);
        }
      }
      return intList;

    }

    public static string ToDisplayFormat(this string str)
    {
      var allowsDisplayFormat = new List<string> { "exact", "thousands", "lakhs", "hundreds", "millions", "crores" };
      if (string.IsNullOrEmpty(str)) return "exact";
      var result = allowsDisplayFormat.FirstOrDefault(x => x == str.Trim().ToLower());
      if (result == null) return "exact";
      return result;

    }

    public static string ValidateSeparator(this string separator)
    {
      var allowedValues = new HashSet<string> { ".", ",", " " };
      if (string.Equals(separator, "space", System.StringComparison.InvariantCultureIgnoreCase)) separator = " ";
      return allowedValues.Contains(separator) ? separator : string.Empty;
    }

  }
}
