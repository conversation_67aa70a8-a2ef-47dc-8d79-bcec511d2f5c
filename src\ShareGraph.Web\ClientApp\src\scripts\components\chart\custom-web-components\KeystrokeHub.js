import { CIQ } from '../chartiq-import';

CIQ.UI.KeystrokeHub.prototype.tabIndexNext = function (reverse) {
  

	if (
		!this.context.stx.uiContext.topNode.hasAttribute(
			'cq-keyboardnavigation-active'
		)
	)
		return;
	if (this.tabOrder.length <= 1) return;
	// The context can lose focus when interacting with certain child elements like inputs.
	// Ensure that it remains focused when tabbing
	this.context.stx.uiContext.topNode.focus();
	// Sort the elements based on screen position.
	this.sortTabOrderElements();
	// A negative index represents a deselected state of that index
	// invert it to select the current index rather than change it
	// This allows the user to re-select a previously deselected item
	if (this.tabIndex < 0) {
		this.tabIndex = Math.abs(this.tabIndex);
	} else {
		// If the tabIndex is already positive, deselect the current element but keep the index unchanged.
		this.tabOrderDeselect(true);
	}
	if (this.tabIndex < 1 || this.tabIndex >= this.tabOrder.length) {
		// this.tabIndex = reverse ? this.tabOrder.length : 1;
    if(reverse && this.context.topNode.getAttribute('cq-exit') === 'foot') {
      this.tabIndex = this.tabOrder.length;
    } 
	}
	// If the current selected element isn't visible, find the next in line that is.
	// Also look ahead to see if we're at the last selectable element in tabOrder
	let nextIndex = this.tabIndex;
	let tabIndexMoved = false;
	do {
		if (reverse === true) {
			nextIndex--;
		} else {
			nextIndex++;
		}
		if (
			this.tabOrder[nextIndex] &&
			CIQ.trulyVisible(this.tabOrder[nextIndex].element) &&
			this.tabOrder[nextIndex].element.getAttribute('keyboard-selectable') !==
				'false'
		) {
			if (!tabIndexMoved) {
				this.tabIndex = nextIndex;
				tabIndexMoved = true;
			} else {
				break;
			}
		}
	} while (nextIndex < this.tabOrder.length && nextIndex > 0);
	// At this point we either have an element or we're outside of the array
	if (nextIndex >= this.tabOrder.length || nextIndex <= 1) {
		if (nextIndex >= this.tabOrder.length)
			this.context.topNode.setAttribute('cq-exit', 'foot');
		else if (nextIndex <= 1)
			this.context.topNode.setAttribute('cq-exit', 'head');
		this.context.topNode.removeAttribute('cq-keyboardnavigation-active');
	} 
	this.tabOrderSelect();
	this.uiManager.closeMenu();
};