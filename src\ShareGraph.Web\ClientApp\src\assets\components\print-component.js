(function PrintComponentFactory(euroland) {
  const props = {
    key: {
      type: 'string',
      queryParam: true // ?email=<EMAIL>
    },
    mainTickerId: {
      type: 'number',
      queryParam: true // ?email=<EMAIL>
    },
    isPrintShareDetail: {
      type: 'boolean',
      queryParam: true // ?email=<EMAIL>
    },
    data: {
      type: 'object',
      required: true
    }
  };

  const baseUrl = window.location.origin + window.appSettings.toolUrlBase;


/**
 * Creates the PressReleaseComponent.
 * @returns {euroland.components.PrintComponent}
 */
  euroland.createComponent('PrintComponent', {
    tag: 'print-component',
    url: baseUrl + 'print' + location.search,
    dimensions: {
      width: '1024px',
      height: '100%'
    },
    template: {
      name: 'popup',
      backdrop: false
    },
    props: props
  });
})(window.euroland);
