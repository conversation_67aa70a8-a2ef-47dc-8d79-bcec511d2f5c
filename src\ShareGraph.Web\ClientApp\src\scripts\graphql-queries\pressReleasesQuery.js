import {gql} from './utils';

export const PRESS_RELEASE_QUERY = gql(/* GraphQL */`query PressReleases(
  $companyCode: String!
  $fromDate: DateTime!
  $toDate: DateTime!
  $orCondition: [PressReleaseDtoFilterInput!]
  $andCondition: [PressReleaseDtoFilterInput!]
) {
  company(code: $companyCode) {
    pressReleases(
      where: {
        or: $orCondition
        and: $andCondition
        dateTime: { gte: $fromDate, lt: $toDate }
      }
      order: { dateTime: ASC }
      first: 99999999
    ) {
      nodes {
        id
        dateTime
        title
        messageTypeId
      }
    }
  }
}`);

export const PRESS_RELEASE_PAGINATION_QUERY = gql(/* GraphQL */`query PressReleasesPagination(
  $companyCode: String!
  $fromDate: DateTime!
  $toDate: DateTime!
  $orCondition: [PressReleaseDtoFilterInput!]
  $andCondition: [PressReleaseDtoFilterInput!]
  $cursor: String
) {
  company(code: $companyCode) {
    pressReleases(
      where: {
        or: $orCondition
        and: $andCondition
        dateTime: { gte: $fromDate, lt: $toDate }
      }
      order: { dateTime: DESC }
      first: 500
      after: $cursor
    ) {
      edges {
        node {
          id
          dateTime
          title
          messageTypeId
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
}
`);