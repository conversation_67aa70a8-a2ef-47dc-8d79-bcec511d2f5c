import { useLayoutEffect, useState } from 'react';
import useResize from './useResize';

function useCheckHorizontalScroll(containerRef) {
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);

  const { width } = useResize();

  useLayoutEffect(() => {
    function checkHorizontalScroll() {
      const containerNode = containerRef?.current;
      if (!containerNode) return;

      if (containerNode) {
        const hasScroll = containerNode.scrollWidth > containerNode.clientWidth;
        setHasHorizontalScroll(hasScroll);
      }
    }

    checkHorizontalScroll();
  }, [width, containerRef]);

  return { hasHorizontalScroll };
}

export default useCheckHorizontalScroll;
