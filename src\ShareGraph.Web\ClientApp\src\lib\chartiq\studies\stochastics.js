import { CIQ } from 'chartiq/js/components';
import { appSettings } from '../../../appSettings';

CIQ.Euroland.Studies.calculateStochastics = function (stx, sd) {
  var field, periodK, quotes, periodSmoothK, periodD, fastK;

  function calculateFastK(i, field, periodK) {
    var highestHigh, lowestLow, currentLow, currentHigh, fastK;
    highestHigh = Number.MAX_VALUE * -1;
    lowestLow = Number.MAX_VALUE;
    for (var j = i - periodK + 1; j <= i; j++) {
      currentLow = quotes[j][field == 'Close' ? 'Low' : field];
      currentHigh = quotes[j][field == 'Close' ? 'High' : field];
      if (!currentLow && currentLow !== 0) continue;
      if (!currentHigh && currentHigh !== 0) continue;
      lowestLow = Math.min(lowestLow, currentLow);
      highestHigh = Math.max(highestHigh, currentHigh);
    }
    if (highestHigh == Number.MAX_VALUE * -1 || lowestLow == Number.MAX_VALUE) {
      return null;
    }
    fastK =
      highestHigh == lowestLow
        ? 0
        : ((quotes[i][field] - lowestLow) / (highestHigh - lowestLow)) * 100;
    return fastK;
  }

  if (!sd.smooth) {
    sd.smooth = sd.inputs.Smooth;
  }
  field = sd.inputs.Field;
  if (!field || field == 'field') {
    field = 'Close';
  }
  periodK = sd.inputs['%K Periods'];
  if (!periodK) {
    periodK = sd.days;
  }
  quotes = sd.chart.scrubbed;
  if (quotes.length < Math.max(periodK, sd.days) + 1) {
    sd.error = true;
    return;
  }
  periodSmoothK = sd.inputs['%K Smoothing Periods'];
  if (periodSmoothK && !sd.inputs.Fast) {
    sd.smooth = true;
  } else if (sd.smooth) {
    periodSmoothK = 3;
  }
  periodD = sd.inputs['%D Periods'];
  if (!periodD) {
    periodD = 3;
  }
  if (sd.outputs.Fast) {
    sd.outputMap = {};
    sd.outputMap['%K ' + sd.name] = 'Fast';
    sd.outputMap['%D ' + sd.name] = 'Slow';
  }
  for (var i = Math.max(periodK, sd.startFrom); i < quotes.length; i++) {
    fastK = calculateFastK(i, field, periodK);
    if (fastK !== null) {
      quotes[i]['_Fast%K ' + sd.name] = calculateFastK(i, field, periodK);
    }
  }
  CIQ.Studies.MA(
    'simple',
    sd.smooth ? periodSmoothK : 1,
    '_Fast%K ' + sd.name,
    0,
    '%K',
    stx,
    sd
  );
  CIQ.Studies.MA('simple', periodD, '%K ' + sd.name, +'0', '%D', stx, sd);
};

// CIQ.Euroland.Studies.createStudy('stochastics_simple', {
//   name: 'Stochastics (Simple)',
//   calculateFN: CIQ.Euroland.Studies.calculateStochastics,
//   inputs: { Period: 14, Field: 'field', Smooth: true },
//   range: '0 to 100',
//   outputs: { Fast: 'auto', Slow: '#FF0000' },
//   parameters: {
//     init: {
//       studyOverZonesEnabled: true,
//       studyOverBoughtValue: 80,
//       studyOverBoughtColor: 'auto',
//       studyOverSoldValue: 20,
//       studyOverSoldColor: 'auto'
//     }
//   }
// });

const studyName = CIQ.I18N.translate('Stochastics');

CIQ.Euroland.Studies.createStudy(studyName, {
  name: studyName,
  calculateFN: CIQ.Euroland.Studies.calculateStochastics,
  inputs: {
    // Field: 'field',
    '%K Periods': 14,
    Fast: false,
    '%K Smoothing Periods': 3,
    '%D Periods': 3
  },
  range: '0 to 100',
  outputs: { '%K': 'auto', '%D': '#FF0000' },
  parameters: {
    init: {
      studyOverZonesEnabled: true,
      studyOverBoughtValue: 80,
      studyOverBoughtColor: 'auto',
      studyOverSoldValue: 20,
      studyOverSoldColor: 'auto'
    }
  },
  attributes: {
    '%K Smoothing Periods': {
      hidden: function () {
        return this.inputs.Fast;
      }
    }
  },
  centerline: 50
});

export { CIQ };
