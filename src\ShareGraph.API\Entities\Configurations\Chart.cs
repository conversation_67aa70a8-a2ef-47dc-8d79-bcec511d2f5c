using Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Studies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class Chart
  {
    public string DefaultTooltipType { get; set; }
    public string DefaultChartType { get; set; }
    public IEnumerable<string> EnabledChartTypes { get; set; }
    public IEnumerable<string> ExcludeStudies { get; set; }
    public IEnumerable<string> EnabledEvents { get; set; }
    public IEnumerable<string> DefaultEvents { get; set; }
    public IEnumerable<string> EnabledChartPreferences { get; set; }
    public IEnumerable<string> EnabledYAxisPreferences { get; set; }
    public IEnumerable<string> EnabledPeriods { get; set; }
    public string DefaultPeriod { get; set; }
    public bool HideChartTitle { get; set; }
    public bool EnabledDefaultVolumeChart { get; set; }
    public bool EnabledHoverEvent { get; set; }
    public string HighPriceIndicatorColor { get; set; }
    public string LowPriceIndicatorColor { get; set; }

    public bool? IsStripedBackground { get; set; }

    public IEnumerable<string> EnabledAdditionalOptions { get; set; }
    public IEnumerable<string> EnabledSocialMedias { get; set; }
    public Studies Studies { get; set; }

    public string? DefaultVolumeChart { get; set; }

    public ExcelDownloadOption ExcelDownloadOption { get; set; }

    public BlinkingPricePointer BlinkingPricePointer { get; set; }
  }

  public class ChartConfig
  {
    public string DefaultTooltipType { get; set; }
    public string DefaultChartType { get; set; }
    public string EnabledChartTypes { get; set; }
    public string ExcludeStudies { get; set; }
    public string EnabledEvents { get; set; }
    public string DefaultEvents { get; set; }
    public string EnabledChartPreferences { get; set; }
    public string EnabledYAxisPreferences { get; set; }
    public string EnabledPeriods { get; set; }
    public string DefaultPeriod { get; set; }
    public string EnabledAdditionalOptions { get; set; }
    public string EnabledSocialMedias { get; set; }
    public bool EnabledDefaultVolumeChart { get; set; }
    public bool EnabledHoverEvent { get; set; }
    public bool HideChartTitle { get; set; }
    public bool? IsStripedBackground { get; set; }
    public string HighPriceIndicatorColor { get; set; }
    public string LowPriceIndicatorColor { get; set; }
    public string? DefaultVolumeChart { get; set; }
    public Studies Studies { get; set; }
    public ExcelDownloadOption ExcelDownloadOption { get; set; }
  }

  public class ExcelDownloadOption
  {
    public bool IncludedTotalReturn { get; set; }
    public bool IncludedSelectedPeersAndIndicies { get; set; }
  }

  public class BlinkingPricePointer
  {
    public bool Enabled { get; set; }
    public string BullishTrendColor { get; set; }
    public string BearishTrendColor { get; set; }
  }
}
