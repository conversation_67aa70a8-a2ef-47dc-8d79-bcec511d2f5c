import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { fetchSharePriceDevelopment } from '../actions/SharePriceDevelopmentAction';
import SharePriceDevelopments from '../entities/SharePriceDevelopments';
import { dynamicSort } from '../helper';
import { useSharePriceOrder } from './useSharePriceOrder';
import { useChangeQuoteCurrency } from './useSelectedCurrency';
import { useFormatNumberByInstrument } from './useFormatNumberByInstrument';

const useSharePriceDevelopment = () => {
  const { loading, instruments, fetchError } = useSelector(state => state.sharePriceDevelopment) || {};
  const dispatch = useDispatch();
  const { sharePriceOrders } = useSharePriceOrder();
  const {
    formatNumberByInstrument,
    formatNumberChangeByInstrument
  }= useFormatNumberByInstrument();

  useEffect(() => {
    dispatch(fetchSharePriceDevelopment());
  }, []);

  useChangeQuoteCurrency(() => {
    dispatch(fetchSharePriceDevelopment());
  });

  return [
    {
      sharePriceDevelopments: instruments
        .map(
          (instrument) =>
            new SharePriceDevelopments(
              {
                ...instrument,
                order: sharePriceOrders[instrument.instrumentId]
              },
              formatNumberByInstrument,
              formatNumberChangeByInstrument
            )
        )
        .sort(dynamicSort('order')),
      getState: {
        loading,
        error: fetchError
      }
    }
  ];
};

export default useSharePriceDevelopment;
