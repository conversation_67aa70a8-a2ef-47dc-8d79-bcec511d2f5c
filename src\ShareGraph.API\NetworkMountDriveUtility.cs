using System;
using System.Diagnostics;
using System.IO;
using Microsoft.Extensions.Logging;

namespace Euroland.FlipIT.ShareGraph.API
{
  public class MountDriveParams
  {
    /// <summary>
    /// Local folder name. I.e. /LocalFolderName
    /// </summary>
    public string MountPath { get; set; } = System.Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);

    /// <summary>
    /// i.e. //<host>/<path>
    /// </summary>
    public string SharedPath { get; set; }

    public string Username { get; set; }

    public string Password { get; set; }
  }
  /// <summary>
  /// Drive mounting utility to mount Windows Shared drive Network on Linux container.
  /// </summary>
  public class NetworkMountDriveUtility {
    private readonly ILogger<NetworkMountDriveUtility> _logger;
    public NetworkMountDriveUtility(ILogger<NetworkMountDriveUtility> logger) {
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public bool Mount(MountDriveParams mountParams) {
      var result = false;
      try
      {
        string mkdirArgs = $"-p \"{mountParams.MountPath}\"";
        string mountArgs = $"-t cifs -o username={mountParams.Username},password={mountParams.Password} {mountParams.SharedPath} {mountParams.MountPath}";

        string message = string.Empty;

        if (RunCommand("mkdir", mkdirArgs, out message))
        {
            //Logger.LogInformation($"Output 1: {message}");

            if (RunCommand("mount", mountArgs, out message))
            {
                //_logger.LogInformation($"Output 2: {message}");

                string connectingTestingFile = $"{Guid.NewGuid()}.txt";
                string filePath = Path.Combine(mountParams.MountPath, connectingTestingFile);

                _logger.LogInformation("Testing file path: " + filePath);

                File.Create(filePath);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    result = true;
                }
                if (result)
                {
                    _logger.LogInformation("Network drive mounted successfully");
                }
                else
                {
                    _logger.LogError("Network drive mounting failed");
                }
            }
            else
            {
                _logger.LogError($"{message}");
            }
        }
        else
        {
            _logger.LogError($"{message}");
        }
      }
      catch (System.Exception ex)
      {
        _logger.LogError(ex, "There's an exception occurred during mounting network drive.");
      }

      return result;
    }

    /// <summary>
  /// This method runs command on shell/bash
  /// </summary>
  /// <param name="command">Command name</param>
  /// <param name="args">Command argument</param>
  /// <param name="message">Output message</param>
  /// <returns>Boolean
  public static bool RunCommand(string command, string args, out string message)
  {
      var process = new Process()
      {
          StartInfo = new ProcessStartInfo
          {
              FileName = command,
              Arguments = args,
              RedirectStandardOutput = true,
              RedirectStandardError = true,
              UseShellExecute = false,
              CreateNoWindow = true,
          }
      };
      process.Start();
      string output = process.StandardOutput.ReadToEnd();
      string error = process.StandardError.ReadToEnd();
      process.WaitForExit();

      if (string.IsNullOrEmpty(error))
      {
          message = output;
          return true;
      }
      else
      {
          message = error;
          return false;
      }
  }
  }
}
