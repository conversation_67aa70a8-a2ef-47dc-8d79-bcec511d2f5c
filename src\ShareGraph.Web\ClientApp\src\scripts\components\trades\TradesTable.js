
import { useContext, useMemo } from 'react';
import useTrades from '../../customHooks/useTrades';
import { classNames, convertNumber, formatShortDate, translateStringFormat } from '../../helper';
import { TableV2 } from '../commons/tableV2/Table';
import { getInstrumentSettingById } from '../../utils/format-number';
import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import { Skeleton } from '../Skeleton';
import { AppContext } from '../../AppContext';
import { REFRESH_TRADES_TIME, TRADE_TIME_FORMAT } from '../../constant/common';
import i18n from '../../services/i18n';
import { formatDateFn } from '../../utils/dayjs/formatDate';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';


function TradesTable() {
  const { formatNumberByInstrument } = useFormatNumberByInstrument();
  const selectedInstrumentId = useSelector(
    (state) => state.tickers.selectedInstrumentId
  );
  const { data, loading } = useTrades();
  const settings = useContext(AppContext);

  const { timeFormat, tradeRefreshSeconds } = settings.trade || {};
  const hasData = data.length > 0;


  const tableHeadings = [
    { columnDisplay: i18n.translate('priceLabel'), fieldName: 'price' },
    { columnDisplay: i18n.translate('volumeLabel'), fieldName: 'volume' },
    { columnDisplay: i18n.translate('updated'), fieldName: 'updated' }
  ];

  const displayDate = (date) => {
    const isToday = checkIsToday(date);
    const TRADES_FORMAT = timeFormat || TRADE_TIME_FORMAT;

    const formatTime = formatDateFn(date, TRADES_FORMAT);

    return isToday ? formatTime : `${formatShortDate(date)} ${formatTime}`;
  };

  const checkIsToday = (date) => {
    const todayUTC = dayjs().utc();
    const isTodayUTC = dayjs(date).isSame(todayUTC, 'day');

    return isTodayUTC;
  };

  const datasTable = useMemo(
    () =>
      data.map((item, index) => {
        const { close, date, size } = item;

        // Calc price change
        const nextItem = data[index + 1];
        let changeFromPreviousValue = 0;
        const settingInstrument = getInstrumentSettingById(selectedInstrumentId);
        const decimalDigits = settingInstrument.decimalDigits || 2;
        if (nextItem) changeFromPreviousValue = close.toFixed(decimalDigits) - nextItem.close.toFixed(decimalDigits);
        return ({
          price: [
            {
              value: close,
              display: (
                <p
                  className={classNames('trades__change', {
                    up: changeFromPreviousValue > 0,
                    down: changeFromPreviousValue < 0,
                    neutral: changeFromPreviousValue === 0
                  })}
                >
                  <i className="fs fs-triangle-up" />
                  <i className="fs fs-triangle-down" />
                  <span className="trades__change-value">{formatNumberByInstrument(close, selectedInstrumentId)}</span>
                </p>
              )
            }
          ],
          volume: [{ value: size, display: convertNumber(size) }],
          updated: [{ value: date, display: displayDate(date) }]
        });
      }),
    [data]
  );

  if (loading) return <Skeleton style={{height: '300px'}} ></Skeleton>;

  return (
    <>
      {hasData ? (
        <div className="table-responsive">
          <TableV2
            datas={datasTable}
            headings={tableHeadings}
            className="trades__table"
            caption={i18n.translate('trades')}
          />
        </div>
      ) : (
        <div className="trades__nodata">{i18n.translate('notFoundData')}</div>
      )}

      <p className="trades__description">
        {translateStringFormat('tradesDataRefreshTime', [
          convertNumber(tradeRefreshSeconds || REFRESH_TRADES_TIME)
        ])}
      </p>
    </>
  );
}

export default TradesTable;
