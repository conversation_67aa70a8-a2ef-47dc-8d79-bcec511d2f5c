import SharePriceDevelopmentByYears from '../../components/accessibles/SharePriceDevelopmentByYears';
import SharePriceDevelopmentOverVariousPeriods from '../../components/accessibles/SharePriceDevelopmentOverVariousPeriods';
import i18n from '../../services/i18n';
import appConfig from '../../services/app-config';
import { PERFORMANCE_SELECT_TYPE } from '../../common';
import Weeks52HighLow from '../../components/accessibles/Weeks52HighLow';

export default function AccessibleSharePerformance() {
  const {
    performance: { performanceTypes }
  } = appConfig.get();
  const componentMaping = {
    [PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT]: SharePriceDevelopmentOverVariousPeriods,
    [PERFORMANCE_SELECT_TYPE.SHARE_PRICE_DEVELOPMENT_BY_YEARS]: SharePriceDevelopmentByYears,
    [PERFORMANCE_SELECT_TYPE.WEEKS_52_HIGH_LOW]: Weeks52HighLow
  };
  return (
    <div>
      <h1 className="share-performance__titlePage">{i18n.translate('accessibleShareGraph')}</h1>
      <h2 className="share-performance__heading">{i18n.translate('performanceTable')}</h2>
      {performanceTypes.map(item => {
        const Component = componentMaping[item];
        if (!Component) return null;
        return <Component key={item} />;
      })}
    </div>
  );
}
