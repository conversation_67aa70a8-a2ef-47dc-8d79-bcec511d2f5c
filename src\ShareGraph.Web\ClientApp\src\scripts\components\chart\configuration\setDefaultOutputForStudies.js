import {CIQ} from 'chartiq/js/standard';
import appConfig from '../../../services/app-config';

export function setDefaultOutputForStudies(stx) {
  // Set default output for studies
  const chartSettings = appConfig.get().chart;
  const studiesConfig = chartSettings.studies;
  if (!studiesConfig) console.log('Missing config output default for studies');

  const { rSI } = studiesConfig.rSI;
  //var rsiName = CIQ.I18N.translate('RSI');
  CIQ.Studies.studyLibrary['RSI'].outputs = { RSI: rSI };

  const { mACD, signal, increasingBar, decreasingBar } = studiesConfig.mACD;
  CIQ.Studies.studyLibrary['MACD'].outputs = {
    MACD: { color: mACD },
    Signal: { color: signal },
    'Increasing Bar': increasingBar,
    'Decreasing Bar': decreasingBar
  };

  const { k, d, overBought, overSold } = studiesConfig.stochastics;
  CIQ.Studies.studyLibrary['Stochastics'].outputs = {
    '%K': { color: k },
    '%D': { color: d }
  };
  let stochasticsParams = CIQ.Studies.studyLibrary['Stochastics'].parameters.init;
  CIQ.Studies.studyLibrary['Stochastics'].parameters.init = {
    ...stochasticsParams,
    studyOverBoughtColor: overBought ,
    studyOverSoldColor: overSold
  };

  const { conversionLine, baseLine, leadingSpanA, leadingSpanB, leadingSpan } = studiesConfig.ichimokuClouds;
  CIQ.Studies.studyLibrary['Ichimoku Clouds'].outputs = {
    'Conversion Line': conversionLine,
    'Base Line': baseLine,
    'Leading Span A': leadingSpanA,
    'Leading Span B': leadingSpanB,
    'Lagging Span': leadingSpan
  };

  const { bollingerBandsTop, bollingerBandsMedian, bollingerBandsBottom } = studiesConfig.bollingerBands;
  CIQ.Studies.studyLibrary['Bollinger Bands'].outputs = {
    'Bollinger Bands Top': bollingerBandsTop,
    'Bollinger Bands Median': bollingerBandsMedian,
    'Bollinger Bands Bottom': bollingerBandsBottom
  };

  const { tTRT } = studiesConfig.totalReturn;
  CIQ.Studies.studyLibrary['Total Return'].outputs = {
    TTRT: { color: tTRT }
  };

  const { diPlus, diMinus, aDX, positiveBar, negativeBar } = studiesConfig.aDX;
  CIQ.Studies.studyLibrary['ADX_DMS'].outputs = {
    '+DI': diPlus,
    '-DI': diMinus,
    ADX: aDX,
    'Positive Bar': positiveBar,
    'Negative Bar': negativeBar
  };

  const { downVolumeColor, upVolumeColor, yAxisHeightFactor } = studiesConfig.volumeUnderlay || {};
  const volumeUnderlayStudy = CIQ.Studies.studyLibrary['vol undr'];
  // override volume underlay config
  volumeUnderlayStudy.yAxis.heightFactor = yAxisHeightFactor || volumeUnderlayStudy.yAxis.heightFactor;
  volumeUnderlayStudy.outputs = {
    'Down Volume': downVolumeColor || volumeUnderlayStudy.outputs['Down Volume'],
    'Up Volume': upVolumeColor || volumeUnderlayStudy.outputs['Up Volume']
  };

  const volumeSetting = studiesConfig.volume || {};
  const volumeStudy = CIQ.Studies.studyLibrary['volume'];
  volumeStudy.customOptions = volumeStudy.customOptions ?? {};
  volumeStudy.customOptions.multiple = false;

  volumeStudy.outputs = {
    'Down Volume': volumeSetting?.downVolumeColor || volumeStudy.outputs['Down Volume'],
    'Up Volume': volumeSetting?.upVolumeColor || volumeStudy.outputs['Up Volume']
  };
  volumeStudy.parameters = {
    ...volumeStudy.parameters,
    isBlindMode: appConfig.get()?.colorBlindMode?.enabled && appConfig.get()?.colorBlindMode?.defaultSelected
  };

  const totalReturn = CIQ.Studies.studyLibrary['Total Return'];
  totalReturn.customOptions = totalReturn.customOptions ?? {};
  totalReturn.customOptions.multiple = false;
}
