using System.Collections.Generic;

namespace Euroland.FlipIT.ShareGraph.API.Entities.Configurations;

public class CurrenciesConfig
{
  public bool Enabled { get; set; }
  public IEnumerable<Currency> Currencies { get; set; }
}

public class Currency
{
  public string Code { get; set; }
  public string Text { get; set; }
  public int? DecimalDigits { get; set; }
  public string? FullName { get; set; }
}


