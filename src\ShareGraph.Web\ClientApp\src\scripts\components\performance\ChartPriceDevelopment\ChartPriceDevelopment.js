import { useEffect, useRef, useState, Fragment } from 'react';
import { useSelector, useStore } from 'react-redux';
import { CIQ } from '../../../../lib/chartiq/renderer/sPDRenderer';

import { convertPercentDecimal, dynamicSort, getDeviceType } from '../../../helper';
import { DEVICES } from '../../../common';
import Legend from '../ChartPrice/Legend';
import {blockScrollLeftAndRight} from '../chartPerformanceHelper';
import TickerName, {getRawHTMLTickerName, getTickerName} from '../../TickerName';
import i18n from '../../../services/i18n';


const ChartPriceDevelopment = ({ datas }) => {

  const store = useStore();
  const containerRef = useRef(null);
  const eventRef = useRef(null);
  const selectedTickerId = useSelector(state => state.tickers.selectedInstrumentId);
  const [series, setSeries] = useState([]);
  const [legends, setLegends] = useState(() => {
    var legends = datas.map((x) => {
      return { ...x, isSelected: x.instrumentId === selectedTickerId };
    });
    return legends;
  });
  const [chartEngine, setChartEngine] = useState(null);

  const cleanup = () => {
    if (chartEngine) {
      chartEngine.destroy();
      chartEngine.draw = () => { };
    }
  };

 // handle event when click a legend
  const clickedLegend = (id) => {
    const state = store.getState();
    if (id === state.tickers.selectedInstrumentId) return;
    //Update legend
    var legendUpdates = legends.map((x) => {
      var isSelected = x.instrumentId === id ? !x.isSelected : false;
      isSelected = x.instrumentId === state.tickers.selectedInstrumentId ? true : isSelected;
      return {
        ...x,
        isSelected: isSelected
      };
    });
    setLegends(legendUpdates);
    //Show highlight in chart
    var dataLengend = legends.find((x) => x.instrumentId === id);

    var { symbol, opacity } = dataLengend;
    //set all series is not current selected legend to assume only once line can be highlight at the same time
    for (let serie in chartEngine.chart.seriesRenderers) {
      if (chartEngine.chart.seriesRenderers[serie].params.renderer !== undefined) {
        if (serie === symbol) {
          chartEngine.chart.seriesRenderers[serie].seriesParams[0].highlight = !chartEngine.chart.seriesRenderers[serie].seriesParams[0].highlight;
          chartEngine.chart.seriesRenderers[serie].seriesParams[0].opacity = 1;
        } else {
          chartEngine.chart.seriesRenderers[serie].seriesParams[0].highlight = false;
          chartEngine.chart.seriesRenderers[serie].seriesParams[0].opacity = 0.2;
        }
      }
    }
      reRenderSeries(symbol);
      highlightCurrentLegend(chartEngine);
  };

  //high light serie
  const highlightCurrentLegend = (chartEngine) => {
    let haveHighlightSerie = false;

    for (var n in chartEngine.chart.seriesRenderers) {
      var r2 = chartEngine.chart.seriesRenderers[n];
      if (!r2.params.highlightable) continue; //might not be necessary to check this here

      for (var m2 = 0; m2 < r2.seriesParams.length; m2++) {
        const serie = r2.seriesParams[m2];
        //if(serie.id != )
        if (serie.highlight) {
          serie.opacity = 1;
          haveHighlightSerie = true;
          // remove all marker to draw new main chart
          CIQ.Marker.removeByLabel(chartEngine, 'B');
          CIQ.Marker.removeByLabel(chartEngine, 'A');
          // create new marker for each point
          for (var i = 0; i < serie.data.length; i++) {
            let newNode = eventRef.current.cloneNode(true);
            newNode.id = null;
            newNode.innerHTML =
              convertPercentDecimal(serie.data[i].Close) + '%';

            new CIQ.Marker({
              stx: chartEngine,
              xPositioner: 'date',
              yPositioner: 'top_lane',
              x: serie.data[i].DT,
              y: serie.data[i].Close,
              label: 'B',
              node: newNode
            });

            if (i === serie.data.length - 1) {
              let newNode = eventRef.current.cloneNode(true);
              newNode.id = null;
              newNode.innerHTML = getRawHTMLTickerName({
                instrumentId: serie.insId,
                marketAbbreviation: serie.marketAbbreviation,
                shareName: serie.shareName
              });
              newNode.classList.remove('myEvents');
              newNode.style.color = serie.color;
              newNode.classList.add('main-share-name');
              new CIQ.Marker({
                stx: chartEngine,
                xPositioner: 'date',
                yPositioner: 'value',
                x: serie.data[i].DT,
                y: serie.data[i].Close,
                label: 'A',
                node: newNode
              });
            }
          }
          chartEngine.draw();

        } else {
          serie.opacity = 0.2;
        }
      }
    }
    if (!haveHighlightSerie) {
      // remove all marker to draw new main chart
      CIQ.Marker.removeByLabel(chartEngine, 'A');
      CIQ.Marker.removeByLabel(chartEngine, 'B');
      highlightMainSerie(chartEngine);
    }
  };

  // get instance chart
  const getInstanceChart = () => {
    let stxx = new CIQ.ChartEngine({
      controls: {
        mSticky: null
      },
      allowScroll: CIQ.isMobile && !CIQ.ipad ? true : false,
      allowZoom: false,
      container: containerRef.current,
      maximumCandleWidth: 900,
      longHoldTime: 100000,
      layout: {
        chartType: 'sharepricedevelopment',
        periodicity: 1,
        interval: 'day'
      },
      preferences: {
        currentPriceLine: false,
        whitespace: 0
      },
      chart: {
        allowScrollFuture: false,
        allowScrollPast: false,
        yAxis: {
          width: 20, // remove y axis
          initialMarginTop: 30, // adjust top margin - useful on very narrow charts.
          initialMarginBottom: 20, // adjust top margin - useful on very narrow charts.
          position: 'left',
          drawCurrentPriceLabel: false,
          drawSeriesPriceLabels: false,
          idealTickSizePixels: 200,
          priceFormatter: function (stx, panel, price, decimalPlaces) {
            return convertPercentDecimal(price) + '%';
          },
          setBackground: function (stx, params) {
            params.color = 'none';
          }
        },
        xAxis: {
          displayGridLines: true,
          initialMarginLeft: 100,
          fitLeftToRight: true,
          fitTight: true,
          formatter: function (
            labelDate,
            gridType,
            timeUnit,
            timeUnitMultiplier,
            defaultText
          ) {
            const mainData = datas.find((x) => x.instrumentId === selectedTickerId);
            const data = mainData.data.find(x => x.DT.getDate().toString() === labelDate.getDate().toString());
            // if (getDeviceType() === DEVICES.XL || getDeviceType() === DEVICES.MD || getDeviceType() === DEVICES.SM || getDeviceType() === DEVICES.XS) {
              return data ? data.shortLabel : '';
            // } else {
              // return data ? data.longLabel : '';
            // }
          }
        }
      },
      manageTouchAndMouse: true, // disable chart interactivity
      xaxisHeight: 50 // remove x axis

    });

    return stxx;
  };

  // config chart and add data for main chart, add series
  const initialChart = () => {
    const mainData = datas.find(x => x.instrumentId === selectedTickerId);
    var legendUpdates = legends.map((x) => {
      var isSelected = x.instrumentId === selectedTickerId ? !x.isSelected : false;
      isSelected = x.instrumentId === selectedTickerId ? true : isSelected;
      return {
        ...x,
        isSelected: isSelected
      };
    });

    setLegends(legendUpdates);
    if (!chartEngine) return;


    //Now load a chart with its data

    let dataSorting = mainData.data.sort(dynamicSort('DT'));
    const changePercentList = mainData.changePercentList.length;
    const maxList = changePercentList + 1;
    const minList = changePercentList - 1;

    chartEngine.loadChart(
      mainData.symbol, {
      masterData: dataSorting,
      stretchToFillScreen: true,
      range: {
        dtLeft: new Date(2020, 0, 1),
        dtRight: new Date(2020, 0, maxList + 1)
      }
    }
    );
    var axis = new CIQ.ChartEngine.YAxis({ position: 'left' });
    //sPDRenderer
    const ssdRenderer = chartEngine.setSeriesRenderer(
      new CIQ.Renderer.SharePriceDevelopment({
        params: {
          type: 'SharePriceDevelopment'
        }
      })
    );
    //add series
    legends.forEach((serie, idx) => {
      let { symbol, color, data } = serie;
      let dataSorting = data.sort(dynamicSort('DT'));
      let setinstrumentId = serie.instrumentId;
      let isSelected = serie.isSelected;
      let opacity = isSelected ? 1 : 0.2;
      let drawCount = legends.length;
      chartEngine.addSeries(
        symbol,
        {
          renderer: 'SharePriceDevelopment',
          color: color,
          data: dataSorting,
          insId: setinstrumentId,
          width: 1,
          isComparison: false,
          shareYAxis: true,
          permanent: true,
          isSelected: !isSelected,
          opacity: opacity,
          drawCount: drawCount,
          shareName: serie.shareName,
          marketAbbreviation: serie.marketAbbreviation
        });
    });
    highlightMainSerie(chartEngine);
  };

  // show legend name and value to points in chart
  const highlightMainSerie = (chartEngine) => {
    const state = store.getState();
    // remove all marker to draw new main chart
    CIQ.Marker.removeByLabel(chartEngine, 'A');
    CIQ.Marker.removeByLabel(chartEngine, 'B');
    const mainData = datas.find((x) => x.instrumentId === state.tickers.selectedInstrumentId);

    // create new marker for each point
    for (var i = 0; i < chartEngine.masterData.length; i++) {
      let data = chartEngine.masterData[i];
      let newNode = eventRef.current.cloneNode(true);
      newNode.id = null;
      newNode.innerHTML = convertPercentDecimal(data.Close) + '%';

      new CIQ.Marker({
        stx: chartEngine,
        xPositioner: 'date',
        yPositioner: 'top_lane',
        x: data.DT,
        y: data.Close,
        label: 'B',
        node: newNode
      });
    }
    if (chartEngine.masterData && chartEngine.masterData.length > 0) {

      let lastData = chartEngine.masterData[chartEngine.masterData.length - 1];
      let newNode = eventRef.current.cloneNode(true);
      newNode.id = null;
      newNode.innerHTML = getRawHTMLTickerName(mainData);
      newNode.classList.remove('myEvents');
      newNode.style.color = mainData.color;
      newNode.classList.add('main-share-name');
      new CIQ.Marker({
        stx: chartEngine,
        xPositioner: 'date',
        yPositioner: 'value',
        x: lastData.DT,
        y: lastData.Close,
        label: 'A',
        node: newNode
      });
    }
    reRenderSeries(mainData.tickerName);
    chartEngine.draw();
  };

//series Re-Renderers and seleted ticker crete top of the layer

const reRenderSeries = (dataId)=>{
  var rendererName = dataId;
  var newRenderers = {};
  var pos = 0;
  var r;

  for (r in chartEngine.chart.seriesRenderers) {
    if (r == rendererName) break;
    pos++;
  }

  if (pos) {
    // Not already at top.
    var k = 0;
    var count = Object.keys(chartEngine.chart.seriesRenderers).length;
    for (r in chartEngine.chart.seriesRenderers) {
      if(k == count-2){
        newRenderers[rendererName] = chartEngine.chart.seriesRenderers[rendererName];
        newRenderers[rendererName].seriesParams[0].opacity = 1;
      }
      if (r == rendererName) continue;
        newRenderers[r] = chartEngine.chart.seriesRenderers[r];
        if(newRenderers[r].seriesParams.length == 1) {
          newRenderers[r].seriesParams[0].opacity = 0.2;
        }
      k++;
    }
    chartEngine.chart.seriesRenderers = newRenderers;
  }
};

  useEffect(() => {
    const state = store.getState();
    setSeries(datas.filter(x => x.instrumentId !== state.tickers.selectedTickerId));
  }, [datas, selectedTickerId]);

  useEffect(() => {
    if (!chartEngine) {
      setChartEngine(getInstanceChart());
    } else {
      // need remove all series to draw chart again.
      let seriesRenders = chartEngine.chart.seriesRenderers;
      if (Object.keys(seriesRenders).length) {
        for (let serie in seriesRenders) {
          chartEngine.removeSeriesRenderer(seriesRenders[serie]);
          chartEngine.removeSeries(chartEngine.chart.series[serie]);
        }
      }
    }

  }, [selectedTickerId]);

  useEffect(() => {
    initialChart();

    if(!chartEngine) return;
    const cleaner = blockScrollLeftAndRight(chartEngine);

    return cleaner;
  }, [chartEngine, selectedTickerId]);

  useEffect(() => { return cleanup; }, []);


  return (
    <>
    <div className='scroll-graph'>
      <div className='chart-price'>
        <cq-context ref={containerRef} class='sPD-chart-view' aria-hidden></cq-context>
      </div>
      <div id='stxEventPrototype' className='myEvents' ref={eventRef}></div>
      </div>
      <LegendWrapper
        initialData={legends}
        selectedTickerId={selectedTickerId}
        clickedLegend={clickedLegend}
        ariaLabel={i18n.translate('sharePriceDevelopmentLegend')}
      ></LegendWrapper>
    </>
  );
};

const LegendWrapper = ({ initialData, selectedTickerId, clickedLegend, ariaLabel }) => {

  return (
    <>
      <div className='legends' role="group" aria-label={ariaLabel}>
        {initialData !== null &&
          initialData.sort(dynamicSort('order')).map((item) => {
            return (
              <Fragment key={item.instrumentId}>
                <Legend
                  instid={item.instrumentId}
                  value={item.tickerName}
                  label={<TickerName instrumentId={item.instrumentId} marketAbbreviation={item.marketAbbreviation} shareName={item.shareName} />}
                  clickedLegend={clickedLegend}
                  isSelected={item.isSelected}
                  color={item.color}
                  isMainLegend={selectedTickerId === item.instrumentId}
                  labelText={getTickerName({instrumentId: item.instrumentId, marketAbbreviation: item.marketAbbreviation, shareName: item.shareName})}
                />
              </Fragment>
            );
          })}
      </div>
    </>
  );
};
export default ChartPriceDevelopment;
