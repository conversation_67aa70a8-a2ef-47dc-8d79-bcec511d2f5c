import { CIQ } from '../chartiq-import';

// const liftFunc = CIQ.UI.UIManager.prototype.lift;
// CIQ.UI.UIManager.prototype.lift = function (element) {
//   var cqStudyInput = element.closest('cq-study-input');
//   if (cqStudyInput && cqStudyInput.getAttribute('fieldname') === 'Moving Average Type') {
//     var n = element;
//     element.remember = {
//       parentNode: n.parentNode,
//       css: {}
//     };
//   } else {
//     liftFunc.call(this, element);
//   }
// };

const connectedCallback = CIQ.UI.UIManager.prototype.connectedCallback;
CIQ.UI.UIManager.prototype.connectedCallback = function() {
  CIQ.safeClickTouch(document.documentElement, () => {
    this.closeTopMenu();
  });
  return connectedCallback.apply(this, arguments);
};