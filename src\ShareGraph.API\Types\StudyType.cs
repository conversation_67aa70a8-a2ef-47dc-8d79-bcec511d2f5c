using Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Studies;
using Euroland.FlipIT.ShareGraph.API.Resolvers;
using HotChocolate.Types;

namespace Euroland.FlipIT.ShareGraph.API.Types
{
  public class StudyType : ObjectType<Studies>
  {
    protected override void Configure(IObjectTypeDescriptor<Studies> descriptor)
    {
      descriptor
          .Field(t => t.MACD)
          .ResolveWith<StudiesResolver>(t => t.GetMACD(default!));

      descriptor
          .Field(t => t.RSI)
          .ResolveWith<StudiesResolver>(t => t.GetRSI(default!));

      descriptor
          .Field(t => t.BollingerBands)
          .ResolveWith<StudiesResolver>(t => t.GetBollingerBands(default!));

      descriptor
          .Field(t => t.IchimokuClouds)
          .ResolveWith<StudiesResolver>(t => t.GetIchimokuClouds(default!));

      descriptor
          .Field(t => t.Stochastics)
          .ResolveWith<StudiesResolver>(t => t.GetStochastics(default!));

      descriptor
          .Field(t => t.TotalReturn)
          .ResolveWith<StudiesResolver>(t => t.GetTotalReturn(default!));

      descriptor
        .Field(t => t.ADX)
        .ResolveWith<StudiesResolver>(t => t.GetADX(default!));

      descriptor
       .Field(t => t.Volume)
       .ResolveWith<StudiesResolver>(t => t.GetVolume(default!));

      descriptor
      .Field(t => t.VolumeUnderlay)
      .ResolveWith<StudiesResolver>(t => t.GetVolumeUnderlay(default!));

    }
  }
}
