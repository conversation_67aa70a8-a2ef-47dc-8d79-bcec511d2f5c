﻿CREATE TABLE WatchList (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL,
    Username NVARCHAR(255) NOT NULL,
	CreatedDate DATETIME2 NOT NULL DEFAULT SYSDATETIME(),
    ModifiedDate DATETIME2 NULL
);

CREATE TABLE WatchListDetail (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    WatchListId UNIQUEIDENTIFIER NOT NULL,
    InstrumentId INT NOT NULL,
    CONSTRAINT FK_WatchListDetail_WatchList 
        FOREIGN KEY (WatchListId)
        REFERENCES WatchList(Id)
        ON DELETE CASCADE
);
