import { getCustomPhraseTicker } from '../helper';

export default class SharePricePerformance {
  constructor(parms = {}) {
    const params = getCustomPhraseTicker(parms);
    Object.assign(this, params);
    this.open = params.open || 0;
    this.last = params.last || 0;
    this.change = params.change || 0;
    this.changePercentage = params.changePercentage || 0;
    this.high = params.high || 0;
    this.low = params.low || 0;
    this.volume = params.volume || 0;
    this.currency = params.currency || '';
    this.countdownToTheClosingBell = params.countdownToTheClosingBell || 0;
    this.marketStatus = params.marketStatus || '';
  }
}
