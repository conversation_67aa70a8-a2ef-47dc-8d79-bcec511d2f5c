import {useMemo} from 'react';
import useIndices from '../../customHooks/useIndices';
import i18n from '../../services/i18n';
import AccessibleCheckbox from './AccessibleCheckbox';
import TickerName from '../TickerName';

const IndiciesCheckbox = ({ value = [], onChange }) => {
  const [{ indices }] = useIndices();

  const options = useMemo(
    () =>
    indices.map((item) => ({
        ...item,
        title: (
          <TickerName
            marketAbbreviation={item.marketAbbreviation}
            shareName={item.shareName}
            instrumentId={item.instrumentId}
          />
        )
      })),
    [indices]
  );

  if(!indices?.length) return null;
  return (
    <>
      <h4 id="selectindices">{i18n.translate('selectIndices')}</h4>
      <AccessibleCheckbox id="cklIndices" options={options} value={value} onChange={onChange} />
    </>
  );
};

export default IndiciesCheckbox;
