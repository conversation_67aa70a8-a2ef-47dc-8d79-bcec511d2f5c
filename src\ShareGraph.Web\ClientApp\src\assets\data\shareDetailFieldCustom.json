{"TIME": {"dataField": "lastUpdatedDate", "label": "time<PERSON><PERSON><PERSON>"}, "CURRENCY": {"dataField": "currencyCode", "label": "currencyLabel"}, "MARKET": {"dataField": "marketName", "label": "marketLabel"}, "MARKET_STATUS": {"dataField": "marketStatus", "label": "marketStatusLabel"}, "ISIN ": {"dataField": "iSIN", "label": "isinLabel"}, "SYMBOL": {"dataField": "ticker", "label": "symbol<PERSON><PERSON><PERSON>"}, "BID": {"dataField": "bid", "label": "bidLabel"}, "BID_SIZE": {"dataField": "bidSize", "label": "bidSizeLabel"}, "ASK": {"dataField": "ask", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "ASK_SIZE": {"dataField": "askSize", "label": "askSizeLabel"}, "OPEN": {"dataField": "open", "label": "openLabel"}, "LAST": {"dataField": "last", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "CHANGE": {"dataField": "change", "label": "changeLabel"}, "CHANGE_PERCENT": {"dataField": "changePercentage", "label": "changePercentLabel"}, "HIGH": {"dataField": "high", "label": "highLabel"}, "LOW": {"dataField": "low", "label": "lowLabel"}, "VOLUME": {"dataField": "volume", "label": "volumeLabel"}, "TOTAL_TRADES": {"dataField": "totalTrades", "label": "totalTradesLabel"}, "PREVIOUS_CLOSE": {"dataField": "prevClose", "label": "previousCloseLabel"}, "YTD_HIGH": {"dataField": "highYTD", "label": "ytdHighLabel"}, "YTD_LOW": {"dataField": "lowYTD", "label": "ytdLowLabel"}, "WEEKS_52_HIGH": {"dataField": "highest52w", "label": "weeks52HighLabel"}, "WEEKS_52_LOW": {"dataField": "lowest52w", "label": "weeks52LowLabel"}, "ALL_TIME_HIGH": {"dataField": "allTimeHigh", "label": "allTimeHighLabel"}, "ALL_TIME_LOW": {"dataField": "allTimeLow", "label": "allTimeLowLabel"}, "YTD_PERCENT": {"dataField": "yTD", "label": "ytdPercentLabel"}, "WEEKS_52_PERCENT": {"dataField": "percent52W", "label": "weeks52PercentLabel"}, "LIST": {"dataField": "listName", "label": "listLabel"}, "INDUSTRY": {"dataField": "industry", "label": "industryLabel"}, "NUMBER_OF_SHARES": {"dataField": "noShares", "label": "numberOfSharesLabel"}, "MARKET_CAP": {"dataField": "marketCap", "label": "marketCapLabel"}, "LOT_SIZE": {"dataField": "lotSize", "label": "lotSizeLabel"}, "P_E": {"dataField": "pE", "label": "pe<PERSON><PERSON><PERSON>"}, "AVERAGE_PRICE": {"dataField": "vWAP", "label": "averagePriceLabel"}, "TURNOVER": {"dataField": "todayTurnover", "label": "turnoverLabel"}}