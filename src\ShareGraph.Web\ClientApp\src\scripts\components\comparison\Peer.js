import { useRef, useContext, useEffect, useCallback } from 'react';
import { useSelector, useDispatch, useStore } from 'react-redux';

import { AppContext } from '../../AppContext';
import { PeerItem } from './PeerItem';
import { selectPeers } from '../../actions/peerActions';
import useLayoutCarousel from '../../customHooks/useLayoutCarousel';
import TinySlider from '../CustomeCarousel/TinySlider';
import useStocksRealtime from '../../real-time/useStockRealtime';
import {updateStock} from '../../actions';
import isSameDayWithLastTick from '../../real-time/isSameDayWithLastTick';
const appSettings = window.appSettings;

export const Peer = () => {
  const store = useStore();
  const settings = useContext(AppContext);
  const selectedPeerIds = useSelector(state => state.peers.selectedPeerIds);
  const peerContainerRef = useRef();
  const tickerAnimation = settings.peers.animation || 'fade';
  const dispatch = useDispatch();
  const data = useSelector(state => state.peers.instruments);

  const refreshing = useSelector(state => state.peers.refreshing);
  const [{ dataOrderedCarousel }] = useLayoutCarousel(peerContainerRef, {
    data,
    settings: settings.peers.peers
  });

  useEffect(() => {
    if (!refreshing) return;

    if (peerContainerRef.current) {
      const peerContainer = peerContainerRef.current;
      setTimeout(() => {
        peerContainer.classList.add('loading');
        setTimeout(() => {
          peerContainer.classList.remove('loading');
        }, 1000);
      }, 50);
    }
  }, [peerContainerRef, refreshing]);

  const onPeerSelectedChange = instrumentId => {
    if (instrumentId) {
      dispatch(selectPeers(instrumentId));
    }
  };

  useStocksRealtime(function(data) {
    if(!isSameDayWithLastTick({stockId: data.id, date: data.date, store })) return;
    dispatch(updateStock(data.id, { close: data.price, date: data.date}));
  }, settings.peers.peers.map(item => item.id));

  return (
    <div
      ref={peerContainerRef}
      className={`peer__inner comparison__inner comparison__animation--${tickerAnimation.toLowerCase()}`}
    >
      <TinySlider isRtl={appSettings.isRtl}>
        {dataOrderedCarousel.map((itm, indexWrap) => {
          return (
            <div key={indexWrap}>
              {itm.map((item, index) => {
                return (
                  <div className="tiny-slider__item" key={index}>
                    <div>
                      <PeerItem
                        isSelected={selectedPeerIds.includes(item.instrumentId)}
                        onItemClick={onPeerSelectedChange}
                        data={item}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })}
      </TinySlider>
    </div>
  );
};
