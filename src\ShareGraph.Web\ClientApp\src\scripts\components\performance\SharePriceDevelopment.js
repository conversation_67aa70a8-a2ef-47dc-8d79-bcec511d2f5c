import ChartPriceDevelopment from './ChartPriceDevelopment/ChartPriceDevelopment';
import { useEffect, useContext, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchSharePriceDevelopment } from '../../actions/SharePriceDevelopmentAction';
import {
  dynamicSort,
  getCustomPhraseTicker,
  getCustomPhraseIndices,
  getCustomPhrasePeer,
  convertChangePercentDecimal
} from '../../helper';
import i18n from '../../services/i18n';
import { AppContext } from '../../AppContext';
import { Skeleton } from '../Skeleton';
import { TableV2 } from '../commons/tableV2/Table';
import TickerName from '../TickerName';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';

export const SharePriceDevelopment = ({
  sPDByYearSetting,
  format,
  refreshTime
}) => {
  const { formatNumberByInstrument, formatNumberChangeByInstrument } =
    useFormatNumberByInstrument();
  const settings = useContext(AppContext);
  const fetchLoading = useSelector(
    (state) => state.sharePriceDevelopment.loading
  );
  const sharePriceDevelopmentdata = useSelector(
    (state) => state.sharePriceDevelopment.instruments
  );
  const dispatch = useDispatch();
  const [datasGraph, setDatasGraph] = useState([]);
  const [dataFortable, setDatasTable] = useState([]);

  useEffect(() => {
    function fetchData() {
      dispatch(fetchSharePriceDevelopment());
    }
    fetchData();
    const timer = setInterval(() => {
      fetchData();
    }, refreshTime * 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [dispatch, refreshTime]);

  useChangeQuoteCurrency(() => {
    dispatch(fetchSharePriceDevelopment());
  });

  const columnsAvailable = [
    {
      columnDisplay: i18n.translate('sharesLabel'),
      displayShortLabel: i18n.translate('sharesLabel'),
      fieldName: 'shareName'
    },
    {
      columnDisplay: i18n.translate('currencyLabel'),
      displayShortLabel: i18n.translate('currencyLabel'),
      fieldName: 'currencyCode'
    },
    {
      columnDisplay: i18n.translate('lastLabel'),
      displayShortLabel: i18n.translate('lastLabel'),
      fieldName: 'last'
    },
    {
      columnDisplay: i18n.translate('changeLabel'),
      displayShortLabel: i18n.translate('changeLabel'),
      fieldName: 'change'
    },
    {
      columnDisplay: i18n.translate('oneWeekChangePercentLabel'),
      displayShortLabel: i18n.translate('oneWeekShortLabel'),
      fieldName: 'week'
    },
    {
      columnDisplay: i18n.translate('oneMonthChangePercentLabel'),
      displayShortLabel: i18n.translate('oneMonthShortLabel'),
      fieldName: 'month'
    },
    {
      columnDisplay: i18n.translate('threeMonthChangePercentLabel'),
      displayShortLabel: i18n.translate('threeMonthShortLabel'),
      fieldName: 'threeMonthChange'
    },
    {
      columnDisplay: i18n.translate('sixMonthChangePercentLabel'),
      displayShortLabel: i18n.translate('sixMonthShortLabel'),
      fieldName: 'sixMonthsChange'
    },
    {
      columnDisplay: i18n.translate('ytdChangePercentLabel'),
      displayShortLabel: i18n.translate('ytdShortLabel'),
      fieldName: 'yTD'
    },
    {
      columnDisplay: i18n.translate('weeks52ChangePercentLabel'),
      displayShortLabel: i18n.translate('weeks52ShortLabel'),
      fieldName: 'percent52W'
    },
    {
      columnDisplay: i18n.translate('years3ChangePercentLabel'),
      displayShortLabel: i18n.translate('years3ShortLabel'),
      fieldName: 'threeYearsChange'
    },
    {
      columnDisplay: i18n.translate('years5ChangePercentLabel'),
      displayShortLabel: i18n.translate('years5ShortLabel'),
      fieldName: 'fiveYearsChange'
    },
    {
      columnDisplay: i18n.translate('years10ChangePercentLabel'),
      displayShortLabel: i18n.translate('years10ShortLabel'),
      fieldName: 'tenYearsChange'
    },
    {
      columnDisplay: i18n.translate('weeks52HighandLow'),
      displayShortLabel: i18n.translate('weeks52HighandLow'),
      fieldName: 'w52HighLow'
    },
    {
      columnDisplay: i18n.translate('allTimeHighLowLabel'),
      displayShortLabel: i18n.translate('allTimeHighLowLabel'),
      fieldName: 'allTimeHighLow'
    }
  ];
  const columnSettings =
    settings.performance.enableSharePriceDevelopmentColumns || [];
  const columnHeadings = columnsAvailable
    .filter((p) => columnSettings.includes(p.fieldName.toUpperCase()))
    .map((item) => ({
      ...item,
      order: columnSettings.indexOf(item.fieldName.toLocaleUpperCase())
    }))
    .sort(dynamicSort('order'));
  const indicatorColumns = [
    'change',
    'week',
    'month',
    'threeMonthChange',
    'sixMonthsChange',
    'yTD',
    'percent52W',
    'threeYearsChange',
    'fiveYearsChange',
    'tenYearsChange'
  ];

  const graphColumnsAvailable = [
    {
      displayLongLabel: i18n.translate('oneWeekChangePercentLabel'),
      displayShortLabel: i18n.translate('oneWeekShortLabel'),
      fieldName: 'week'
    },
    {
      displayLongLabel: i18n.translate('oneMonthChangePercentLabel'),
      displayShortLabel: i18n.translate('oneMonthShortLabel'),
      fieldName: 'month'
    },
    {
      displayLongLabel: i18n.translate('threeMonthChangePercentLabel'),
      displayShortLabel: i18n.translate('threeMonthShortLabel'),
      fieldName: 'threeMonthChange'
    },
    {
      displayLongLabel: i18n.translate('sixMonthChangePercentLabel'),
      displayShortLabel: i18n.translate('sixMonthShortLabel'),
      fieldName: 'sixMonthsChange'
    },
    {
      displayLongLabel: i18n.translate('ytdChangePercentLabel'),
      displayShortLabel: i18n.translate('ytdShortLabel'),
      fieldName: 'yTD'
    },
    {
      displayLongLabel: i18n.translate('weeks52ChangePercentLabel'),
      displayShortLabel: i18n.translate('weeks52ShortLabel'),
      fieldName: 'percent52W'
    },
    {
      displayLongLabel: i18n.translate('years3ChangePercentLabel'),
      displayShortLabel: i18n.translate('years3ShortLabel'),
      fieldName: 'threeYearsChange'
    },
    {
      displayLongLabel: i18n.translate('years5ChangePercentLabel'),
      displayShortLabel: i18n.translate('years5ShortLabel'),
      fieldName: 'fiveYearsChange'
    },
    {
      displayLongLabel: i18n.translate('years10ChangePercentLabel'),
      displayShortLabel: i18n.translate('years10ShortLabel'),
      fieldName: 'tenYearsChange'
    }
  ];

  const graphColumsHeadings = graphColumnsAvailable.filter((p) =>
    settings.performance.enableSharePriceDevelopmentColumns.includes(
      p.fieldName.toUpperCase()
    )
  );

  const changeSPDPercentage = sharePriceDevelopmentdata.map((item) => {
    let datas = { ...item };
    datas.changePercentList = graphColumsHeadings.map((graphhead) => {
      return {
        changePercent: item[graphhead.fieldName],
        longLabel: graphhead.displayLongLabel,
        shortLabel: graphhead.displayShortLabel
      };
    });
    return datas;
  });

  const getCustomPhareByType = (type, item) => {
    switch (type) {
      case 'T':
        return getCustomPhraseTicker(item);
      case 'P':
        return getCustomPhrasePeer(item);
      case 'I':
        return getCustomPhraseIndices(item);
    }
  };

  const _formatDataGraph = (graphDatas) => {
    if (graphDatas.length === 0 || sPDByYearSetting.length === 0) return [];

    return graphDatas.map((item) => {
      const data = item.changePercentList.map((x, idx) => {
        let close = x.changePercent ? x.changePercent : 0;
        let day = idx + 2;
        if (day < 10) {
          day = `0${day}`;
        }
        return {
          DT: new Date(`2020-01-${day}`),
          Close: close,
          longLabel: x.longLabel,
          tickerName: item.shareName,
          shortLabel: x.shortLabel
        };
      });
      const itemSetting = sPDByYearSetting.find(
        (x) => x.id === item.instrumentId
      );
      const { color, type, order } = itemSetting;
      const colorSetting = color || 'gray';
      let itemCustom = getCustomPhareByType(type, item);
      return {
        instrumentId: item.instrumentId,
        tickerName: itemCustom.tickerName,
        symbol: itemCustom.tickerName,
        order: order,
        color: colorSetting,
        data: data,
        changePercentList: item.changePercentList,
        shareName: item.shareName,
        marketAbbreviation: item.marketAbbreviation
      };
    });
  };

  const _normalizeDataTable = (item) => {
    const itemSetting = sPDByYearSetting.find(
      (x) => x.id === item.instrumentId
    );
    const { order, type } = itemSetting;
    let itemCustom = getCustomPhareByType(type, item);
    const data = { ...item };
    const instrumentId = item.instrumentId;
    data.order = order;
    data.shareName = [
      {
        value: itemCustom.tickerName,
        display: (
          <TickerName
            instrumentId={itemCustom.instrumentId}
            marketAbbreviation={itemCustom.marketAbbreviation}
            shareName={itemCustom.shareName}
          />
        )
      }
    ];
    data.currencyCode = [
      { value: item.currencyCode, display: item.currencyCode }
    ];
    data.last = [
      {
        value: item.last,
        display: formatNumberByInstrument(item.last, instrumentId)
      }
    ];
    data.change = [
      {
        value: item.change,
        display: formatNumberChangeByInstrument(item.change || 0, instrumentId)
      }
    ];
    data.w52HighLow = [
      {
        value: item.high52W,
        display: formatNumberByInstrument(item.high52W || 0, instrumentId),
        lable: i18n.translate('h')
      },
      {
        value: item.low52W,
        display: formatNumberByInstrument(item.low52W || 0, instrumentId),
        lable: i18n.translate('l')
      }
    ];
    data.allTimeHighLow = [
      {
        value: item.allTimeHigh,
        display: formatNumberByInstrument(item.allTimeHigh || 0, instrumentId),
        lable: i18n.translate('h')
      },
      {
        value: item.allTimeLow,
        display: formatNumberByInstrument(item.allTimeLow || 0, instrumentId),
        lable: i18n.translate('l')
      }
    ];
    data.week = [
      {
        value: item.week,
        display: convertChangePercentDecimal(item.week || 0) + '%'
      }
    ];
    data.month = [
      {
        value: item.month,
        display: convertChangePercentDecimal(item.month || 0) + '%'
      }
    ];
    data.threeMonthChange = [
      {
        value: item.threeMonthChange,
        display: convertChangePercentDecimal(item.threeMonthChange || 0) + '%'
      }
    ];
    data.sixMonthsChange = [
      {
        value: item.sixMonthsChange,
        display: convertChangePercentDecimal(item.sixMonthsChange || 0) + '%'
      }
    ];
    data.yTD = [
      {
        value: item.yTD,
        display: convertChangePercentDecimal(item.yTD || 0) + '%'
      }
    ];
    data.percent52W = [
      {
        value: item.percent52W,
        display: convertChangePercentDecimal(item.percent52W || 0) + '%'
      }
    ];
    data.threeYearsChange = [
      {
        value: item.threeYearsChange,
        display: convertChangePercentDecimal(item.threeYearsChange || 0) + '%'
      }
    ];
    data.fiveYearsChange = [
      {
        value: item.fiveYearsChange,
        display: convertChangePercentDecimal(item.fiveYearsChange || 0) + '%'
      }
    ];
    data.tenYearsChange = [
      {
        value: item.tenYearsChange,
        display: convertChangePercentDecimal(item.tenYearsChange || 0) + '%'
      }
    ];

    return data;
  };

  const tableSummary = i18n.translate('sPDtableSummaryText');
  const tableCaption = i18n.translate('sPDtableCaptionText');
  useEffect(() => {
    if (!fetchLoading) {
      const dataFortable = sharePriceDevelopmentdata.map((item) =>
        _normalizeDataTable(item)
      );
      const dataSorting = dataFortable.sort(dynamicSort('order'));
      format === 'GRAPH'
        ? setDatasGraph(_formatDataGraph(changeSPDPercentage))
        : setDatasTable(dataSorting);
    }
  }, [sharePriceDevelopmentdata, format]);
  return (
    <>
      {fetchLoading && <Skeleton style={{ height: '500px' }}></Skeleton>}
      {!fetchLoading && format === 'GRAPH' && datasGraph.length !== 0 && (
        <div
          id='spd-graph'
          role='tabpanel'
          aria-labelledby='tab-spd-graph'
          tabIndex={-1}
          hidden={format !== 'GRAPH'}
          className='spd-graph'
        >
          <ChartPriceDevelopment datas={datasGraph}></ChartPriceDevelopment>
        </div>
      )}
      {!fetchLoading && format === 'TABLE' && (
        <div
          id='spd-table'
          role='tabpanel'
          aria-labelledby='tab-spd-table'
          tabIndex={-1}
          hidden={format !== 'TABLE'}
          className='table-responsive'
        >
          <TableV2
            loading={fetchLoading}
            className='performance--table sharePricedevelopment'
            datas={dataFortable}
            headings={columnHeadings}
            indicatorColumns={indicatorColumns}
            summary={tableSummary}
            caption={tableCaption}
          />
        </div>
      )}
      {/* {!fetchLoading && format === 'TABLE' && <div className='table-responsive'><Table className='performance--table sharePricedevelopment' datas={dataFortable} headings={columnHeadings} indicatorColumns={indicatorColumns} summary={tableSummary} caption={tableCaption} /></div>} */}
    </>
  );
};
