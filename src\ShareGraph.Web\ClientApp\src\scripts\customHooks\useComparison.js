import { useCallback, useContext, useEffect, useState } from 'react';
import { COMPARISON_SWITCH_TYPE } from '../common';
import i18n from '../services/i18n';
import { AppContext } from '../AppContext';
import { useDispatch } from 'react-redux';
import { fetchIndices, refreshIndices } from '../actions/indicesActions';
import { fetchPeers, refreshPeers } from '../actions/peerActions';
import { safeInterval } from '../utils';

/**
 * @returns {{
*  comparisonType: string,
*  handleSwitchFormat: (type: string) => void,
*  enableComparisonTypes: Array<{
*    type: string,
*    text: string,
*    isDefaultSelected: boolean,
*    isEnabled: boolean
*  }>,
*  isType: (type: string) => boolean
* }}
*/
const useComparison = () => {
    const settings = useContext(AppContext);

    const comparisonTypes = [
        {
          type: COMPARISON_SWITCH_TYPE.PEER_TYPE,
          text:  i18n.translate('peers') || 'PEERS',
          isDefaultSelected: true,
          isEnabled: settings.peers.peers.length > 0,
          id: 'peers'
        },
        {
          type: COMPARISON_SWITCH_TYPE.INDICES_TYPE,
          text:  i18n.translate('indices') || 'INDICES',
          isDefaultSelected: false,
          isEnabled: settings.indices.indices.length > 0,
          id: 'indices'
        }
      ];
      const enableComparisonTypes = comparisonTypes.filter(t => t.isEnabled);
      const [comparisonType, setComparisonType] = useState(() => {
        const type = enableComparisonTypes.find(c => c.isDefaultSelected && c.isEnabled) || enableComparisonTypes[0];
        return type?.type;
      });
      const dispatch = useDispatch();
      const REFRESH_INTERVAL = settings.tickerRefreshSeconds ? settings.tickerRefreshSeconds * 1000  : 10000;

      useEffect(() => {
        if(isType(COMPARISON_SWITCH_TYPE.PEER_TYPE)){
          dispatch(fetchPeers());
          const cancelInterval = safeInterval(() => dispatch(refreshPeers()), REFRESH_INTERVAL);
          return function () {
            cancelInterval.cancel();
          };
        }else if(isType(COMPARISON_SWITCH_TYPE.INDICES_TYPE)){
          dispatch(fetchIndices());
          const cancelInterval = safeInterval(() => dispatch(refreshIndices()), REFRESH_INTERVAL);
          return function () {
            cancelInterval.cancel();
          };
        }
      }, [dispatch, comparisonType, isType]);

      const handleSwitchFormat = (type) => {
        setComparisonType(type);
      };

      const isType = useCallback(
        (type) => comparisonType === type,
        [comparisonType]
      );

      return { comparisonType, handleSwitchFormat, enableComparisonTypes, isType };
};

export default useComparison;