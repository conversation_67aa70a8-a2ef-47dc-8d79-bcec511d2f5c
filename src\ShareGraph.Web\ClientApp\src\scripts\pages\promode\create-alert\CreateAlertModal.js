import { useEffect, useState } from "react";
import "./CreateAlertModal.scss";
import { useSelectedTicker } from "../../../customHooks/useTickers";

function CreateAlertModal({ isOpen, onClose }) {
  const selectedInstrument = useSelectedTicker(); 
  console.log(selectedInstrument);
  
  useEffect(() => {
    const handleAlertCreatedSuccess = () => {   
      onClose();
    };
    if (window.EurolandAppContext) {
      window.EurolandAppContext.on("alert-created-success", handleAlertCreatedSuccess);
    }
    return () => {
      if (window.EurolandAppContext) {
        window.EurolandAppContext.off("alert-created-success", handleAlertCreatedSuccess);
      }
    };
  }, [onClose]);

  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleOverlayKeyDown = (e) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  return (
    <div
      className="create-alert-modal-overlay"
      onClick={handleOverlayClick}
      onKeyDown={handleOverlayKeyDown}
      role="button"
      aria-label="Close modal"
      tabIndex={0}
    >
      <div className="create-alert-modal">
        <div className="create-alert-modal__header">
          <h2>Create Alert</h2>
          <button className="create-alert-modal__close-btn" onClick={onClose}>
            &times;
          </button>
        </div>

        <div className="create-alert-modal__body">
          <div className="create-alert-modal__content">
            {/* Empty content - ready for future implementation */}
            <div className="create-alert-modal__empty-content">
             <euroland-create-share-alert instrumentId={selectedInstrument.instrumentId} name={selectedInstrument.shareName}/>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateAlertModal;

export const CreateAlertButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(true);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  
  return (
    <>
      <cq-toggle
        feature="tooltip"
        class="ciq-pop-out"
        onClick={handleOpenModal}
        onKeyUp={(e) => e.key === "Enter" && handleOpenModal(e)}
      >
        <i class="fs-alert"></i>
        <cq-tooltip>Create Alert</cq-tooltip>
      </cq-toggle>
      <CreateAlertModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
};
