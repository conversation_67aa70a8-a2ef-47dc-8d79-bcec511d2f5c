import { useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { fetchShareDetails, fetchTickers, selectTicker } from '../actions';
import { AppContext } from '../AppContext';
import SharePricePerformance from '../entities/SharePricePerformance';
import { dynamicSort } from '../helper';

export const useSelectedTicker = () => {
  const settings = useContext(AppContext);
  const dispatch = useDispatch();
  const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);

  useEffect(() => {
    if (!settings || !settings.instruments || selectedInstrumentId) return;
    const getDefaultSelectedTickerId = () => {
      const tickerDefault = [...settings.instruments].sort(dynamicSort('order')).find(x => x.default);
      return tickerDefault ? tickerDefault.id : settings.instruments[0].id;
    };
    dispatch(selectTicker(getDefaultSelectedTickerId()));
  }, [settings.instruments]);
  return { selectedInstrumentId };
};

const useSharePricePerformance = () => {
  const settings = useContext(AppContext);
  const instruments = useSelector(state => state.tickers.instruments);
  const loading = useSelector(state => state.tickers.loading);
  const { selectedInstrumentId } = useSelectedTicker();
  const dispatch = useDispatch();

  useEffect(() => {
    if (!settings) return;
    if ((instruments || []).some(instrument => !!instrument.marketName)) return;

    dispatch(fetchTickers());

    if (settings.shareDetails.enabled && settings.shareDetails.shareDataItems) {
      dispatch(fetchShareDetails());
    }
  }, [settings.shareDetails.shareDataItems, settings.shareDetails.enabled]);

  const onTickerSelected = instrumentId => {
    if (!instrumentId) return;
    dispatch(selectTicker(instrumentId));
  };

  return [
    {
      selectedSharePrice: new SharePricePerformance(
        instruments.find(instrument => instrument.instrumentId === selectedInstrumentId)
      ),
      selectedInstrumentId,
      sharePrices: instruments,
      loading
    },
    {
      onTickerSelected
    }
  ];
};

export default useSharePricePerformance;
