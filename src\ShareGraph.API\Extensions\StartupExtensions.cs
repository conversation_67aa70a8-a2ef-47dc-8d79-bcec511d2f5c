using System.IO;
using Euroland.FlipIT.CompanyStylesheetMiddleware;
using Euroland.FlipIT.ShareGraph.API.Infrastructure;
using Euroland.FlipIT.ShareGraph.API.Services;
using Euroland.NetCore.ToolsFramework.AzureSetting;
using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;

namespace Euroland.FlipIT.ShareGraph.API.Extensions
{
  public static class StartupExtensions
  {
    public static void AddAzureBlobClients(this IServiceCollection services, IConfiguration configuration)
    {
      var configurations = configuration.GetSection(nameof(Models.StorageConfiguration)).Get<Models.StorageConfiguration>();

      services.AddAzureClients(builder =>
      {
        builder
          .AddBlobServiceClient(configurations.AzureStorage.ConnectionString)
          .ConfigureOptions(options =>
          {
            var retry = configurations.AzureStorage.Retry;
            options.Retry.Mode = Azure.Core.RetryMode.Exponential;
            options.Retry.MaxRetries = retry.MaxRetry;
            options.Retry.MaxDelay = System.TimeSpan.FromSeconds(retry.MaxDelay);
          });
      });
    }

    public static IServiceCollection AddToolSetting(this IServiceCollection services, IHostEnvironment environment, IConfiguration configuration)
    {
      var (GeneralSettingRootPath, ToolSettingRootPath) = GetSettingRootPaths(configuration, environment);

      services.AddAppSetting<SettingOptions>(cfg =>
      {
        cfg.AppConfigurationFileName = "ShareGraph3.xml";
        cfg.GeneralSettingRootDirectory = GeneralSettingRootPath;
        cfg.ToolSettingRootDirectory = ToolSettingRootPath;

        cfg.ExcludePaths = new Microsoft.AspNetCore.Http.PathString[] {
            "/Config/Company",
            "/General/Config/Company",
            "/health"
        };
      });

      return services;
    }

    public static IServiceCollection AddTranslationService(this IServiceCollection services, IConfiguration configuration)
    {
      var translationDBContext = configuration.GetConnectionString("TranslationDbContext");
      return services.AddEurolandTranslationService(opt => opt.ConnectionString = translationDBContext);
    }

    public static IServiceCollection AddTickerTemplate(this IServiceCollection services, IHostEnvironment environment, IConfiguration configuration)
    {
      var templateFileProvider = default(IFileProvider);
      var (GeneralSettingRootPath, ToolSettingRootPath) = GetSettingRootPaths(configuration, environment);

      if (configuration.GetValue<bool>("UseAzureFileSettingStorage", false))
      {
        templateFileProvider = new AzureFile2Provider(new AzureFileSettingOptions
        {
          ConnectionString = configuration.GetConnectionString("ToolsConfig"),
          ShareName = configuration["AzureFileShareName"],
          RootDirectory = ToolSettingRootPath
        });
      }
      else
      {
        templateFileProvider = new PhysicalFileProvider(ToolSettingRootPath);
      }

      services.AddSingleton<ITickerTemplateService>(_ => new DefaultTickerTemplateService(templateFileProvider));

      return services;
    }

    public static IApplicationBuilder UseCompanyStyleSheet(this IApplicationBuilder app, IConfiguration configuration, IHostEnvironment environment)
    {
      var provider = new FileExtensionContentTypeProvider();
      var generalSettingFileProvider = default(IFileProvider);
      var toolSettingFileProvider = default(IFileProvider);
      var (GeneralSettingRootPath, ToolSettingRootPath) = GetSettingRootPaths(configuration, environment);

      // Not allow request to *.xml and *.html files inside the company folder.
      provider.Mappings.Remove(".xml");
      provider.Mappings.Remove(".html");

      if (configuration.GetValue<bool>("UseAzureFileSettingStorage", false))
      {
        generalSettingFileProvider = new AzureFile2Provider(new AzureFileSettingOptions

        {
          ConnectionString = configuration.GetConnectionString("ToolsConfig"),
          ShareName = configuration["AzureFileShareName"],
          RootDirectory = Path.Combine(GeneralSettingRootPath, "Company")
        });
        toolSettingFileProvider = new AzureFile2Provider(new AzureFileSettingOptions
        {
          ConnectionString = configuration.GetConnectionString("ToolsConfig"),
          ShareName = configuration["AzureFileShareName"],
          RootDirectory = Path.Combine(ToolSettingRootPath, "Company")
        });
      }
      else
      {
        generalSettingFileProvider = new PhysicalFileProvider(Path.Combine(GeneralSettingRootPath, "Company"));
        toolSettingFileProvider = new PhysicalFileProvider(Path.Combine(ToolSettingRootPath, "Company"));
      }

      app.UseCompanyStylesheetStaticFiles(new CompanyStylesheetStaticFilesOptions()
      {
        ContentTypeProvider = provider,
        ServeUnknownFileTypes = false,
        FileProvider = generalSettingFileProvider,
        RequestPath = "/General/Config/Company",
        ServeNotFoundAsNoContentForFileTypes = new string[] { ".css" }
      });
      app.UseCompanyStylesheetStaticFiles(new CompanyStylesheetStaticFilesOptions()
      {
        ContentTypeProvider = provider,
        ServeUnknownFileTypes = false,
        FileProvider = toolSettingFileProvider,
        RequestPath = "/Config/Company"
      });

      return app;
    }

    private static (string, string) GetSettingRootPaths(IConfiguration configuration, IHostEnvironment environment)
    {
      var GeneralSettingRootPath = configuration["GeneralSettingsPath"];
      var ToolSettingRootPath = configuration["ToolSettingsPath"];
      var isAzureEnv = configuration.GetValue<bool>("UseAzureFileSettingStorage", false);
      if (!isAzureEnv && !System.IO.Path.IsPathRooted(GeneralSettingRootPath) && !GeneralSettingRootPath.StartsWith(@"\\"))
      {
        GeneralSettingRootPath = System.IO.Path.Combine(environment.ContentRootPath, GeneralSettingRootPath);
      }

      if (!isAzureEnv && !System.IO.Path.IsPathRooted(ToolSettingRootPath) && !GeneralSettingRootPath.StartsWith(@"\\"))
      {
        ToolSettingRootPath = System.IO.Path.Combine(environment.ContentRootPath, ToolSettingRootPath);
      }

      return (GeneralSettingRootPath, ToolSettingRootPath);
    }
  }
}
