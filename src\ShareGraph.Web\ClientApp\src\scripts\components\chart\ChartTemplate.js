import { useContext, useState } from 'react';

import i18n from '../../services/i18n';
import { AppContext } from '../../AppContext';


export default function ChartTemplate ({ additionalOptionsTypes = [], isPopout = false, onPopUp, onOpenCreateAlertModal }) {
  const settings = useContext(AppContext);
  const isEnabledChartTitle = !settings.chart.hideChartTitle;
 

  const enabledBlinkingPointer = settings.chart?.blinkingPricePointer?.enabled;

  const handleIframeLoaded = function (e) {
    e.target.closest('.modal').classList.remove('preloader');
  };

  const handleClickPopup = e => {
    if(e.nativeEvent instanceof CustomEvent) {
      return;
    }
    onPopUp(e);
  };

  const handleOpenCreateAlertModal = () => {
    onOpenCreateAlertModal();
  };

  return (
    <>
      <cq-color-picker>
        <cq-colors></cq-colors>
        <cq-overrides>
          <template>
            <div className='ciq-btn'></div>
          </template>
        </cq-overrides>
      </cq-color-picker>
      <div className="ciq-nav full-screen-hide" role="navigation">
        {/* <div className="sidenav-toggle ciq-toggles">
          <cq-toggle
            class="ciq-sidenav"
            cq-member="sidenav"
            cq-toggles="sidenavOn,sidenavOff"
            cq-toggle-classes="active,"
            keyboard-navigation="false"
          >
            <span></span>
            <cq-tooltip>More</cq-tooltip>
          </cq-toggle>
        </div> */}

        <div className="sidenav-toggle ciq-toggles">
          <cq-toggle class="ciq-sidenav" member="sidenav" toggles="sidenavOn,sidenavOff" toggle-classes="active," reader="More Options" tooltip="More" icon="morenav"></cq-toggle>
        </div>

        {/* <cq-menu class="ciq-search">
					<cq-lookup
						cq-keystroke-claim
						cq-uppercase
					></cq-lookup>
				</cq-menu> */}

        {/* <cq-side-nav cq-on="sidenavOn">
          <div className="icon-toggles ciq-toggles">
            <cq-toggle class="ciq-draw" cq-member="drawing">
              <span></span>
              <cq-tooltip>Draw</cq-tooltip>
            </cq-toggle> */}
            {/* <cq-info-toggle-dropdown>
							<cq-toggle class="ciq-CH" cq-member="crosshair">
								<span></span>
								<cq-tooltip>{i18n.translate('cCrosshair')}</cq-tooltip>
							</cq-toggle>

							{/* <cq-menu class="ciq-menu toggle-options collapse">
								<span></span>
								<cq-menu-dropdown>
									<cq-item cq-member="crosshair">
										{i18n.translate('cHideHeadsUpDisplay')}
										<span className="ciq-radio">
											<span></span>
										</span>
									</cq-item>

								</cq-menu-dropdown>
							</cq-menu>
						</cq-info-toggle-dropdown> */}

            {/* <cq-toggle class="ciq-CH" cq-member="crosshair">
              <span></span>
              <cq-tooltip>Crosshair (Alt + \)</cq-tooltip>
            </cq-toggle>

            <cq-info-toggle-dropdown>
              <cq-toggle class="ciq-HU" cq-member="headsUp">
                <span></span>
                <cq-tooltip>Info</cq-tooltip>
              </cq-toggle>

              <cq-menu class="ciq-menu toggle-options collapse tooltip-ui">
                <span></span>
                <cq-menu-dropdown>
                  <cq-item cq-member="headsUp-dynamic">
                    Show Dynamic Callout
                    <span className="ciq-radio">
                      <span></span>
                    </span>
                  </cq-item>
                  <cq-item cq-member="headsUp-floating">
                    Show Tooltip
                    <span className="ciq-radio">
                      <span></span>
                    </span>
                  </cq-item>
                  <cq-item cq-member="headsUp-static">
                    Show Heads-Up Static
                    <span className="ciq-radio">
                      <span></span>
                    </span>
                  </cq-item>
                </cq-menu-dropdown>
              </cq-menu>
            </cq-info-toggle-dropdown>
            <cq-toggle
              class="cq-drawing-annotation"
              cq-member="drawing-annotation"
              onClick={onDrawingAnnotation}
            >
              <span className='icon'></span>
              <cq-tooltip>
                <span>Annotation</span><br /><span className='shortcut'>(Alt + W)</span>
              </cq-tooltip>
            </cq-toggle> */}
            {/* <cq-toggle class="ciq-DT tableview-ui" cq-member="tableView">
							<span></span>
							<cq-tooltip>Table View</cq-tooltip>
						</cq-toggle> */}
            {/* {!isPopout ? (
              <cq-toggle class="ciq-pop-out" cq-member="fullScreenChart" onClick={onPopup}>
                <i className="fs-open-link"></i>
                <cq-tooltip>Pop out</cq-tooltip>
              </cq-toggle>
            ): <cq-toggle class="ciq-pop-in" cq-member="fullScreenChart" onClick={onPopin}>
            <i className="fs-pop-in"></i>
            <cq-tooltip>Pop in</cq-tooltip>
          </cq-toggle>}
            <div> </div>
          </div>
        </cq-side-nav> */}

        <cq-side-nav cq-on="sidenavOn">
          <div className="icon-toggles ciq-toggles">
            <cq-toggle class="ciq-draw" member="drawing" reader="Draw" tooltip="Draw" icon="draw" help-id="drawing_tools_toggle"></cq-toggle>
            <cq-toggle class="ciq-CH" config="crosshair" reader="Crosshair" tooltip="Crosshair (Alt + \)" icon="crosshair"></cq-toggle>
            {/* <cq-menu class="nav-dropdown toggle-options" reader="Crosshair Options" config="crosshair"></cq-menu> */}
            <cq-toggle class="ciq-HU" feature="tooltip" config="info" reader="Info" tooltip="Info" icon="info"></cq-toggle>
            <cq-menu config="info" class="nav-dropdown toggle-options" reader="Info Options"></cq-menu>
            <cq-toggle config="annotation" feature="tooltip" class="cq-drawing-annotation" tooltip="Annotation (Alt + W)" icon="" ></cq-toggle>
            {/* <cq-toggle class="ciq-DT" feature="tableview" member="tableView" reader="Table View" tooltip="Table View" icon="tableview"></cq-toggle> */}
            {!isPopout ? (
              <cq-toggle feature="tooltip" class="ciq-pop-out" onClick={handleClickPopup} onKeyUp={(e) => e.key === 'Enter' && handleClickPopup(e)}>
                <i className="fs-open-link"></i>
                <cq-tooltip>Pop out</cq-tooltip>
              </cq-toggle>
            ): <cq-toggle config="popIn" feature="tooltip" class="ciq-pop-in">
                <i className="fs-pop-in"></i>
                <cq-tooltip>Pop in</cq-tooltip>
              </cq-toggle>}
              <cq-toggle
                  feature="tooltip"
                  class="ciq-pop-out"
                  onClick={handleOpenCreateAlertModal}
                  onKeyUp={(e) => e.key === "Enter" && handleOpenCreateAlertModal(e)}
                >
                  <i class="fs-alert"></i>
                  <cq-tooltip>Create Alert</cq-tooltip>
              </cq-toggle>
          </div>
        </cq-side-nav>

        <div className="ciq-menu-section">
          <div className="ciq-dropdowns">
            {/* <cq-menu class="ciq-menu ciq-period">
              <span>
                <cq-clickable class="ciq-period-label" stxbind="Layout.periodicity">{i18n.translate('cPeriod1Day')}</cq-clickable>
              </span>
              <cq-menu-dropdown>
                <cq-menu-container cq-name="menuPeriodicity"></cq-menu-container>
              </cq-menu-dropdown>
            </cq-menu> */}

            {/* <cq-menu class="ciq-menu ciq-views collapse">
							<span>Views</span>
							<cq-menu-dropdown>
								<cq-views></cq-views>
							</cq-menu-dropdown>
						</cq-menu> */}
            {/* <cq-menu class="ciq-menu ciq-display collapse ciq-chart-types">
              <cq-clickable class="ciq-menu-select" stxbind="Layout.chartType" ciq-no-icon-text="Display">
                  <span ciq-menu-icon=""></span>
                  <span ciq-menu-text=""></span>
              </cq-clickable>
              <cq-menu-dropdown>
                <cq-menu-dropdown-section class="chart-types"> */}
                  {/* <cq-heading>Chart Style</cq-heading> */}
                  {/* <cq-menu-container cq-name="menuChartStyle"></cq-menu-container>
                </cq-menu-dropdown-section> */}
                {/* <cq-menu-dropdown-section class="chart-aggregations">
									<cq-menu-container cq-name="menuChartAggregates"></cq-menu-container>
								</cq-menu-dropdown-section> */}
              {/* </cq-menu-dropdown>
            </cq-menu> */}

            {/* <cq-menu class="ciq-menu ciq-studies collapse" cq-focus="input">
              <span>Indicator</span>
              <cq-menu-dropdown> */}
                {/* <cq-study-legend cq-no-close> */}
                  {/* <cq-section-dynamic>
                    <cq-heading>Current Indicator
                    <input style={{display: 'none'}} type="search" placeholder="Search" tabIndex="-1"/>
                    </cq-heading>
                    <cq-study-legend-content>
                      <template cq-study-legend="true">
                        <cq-item>
                          <cq-label class="click-to-edit" label></cq-label>
                          <div className="ciq-icon ciq-close"></div>
                        </cq-item>
                      </template>
                    </cq-study-legend-content>
                    <cq-placeholder>
                      <div stxtap="Layout.clearStudies()" className="ciq-btn sm" keyboard-selectable="true">
                        Clear All
                      </div>
                    </cq-placeholder>
                  </cq-section-dynamic> */}
                {/* </cq-study-legend> */}
                {/* <div className="scriptiq-ui">
									<cq-heading>ScriptIQ</cq-heading>
									<cq-item>
										<cq-clickable
											cq-selector="cq-scriptiq-editor"
											cq-method="open"
										>
											New Script
										</cq-clickable>
									</cq-item>
									<cq-scriptiq-menu></cq-scriptiq-menu>
									<cq-separator></cq-separator>
								</div> */}
                {/* <cq-heading cq-filter="" cq-filter-min="-1">
									Studies
								</cq-heading> */}
                {/* <cq-studies></cq-studies>
              </cq-menu-dropdown>
            </cq-menu> */}

            {/* <cq-menu class="ciq-menu stx-markers collapse">
              <span>Events</span>
              <cq-menu-dropdown>
                <cq-menu-container cq-name="menuChartEvents"></cq-menu-container>
              </cq-menu-dropdown>
            </cq-menu>

            <cq-menu class="ciq-menu ciq-preferences collapse">
              <span></span>
              <cq-menu-dropdown>
                <cq-menu-dropdown-section class="chart-preferences">
                  <cq-heading>Chart Preferences</cq-heading>
                  <cq-menu-container cq-name="menuChartPreferences"></cq-menu-container>
                  <cq-separator></cq-separator>
                </cq-menu-dropdown-section>
                <cq-menu-dropdown-section class="y-axis-preferences">
                  <cq-heading>Y-Axis Preferences</cq-heading>
                  <cq-menu-container cq-name="menuYAxisPreferences"></cq-menu-container>
                </cq-menu-dropdown-section>
              </cq-menu-dropdown>
            </cq-menu> */}

            <cq-menu class="nav-dropdown ciq-period" reader="Periodicity" config="period" text="" binding="Layout.periodicity"></cq-menu>
            <cq-menu class="nav-dropdown ciq-display" reader="Display" config="display" text="Display" binding="Layout.chartType" icon="" help-id="display_dropdown"></cq-menu>
            <cq-menu class="nav-dropdown ciq-studies alignright" cq-focus="input" config="studies" text="Indicator" icon="studies" responsive=""></cq-menu>
            <cq-menu class="nav-dropdown ciq-markers alignright" config="markers" text="Events" icon="events" responsive=""></cq-menu>
            <cq-menu class="nav-dropdown ciq-preferences alignright" reader="Preferences" config="preferences" text="" icon="preferences"></cq-menu>
          </div>

          {/* <div className="trade-toggles ciq-toggles">
            <cq-toggle class="tfc-ui sidebar stx-trade" cq-member="tfc">
              <span></span>
              <cq-tooltip>{i18n.translate('cTrade')}</cq-tooltip>
            </cq-toggle>
            <cq-toggle class="analystviews-ui stx-analystviews" cq-member="analystviews">
              <span></span>
              <cq-tooltip>{i18n.translate('cAnalystViews')}</cq-tooltip>
            </cq-toggle>
            <cq-toggle class="technicalinsights-ui stx-technicalinsights" cq-member="technicalinsights">
              <span></span>
              <cq-tooltip>{i18n.translate('cTechnicalInsights')}</cq-tooltip>
            </cq-toggle>
          </div> */}
        </div>
      </div>

      <cq-scriptiq class="scriptiq-ui"></cq-scriptiq>

      <cq-analystviews
        class="analystviews-ui"
        token="eZOrIVNU3KR1f0cf6PTUYg=="
        partner="1000"
        disabled
      ></cq-analystviews>

      <cq-technicalinsights uid="" lang="en" disabled></cq-technicalinsights>

      <div className={`ciq-chart-area ${isEnabledChartTitle ? 'chart-title-enabled' : ''}`}>
        <div className="ciq-chart">
          <cq-message-toaster
            defaultDisplayTime="10"
            defaultTransition="slide"
            defaultPosition="top"
          ></cq-message-toaster>

          <cq-palette-dock>
            <div className="palette-dock-container">
              <cq-drawing-palette
                class="palette-drawing grid palette-hide"
                docked="true"
                orientation="vertical"
                min-height="300"
                cq-drawing-edit="none"
              ></cq-drawing-palette>
              <cq-drawing-settings
                class="palette-settings"
                docked="true"
                hide="true"
                orientation="horizontal"
                min-height="40"
                cq-drawing-edit="none"
              ></cq-drawing-settings>
            </div>
          </cq-palette-dock>

          <div className="chartContainer">
            <stx-hu-tooltip>
              <stx-hu-tooltip-field field="DT">
                <stx-hu-tooltip-field-name>{i18n.translate('cHuTooltipDate')}</stx-hu-tooltip-field-name>
                <stx-hu-tooltip-field-value></stx-hu-tooltip-field-value>
              </stx-hu-tooltip-field>
              <stx-hu-tooltip-field field="Close">
                <stx-hu-tooltip-field-name></stx-hu-tooltip-field-name>
                <stx-hu-tooltip-field-value></stx-hu-tooltip-field-value>
                <cq-currency></cq-currency>
              </stx-hu-tooltip-field>
            </stx-hu-tooltip>
            {enabledBlinkingPointer && <cq-real-time-point></cq-real-time-point>}
            <cq-chartcontrol-group class="full-screen-show" cq-marker></cq-chartcontrol-group>

            {/* <cq-comparison-lookup></cq-comparison-lookup> */}

            <cq-study-legend marker-label="Plots" clone-to-panel="" filter="panel" button-remove="true" series="true" cq-marker="" heading="Plots"></cq-study-legend>
            <cq-loader class="spinner"></cq-loader>
          </div>
        </div>
      </div>

      <cq-abstract-marker cq-type="helicopter"></cq-abstract-marker>

      <cq-attribution></cq-attribution>

      <div className="cq-context-dialog">
        <cq-dialog>
          <cq-drawing-context></cq-drawing-context>
        </cq-dialog>
        {/* <cq-dialog>
					<cq-study-context></cq-study-context>
				</cq-dialog> */}
      </div>
      <div className="modal preloader" id="pressReleaseModal">
        <div className="modal__inner">
          <button className="modal__close">✖ Close</button>
          <iframe title="PressRelease Modal" className="modal__content" onLoad={handleIframeLoaded}></iframe>
        </div>
      </div>
      <div id="chartEventPrototype" className="event-marker">
        <span className="event-marker__content"></span>
        <span className="event-marker__tooltip">
          <div className="item">
            <strong></strong>
            <time></time>
          </div>
        </span>
      </div>
      <div id="chartEventPrototypeVideo" className="event-marker video">
        <span className="event-marker__content">
          <span id='chartEventPrototypeVideo' className="event-marker__content--icon"></span>
        </span>
        <span className='event-marker__stem'></span>

        <span className="event-marker__tooltip">
          <button className="event-marker__tooltip__close">✖</button>
          <div className="event-marker__tooltip--inner ">
            <div className="event-marker__item">
              {/* eslint-disable jsx-a11y/media-has-caption  */}
              <video src="" controls="controls" preload="none"></video>
              <div className='event-marker__item--info'>
                <p className='event-marker__item--title-wrapper'>
                  <span className='event-marker__item--title'></span>
                  {/* eslint-disable jsx-a11y/anchor-is-valid  */}
                  <a href='#' target="_blank" className='event-marker__item--transcription' title={i18n.translate('transcripts')}>
                    <span className="fs-transcription"></span>
                  </a>
                </p>
                <time></time>
              </div>
            </div>
          </div>
        </span>
      </div>

      <div id="chartEventPrototypePr" className="event-marker press-releases">
        <span className="event-marker__content"></span>

        <span className="event-marker__tooltip">
          <custom-scroll cq-no-resize>
            <div className="event-marker__tooltip--inner ">
              <div className="event-marker__item">
                <strong></strong>
                <time></time>
                <hr />
              </div>
            </div>
          </custom-scroll>
        </span>
      </div>
      <cq-side-panel></cq-side-panel>
    </>
  );
}
