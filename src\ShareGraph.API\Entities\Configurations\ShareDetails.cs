using System.Collections.Generic;

namespace Euroland.FlipIT.ShareGraph.API.Entities
{
  public class ShareDetails
  {
    public bool Enabled { get; set; }
    public List<string> ShareDataItems { get; set; }
    public bool DisplayOnSelectedTicker { get; set; }
    public string DisplayType { get; set; }
    public bool PartialDisplay { get; set; }
    public int NumberItemsInCollapsedMode { get; set; }
    public int NumberItemsInPrinting { get; set; }
    public string Print { get; set; }
    public string MarketCapDisplay { get; set; }
    public string TurnOverDisplay { get; set; }
    public string NumberOfShareDisplay { get; set; }
  }

  public class ShareDetailsConfig
  {
    public bool Enabled { get; set; }
    public string ShareDataItems { get; set; }
    public bool DisplayOnSelectedTicker { get; set; }
    public string DisplayType { get; set; }
    public bool PartialDisplay { get; set; }
    public int NumberItemsInCollapsedMode { get; set; }
    public int NumberItemsInPrinting { get; set; }
    public string Print { get; set; }
    public string MarketCapDisplay { get; set; }
    public string TurnOverDisplay { get; set; }
    public string NumberOfShareDisplay { get; set; }
  }
}
