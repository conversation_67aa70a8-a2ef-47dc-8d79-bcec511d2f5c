import { CIQ } from '../chartiq-import';

const CQDrawingSettings = CIQ.UI.components('cq-drawing-settings')[0].classDefinition;

CQDrawingSettings.prototype.setFontFamily = function (activator, fontFamily) {
  if (!this.context) return;
			this.activator = activator;
			const { stx } = this.context;

			if (stx.currentVectorParameters.annotation) {
				if (fontFamily == 'Default') {
          const style = stx.canvasStyle('stx_annotation');
					stx.currentVectorParameters.annotation.font.family = style?.fontFamily;
				} else {
					stx.currentVectorParameters.annotation.font.family = fontFamily;
				}
			}
			this['font-family'] = fontFamily;
			this.params.fontFamilySelection.setAttribute('text', fontFamily);
			this.emit();
};

CQDrawingSettings.prototype.sync = function(cvp) {
  this.syncing = true;
  const { stx } = this.context;

  if (!cvp) cvp = stx.currentVectorParameters;
  else
    stx.currentVectorParameters = CIQ.extend(
      stx.currentVectorParameters || {},
      cvp
    );

  const { params } = this;
  this.setLine(null, cvp.lineWidth, cvp.pattern);

  const style = stx.canvasStyle('stx_annotation');
  const font = cvp.annotation?.font || {};

  const initialSize = font.size || style.fontSize;
  this.setFontSize(null, initialSize);

  const dropdown = [...this.params.fontFamilySelection.childNodes]
    .find(node => node.nodeName.toLowerCase() === 'cq-dropdown');

  let initialFamily = 'Default';
  if (dropdown?.content) {
    const supportedFonts = dropdown.content.map(({ label }) => label);
    initialFamily = supportedFonts.includes(font.family) ? font.family : initialFamily;
    
  }
  this.setFontFamily(null, initialFamily);

  const initialFontStyle = (font && font.style) || style.fontStyle;
  const initialWeight = (font && font.weight) || style.fontWeight;
  this.setFontStyle('italic', initialFontStyle === 'italic');
  this.setFontStyle(
    'bold',
    initialWeight === 'bold' || initialWeight >= 700
  );

  this.toggleCheckbox(null, 'axis-label', cvp.axisLabel);
  this.toggleCheckbox(null, 'show-callout', cvp.showCallout);
  this.toggleCheckbox(null, 'span-panels', cvp.spanPanels);

  this.getColor({ node: params.fillColor }, 'fill');
  this.getColor({ node: params.lineColor }, 'line');

  params.cvpControllers.forEach((controller) => {
    controller.sync(cvp);
  });

  const { waveParameters } = params;
  if (waveParameters) {
    waveParameters.activate();
  }
  this.syncing = false;
};


CQDrawingSettings.prototype.tool = function (activator, toolName) {
  this.toolSettings(activator, toolName);
  this.setActiveToolLabel(this.context.stx.translateIf(toolName));
  this.sendMessage('changeToolSettings', {
    activator: activator,
    toolName: toolName
  });
};
