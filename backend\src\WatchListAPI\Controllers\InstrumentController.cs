﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WatchListAPI.Common;
using WatchListAPI.Services;

namespace WatchListAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class InstrumentController : ControllerBase
    {
        private readonly IInstrumentService _instrumentService;
        public InstrumentController(IInstrumentService instrumentService)
        {
            _instrumentService = instrumentService;
        }

        [HttpGet("find-all")]
        public async Task<IActionResult> GetInstrumentsAsync([FromQuery] PagingRequestBaseDto input)
        {
            var response = await _instrumentService.GetInstrumentsAsync(input);
            return Ok(response);
        }
    }
}
