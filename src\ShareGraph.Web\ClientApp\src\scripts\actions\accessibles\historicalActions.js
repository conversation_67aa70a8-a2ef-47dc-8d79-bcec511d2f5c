import fetchApi from '../../services/fetch-api';
import { appSettings } from '../../../appSettings';
import { convertParamsToString } from '../../helper';

function getHistoricalData(instrumentId, params = {}) {
  const fields = [
    'dateTime',
    'instrumentId',
    'totalVolume',
    'totalReturn',
    'lowestVolumeDate',
    'highestVolumeDate',
    'mA10',
    'mA20',
    'mA50',
    'firstPrice',
    'lastPrice',
    'change',
    'changePercentage',
    'highestPrice',
    'highestPriceDate',
    'lowestPrice',
    'lowestPriceDate',
    'dividendEvent',
    `compares {
      id
      name
      changePercentage
    }`
  ];
  return fetchApi(appSettings.sDataApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `query {
        historicalData(instrumentId: ${instrumentId}, ${convertParamsToString(params)}){
          ${fields.join(',')}
        }
      }`
    })
  }).then(res => res.json());
}

export const FETCH_HISTORICAL_BEGIN = 'FETCH_HISTORICAL_BEGIN';
export const FETCH_HISTORICAL_SUCCESS = 'FETCH_HISTORICAL_SUCCESS';
export const FETCH_HISTORICAL_FAILURE = 'FETCH_HISTORICAL_FAILURE';

export const fetchHistoricalBegin = () => ({
  type: FETCH_HISTORICAL_BEGIN
});

export const fetchHistoricalSuccess = (data = []) => ({
  type: FETCH_HISTORICAL_SUCCESS,
  payload: { data }
});

export const fetchHistoricalFailure = error => ({
  type: FETCH_HISTORICAL_FAILURE,
  payload: { error }
});

export function fetchHistorical(selectedInstrumentId, params) {
  return dispatch => {
    dispatch(fetchHistoricalBegin());

    return getHistoricalData(selectedInstrumentId, params)
      .then(json => {
        const data = json.data.historicalData;
        dispatch(fetchHistoricalSuccess(data));
        return json.data.instruments;
      })
      .catch(err => dispatch(fetchHistoricalFailure(err)));
  };
}
