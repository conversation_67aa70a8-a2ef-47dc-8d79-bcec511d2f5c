import {
  useQuery$,
} from "@preact-signals/query";
import { instrumentService } from "./instrumentService";

export const useInstrumentQueries = (page: number, pageSize: number, keyword: string) => {

  const instrumentsQuery = useQuery$(() => ({
    queryKey: ["instruments"],
    queryFn: () => instrumentService.searchInstruments(page, pageSize, keyword),
    staleTime: 1000 * 60 * 5, 
  }));



  return {
    instrumentsQuery,
  };
};
