import {useContext} from 'react';
import { appSettings } from '../../appSettings';
import i18n from '../services/i18n';
import {convertNumberNoSeparator, translateStringFormat} from '../helper';
import {ExtraButton} from './ExtraButton';
import ChartContext from '../context/ChartContext';
import { classNames } from '@euroland/libs';
import { useSelectedTicker } from '../customHooks/useTickers';
import { UserAgent } from '../customHooks/useResize';

export default function Footer() {
  const { shareType } = useContext(ChartContext);
  const {language, termsOfServiceURL, privacyPolicyURL, cookiePolicyURL, suppliedByLanguage, eurolandURL} = appSettings;
  const euroland = eurolandURL || 'https://www.euroland.com/';
  const termsOfService = termsOfServiceURL ||'https://tools.eurolandir.com/legal/terms/';
  const privacyPolicy = privacyPolicyURL ||'https://tools.eurolandir.com/legal/privacy/';
  const cookiePolicy = cookiePolicyURL ||'https://tools.eurolandir.com/legal/cookie/';
  const selectedTicker = useSelectedTicker();

  const isMobile = UserAgent.isMobile;
  return (
    <>
      {shareType === 'SHARE_GRAPH' ? (
        <ExtraButton />
      ) : null}
      <footer className="footer">
        <div className="footer-content supply-by-box">
          <div className="footer-content_primary supply-by">
              {i18n.translate('supplyBy')} ©{' '}
            <a className="footer__supplierInfoLink footer__logo" target="_blank" href={`${euroland}?SelectLanguage=${suppliedByLanguage || 'english'}`}
            aria-label={isMobile ? i18n.translate('logoAlternativeText') : undefined}
            >
              <img src='https://tools.euroland.com/tools/data/logo-transparent.png' alt={i18n.translate('logoAlternativeText')} aria-hidden={isMobile ? 'true' : undefined} />
            </a>
          </div>
          <p
            className={classNames('footer-content__dataDelayed', {
              'footer-content__dataDelayed--hidden': selectedTicker?.isRT
            })}
          >
            {translateStringFormat('dataDelayed', [convertNumberNoSeparator(15)])}
          </p>
        </div>
        <div className="cookies-box">
          <a href={`${termsOfService}?lang=${language}`} target="_blank">
            {i18n.translate('termsOfService')}
          </a>
            {' '}
          <span className="footer-separator">|</span>
          {' '}
          <a href={`${privacyPolicy}?lang=${language}`} target="_blank">
            {i18n.translate('privacyPolicy')}
          </a>{' '}
          <span className="footer-separator">|</span>
            {' '}
          <a href={`${cookiePolicy}?lang=${language}`} target="_blank">
            {i18n.translate('cookiePolicy')}
          </a>{' '}
        </div>
      </footer>
    </>

  );
}
