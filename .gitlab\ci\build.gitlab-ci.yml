build-merge-request:
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
  stage: build
  only:
    refs:
      - merge_requests
    variables:
      - '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develope"'
  script:
    - 'call "./build/build.bat" --build-client'

build-qa:
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
  stage: build
  only:
    refs:
      - develope
  variables:
    PUBLISH_OUTPUT_DIR: 'bin/Release/net6.0/publish'
  script:
    - |
      ECHO Building API app...
      call "./build/build.bat" --build-client --sln "src/ShareGraph.API" --output-path "%PUBLISH_OUTPUT_DIR%"
      call "./build/zip.bat" --output "src/ShareGraph.API/%PUBLISH_OUTPUT_DIR%/ShareGraph.API.zip" --input "src/ShareGraph.API/%PUBLISH_OUTPUT_DIR%/*"
    - |
      ECHO Building Web app...
      call "./build/build.bat" --build-client --sln "src/ShareGraph.Web" --output-path "%PUBLISH_OUTPUT_DIR%"
      call "./build/zip.bat" --output "src/ShareGraph.Web/%PUBLISH_OUTPUT_DIR%/ShareGraph.Web.zip" --input "src/ShareGraph.Web/%PUBLISH_OUTPUT_DIR%/*"
  artifacts:
    expose_as: 'publish-package'
    untracked: false
    expire_in: 2 hrs
    paths:
      - 'src/ShareGraph.Web/bin/Release/net6.0/publish/ShareGraph.Web.zip'
      - 'src/ShareGraph.API/bin/Release/net6.0/publish/ShareGraph.API.zip'

build-staging:
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: build
  only:
    refs:
      - next
  variables:
    PUBLISH_OUTPUT_DIR: 'bin/Release/net6.0/publish'
  script:
    - |
      ECHO Building API app...
      call "./build/build.bat" --release --build-client --sln "src/ShareGraph.API" --output-path "%PUBLISH_OUTPUT_DIR%"
      call "./build/zip.bat" --output "src/ShareGraph.API/%PUBLISH_OUTPUT_DIR%/ShareGraph.API.zip" --input "src/ShareGraph.API/%PUBLISH_OUTPUT_DIR%/*"
    - |
      ECHO Building Web app...
      call "./build/build.bat" --build-client --sln "src/ShareGraph.Web" --output-path "%PUBLISH_OUTPUT_DIR%"
      call "./build/zip.bat" --output "src/ShareGraph.Web/%PUBLISH_OUTPUT_DIR%/ShareGraph.Web.zip" --input "src/ShareGraph.Web/%PUBLISH_OUTPUT_DIR%/*"
  artifacts:
    expose_as: 'publish-package'
    untracked: false
    expire_in: 1 week
    paths:
      - 'src/ShareGraph.Web/bin/Release/net6.0/publish/ShareGraph.Web.zip'
      - 'src/ShareGraph.API/bin/Release/net6.0/publish/ShareGraph.API.zip'

build-prod:
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: build
  only:
    refs:
      - master
  variables:
    PUBLISH_OUTPUT_DIR: 'bin/Release/net6.0/publish'
  script:
    - |
      ECHO Building API app...
      call "./build/build.bat" --release --build-client --sln "src/ShareGraph.API" --output-path "%PUBLISH_OUTPUT_DIR%"
      call "./build/zip.bat" --output "src/ShareGraph.API/%PUBLISH_OUTPUT_DIR%/ShareGraph.API.zip" --input "src/ShareGraph.API/%PUBLISH_OUTPUT_DIR%/*"
    - |
      ECHO Building Web app...
      call "./build/build.bat" --build-client --sln "src/ShareGraph.Web" --output-path "%PUBLISH_OUTPUT_DIR%"
      call "./build/zip.bat" --output "src/ShareGraph.Web/%PUBLISH_OUTPUT_DIR%/ShareGraph.Web.zip" --input "src/ShareGraph.Web/%PUBLISH_OUTPUT_DIR%/*"
  artifacts:
    expose_as: 'publish-package'
    untracked: false
    expire_in: 6 mos
    paths:
      - 'src/ShareGraph.Web/bin/Release/net6.0/publish/ShareGraph.Web.zip'
      - 'src/ShareGraph.API/bin/Release/net6.0/publish/ShareGraph.API.zip'
