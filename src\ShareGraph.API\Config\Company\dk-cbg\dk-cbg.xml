<?xml version="1.0" encoding="utf-8"?>
<settings>
	<Layout>FIXED</Layout>
	<TimeZone>Europe/Brussels</TimeZone>
	<UseLatinNumber>false</UseLatinNumber>
	<StreamUpdateDelay>500</StreamUpdateDelay>

	<Instruments>
		<Instrument>
			<Id>32864</Id>
			<Color>#7DCA48</Color>
			<!-- <Order>1</Order> -->
			<Default>true</Default>
			<TickerName>
				<en-GB>Share 3</en-GB>
				<ar-AE>CARL B</ar-AE>
			</TickerName>
			<PressReleases>
				<SourceFilter></SourceFilter>
			</PressReleases>
			<IsRT>False</IsRT>
          	<DecimalDigits>3</DecimalDigits>
		</Instrument>

	</Instruments>

	<Ticker>
		<!-- Posible values: GRAPH, TABLE -->
		<EnabledFormat>GRAPH, TABLE</EnabledFormat>
		<!-- Posible values: SINGLE, MULTIPLE -->
		<TickerType>SINGLE</TickerType>
		<!-- Posible values: SINGLE_TICKER_1, SINGLE_TICKER_2, MULTIPLE_TICKER_1, MULTIPLE_TICKER_2,
		{CustomTemplate}  -->
		<!-- <GraphTickerTemplate>SINGLE_TICKER_1_WITH_DANGEROUS_HTML_TAGS</GraphTickerTemplate> -->
		<GraphTickerTemplate>SINGLE_TICKER_1</GraphTickerTemplate>
		<!-- <TableColumns>TABLE_TICKER_SINGLE, TABLE_TICKER_MULTIPLE</TableColumns> -->
		<TableTickerTemplate>TABLE_TICKER_SINGLE</TableTickerTemplate>
	</Ticker>

	<Performance>
		<!-- Posible values: GRAPH, TABLE
		Empty for disable -->
		<EnabledFormat>GRAPH, TABLE</EnabledFormat>
		<!-- Posible values: SHARE_PRICE_DEVELOPMENT, SHARE_PRICE_DEVELOPMENT_BY_YEARS,
		52_WEEKS_HIGH_LOW
		Empty for disable -->
		<PerformanceType>SHARE_PRICE_DEVELOPMENT_BY_YEARS, 52_WEEKS_HIGH_LOW,
			SHARE_PRICE_DEVELOPMENT</PerformanceType>
		<!-- List field available shareName,last,low52W,high52W,percent52WLow,percent52WHigh -->
		<Enable52WTableColumn>shareName,last,low52W,high52W,percent52WLow,percent52WHigh</Enable52WTableColumn>
		<!-- List field available
		shareName,currencyCode,change,w52HighLow,allTimeHighLow,week,month,threeMonthChange,sixMonthsChange,yTD,percent52W,threeYearsChange,fiveYearsChange,tenYearsChange -->
		<EnableSharePriceDevelopmentColumn>
			shareName,last,currencyCode,change,w52HighLow,allTimeHighLow,week,month,threeMonthChange,yTD,percent52W,fiveYearsChange,</EnableSharePriceDevelopmentColumn>
		<!--Option
		values: true, false-->
		<ShowEarliestYearFirstSPByYear>false</ShowEarliestYearFirstSPByYear>
	</Performance>

	<Peers>
		<!--<Enabled>true</Enabled>-->
		<Peer>
			<Id>9287</Id>
			<Color>#0097A7</Color>
			<Order>1</Order>
			<TickerName>
				<en-GB>Heineken</en-GB>
			</TickerName>
		</Peer>
		<Peer>
			<Id>10000</Id>
			<Color>#04A851</Color>
			<Order>2</Order>
			<TickerName>
				<en-GB>In Bev</en-GB>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>32904</Id>
			<Color>#2AABE4</Color>
			<Order>3</Order>
			<TickerName>
				<en-GB>Diageo</en-GB>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>3541</Id>
			<Color>#0071BD</Color>
			<Order>4</Order>
			<TickerName>
				<en-GB>Efes</en-GB>
			</TickerName>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>3562</Id>
			<Color>#111E6C</Color>
			<Order>5</Order>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
		<Peer>
			<Id>6346004</Id>
			<Color>#126548</Color>
			<Order>6</Order>
			<CurrencyCode>
				<en-GB>DKK</en-GB>
				<ar-AE>DKK</ar-AE>
			</CurrencyCode>
		</Peer>
	</Peers>
	<Indices>
		<Enabled>true</Enabled>
		<Index>
			<Id>6346004</Id>
			<Color>#F90703</Color>
			<Order>1</Order>
		</Index>
	</Indices>

	<ShareDetails>
		<!-- Options: True|False -->
		<Enabled>True</Enabled>
		<!-- Fields available:
		TIME,CURRENCY,MARKET,MARKET_STATUS,ISIN,SYMBOL,BID,BID_SIZE,ASK,ASK_SIZE,OPEN,LAST,CHANGE,
		CHANGE_PERCENT,HIGH,LOW,VOLUME,TOTAL_TRADES,PREVIOUS_CLOSE,YTD_HIGH,YTD_LOW,WEEKS_52_HIGH,WEEKS_52_LOW,
        ALL_TIME_HIGH,ALL_TIME_LOW,YTD_PERCENT,WEEKS_52_PERCENT,LIST,INDUSTRY,NUMBER_OF_SHARES,MARKET_CAP,
        LOT_SIZE,P_E,AVERAGE_PRICE,TURNOVER ...-->
		<ShareDataItems>
			TIME,CURRENCY,MARKET,MARKET_STATUS,ISIN
		</ShareDataItems>
		<!-- Options: True|False -->
		<DisplayOnSelectedTicker>True</DisplayOnSelectedTicker>
		<!-- Options: Grid|Flow -->
		<DisplayType>Flow</DisplayType>
		<!-- Options: True|False -->
		<PartialDisplay>True</PartialDisplay>
		<NumberItemsInCollapsedMode>6</NumberItemsInCollapsedMode>

	</ShareDetails>

	<PressReleases>
		<LanguageForwarding>5|ar-AE|en-GB;6|zh-TW|zh-CN;</LanguageForwarding>
		<TypeFilter></TypeFilter>
		<ExcludedTypeFilter></ExcludedTypeFilter>
		<!-- Popup, NewPage. Default popup-->
		<OpenAs>Popup</OpenAs>
		<!-- only for NewPage.
		https://dev.vn.euroland.com/tools/iframe-test/sg3-press-release-detail.html?ID={ID}%26lang={lang}%26companycode={companycode}%26=press-release-url={pressReleaseUrl}-->
		<NewPageUrlPattern>
			https://dev.vn.euroland.com/tools/iframe-test/sg3-press-release-detail.html?ID={ID}%26lang={lang}%26companycode={companycode}</NewPageUrlPattern>
	</PressReleases>

	<Format>
		<en-GB>
			<TickerDateTimeFormat>MMM DD, YYYY [|] hh:mm [UTC]Z</TickerDateTimeFormat>
			<ShortDate xml:space="preserve">MMM/DD/YYYY</ShortDate>
			<DecimalDigits>2</DecimalDigits>
			<PercentDigits>2</PercentDigits>
			<!--DecimalSeparator:
			comma or dot-->
			<DecimalSeparator xml:space="preserve">.</DecimalSeparator>
			<!--ThousandsSeparator:
			comma, dot, space-->
			<ThousandsSeparator xml:space="preserve">,</ThousandsSeparator>
			<!--NegativeNumberFormat
			Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
			Possible values: (n), -n, - n, n-, n -
			Default value: -n-->
			<NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
      		<PositiveNumberFormat>n</PositiveNumberFormat>
            <PositiveChangeFormat xml:space="preserve">+n</PositiveChangeFormat>
		</en-GB>
		<fr-FR>
			<TickerDateTimeFormat>MMM d, HH:mm:ss (FR Z)</TickerDateTimeFormat>
			<ShortDate xml:space="preserve">dd/MM/yyyy</ShortDate>
			<DecimalDigits>7</DecimalDigits>
			<PercentDigits>2</PercentDigits>
			<!--DecimalSeparator:
			comma or dot-->
			<DecimalSeparator xml:space="preserve">,</DecimalSeparator>
			<!--ThousandsSeparator:
			comma, dot, space-->
			<ThousandsSeparator xml:space="preserve">.</ThousandsSeparator>
			<!--NegativeNumberFormat
			Note: It’s CultureInfo.NumberFormat.NumberNegativePattern.
			Possible values: (n), -n, - n, n-, n -
			Default value: -n-->
			<NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
		</fr-FR>
		<!-- <fr-FR>
        <ShortDate xml:space="preserve">Haha</ShortDate>
      </fr-FR> -->
	</Format>

	<CustomPhrases>
		<percentFrom52WLowLabel>
			<en-GB>
				% From 52W Low
			</en-GB>
		</percentFrom52WLowLabel>
		<percentFrom52WHighLabel>
			<en-GB>
				% From 52W High
			</en-GB>
		</percentFrom52WHighLabel>
	</CustomPhrases>

	<!-- <Accessibilities>
    <Enabled>true</Enabled>
    <Format>
      <en-GB>
        <CalendarDate>en</CalendarDate>
      </en-GB>
      <ar-AE>
        <CalendarDate>haha</CalendarDate>
      </ar-AE>
    </Format>
  </Accessibilities> -->
  <CompanyLogo>
    <Path>logo.png</Path>
  </CompanyLogo>
  <ColorBlindMode>
    <Enabled>true</Enabled>
    <DefaultSelected>false</DefaultSelected>
  </ColorBlindMode>
</settings>
