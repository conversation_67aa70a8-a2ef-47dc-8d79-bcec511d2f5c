import React, { useContext, useEffect, useState } from  'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation, useSearchParams } from 'react-router-dom';
import { selectTicker } from '../../actions';
import { AppContext } from '../../AppContext';
import { getCustomShareNameByInstrumentId } from '../../helper';
import i18n from '../../services/i18n';

const ShareSelect =() => {
    const instruments = useSelector(state => state.tickers.instruments);
    const selectedInstrumentId = useSelector(state => state.tickers.selectedInstrumentId);
    const [selected, setSelected] = useState('');
    const location = useLocation();
    const dispatch = useDispatch();
    const settings = useContext(AppContext);

    const performanceEnabled = () => {
        return settings.performance.enabledFormats.length > 0 && settings.performance.enabledFormats[0] && settings.performance.performanceTypes.length > 0 && settings.performance.performanceTypes[0];
      };

    const getShareName = (instrument) => {
        if(instrument.shareName) {
            let name = getCustomShareNameByInstrumentId(instrument.instrumentId) || instrument.shareName;

            return name.replace(/\s*\(.*\)$/, '') + ` (${instrument.marketName})`;
        }
        return '';
    };

    useEffect(() => {
        if(selectedInstrumentId && selectedInstrumentId !== selected) {
            setSelected(selectedInstrumentId);
        }
    }, [selectedInstrumentId]);

    const handleChangeSelected = (e) => {
        e.preventDefault();
        dispatch(selectTicker(parseInt(selected)));
    };

    const makeHashLink = hash => `${location.pathname}${location.search}${hash}`;
    const scrollToView = hash => {
        const id = hash.replace('#', '');
        // window.xprops?.scrollTop(0);
        const element = document.getElementById(id);
        if (element) element.scrollIntoView();
    };

    return <div className='select-share'>
        <div id="selectshare">
            <label htmlFor="select-share">
                {i18n.translate('selectShare')}
            </label>
            &nbsp;&nbsp;
            <select value={selected} id="select-share" onChange={e => setSelected(e.target.value)}>
                {instruments.map((item) => (
                    <option key={item.instrumentId} value={item.instrumentId}>{getShareName(item)}</option>
                ))}
            </select>
            &nbsp;&nbsp;
            <a onClick={handleChangeSelected} href="/#">{i18n.translate('viewSelectedShare')}</a>
        </div>

        <div id='buttontab'>
            <nav id="navbuttontab">
                <Link onClick={() => scrollToView('#periodSelectionPanel')} to={makeHashLink('#periodSelectionPanel')} id="skipToSharePriceDetails">{i18n.translate('skipToSharePriceDetails')}</Link>
                    <span role="presentation" aria-hidden="true">&nbsp;|&nbsp;</span>
                <Link onClick={() => scrollToView('#settingdetail')} to={makeHashLink('#settingdetail')} id="skipToDetailSettings">{i18n.translate('skipToDetailedSettings')}</Link>
                    {performanceEnabled() ? <>
                        <span id="spanSplitSettingP" role="presentation" aria-hidden="true">&nbsp;|&nbsp;</span>
                        <Link to={`/accessibility/share-performance${location.search}`} id="lbtTabPerformance">&nbsp;{i18n.translate('sharePerformance')}</Link>
                    </> : null}
			</nav>
        </div>
    </div>;
};

export default ShareSelect;