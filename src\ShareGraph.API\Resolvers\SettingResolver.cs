using Euroland.FlipIT.ShareGraph.API.Common;
using Euroland.FlipIT.ShareGraph.API.Entities;
using Euroland.FlipIT.ShareGraph.API.Entities.Configurations;
using Euroland.FlipIT.ShareGraph.API.Entities.Configurations.Accessibilities;
using Euroland.FlipIT.ShareGraph.API.Extensions;
using Euroland.FlipIT.ShareGraph.API.Mappers;
using Euroland.FlipIT.ShareGraph.API.Services;
using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using Euroland.NetCore.ToolsFramework.Translation;
using HotChocolate;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Resolvers
{
  public class SettingResolver
  {
    public CurrenciesConfig GetCurrencies([Service] ISetting _settings, [Service] ITranslationService translationService,
      [Service] ICurrencyService currencyService)
    {
      var currenciesConfig = _settings.GetChild("Currencies").Bind<CurrenciesConfig>();

      if (currenciesConfig?.Enabled is true)
      {
        var currenciesDto = new List<Entities.Configurations.Currency>();

        var currencies = _settings.GetChild($"Currencies").GetChildren().Where(x => x.Name.Contains("Currency"));
        foreach (var currency in currencies)
        {
          var currencyDto = currency.GetChildren()
            .ToDictionary(x => x.Name, x => string.IsNullOrEmpty(x.Value) ? x.GetChild($"{CultureInfo.CurrentCulture.Name}").Value : x.Value)
            .ToObject<Entities.Configurations.Currency>();
          currenciesDto.Add(currencyDto);
        }
        currenciesConfig.Currencies = GetFullNameForCurrency(currenciesDto, translationService, currencyService);

        return currenciesConfig;
      }

      return new();
    }

    private List<Entities.Configurations.Currency> GetFullNameForCurrency(
      List<Entities.Configurations.Currency> currenciesDto,
      ITranslationService translationService,
      ICurrencyService currencyService)
    {
      if (currenciesDto.Any(x => string.IsNullOrEmpty(x.FullName)))
      {
        var lstCurrencyCode = currenciesDto.Select(x => x.Code).ToList();
        var currenciesInfor = currencyService.GetCurrenciesByCodes(lstCurrencyCode).Result;

        if (currenciesInfor != null)
        {
          var lstTranslationIds = currenciesInfor.Where(x => x.TranslationId.HasValue).Select(x => x.TranslationId.Value);
          var tranlations = new List<Translation>();
          try
          {
            tranlations = translationService.GetTranslation(lstTranslationIds).ToList();
          }
          catch (Exception)
          {
            tranlations = new List<Translation>();
          }

          for (int i = 0; i < currenciesDto.Count; i++)
          {
            var currency = currenciesDto[i];
            var currencyDB = currenciesInfor.FirstOrDefault(x => String.Equals(x.Code, currency.Code, StringComparison.InvariantCultureIgnoreCase));
            var transalteId = currencyDB?.TranslationId;
            var tranlate = tranlations.FirstOrDefault(x => x.Id == transalteId);
            var currentCulture = GetTwoLetterCulture();
            var fullNameDB = currencyDB?.Name ?? currencyDB?.Code;
            if (tranlate != null)
            {
              tranlate.TranslationMap.TryGetValue(currentCulture, out string tranlationDB);
              fullNameDB = tranlationDB ?? fullNameDB;
            }
            currenciesDto[i].FullName = currenciesDto[i].FullName ?? fullNameDB;
          }
        }
        return currenciesDto;
      }
      return currenciesDto;
    }

    private string GetTwoLetterCulture()
    {
      CultureInfo objCulture = CultureInfo.CurrentUICulture;
      var languageCode = objCulture.TwoLetterISOLanguageName;
      var chineseLanguae = new List<string> { "zh-CN", "zh-TW" };
      if (chineseLanguae.Contains(objCulture.Name))
      {
        languageCode = objCulture.Name.Split("-")[1];
      }
      return languageCode;
    }

    /// <summary>
    /// Resolver for instruments
    /// </summary>
    /// <returns>Instruments configuration</returns>
    public IEnumerable<Instrument> GetInstruments([Service] ISetting _settings)
    {
      var cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
      var result = new List<Instrument>();

      var instruments = _settings.GetChild($"Instruments")?.GetChildren();
      if (instruments is null) return result;

      foreach (var item in instruments)
      {
        var instrument = item.GetChildren()
            .ToDictionary(s => s.Name, s => string.IsNullOrEmpty(s.Value) ? s.GetChild($"{cultureInfo.Name}").Value : s.Value)
            .ToObject<Instrument>();

        if (string.IsNullOrEmpty(instrument.Color))
        {
          //instrument.Color = RandomColor(instrument.Id);
          instrument.Color = string.Empty;
        }

        var pressRelease = new PressRelease { SourceFilter = item.GetChild("PressReleases:SourceFilter").Value };
        instrument.PressRelease = pressRelease;

        result.Add(instrument);
      }
      return result;
    }

    /// <summary>
    /// Resolver for ticker
    /// </summary>
    /// <returns>Ticker configuration</returns>
    public Ticker GetTickers(
        [Service] ISetting _settings,
        [Service] IHttpContextAccessor accessor,
        [Service] ITickerTemplateService _toolSettings)
    {
      var cCode = accessor.HttpContext.Request.Query["companyCode"].FirstOrDefault();
      var ticker = _settings.GetChild($"Ticker")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<TickerConfig>()
          .ToModel();

      if (ticker is null) return null;

      // build ticker template
      ticker.GraphTickerTemplate = _toolSettings.BuildTickerTemplate(cCode, ticker.GraphTickerTemplate).GetAwaiter().GetResult();
      ticker.TableTickerTemplate = _toolSettings.BuildTickerTemplate(cCode, ticker.TableTickerTemplate).GetAwaiter().GetResult();

      return ticker;
    }

    /// <summary>
    /// Resolver for peers
    /// </summary>
    /// <returns>Peers configuration</returns>
    public PeersConfig GetPeers([Service] ISetting _settings)
    {
      bool enabled = true;
      if (bool.TryParse(_settings.GetChild($"Peers:Enabled")?.Value, out enabled) && enabled)
      {
        return new PeersConfig
        {
          Enabled = true,
          Animation = _settings.GetChild($"Peers:Animation")?.Value,
          Peers = GetPeersConfig(_settings, CultureInfo.CurrentCulture.Name)
        };
      }

      return new PeersConfig { Enabled = false };
    }

    /// <summary>
    /// Resolver for indices
    /// </summary>
    /// <returns>Indices configuration</returns>
    public IndicesConfig GetIndices([Service] ISetting _settings)
    {
      bool enabled = true;
      if (bool.TryParse(_settings.GetChild($"Indices:Enabled")?.Value, out enabled) && enabled)
      {
        return new IndicesConfig
        {
          Enabled = true,
          Animation = _settings.GetChild($"Indices:Animation")?.Value,
          Indices = GetIndicesConfig(_settings, CultureInfo.CurrentCulture.Name)
        };
      }

      return new IndicesConfig { Enabled = false };
    }

    private IEnumerable<Peer> GetPeersConfig(ISetting _settings, string culture)
    {
      var result = new List<Peer>();

      var peers = _settings.GetChild($"Peers")?.GetChildren().Where(p => p.Name.Contains("Peer"));
      if (peers is null) return result;

      foreach (var item in peers)
      {
        var peer = item.GetChildren()
                  .ToDictionary(s => s.Name, s => string.IsNullOrEmpty(s.Value) ? s.GetChild($"{culture}").Value : s.Value)
                  .ToObject<Peer>();

        if (string.IsNullOrEmpty(peer.Color))
        {
          peer.Color = RandomColor(peer.Id);
        }
        result.Add(peer);
      }

      return result;
    }

    private IEnumerable<Entities.Index> GetIndicesConfig(ISetting _settings, string culture)
    {
      var result = new List<Entities.Index>();

      var indices = _settings.GetChild($"Indices")?.GetChildren().Where(p => p.Name.Contains("Index"));
      if (indices is null) return result;

      foreach (var item in indices)
      {
        var index = item.GetChildren()
                  .ToDictionary(s => s.Name, s => string.IsNullOrEmpty(s.Value) ? s.GetChild($"{culture}").Value : s.Value)
                  .ToObject<Entities.Index>();

        if (string.IsNullOrEmpty(index.Color))
        {
          index.Color = RandomColor(index.Id);
        }
        result.Add(index);
      }

      return result;
    }

    /// <summary>
    /// Resolver for share details
    /// </summary>
    /// <returns>Share details configuration</returns>
    public ShareDetails GetShareDetails([Service] ISetting _settings)
    {
      var shareDetails = _settings.GetChild($"ShareDetails")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<ShareDetailsConfig>()
          .ToModel();

      return shareDetails;
    }

    /// <summary>
    /// Resolver for format
    /// </summary>
    /// <returns>Format</returns>
    public Entities.Format GetFormats([Service] ISetting _settings)
    {
      var culture = System.Globalization.CultureInfo.CurrentCulture.Name;

      var formats = _settings.GetChild($"format:{culture}")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<Entities.Format>();

      if (culture.Equals("en-GB", StringComparison.OrdinalIgnoreCase) && formats != null)
      {
        formats.ThousandsSeparator = formats.ThousandsSeparator.ValidateSeparator();
        formats.DecimalSeparator = formats.DecimalSeparator.ValidateSeparator();
        return formats;
      }

      var defaultFormats = _settings.GetChild($"format:en-GB")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<Entities.Format>();

      return ReFormatWithDefaultValue(formats, defaultFormats);
    }

    /// <summary>
    /// Resolver for custom phrases
    /// </summary>
    /// <returns>Custom phrases configuration</returns>
    public string GetCustomPhrases([Service] ISetting _settings)
    {
      var cultureInfo = System.Globalization.CultureInfo.CurrentCulture;
      var dictionary = new Dictionary<string, string>();

      var customPhrases = _settings.GetChild($"customPhrases")?.GetChildren();
      if (customPhrases is null)
      {
        return null;
      }

      customPhrases?.ToList().ForEach(
          item => dictionary.Add(item.Name, item.GetChild($"{cultureInfo.Name}").Value?.Trim()));

      return JsonConvert.SerializeObject(dictionary);
    }

    public Chart GetChart([Service] ISetting _settings)
    {
      var chart = _settings.GetChild($"Chart")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<ChartConfig>()
          .ToModel();

      return chart;
    }

    public Accessibilities GetAccessibilities([Service] ISetting _settings)
    {
      var culture = System.Globalization.CultureInfo.CurrentCulture.Name;
      var isEnabled = false;

      if (Boolean.TryParse(_settings.GetChild("Accessibilities:Enabled")?.Value, out isEnabled))
      {
        var timezone = _settings.GetChild($"Accessibilities:TimeZone:{culture}")?.Value;

        var formats = _settings.GetChild($"Accessibilities:Format:{culture}")?.GetChildren()
            .ToDictionary(s => s.Name, s => s.Value)
            .ToObject<Formats>();

        if (formats is null)
        {
          formats = _settings.GetChild($"Accessibilities:Format:en-GB")?.GetChildren()
            .ToDictionary(s => s.Name, s => s.Value)
            .ToObject<Formats>();
        }

        return new Accessibilities
        {
          Enabled = isEnabled,
          TimeZone = timezone,
          Formats = formats
        };
      }

      return new Accessibilities { Enabled = false };
    }

    public Trade GetTrade([Service] ISetting _settings)
    {
      var trade = _settings.GetChild($"Trade")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<Trade>();
      if (trade is null) trade = new Trade();
      return trade;
    }

    /*****************************************/
    /* helper Resolvers for the SettingType  */
    /*****************************************/

    public Performance GetPerformance([Service] ISetting _settings)
    {
      var performance = _settings.GetChild($"Performance")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<PerformanceConfig>()
          .ToModel();

      return performance;
    }

    private static string RandomColor(int instrumentId)
    {
      var hash = 0;
      var str = instrumentId.ToString();

      foreach (var chr in str)
      {
        hash = (int)chr + ((hash << 5) - hash);
      }

      var colour = new StringBuilder("#");
      for (var i = 0; i < 3; i++)
      {
        var value = (hash >> (i * 8)) & 0xFF;
        var convertedStr = ("00" + Convert.ToString(value, 16));

        colour.Append(convertedStr.Substring(convertedStr.Length - 2));
      }
      return colour.ToString();
    }

    private Entities.Format ReFormatWithDefaultValue(Entities.Format cur, Entities.Format def)
    {
      if (cur == null) return def;

      if (string.IsNullOrEmpty(cur.TickerDateTimeFormat))
        cur.TickerDateTimeFormat = def.TickerDateTimeFormat;

      if (string.IsNullOrEmpty(cur.ShortDate))
        cur.ShortDate = def.ShortDate;

      if (string.IsNullOrEmpty(cur.LongDate))
        cur.LongDate = def.LongDate;

      if (cur.DecimalDigits == null)
        cur.DecimalDigits = def.DecimalDigits;

      if (cur.PercentDigits == null)
        cur.PercentDigits = def.PercentDigits;

      if (string.IsNullOrEmpty(cur.DecimalSeparator))
        cur.DecimalSeparator = def.DecimalSeparator;

      if (string.IsNullOrEmpty(cur.ThousandsSeparator))
        cur.ThousandsSeparator = def.ThousandsSeparator;

      if (string.IsNullOrEmpty(cur.NegativeNumberFormat))
        cur.NegativeNumberFormat = def.NegativeNumberFormat;

      if (string.IsNullOrEmpty(cur.PositiveNumberFormat))
        cur.PositiveNumberFormat = def.PositiveNumberFormat;

      if (string.IsNullOrEmpty(cur.TimeFormat))
        cur.TimeFormat = def.TimeFormat;

      //Specical case for Separator
      cur.ThousandsSeparator = cur.ThousandsSeparator.ValidateSeparator();
      cur.DecimalSeparator = cur.DecimalSeparator.ValidateSeparator();

      return cur;
    }

    /// <summary>
    /// Resolver for Company Logo
    /// </summary>
    /// <returns>Company Logo configuration</returns>
    public CompanyLogo GetCompanyLogo(
        [Service] ISetting _settings)
    {
      var companyLogo = _settings.GetChild($"CompanyLogo")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<CompanyLogoConfig>();

      if (companyLogo is null)
      {
        return null;
      }

      return new CompanyLogo()
      {
        Path = companyLogo.Path.Trim(),
        Alpha = companyLogo.Alpha,
        Width = companyLogo.Width,
        Height = companyLogo.Height
      };
    }

    /// <summary>
    /// Resolver for company name
    /// </summary>
    /// <returns>Company Name configuration</returns>
    public List<CompanyName> GetCompanyName([Service] ISetting _settings)
    {
      var dictionary = new List<CompanyName>();

      var customPhrases = _settings.GetChild($"CompanyName")?.GetChildren();
      if (customPhrases is null)
      {
        return null;
      }

      customPhrases?.ToList().ForEach(
          item => dictionary.Add(new CompanyName { CultureCode = item.Name, Value = item.Value?.Trim() }));

      return (dictionary);
    }

    public PressReleases GetPressReleases([Service] ISetting _settings)
    {
      return _settings.GetChild($"PressReleases")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<PressReleases>();
    }

    /// <summary>
    /// Resolver for Video setting
    /// </summary>
    /// <param name="_settings"></param>
    /// <returns>Video Setting</returns>

    public VideoSetting GetVideoSetting([Service] ISetting _settings)
    {
      var videoSetting = _settings.GetChild($"VideoSetting")?.GetChildren()
            .ToDictionary(s => s.Name, s => s.Value)
            .ToObject<VideoSetting>();
      if (videoSetting is null)
      {
        videoSetting = new VideoSetting();
      }

      //if value is invalid then set default value is VIMEO
      var videoType = string.IsNullOrEmpty(videoSetting.VideoType) ? VideoType.VIMEO.ToString() : videoSetting.VideoType.Trim().ToUpperInvariant();
      if (!Enum.IsDefined(typeof(VideoType), videoType))
      {
        videoType = VideoType.VIMEO.ToString();
      }
      videoSetting.VideoType = videoType;
      return videoSetting;
    }

    public OrderDepth GetOrderDepth([Service] ISetting _settings)
    {
      var orderDepth = _settings.GetChild($"OrderDepth")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<OrderDepth>();
      if (orderDepth is null)
      {
        return new OrderDepth();
      }

      //Validate caluclate by. if value is invalid then set default value is VIMEO
      var defaultValue = OrderDepthCalculate.VOLUME.ToString();
      var calculateBy = string.IsNullOrEmpty(orderDepth.CalculateBy) ? defaultValue : orderDepth.CalculateBy?.Trim().ToUpperInvariant();
      if (!Enum.IsDefined(typeof(OrderDepthCalculate), calculateBy))
      {
        calculateBy = defaultValue;
      }
      orderDepth.CalculateBy = calculateBy;
      return orderDepth;
    }

    public ColorBlindMode GetColorBlindMode([Service] ISetting _settings)
    {
      var colorBlindMode = _settings.GetChild($"ColorBlindMode")?.GetChildren()
          .ToDictionary(s => s.Name, s => s.Value)
          .ToObject<ColorBlindMode>();
      if (colorBlindMode is null)
      {
        return new ColorBlindMode();
      }
      return colorBlindMode;
    }

    public ForeignOwnership GetForeignOwnership([Service] ISetting setting)
    {
      var foreignOwnership = setting.GetChild("ForeignOwnership").Bind<ForeignOwnership>();
      if (foreignOwnership?.Enabled is true)
      {
        foreignOwnership.NumberOfRows ??= ForeignOwnership.DefaultNumberOfRows;

        return foreignOwnership;
      }

      return new();
    }
  }
}
