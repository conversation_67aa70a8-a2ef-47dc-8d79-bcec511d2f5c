export function blockScrollLeftAndRight (chartEngine) {
  if(!chartEngine.allowZoom) {
    chartEngine.controls['home'] = null;
  }

  const minWidthTick = 80;
  const chartWidth = chartEngine.chart.width;
  const masterData = chartEngine.masterData;
  if(chartEngine.allowScroll && (chartWidth / masterData.length) < minWidthTick) {
    const maxTick = chartWidth / minWidthTick;
    chartEngine.setMaxTicks(Math.round(maxTick));
  }

  const cleaner = chartEngine.prepend('correctIfOffEdge', function () {
    for (var chartName in this.charts) {
      const chart = this.charts[chartName],
        dataSet = chart.dataSet,
        maxTicks = chart.maxTicks,
        minimumLeftBars = this.minimumLeftBars;
      
      if (chart.allowScrollPast) continue;
      
      const leftPad = Math.min(minimumLeftBars, maxTicks); // in case the minimumLeftBars is larger than what we can display

      // earliest point in time is always anchored on left side of chart
      if (chart.scroll < leftPad) {
        this.micropixels = 0;
      }
      if (chart.scroll > dataSet.length) {
        this.micropixels = 0;
      }
    }
  });

  chartEngine.draw();
  return () => {
    chartEngine.removeInjection(cleaner);
  };
}