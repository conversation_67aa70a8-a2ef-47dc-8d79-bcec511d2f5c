import i18n from '../../services/i18n';
import { DEVICES } from '../../common';
import { getDeviceType, isValidatedNumber } from '../../helper';

const Table = ({ headings, datas, summary, caption, className, indicatorColumns=[], footer }) => {
  if(!headings || headings.length === 0) return <></>;
  const tableHeaders = (
    <thead className='table__head'>
      <tr className='table__head-tr'>
        {headings.map((heading, index) => {
                if(heading.displayShortLabel){
                  if (getDeviceType() === DEVICES.SM || getDeviceType() === DEVICES.XS) {
                      return (
                          <th scope='col' data-column={heading.fieldName} key={index}>
                          {heading.displayShortLabel}
                          </th>);
                      } else {
                        return (
                          <th scope='col' data-column={heading.fieldName} key={index}>
                          {heading.columnDisplay}
                          </th>);
                      }
                } else {
                  return (
                    <th scope='col' data-column={heading.fieldName} key={index}>
                    {heading.columnDisplay}
                    </th>);
                }

        })}
      </tr>
    </thead>
  );
  const classByValue = (value) => {
    if(!isValidatedNumber(value)) return '';
    return value > 0 ? 'indicator-up' : value < 0 ? 'indicator-down' : 'indicator-neutral';
  };
  const tableBody = datas.map( (data, index)=> {

    if(Object.keys(data).length !== 0){
        return (
              <tr key={index}>
                  {
                      headings.map((heading, ind)=>{
                        const {fieldName} = heading;
                        const valueFields = data[fieldName];
                        const isIndicatorColumn = indicatorColumns.includes(fieldName);

                        if(valueFields.length == 1){
                          const {value, display} = valueFields[0];
                          const classIndicator = isIndicatorColumn ? classByValue(value): '';
                          return <td key={ind} className={classIndicator} data-column={fieldName}>{display}</td>;
                        }
                        else if(valueFields.length == 2){
                          const {value} = valueFields;
                          const classIndicator = isIndicatorColumn ? classByValue(value): '';
                          const dataDisplay = valueFields.map((d, i) => {
                            return <div key={i} className={`field${d.lable.replace(':','')}_value`}><span className={`field${d.lable.replace(':','')}-lable`}>{d.lable}</span> <span className={`field${d.lable.replace(':','')}-data`}>{d.display}</span></div>;
                          });
                          return <td key={ind} className={classIndicator} data-column={fieldName} >{dataDisplay}</td>;
                        }
                        else {
                          const {value, display} = valueFields;
                          const classIndicator = isIndicatorColumn ? classByValue(value): '';
                          return <td key={ind} className={classIndicator} data-column={fieldName} >{display}</td>;
                        }

                      })
                  }
              </tr>
            );
          } else {
            return (
            <tr key={index}>
                    <td colSpan={headings.length} className='text-center'>{i18n.translate('notFoundData')}</td>
            </tr>);
          }
      }
  );

  return (
    <>
      <table summary={summary ? summary : i18n.translate('notFoundSummary')} className={`table commantable--striped ${className ? className + '--table' : ''}`}>
        <caption>{caption ? caption : i18n.translate('notFoundCaption')}</caption>
        {tableHeaders}
        <tbody className='table__body'>
          {tableBody}
          </tbody>
          {footer &&
            <tfoot>
              <tr><td colSpan={headings.length}>{footer}</td></tr>
            </tfoot>
          }
      </table>

    </>
  );
};

export default Table;
