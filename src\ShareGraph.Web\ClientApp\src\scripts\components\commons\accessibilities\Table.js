import classNames from 'classnames';

const Table = ({ className, columns = [], data = [], caption }) => {
  return (
    <table className={classNames('accessibility-table', className)} cellSpacing="0" rules="all" border="1">
      {caption ? <caption>{caption}</caption> : null}
      <tbody>
        <tr>
          {columns.map(column => (
            <th key={column.fieldName} scope="col">
              {column.columnDisplay}
            </th>
          ))}
        </tr>
        {data.map((d, index) => (
          <tr key={index}>
            {columns.map((column, ii) => {
              if (typeof column.render === 'function') {
                return (
                  <td key={column.fieldName} className={column.className} style={{ minWidth: column.width }}>
                    {column.render(d, data, ii)}
                  </td>
                );
              }
              return (
                <td key={column.fieldName} className={column.className}>
                  {d[column.fieldName]}
                </td>
              );
            })}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default Table;
