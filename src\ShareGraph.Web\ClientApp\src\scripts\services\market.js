/**
 * @typedef {{h: number, m: number}} TimeMarket
 */

import {CIQ} from '../../scripts/components/chart/chartiq-import';
import {windowTimeZoneToIANA} from '../components/chart/utils';

export class AbstractMarket {
  /**
   *
   * @param {Date} date
   */
  static getDateFormat(date) {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
  }

  /**
   *
   * @param {string} time
   * @returns { TimeMarket }
   */
  static parseTime(time) {
    const [h,m] = time.split(':').map(parseFloat);
    return {h, m};
  }

  /**
   * @template T
   * @param {Array<T>} arr
   * @param {(arg: T) => string} getDate
   * @returns {Record<string, T>}
   */
  static indexByDate(arr, getDate) {
    return arr.reduce((acc, i) => {
      acc[MarketTime.getDateFormat(new Date(getDate(i)))] = i;
      return acc;
    }, {});
  }

  /**
   *
   * @param {Date} date
   * @param {string} timeZone
   * @returns
   */
  static getOffset(date, timeZone) {
    const offsetRaw = new Intl.DateTimeFormat('en-US', {
      timeZoneName: 'longOffset',
      timeZone
    }).formatToParts(date).filter(i => i.type === 'timeZoneName')[0].value;
    const offsetString = offsetRaw.replace('GMT', '');
    if(!offsetString) throw new Error('parse offset false');
    const minus = offsetString.slice(0,1);

    const {h,m} = this.parseTime(offsetString.slice(1));
    if(minus === '+') return h * 60 + m;
    return -h * 60 + m;
  }

  constructor() {
    this.millisecond = {
      toSecond: 1000,
      toMins: 1000 * 60,
      toHour: 1000 * 60 * 60,
      toDate: 1000 * 60 * 60 * 24
    };
  }

  /**
     * @param { TimeMarket } fromTime
     * @param { number } toTimeZone
     */
  convertTime(fromTime, toTimeZone) {
    const min = fromTime.h * 60 + fromTime.m ;
    let result = min + toTimeZone;
    if(result >= 24 * 60) {
      result = result % (24 * 60);
    } else if(result < 0) {
      result = 24 * 60 + result;
    }

    const h = Math.floor(result / 60);
    const m = result % 60;
    return {h, m};
  }

  /**
  *
  * @param {TimeMarket} open
  * @param {TimeMarket} close
  */
  getDuration (open, close) {
    const openM = (open.h * 60) + open.m;
    const closeM = (close.h * 60) + close.m;

    if(closeM < openM) {
      return ((24 * 60) - openM) + closeM;
    } else {
      return closeM - openM;
    }
  }

  /**
   * @param { Date } date1
   * @param { Date } date2
   * @param { 'year' | 'month' | 'date' | 'hours' | 'min' } same
   */
  isSame(date1, date2, same) {
    const compare = method => date1[method]() === date2[method]();
    switch(same) {
      case 'year':
        return ['getFullYear'].every(compare);
      case 'month':
        return ['getFullYear', 'getMonth'].every(compare);
      case 'date':
        return ['getFullYear', 'getMonth', 'getDate'].every(compare);
      case 'hours':
        return ['getFullYear', 'getMonth', 'getDate', 'getHours'].every(compare);
      case 'min':
        return ['getFullYear', 'getMonth', 'getDate', 'getHours', 'getMinutes'].every(compare);
    }
  }
}

/** @type { Map<string, MarketTime>} */
const marketTimeCache = new Map();

export class MarketTime extends AbstractMarket {
  /**
   * @param {import("chartiq/js/componentUI").CIQ.Market} marketDefine
   */
  constructor(marketDefine) {
    super();
    this.marketDefine = marketDefine;
  }

  /**
   *
   * @param {Date} date
   * @description
   * - If the given date is a holiday or weekend, the "open" time will be `null`, 
   *   and the method retrieves the most recent previous open day.
   * - If the market opens later than the given date (e.g., an after-hours scenario),
   *   the method adjusts to find the previous open day.
   * - Otherwise, it calculates the next opening day relative to the current "open".
   * 
   * This method ensures that the market times are adjusted correctly for special cases 
   * such as holidays, weekends, and after-hours scenarios.
   */
  getOpenCloseInDate(date) {
    let open = this.marketDefine.getOpen(date) || this.marketDefine.getPreviousOpen(date);
    let nextOpen;
    if(open > date) {
      nextOpen = open;
      open = this.marketDefine.getPreviousOpen(open);
    } else {
      nextOpen = this.marketDefine.getNextOpen(open);
    }

    let close = this.marketDefine.getClose(open);

    return { open, close, nextOpen };
  }

  isOpen() {
    return this.marketDefine.isOpen();
  }

  /**
   *
   * @param {string} normal_daily_open
   * @param {string} normal_daily_close
   * @param {string} timeZone
   * @returns { MarketTime }
   */
  static factory (normal_daily_open, normal_daily_close, timeZone) {
    const cacheKey = normal_daily_open + normal_daily_close + timeZone;
    if(marketTimeCache.has(cacheKey)) return /** @type {MarketTime} */(marketTimeCache.get(cacheKey));

    // 09:00 -> 4
    const trimMins = (time) => time.length === 5 ? time : time.replace(/(:00)$/, '');
    const daily_open = trimMins(normal_daily_open.trim());
    const daily_close = trimMins(normal_daily_close.trim());
    const marketDefine = new CIQ.Market ({
      name: 'getMarketDefine',
      market_tz: timeZone,
      hour_aligned: false,
      normal_daily_open: daily_open,
      normal_daily_close: daily_close,
      rules: Array(5).fill(1).map((_, index) =>
          ({
          dayofweek: index + 1,
          open: daily_open,
          close: daily_close
        }))
    });

    const marketTime = new MarketTime(marketDefine);

    marketTimeCache.set(cacheKey, marketTime);
    return marketTime;
  }

  static factoryWindowTimezone (normal_daily_open, normal_daily_close, timeZone) {
    return this.factory(normal_daily_open, normal_daily_close, windowTimeZoneToIANA(timeZone));
  }
}

export class MarketTimeInDate extends AbstractMarket{
  /**
   *
   * @param {MarketTime} marketTime
   * @param {Date} date
   */
  constructor(marketTime, date) {
    super();
    this.time = marketTime.getOpenCloseInDate(date);
    this.dateName = AbstractMarket.getDateFormat(this.time.close);
  }

  /**
   *
   * @param {Date} date
   */
  isSameDay(date) {
    return this.time.open <= date && date < this.time.nextOpen;
  }

  isOpenInDay(date) {
    return this.time.open <= date && date < this.time.close;
  }
}
