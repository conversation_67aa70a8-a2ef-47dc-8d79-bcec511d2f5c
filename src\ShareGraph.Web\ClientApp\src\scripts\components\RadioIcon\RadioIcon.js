import { classNames } from '../../helper';

const RadioIcon = ({ isChecked }) => {
  return <svg
    width={28}
    height={29}
    viewBox="0 0 28 29"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={classNames('svg-radio-icon', { 'checked': isChecked })}
    aria-hidden="true"
  >
    <g filter="url(#filter0_d_498_487)">
      <circle cx={14} cy="14.9043" r={10}  />
      <circle cx={14} cy="14.9043" r={8}  strokeWidth={4} />
    </g>
    <defs>
      <filter
        id="filter0_d_498_487"
        x={0}
        y="0.904297"
        width={28}
        height={28}
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_498_487"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_498_487"
          result="shape"
        />
      </filter>
    </defs>
  </svg>;
};

export default RadioIcon;
