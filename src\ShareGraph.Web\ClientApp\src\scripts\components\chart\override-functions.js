import { CIQ } from './chartiq-import';
import i18n from '../../../scripts/services/i18n.js';
import {
  convertNumber,
  convertNumberInString,
  convertNumberNoSeparator,
  formatShortDate,
  getShortMonthNameByKeys,
  translateStringFormat
} from '../../helper.js';

CIQ.condenseInt = function (txt) {
  if (txt === null || typeof txt == 'undefined') return '';
  if (txt === Infinity || txt === -Infinity) return i18n.translate('notAvailableValue');

  if (!isNaN(txt)) {
    const txtAbs = Math.abs(txt);
    if (txtAbs >= 1000000000000)
      txt = translateStringFormat('condenseIntIntoT', [convertNumber(Math.round(txt / 100000000000) / 10)]);
    else if (txtAbs >= 100000000000)
      txt = translateStringFormat('condenseIntIntoB', [convertNumber(Math.round(txt / 1000000000))]);
    //100b
    else if (txtAbs >= 10000000000)
      txt = translateStringFormat('condenseIntIntoB', [
        convertNumber((Math.round(txt / 100000000) / 10), { decimalDigits: 1 })
      ]);
    //10.1b
    else if (txtAbs >= 1000000000)
      txt = translateStringFormat('condenseIntIntoB', [
        convertNumber((Math.round(txt / 10000000) / 100), { decimalDigits: 2 })
      ]);
    //1.11b
    else if (txtAbs >= 100000000)
      txt = translateStringFormat('condenseIntIntoM', [convertNumber(Math.round(txt / 1000000))]);
    //100m
    else if (txtAbs >= 10000000)
      txt = translateStringFormat('condenseIntIntoM', [
        convertNumber((Math.round(txt / 100000) / 10), { decimalDigits: 1 })
      ]);
    //10.1m
    else if (txtAbs >= 1000000)
      txt = translateStringFormat('condenseIntIntoM', [
        convertNumber((Math.round(txt / 10000) / 100), { decimalDigits: 2 })
      ]);
    //1.11m
    else if (txtAbs >= 100000)
      txt = translateStringFormat('condenseIntIntoK', [convertNumber(Math.round(txt / 1000))]);
    //100k
    else if (txtAbs >= 10000)
      txt = translateStringFormat('condenseIntIntoK', [
        convertNumber((Math.round(txt / 100) / 10), { decimalDigits: 1 })
      ]);
    //10.1k
    else if (txtAbs >= 1000)
      txt = translateStringFormat('condenseIntIntoK', [
        convertNumber((Math.round(txt / 10) / 100), { decimalDigits: 2 })
      ]);
    //1.11k
    else {
      txt = convertNumber(txt).toString();
    }
  } else {
    txt = txt.toString();
  }

  return txt;
};


/**
 * This is the object stored in `CIQ.ChartEngine.chart.xaxis` array which contains information regarding an x-axis tick.
 * See [CIQ.ChartEngine.AdvancedInjectable.createXAxis]{@link CIQ.ChartEngine.AdvancedInjectable#createXAxis} for more detail.
 * @constructor
 * @param {object} params input parameters
 * @param {number} params.hz Horizontal position of center of label in pixels. Any elements with negative positions will be off the edge of the screen and are only maintained to help produce a more predictable display as the chart is zoomed and panned.
 * @param {string} params.grid Either "line" or "boundary" depending on whether the label should be a date/time boundary or just a grid line
 * @param {string} params.text The text to display in the label
 * @param {string} [params.raw] The raw value used to generate the text
 * @param {number} [params.level] The order in which the label gets put into the axis (lowest numbers first)
 * @param {number} [params.marketOpenOffset] For minute labels, number of minutes between midnight market time and label time.  If provided, may be used to align levels with market open times regardless of display time zone.
 * @name CIQ.ChartEngine.XAxisLabel
 * @since 8.4.0 added raw and priority parameter, rationalized to object parameter
 */
CIQ.ChartEngine.XAxisLabel = function (params) {
  if (typeof params === 'number') {
    this.hz = params;
    this.grid = arguments[1];
    switch (typeof arguments[2]) {
      case 'number':
        this.text = convertNumberNoSeparator(arguments[2]);
        break;
      case 'string':
        this.text = arguments[2];
        if (/\d/.test(this.text)) {
          this.text = convertNumberInString(this.text);
        }
        break;
      default:
        this.text = arguments[2];
        break;
    }
  } else Object.assign(this, params);
  if (!this.level && this.level !== 0)
    // line level starts at CIQ.ChartEngine.XAxis.lineBaseLevel so it's always
    // higher than boundary level. As a result, boundary level should not exceed that value.
    this.level = this.grid === 'boundary' ? 0 : CIQ.ChartEngine.XAxis.lineBaseLevel;
    const shortMonthNameByKeys = getShortMonthNameByKeys();
  if (shortMonthNameByKeys[this.text]) this.text = shortMonthNameByKeys[this.text];
  if (typeof this.text === 'undefined') this.text = this.raw;
  this.text = this.text.toString();
};

/**
 * Displays a time in readable form. If Internationalization is in use then the time will be in 24 hour Intl numeric format
 * @param  {date} dt  JavaScript Date object
 * @param  {object} [stx] Chart object if Internationalization is in use
 * @param {number} [precision] Precision to use. If `null` then `hh:mm`. `CIQ.SECOND` then `hh:mm:ss`. If `CIQ.MILLISECOND` then `hh:mm:ss.mmmmm`
 * @return {string}     Human friendly time, usually hh:mm
 * @memberof CIQ
 */
CIQ.timeAsDisplay = function (dt, stx, precision) {
  var internationalizer = stx ? stx.internationalizer : null;
  if (internationalizer) {
    if (precision == CIQ.SECOND) return convertNumberInString(internationalizer.hourMinuteSecond.format(dt));
    else if (precision == CIQ.MILLISECOND)
      return convertNumberInString(internationalizer.hourMinuteSecond.format(dt) + '.' + dt.getMilliseconds());
    return convertNumberInString(internationalizer.hourMinute.format(dt));
  }
  var min = dt.getMinutes();
  if (min < 10) min = '0' + min;
  var str = dt.getHours() + ':' + min;
  var sec = '';
  if (precision <= CIQ.SECOND) {
    sec = dt.getSeconds();
    if (sec < 10) sec = '0' + sec;
    str += ':' + sec;
  }
  if (precision == CIQ.MILLISECOND) {
    var msec = dt.getMilliseconds();
    if (msec < 10) msec = '00' + msec;
    else if (msec < 100) msec = '0' + msec;
    str += '.' + msec;
  }
  return convertNumberInString(str);
};

/**
 * Creates a displayable date string according to the current chart settings and periodicity.
 *
 * Formats the date using one of the following formatters or default format (in order of
 * preference):
 *
 * 1. Chart x-axis formatter
 * 2. Chart engine internationalizer
 * 3. Default format
 *    <br>a. Daily &mdash; mm/dd/yyyy
 *    <br>b. Intraday &mdash; mm/dd hh:mm[:ss[:ms]]
 *
 * This method is used in {@link CIQ.ChartEngine.AdvancedInjectable#headsUpHR} to format the
 * date label that floats over the x-axis. Overwrite this method as needed to achieve the desired
 * date format.
 *
 * @param {CIQ.ChartEngine} stx The charting object.
 * @param {CIQ.ChartEngine.Chart} chart	The chart to which the date applies.
 * @param {Date} dt The date to format.
 * @param {boolean} [includeIntraYear] If true, include the year in the intraday dates.
 * @return {string} The formatted date.
 *
 * @memberof CIQ
 * @since
 * - 4.0.0
 * - 8.2.0 Added the `includeIntraYear` parameter.
 */
CIQ.displayableDate = function (stx, chart, dt, includeIntraYear) {
  function twoPlaces(val) {
    if (val < 10) return '0' + val;
    return val;
  }
  var displayableDate = '';
  var interval = stx.layout.interval;
  var isDaily = CIQ.ChartEngine.isDailyInterval(interval);
  var isSecond =
    (chart.xAxis.activeTimeUnit && chart.xAxis.activeTimeUnit <= CIQ.SECOND) ||
    stx.layout.timeUnit == 'second';
  var isMS =
    interval === 'tick' ||
    (chart.xAxis.activeTimeUnit && chart.xAxis.activeTimeUnit <= CIQ.MILLISECOND) ||
    stx.layout.timeUnit == 'millisecond';
  if (chart.xAxis.formatter) {
    displayableDate = chart.xAxis.formatter(dt);
    // }
    // else if (stx.internationalizer) {
    //   displayableDate = stx.internationalizer.monthDay.format(dt);
    //   if (isSecond || isMS) {
    //     displayableDate += ' ' + stx.internationalizer.hourMinuteSecond.format(dt);
    //     if (isMS) displayableDate += '.' + dt.getMilliseconds();
    //   } else if (!isDaily) {
    //     if (includeIntraYear) displayableDate = stx.internationalizer.yearMonthDay.format(dt);
    //     displayableDate += ' ' + stx.internationalizer.hourMinute.format(dt);
    //   } else {
    //     if (interval == 'month') displayableDate = stx.internationalizer.yearMonth.format(dt);
    //     else displayableDate = stx.internationalizer.yearMonthDay.format(dt);
    //   }
  } else {
    var m = twoPlaces(dt.getMonth() + 1);
    var d = twoPlaces(dt.getDate());
    var h = twoPlaces(dt.getHours());
    var mn = twoPlaces(dt.getMinutes());
    if (isDaily) {
      displayableDate = interval == 'month' ? m + '/' : m + '/' + d + '/';
      displayableDate += dt.getFullYear();
      if(interval == 'day' || interval =='week') {
        displayableDate = formatShortDate(dt, { isTimezone: false });
      }
    } else {
      let date = m + '/' + d;
      if (includeIntraYear) date += '/' + dt.getFullYear();
      displayableDate = date + ' ' + h + ':' + mn;
      if (isSecond || isMS) {
        var sec = twoPlaces(dt.getSeconds());
        displayableDate += ':' + sec;
        if (isMS) {
          var mil = twoPlaces(dt.getMilliseconds());
          if (mil < 100) mil = '0' + mil;
          displayableDate += ':' + mil;
        }
      }
    }
  }
  return convertNumberInString(displayableDate);
};

const oldDrawMountainChart = CIQ.ChartEngine.prototype.drawMountainChart;
CIQ.ChartEngine.prototype.drawMountainChart = function (_, settings) {
  const lineStyle = this.chart.lineStyle || {};
  const fillStyle = lineStyle.fillStyle;

  if(fillStyle) settings.fillStyle = fillStyle;

  return oldDrawMountainChart.apply(this, arguments);
};


// restore supportsAnimation config when set new main series renderer
const oldSetMainSeriesRenderer = CIQ.ChartEngine.prototype.setMainSeriesRenderer;
CIQ.ChartEngine.prototype.setMainSeriesRenderer = function () {
  const supportsAnimation = this?.mainSeriesRenderer?.supportsAnimation;
  const result = oldSetMainSeriesRenderer.apply(this, arguments);

  if(supportsAnimation !== undefined && this?.mainSeriesRenderer?.supportsAnimation !== undefined) {
    this.mainSeriesRenderer.supportsAnimation = supportsAnimation;
  }

  return result;
};

const displaySticky = CIQ.ChartEngine.prototype.displaySticky;
CIQ.ChartEngine.prototype.displaySticky = function (params) {
  if(params?.message) {
    params.message = this.translateIf(params.message);
  }
  return displaySticky.apply(this, arguments);
};


// Current price draw very soon in draw process
// That make mountain cover in price line
const drawCurrentPriceLineOld = CIQ.ChartEngine.prototype.drawCurrentPriceLine; 
CIQ.ChartEngine.prototype.drawCurrentPriceLine = function () {};
CIQ.ChartEngine.prototype.append('displayChart', function () {
  drawCurrentPriceLineOld.call(this);
});


// Avoid gap line in main chart 
// We remove all extra point added when attach series
CIQ.ChartEngine.prototype.append('updateChartData', function (appendQuotes, chart) {
  if(!chart) return;
  if(!appendQuotes || appendQuotes.length === 0) return;
  if(!chart.masterData) return;
  const masterData = chart.masterData;
  const filtered = masterData.filter(item => item.Close !== undefined);
  if(filtered.length < masterData.length) {
    chart.masterData = filtered;
  }
});

const oldHandleMouseOut = CIQ.ChartEngine.prototype.handleMouseOut;

CIQ.ChartEngine.prototype.handleMouseOut = function (e) {
  return oldHandleMouseOut.call(this, {...e, pageX: e.x, pageY: e.y});
};

// Remove scroll behavior after edit annotation done or cancel
CIQ.fixScreen = function () {
  // window.scrollTo(0, 0);
};