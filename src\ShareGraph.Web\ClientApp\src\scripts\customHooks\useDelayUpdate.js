import {throttle} from '@euroland/libs';
import { useMemo, useState} from 'react';

/**
 * @template T
 * @param {T} value 
 * @param { number } time  millisecond
 * @returns { T }
 */
export function useDelay(value, time) {
  const [state, setState] = useState(value);

  const callback = useMemo(() => throttle((val) => setState(val), time), [time]);

  useMemo(() => callback(value), [ callback, value ]);
  return state;
}