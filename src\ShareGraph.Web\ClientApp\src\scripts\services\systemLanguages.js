import {SYSTEM_LANGUAGES_QUERY} from '../graphql-queries/systemLanguagesQuery';
import {client} from './graphql-client';

/**
 * Extracts the type of an array item.
 * 
 * @template T
 * @typedef {T extends Array<infer R> ? R : never} ArrayElement
 */

export const systemLanguagesPromise = client.query(SYSTEM_LANGUAGES_QUERY).toPromise().then(item => {
  const mapping = item.data.systemLanguages.reduce((acc, item) => {
    acc[item.cultureCode.toLowerCase()] = item;
    return acc;
  }, /** @type {Record<string, ArrayElement<typeof item.data.systemLanguages>>} */({}));
  return {
    /**
     * 
     * @param {string} code 
     * @returns 
     */
    getCultureByCode(code) {
      return mapping[code.toLowerCase()];
    }
  };
});