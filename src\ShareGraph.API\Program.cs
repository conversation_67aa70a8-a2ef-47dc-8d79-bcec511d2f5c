using Euroland.FlipIT.ShareGraph.API;
using Microsoft.AspNetCore.Builder;

using Serilog;

var builder = WebApplication.CreateBuilder(args);

var logger = new LoggerConfiguration()
          .WriteTo.Console()
          .ReadFrom.Configuration(builder.Configuration)
          .CreateLogger();
var startup = new Startup(builder.Configuration, builder.Environment);

startup.ConfigureServices(builder.Services);

builder.Logging.AddSerilog(logger);


var app = builder.Build();
startup.Configure(app, app.Environment, logger);

app.Run();
