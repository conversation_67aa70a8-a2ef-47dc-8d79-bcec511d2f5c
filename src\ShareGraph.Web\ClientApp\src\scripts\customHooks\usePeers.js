import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { fetchPeers, selectPeers } from '../actions/peerActions';
import Peer from '../entities/Peer';

/**
 * React hook for managing peer information and selection within the application.
 * @returns {{
*    selectedPeer: Peer | null, // The currently selected peer.
*    peers: Peer[], // An array of all peers.
*    fetchLoadingPeer: boolean, // Indicates loading state for fetching peers.
*    onPeerSelectedChange: (instrumentId: string | null) => void, // Function for handling peer selection change.
*    peerContainerRef: React.MutableRefObject<HTMLElement | null>, // Reference to the peer container element.
*    selectedPeerIds: string[], // Array of selected peer IDs.
*    data: Peer[] // Raw data representing the peer instruments.
* }}
*/
const usePeers = () => {
  const instruments = useSelector(state => state.peers.instruments);
  const selectedPeerIds = useSelector(state => state.peers.selectedPeerIds);
  const fetchLoadingPeer = useSelector(state => state.peers.loading);
  const dispatch = useDispatch();
  const peerContainerRef = useRef();
  const refreshing = useSelector(state => state.peers.refreshing);

  const onPeerSelectedChange = instrumentId => {
    if (instrumentId) {
      dispatch(selectPeers(instrumentId));
    }
  };

  useEffect(() => {
    if (!refreshing) return;

    if (peerContainerRef.current) {
      const peerContainer = peerContainerRef.current;
      setTimeout(() => {
        peerContainer.classList.add('loading');
        setTimeout(() => {
          peerContainer.classList.remove('loading');
        }, 1000);
      }, 50);
    }
  }, [peerContainerRef, refreshing]);

  useEffect(() => {
    if ((instruments || []).some(instrument => !!instrument.shareName)) return;
    dispatch(fetchPeers());
  }, []);

  return {
      selectedPeer: new Peer(
        instruments.find(instrument => instrument.instrumentId === selectedPeerIds)
      ),
      peers: instruments.map(instrument => new Peer(instrument)),
      fetchLoadingPeer,
      onPeerSelectedChange,
      peerContainerRef,
      selectedPeerIds,
      data: instruments
    };
};

export default usePeers;
