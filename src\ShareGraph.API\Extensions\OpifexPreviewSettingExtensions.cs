using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Serilog.Core;
using System.IO;

namespace Euroland.FlipIT.ShareGraph.API.Extensions
{
  public static class OpifexPreviewSettingExtensions
  {
    public static void AddPreviewAppSetting(this IApplicationBuilder app, Logger logger)
    {
      app.UseAppSetting(builder =>
      {
        var options = app.ApplicationServices.GetRequiredService<IOptions<SettingOptions>>()?.Value;
        var env = app.ApplicationServices.GetRequiredService<IWebHostEnvironment>();
        var configuaration = app.ApplicationServices.GetRequiredService<IConfiguration>();
        var toolSettingRootPath = options?.ToolSettingRootDirectory;
        var generalSettingRootPath = options?.GeneralSettingRootDirectory;
        var generalSettingFile = Path.Combine(generalSettingRootPath, "setting.xml");
        var toolSettingFile = Path.Combine(toolSettingRootPath, options?.AppConfigurationFileName);
        
        var opifexToolCompanySettingsPath = configuaration.GetSection("OpifexToolCompanySettingsPath").Get<string>();
        var opifexGeneralSettingDirectory = configuaration.GetSection("OpifexGeneralSettingDirectory").Get<string>();
        if (string.IsNullOrEmpty(opifexToolCompanySettingsPath))
        {
          logger.Warning($"OpifexToolCompanySettingsPath configuration is missing in appSetting of {env.EnvironmentName}");
        }

        if (string.IsNullOrEmpty(opifexGeneralSettingDirectory))
        {
          logger.Warning($"opifexGeneralSettingDirectory configuration is missing in appSetting of {env.EnvironmentName}");
        }

        var opifexToolSettingsPath = string.IsNullOrEmpty(opifexToolCompanySettingsPath) ?
          Path.Combine(toolSettingRootPath, "Company") :
          Path.Combine(env.ContentRootPath, opifexToolCompanySettingsPath);

        var generalCompanySettingDirectory = string.IsNullOrEmpty(opifexGeneralSettingDirectory) ?
          Path.Combine(generalSettingRootPath, "Company") :
          Path.Combine(env.ContentRootPath, opifexGeneralSettingDirectory);


        builder.UseStaticSetting(generalSettingFile, true)  // Level #1
        .UseStaticSetting(toolSettingFile, false)    // Level #2
        .UseHttpRequestSetting(generalCompanySettingDirectory, SettingResourceType.Json_Xml, true) // Level #3
        .UseHttpRequestSetting(opifexToolSettingsPath, SettingResourceType.Json_Xml, false); // Level #5
      });
    }
  }
}
