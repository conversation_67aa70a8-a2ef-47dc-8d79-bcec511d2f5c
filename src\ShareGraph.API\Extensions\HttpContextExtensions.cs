using System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.ShareGraph.API.Extensions
{
  public static class HttpContextExtensions
  {
    public static string UrlContent(this HttpContext context, string relativePath = "~/")
    {
      if (relativePath == null)
      {
        throw new ArgumentNullException(nameof(relativePath));
      }

      if (!relativePath.StartsWith("~/"))
      {
        throw new ArgumentException($"Expecting path is a relative path and starts with \"~/\". Actual result: {relativePath}", nameof(relativePath));
      }

      var actionContext = new Microsoft.AspNetCore.Mvc.ActionContext()
      {
        HttpContext = context,
        RouteData = new Microsoft.AspNetCore.Routing.RouteData(context.Request.RouteValues),
        ActionDescriptor = new Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor()
      };


      var urlHelper = new Microsoft.AspNetCore.Mvc.Routing.UrlHelper(actionContext);

      return urlHelper.Content(relativePath);
    }

    /// <summary>
    /// Determines whether application is in preview mode requested from Opifex application.
    /// </summary>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    public static bool IsPreviewMode(this HttpContext httpContext)
    {
      var configuration = httpContext.RequestServices.GetService<IConfiguration>();
      var opifexPreviewEnabledSection = configuration.GetSection("OpifexPreviewEnabled");
      return opifexPreviewEnabledSection.Exists()
        && opifexPreviewEnabledSection.Get<bool>()
        && httpContext.Request.Query.ContainsKey("isPreview");
    }
  }
}
