import { useSelector } from 'react-redux';
import { createPatternSvg, generateChartPatternFromNumber } from '../../../utils';
import { useSelectedTicker } from '../../../customHooks/useTickers';
import i18n from '../../../services/i18n';

const Legend = ({instid,
  label,
  color,
  isSelected = false,
  clickedLegend = ()=>{},
  needShowBlindMode,
  isMainLegend,
  labelText,
  value
}) => {
  const isBlindMode = useSelector(state => state.blindMode.isBlindMode);
  const selectedTicker = useSelectedTicker();

  let defaultcheckBoxStyles = {
    '--legend__color': color
  };
  const handleLegendClick =() => {
      clickedLegend(instid);
  };



const getClassName = ()=>{
  let className = 'legend__color legend__checkbox';
  if(!isSelected) {
    return className += ' filter-low';
  }
  if(isSelected && isMainLegend){
    className += ' filter-high fs-tick-mark';
  }
  if(isSelected && !isMainLegend){
    className += ' filter-high fs-tick-mark';
  }
  return className;
};


return (
  <div className='legend' style={defaultcheckBoxStyles}>
        <button className={getClassName()} name="shareName" value={value} onClick = {handleLegendClick}
          {...(isMainLegend? {
            'role': 'presentation',
            'aria-label': i18n.translate('legendCheckedAsDefault', labelText)
          }:{
            'aria-labelledby': `legend-${instid}`,
            'role': 'checkbox',
            'aria-checked': isSelected
          })}
          tabIndex={-1}
          aria-hidden
        ></button>
        <span aria-hidden className='legend__label' onClick = {handleLegendClick} id={`legend-${instid}`}>{label}
          {needShowBlindMode && isBlindMode && <span className='legend__label__svg'>
            {createPatternSvg({
              pattern: selectedTicker.id === instid ? undefined : generateChartPatternFromNumber(instid),
              color
            })}
          </span> }
        </span>
      </div>
    );
  };

  export default Legend;
