const { readFile, writeFile, existsSync } = require('fs');
const { argv } = require('node:process');
const args = argv.slice(2);
const version_suffix_separator = '-';
//const csproj_file = '../Version.props';

var version, release_notes, csproj_file, index = 0;

do {
  switch ((args[index] || '').toLowerCase())
  {
    case '-v':
      version = args[++index];
      break;
    case '-r':
      release_notes = args[++index];
      break;
    case '-o':
      csproj_file = args[++index]
      break;
  }

} while (++index < args.length)

if(!existsSync(csproj_file)) {
  console.log(`[ERR] [release.js] File not found path=${csproj_file}`);
  process.exit(1);
}

readFile(csproj_file, 'utf8', function(err, data) {
  if(err) {
    console.log(`[ERR] [release.js] ${err}`);
    process.exit(1);
  }

  var result = data.replace(/(\<(?:Api|Web)BuildVersion\>).*?(\<\/(?:Api|Web)BuildVersion\>)/ig, `$1${version}$2`);

  writeFile(csproj_file, result, 'utf8', function(err1) {
    if(err) {
      console.log(`[ERR] [release.js] ${err1}`);
      process.exit(1);
    }
  })
});

// TODO Do handle replacing for other assets like docs/, etc.
