package-qa:
  tags:
    - vietnam-dev-shell
  extends:
    - .default-retry
  stage: pre:deploy
  dependencies:
    - build-qa
  variables:
    # Deploy package cached from build job, and to be used to extract to PUBLISH_API_PACKAGE_DIR
    DEPLOY_API_PACKAGE: 'src/ShareGraph.API/bin/Release/net6.0/publish/ShareGraph.API.zip'
    DELOY_WEB_PACKAGE: 'src/ShareGraph.Web/bin/Release/net6.0/publish/ShareGraph.Web.zip'
    # Directory contain the app content files which will be published to IIS using msdeploy.bat
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
  script:
    - |
      ECHO Uncompressing deploy package %DEPLOY_API_PACKAGE%...
      call "./build/zip.bat" --unzip --input "%DEPLOY_API_PACKAGE%" --output "%PUBLISH_API_PACKAGE_DIR%"
    - |
      ECHO Uncompressing deploy package %DELOY_WEB_PACKAGE%...
      call "./build/zip.bat" --unzip --input "%DELOY_WEB_PACKAGE%" --output "%PUBLISH_WEB_PACKAGE_DIR%"
  only:
    refs:
      - develope

package-staging:
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: pre:deploy
  dependencies:
    - build-staging
  variables:
    # Deploy package cached from build job, and to be used to extract to PUBLISH_API_PACKAGE_DIR
    DEPLOY_API_PACKAGE: 'src/ShareGraph.API/bin/Release/net6.0/publish/ShareGraph.API.zip'
    DELOY_WEB_PACKAGE: 'src/ShareGraph.Web/bin/Release/net6.0/publish/ShareGraph.Web.zip'
    # Directory contain the app content files which will be published to IIS using msdeploy.bat
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
  script:
    - |
      ECHO Uncompressing deploy package %DEPLOY_API_PACKAGE%...
      call "./build/zip.bat" --unzip --input "%DEPLOY_API_PACKAGE%" --output "%PUBLISH_API_PACKAGE_DIR%"
    - |
      ECHO Uncompressing deploy package %DELOY_WEB_PACKAGE%...
      call "./build/zip.bat" --unzip --input "%DELOY_WEB_PACKAGE%" --output "%PUBLISH_WEB_PACKAGE_DIR%"
  only:
    refs:
      - next

package-prod:
  tags:
    - ee-buildtest-shell
  extends:
    - .default-retry
  stage: pre:deploy
  dependencies:
    - build-prod
  variables:
    # Deploy package cached from build job, and to be used to extract to PUBLISH_API_PACKAGE_DIR
    DEPLOY_API_PACKAGE: 'src/ShareGraph.API/bin/Release/net6.0/publish/ShareGraph.API.zip'
    DELOY_WEB_PACKAGE: 'src/ShareGraph.Web/bin/Release/net6.0/publish/ShareGraph.Web.zip'
    # Directory contain the app content files which will be published to IIS using msdeploy.bat
    PUBLISH_API_PACKAGE_DIR: 'src/ShareGraph.API/bin/Release/net6.0/package'
    PUBLISH_WEB_PACKAGE_DIR: 'src/ShareGraph.Web/bin/Release/net6.0/package'
  script:
    - |
      ECHO Uncompressing deploy package %DEPLOY_API_PACKAGE%...
      call "./build/zip.bat" --unzip --input "%DEPLOY_API_PACKAGE%" --output "%PUBLISH_API_PACKAGE_DIR%"
    - |
      ECHO Uncompressing deploy package %DELOY_WEB_PACKAGE%...
      call "./build/zip.bat" --unzip --input "%DELOY_WEB_PACKAGE%" --output "%PUBLISH_WEB_PACKAGE_DIR%"
  only:
    refs:
      - master

