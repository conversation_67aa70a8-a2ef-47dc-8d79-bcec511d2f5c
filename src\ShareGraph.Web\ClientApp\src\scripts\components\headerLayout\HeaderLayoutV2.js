import { useContext } from 'react';
import LinkAccessible from './LinkAccessible';
import { AppContext } from '../../AppContext';
import { Ticker } from '../tickersV2/Ticker';
import BlindMode from '../BlindMode';
import UserInfo from '../UserInfo/UserInfo';

function HeaderLayout() {
  const settings = useContext(AppContext);

  return (
    <>
      <UserInfo />
      <div id="tickerLayout" className="tickerLayout">
        <LinkAccessible />
        {settings.ticker.enabledFormat.length > 0 && (
          <Ticker tickerType={settings.ticker.tickerType} />
        )}
        <BlindMode />
      </div>
    </>
  );
}

export default HeaderLayout;
