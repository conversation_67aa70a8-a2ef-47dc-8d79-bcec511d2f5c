import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchSharePriceDevelopmentByYears } from '../../actions/sharePriceDevelopmentByYearsAction';
import { AppContext } from '../../AppContext';
import { convertChangePercentDecimal, convertNumberInString, convertPercentDecimal, dynamicSort, formatChangeNumber, getCustomShareNameByInstrumentId } from '../../helper';
import i18n from '../../services/i18n';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';

const SharePriceDevelopmentByYears = () => {
  const dispatch = useDispatch();
  const fetchLoading = useSelector(state => state.performanceByYear.loading);
  const sPDByYearData = useSelector(state => state.performanceByYear.instruments);
  const [tableHeadings, setTableHeading] = useState([]);
  const settings = useContext(AppContext);
  const currentYear = (new Date()).getFullYear();
  const _formatDataTable = (datas) => {
    const instrumentSettings = settings.instruments.map(x => ({id: x.id, order: x.order}));
    const peersSettings = settings.peers.peers.map(x => ({id: x.id, order: 10000 + x.order}));
    const indicesSettings = settings.indices.indices.map(x => ({id: x.id, order : 20000 + x.order}));
    const sPDByYearSetting = [...instrumentSettings, ...indicesSettings, ...peersSettings];
    const showEarliestYearFirstSPByYear = settings.performance.showEarliestYearFirstSPByYear;

    const datasTable = datas.map(item => {
      let yearsItem = {};
      item.yearlyPerformances.forEach(y => {
        const { year, changePercentage } = y;
        yearsItem = {
          ...yearsItem,
          [year]: convertChangePercentDecimal(changePercentage || 0) + '%' };
      });
      const order = sPDByYearSetting.find(x => x.id === item.instrumentId).order;
      const shareName = getCustomShareNameByInstrumentId(item.instrumentId) || item.shareName;

      return {
        shareName,
        order,
        ...yearsItem
      };
    });

    let tableHeaders = [{ columnDisplay: i18n.translate('instrumentLabel') || 'Instrument', fieldName: 'shareName' }];
    let yearHeaders = Object.keys(datasTable[0]).filter(x => x !== 'shareName' && x !=='order').map(x => {
      if (x !== 'shareName') {
        const keyTranslation = x + 'Label';
        return { columnDisplay: i18n.translate(keyTranslation) || convertNumberInString(x), fieldName: x };
      }
    });
    const yearHeadersSorting =  showEarliestYearFirstSPByYear ? yearHeaders.sort(dynamicSort('fieldName')) : yearHeaders.sort(dynamicSort('fieldName')).reverse();
    tableHeaders = [...tableHeaders, ...yearHeadersSorting];
    setTableHeading(tableHeaders);

    return datasTable;
  };

  const datasTable = useMemo(() => {
    if(!fetchLoading) {
      return _formatDataTable(sPDByYearData).sort(dynamicSort('order'));
    }

    return [];
  }, [sPDByYearData, fetchLoading]);

  useEffect(() => {
    dispatch(fetchSharePriceDevelopmentByYears());
  }, []);

  useChangeQuoteCurrency(() => {
    dispatch(fetchSharePriceDevelopmentByYears());
  });

  return (
    <div className='spd-accessibility'>
      <table className='accessibility-table'>
        <caption>
          {i18n.translate('sharePriceDevelopmentByYearsCaption')}
        </caption>
        <thead>
          <tr>
            {tableHeadings.map(item => (
              <th scope='col' key={item.columnDisplay}>{item.columnDisplay} {currentYear == item.columnDisplay ? (
                <span  aria-labelledby='indicatesCurrentYear'>*</span>
              ) : ''}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {datasTable.map((item, i) => (<tr key={i}>
            {tableHeadings.map(head => (
              <td key={head.fieldName}>{
                item[head.fieldName]
              }</td>
            ))}
          </tr>))}
        </tbody>
      </table>
      <p id='indicatesCurrentYear' className='mt-10'>* {i18n.translate('indicatesCurrentYear')}</p>
    </div>
  );
};

export default SharePriceDevelopmentByYears;