.promode-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.32);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.promode-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-width: 1600px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  border: 1px solid #e8eaed;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8eaed;
    background: #ffffff;
    
    h2 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #202124;
      font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
  }
  
  &__close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    color: #5f6368;
    border-radius: 20px;
    transition: background-color 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background: #f1f3f4;
    }
    
    &:active {
      background: #e8eaed;
    }
  }
  
  &__body {
    display: flex;
    height: 650px;
  }
  
  &__tabs {
    display: flex;
    flex-direction: column;
    width: 60px;
    background: #ffffff;
    border-right: 1px solid #e8eaed;
  }
  
  &__tab {
    background: none;
    border: none;
    padding: 12px 8px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    color: #5f6368;
    transition: all 0.2s ease;
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 55px;

    &:hover {
      background: #f1f3f4;
    }
    
    & .fs-alert {
      padding: 0;
    }

    &.active {
      color: #1a73e8;
      background: #e8f0fe;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: #1a73e8;
      }
    }
  }
  
  &__content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: #ffffff;
  }
  
  &__tab-content {
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
      color: #202124;
      font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    p {
      margin: 0;
      color: #5f6368;
      line-height: 1.5;
      font-size: 14px;
      font-family: Roboto, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .promode-modal {
    margin: 16px;
    max-width: none;
    width: calc(100% - 32px);
    border-radius: 8px;
    
    &__header {
      padding: 12px 16px;
      
      h2 {
        font-size: 14px;
      }
    }
    
    &__body {
      flex-direction: column;
      height: auto;
    }
    
    &__tabs {
      width: 100%;
      flex-direction: row;
      overflow-x: auto;
      padding: 8px 16px;
      border-right: none;
      border-bottom: 1px solid #e8eaed;
      
      &::-webkit-scrollbar {
        height: 0;
      }
    }
    
    &__tab {
      flex-shrink: 0;
      white-space: nowrap;
      margin: 0 4px;
      
      &.active::after {
        left: 0;
        right: 0;
        top: auto;
        bottom: 0;
        width: auto;
        height: 2px;
        border-radius: 2px 2px 0 0;
      }
    }
    
    &__content {
      padding: 16px;
    }
  }
}
