/* eslint-disable no-irregular-whitespace */
// import styled from "styled-components";

export const NEXT = '<i class="fs fs-caret-right"></i>';
export const PREV = '<i class="fs fs-caret-left"></i>';

export const CarouselContainer = props => {
  const getClassNameContainer = () => {
    if (!props.isSlider) {
      return { className: 'carousel__container', transform: 'translateX(0)' };
    }
    if (!props.sliding) {
      return {
        className: 'carousel__container sliding',
        //transform: `translateX(calc(-100% / ${props.slidesPerView}))`
        transform: 'translateX(0)'
      };
    }

    if (props.dir === PREV) {
      return {
        className: 'carousel__container sliding--prev',
        //transform: `translateX(calc(-200% / ${props.slidesPerView}))`
        transform: 'translateX(-200%)'
      };
    }
    return {
      className: 'carousel__container',
      transform: 'translateX(100%)'
    };
  };

  return (
    <div
      className={getClassNameContainer().className}
      style={{
        display: 'flex' /* ,
        transition: props.sliding ? 'none' : 'transform .5s ease',
        transform: getInfo() */,
        transform: getClassNameContainer().transform
      }}
    >
      {props.children}
    </div>
  );
};

export const Wrapper = props => {
  return <div className="carousel__wrapper">{props.children}</div>;
};

export const CarouselSlot = props => {
  return (
    <div
      className={props.order > 0 && props.order <= props.slidesPerView ? 'carousel__item active' : 'carousel__item'}
      //  style={{ order: props.order, flex: `0 0 ${props.carouselItemWidth}px` }}
      style={{ order: props.order, flex: '1 0 100%' }}
    >
      {props.children}
    </div>
  );
};

export const SlideButton = props => {
  return (
    <button {...props} className={`carousel__btn ${props.className ? props.className : ''}`}>
      {props.children}
    </button>
  );
};
