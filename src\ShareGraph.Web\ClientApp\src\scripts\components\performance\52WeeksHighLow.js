import { useEffect, useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import i18n from '../../services/i18n';
import { dynamicSort, convertChangePercentDecimal } from '../../helper';
import { fetch52Weeks } from '../../actions/52WeeksAction';
import { AppContext } from '../../AppContext';
import Graph from './52WeeksGraph';
import { Skeleton } from '../Skeleton';
import { useSharePriceOrder } from '../../customHooks/useSharePriceOrder';
import { TableV2 } from '../commons/tableV2/Table';
import TickerName from '../TickerName';
import { useFormatNumberByInstrument } from '../../customHooks/useFormatNumberByInstrument';
import { useChangeQuoteCurrency } from '../../customHooks/useSelectedCurrency';

export const Weeks52HighLow = ({ format, refreshTime }) => {
  const { formatNumberByInstrument } = useFormatNumberByInstrument();
  const settings = useContext(AppContext);
  const weeks52List = useSelector(state => state.week52.instruments);
  const fetchLoading = useSelector(state => state.week52.loading);
  const { sharePriceOrders } = useSharePriceOrder();
  const dispatch = useDispatch();

  useEffect(() => {
    function fetchData() {
      dispatch(fetch52Weeks());
    }
    fetchData();
    const timer = setInterval(() => {
      fetchData();
    }, refreshTime * 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [dispatch, refreshTime]);

  useChangeQuoteCurrency(() => {
    dispatch(fetch52Weeks());
  });

  /*TODO commbine data ticker, peer, indices.Consider get data from state or create new api to get data.
  Because. peerList, indices not get low52W, high52W
  */
  const columnsAvailable = [
    { columnDisplay: i18n.translate('sharesLabel'), fieldName: 'shareName' },
    { columnDisplay: i18n.translate('priceLabel'), fieldName: 'last' },
    { columnDisplay: i18n.translate('weeks52LowLabel'), fieldName: 'low52W' },
    { columnDisplay: i18n.translate('weeks52HighLabel'), fieldName: 'high52W' },
    { columnDisplay: i18n.translate('percentFrom52WLowLabel'), fieldName: 'percent52WLow' },
    { columnDisplay: i18n.translate('percentFrom52WHighLabel'), fieldName: 'percent52WHigh' }
  ];
  const columnHeadings = columnsAvailable
    .filter(p => settings.performance.enable52WTableColumns.includes(p.fieldName.toUpperCase()))
    .map(item => ({
      ...item,
      order: settings.performance.enable52WTableColumns.indexOf(item.fieldName.toLocaleUpperCase())
    }))
    .sort(dynamicSort('order'));

  const indicatorColumns = ['percent52WLow', 'percent52WHigh'];

  const graphColumnsAvailable = [
    { columnDisplay: i18n.translate('sharesLabel'), fieldName: 'shareName' },
    { columnDisplay: i18n.translate('weeks52LowLabel'), fieldName: 'low52W' },
    { columnDisplay: i18n.translate('weeks52HighLabel'), fieldName: 'high52W' }
  ];

  const graphColumsHeadings = graphColumnsAvailable.filter(p =>
    settings.performance.enable52WTableColumns.includes(p.fieldName.toUpperCase())
  );

  const _normalizeData = item => {
    const data = { ...item };
    const instrumentId = item.instrumentId;
    //TODO format fields which will be need format
    let valueLast = parseFloat(item.last);
    let valueLow = parseFloat(item.low52W);
    let valueHigh = parseFloat(item.high52W);
    let percent52W_Low = parseFloat((valueLast / valueLow - 1) * 100);
    let percent52W_High = parseFloat((1 - valueLast / valueHigh) * 100);
    data.last = item.last ? formatNumberByInstrument(item.last, instrumentId) : '';
    data.low52W = item.low52W ? formatNumberByInstrument(item.low52W, instrumentId) : '';
    data.high52W = item.high52W ? formatNumberByInstrument(item.high52W, instrumentId) : '';
    // item.low52W ? data.percent52WLow = parseFloat(convertNumberDecimal(((valueLast / valueLow) - 1) * 100)) : '';
    // item.high52W ? data.percent52WHigh = parseFloat(convertNumberDecimal((1 - (valueLast / valueHigh)) * 100)) : '';

    data.percent52WLow = item.low52W
      ? parseFloat(String(percent52W_Low).replace('-', ''))
      : '';
    data.percent52WHigh = item.high52W
      ? parseFloat(percent52W_High)
      : '';

    return data;
  };

  const _normalizeDataTable = item => {
    const data = { ...item };
    const instrumentId = item.instrumentId;
    let valueLast = parseFloat(item.last);
    let valueLow = parseFloat(item.low52W);
    let valueHigh = parseFloat(item.high52W);
    // let percent52WLow = ((valueLast / valueLow) - 1) * 100;
    // let percent52WHigh = -(1 - (valueLast /valueHigh)) * 100;
    let percent52W_Low = (valueLast / valueLow - 1) * 100;
    let percent52W_High = -(1 - valueLast / valueHigh) * 100;
    let percent52WLowDisplay = parseFloat(percent52W_Low);
    let percent52WHighDisplay = parseFloat(percent52W_High);
    const shareName = item.shareName;

    data.shareName = shareName ? [{ value: shareName, display: <TickerName instrumentId={item.instrumentId} marketAbbreviation={item.marketAbbreviation} shareName={item.shareName} /> }] : [];
    data.last = [{ value: item.last, display: formatNumberByInstrument(item.last, instrumentId) }];
    data.low52W = [{ value: item.low52W, display: formatNumberByInstrument(item.low52W, instrumentId) }];
    data.high52W = [{ value: item.high52W, display: formatNumberByInstrument(item.high52W, instrumentId) }];
    data.percent52WLow = [
          { value: percent52W_Low, display: convertChangePercentDecimal(Number(percent52WLowDisplay)) }
        ];
    data.percent52WHigh = [
          { value: percent52W_High, display: convertChangePercentDecimal(Number(percent52WHighDisplay)) }
        ];

    return data;
  };
  // const test =[{}];
  const rowDatas = weeks52List
    .map(item => ({ ..._normalizeData(item), order: sharePriceOrders[item.instrumentId] }))
    .sort(dynamicSort('order'));
  const dataFortable = weeks52List
    .map(item => ({ ..._normalizeDataTable(item), order: sharePriceOrders[item.instrumentId] }))
    .sort(dynamicSort('order'));
  const tableSummary = i18n.translate('tableSummaryText');
  const tableCaption = i18n.translate('tableCaptionText');
  return (
    <>
      {fetchLoading && <Skeleton style={{ height: '500px' }}></Skeleton>}
      {!fetchLoading && format === 'TABLE' && (
        <div id='52whl-table' role='tabpanel' aria-labelledby='tab-52whl-table' tabIndex={-1} hidden={format !== 'TABLE'} className="table-responsive">
          <TableV2
            indicatorColumns={indicatorColumns}
            headings={columnHeadings}
            datas={dataFortable}
            className={'performance--table weeks52highlow'}
            summary={tableSummary}
            caption={tableCaption}
          />
        </div>
      )}
      {!fetchLoading && format === 'GRAPH' && (
        <div id='52whl-graph' role='tabpanel' aria-labelledby='tab-52whl-graph' tabIndex={-1} hidden={format !== 'GRAPH'} className='weeks52graph'>
          <Graph
            headings={graphColumsHeadings}
            datas={rowDatas}
            summary={tableSummary}
            caption={tableCaption}
            weeks52PriceList={weeks52List}
          />
        </div>
      )}
    </>
  );
};
