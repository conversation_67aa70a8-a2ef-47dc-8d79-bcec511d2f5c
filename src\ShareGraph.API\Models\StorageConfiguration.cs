using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Euroland.FlipIT.ShareGraph.API.Models
{
  public class StorageConfiguration
  {
    public AzureStorage AzureStorage { get; set; }
    public int MultipartBodyLengthLimit { get; set; }
    public Screenshot Screenshot { get; set; }
  }

  public class AzureStorage
  {
    public string ConnectionString { get; set; }
    public Retry Retry { get; set; }
  }
  public class Retry
  {
    public int MaxRetry { get; set; }
    public int MaxDelay { get; set; }
  }

  public class Screenshot
  {
    public string BlobContainerName { get; set; }
    public string[] PermittedExtensions { get; set; }
    public int FileSizeLimit { get; set; }
  }
}
