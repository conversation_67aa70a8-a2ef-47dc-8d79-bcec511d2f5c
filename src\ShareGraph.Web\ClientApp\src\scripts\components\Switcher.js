/* eslint-disable jsx-a11y/interactive-supports-focus */
import { useRef, useState } from 'react';
import ToolTip from './commons/ToolTip';

const Switcher = ({
  onClick,
  className,
  tabs,
  tabActive,
  isButton,
  ariaLabel
}) => {
  const [tabFocus, setTabFocus] = useState(tabActive);
  const tabRef = useRef();

  const handleKeyDown = (e) => {
    const tabElements = tabRef.current.querySelectorAll('[role="tab"]');
    const currentTabIndex = tabs.findIndex((item) => item.type === tabFocus);
    let tabFocusIndex;
    if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
      if (e.key === 'ArrowRight') {
        tabFocusIndex =
          currentTabIndex < tabs.length - 1 ? currentTabIndex + 1 : 0;
      } else if (e.key === 'ArrowLeft') {
        tabFocusIndex =
          currentTabIndex > 0 ? currentTabIndex - 1 : tabs.length - 1;
      }
      setTabFocus(tabs[tabFocusIndex].type);
      if (tabRef.current && tabElements && tabElements.length > 0) {
        tabElements[tabFocusIndex]?.focus();
      }
    }
  };

  return (
    <div
      className={`switcher ${className ? className : ''}`}
      aria-label={ariaLabel}
      role="tablist"
      onKeyDown={handleKeyDown}
      ref={tabRef}
    >
      {tabs.map((tab, index) => {
        return (
          <div key={index} className={`switcher__item ${tabActive === tab.type ? 'active' : ''}`}>
            {isButton && (
              <button
                id={`tab-${tab.id}`}
                role="tab"
                aria-selected={tabActive === tab.type}
                aria-label={tab.text}
                tabIndex={tabFocus === tab.type ? 0 : -1}
                aria-controls={tab.id}
                type="button"
                onClick={() => onClick(tab.type)}
                className="switcher__btn tooltip-wrapper"
              >
                <ToolTip right>{tab.dataTooltip}</ToolTip>
                {<i name={tab.format} className={`fs ${tab.icon}`}></i>}
              </button>
            )}

            {!isButton && (
              <button
                id={`tab-${tab.id}`}
                role="tab"
                aria-selected={tabActive === tab.type}
                aria-label={tab.text}
                tabIndex={tabFocus === tab.type ? 0 : -1}
                aria-controls={tab.id}
                type="button"
                onClick={() => onClick(tab.type)}
                className=" switcher__item-title"
              >
                {tab.text}
              </button>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Switcher;
