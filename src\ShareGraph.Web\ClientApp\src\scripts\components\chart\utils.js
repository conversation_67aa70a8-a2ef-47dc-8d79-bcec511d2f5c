import { CIQ, timezoneJS } from './chartiq-import.js';
import i18n from '../../../scripts/services/i18n.js';
import {findIana} from 'windows-iana';
import { CHART } from '../../common.js';
export const handleHighLightSeries = chartEngine => {
  // if something is highlighted, let's find what that is.
  if (chartEngine.anyHighlighted) {
    // loop through the series and see if anything is highlighted
    const chart = chartEngine.currentPanel.chart;
    for (const r in chart.seriesRenderers) {
      const renderer = chart.seriesRenderers[r];
      for (let sp = renderer.seriesParams.length - 1; sp >= 0; sp--) {
        const series = renderer.seriesParams[sp];
        if (series.highlight) {
          highlightCQComparisonItem(chartEngine, series);
          // swapOrderHighlightCQComparisonItem(chartEngine, series.symbol);
        } else {
          removeHighlightedCQComparisonItem(chartEngine, series.symbol);
        }
      }
    }
  } else {
    removeAllHighlightedCQComparisonItem(chartEngine);
  }
};

export const highlightCQComparisonItem = (chartEngine, series) => {
  const cqSymbol = series.symbol;
  const cqComparisonKeyDOM = chartEngine.container
    .querySelector('.cq-comparison-key-wrapper cq-comparison-key')
    .querySelector(`[cq-symbol="${cqSymbol}"]`);

  if(!cqComparisonKeyDOM) return;
  cqComparisonKeyDOM.classList.add('highlighted');
  const opacityBackground =
    parseInt(getComputedStyle(cqComparisonKeyDOM).getPropertyValue('--opacity-background')) || 95;
  // cqComparisonKeyDOM.style.setProperty('background-color', lighter(series.color, opacityBackground));
};

export const removeHighlightedCQComparisonItem = (chartEngine, cqSymbol) => {
  const cqComparisonKeyDOM = chartEngine.container
    .querySelector('.cq-comparison-key-wrapper cq-comparison-key')
    .querySelector(`cq-comparison-item[cq-symbol="${cqSymbol}"]`);

  cqComparisonKeyDOM?.classList?.remove('highlighted');
  cqComparisonKeyDOM?.style?.removeProperty('background-color');
};

export const removeAllHighlightedCQComparisonItem = chartEngine => {
  const cqComparisonKeyDOM = chartEngine.container
    .querySelector('.cq-comparison-key-wrapper cq-comparison-key')
    .querySelectorAll('cq-comparison-item');
  cqComparisonKeyDOM.forEach(itemDom => itemDom.classList?.remove('highlighted'));
};

export const handleShowMoreShowLessStudyLegend = (chartEngine, cqComparisonKeyWrapperRef) => {
  const cqComparisonKeyWrapperDOM = chartEngine.container.querySelector('.cq-comparison-key-wrapper');
  const cqComparisonKeyDOM = chartEngine.container.querySelector(
    '.cq-comparison-key-wrapper cq-comparison-key'
  );
  const showMoreBtnDOM = chartEngine.container.querySelector('#custom-comparison .show-more-btn');
  if (!cqComparisonKeyDOM || !showMoreBtnDOM) return;
  if (!showMoreBtnDOM.textContent) {
    showMoreBtnDOM.innerHTML = i18n.translate('showMoreBtn');
  }
  const comparisonKeyWrapperHeight =
    parseInt(getComputedStyle(cqComparisonKeyWrapperDOM).getPropertyValue('--comparisonKeyWrapperHeight')) ||
    30;

  const handleClickShowMore = () => {
    const typeBtn = showMoreBtnDOM.dataset.typeBtn;
    if (typeBtn === 'show-more-btn') {
      showMoreBtnDOM.setAttribute('data-type-btn', 'show-less-btn');
      showMoreBtnDOM.innerHTML = i18n.translate('showLessBtn');
      cqComparisonKeyWrapperDOM.style.setProperty('height', 'unset');
      return;
    }
    showMoreBtnDOM.setAttribute('data-type-btn', 'show-more-btn');
    showMoreBtnDOM.innerHTML = i18n.translate('showMoreBtn');
    cqComparisonKeyWrapperDOM.style.setProperty('height', '');
  };

  const handleResizeShowMore = () => {
    for (let i = 0; i < cqComparisonKeyDOM.children.length; i++) {
      cqComparisonKeyDOM.children[i].style.setProperty('order', i);
    }

    if (cqComparisonKeyDOM.offsetHeight > comparisonKeyWrapperHeight) {
      showMoreBtnDOM.style.setProperty('display', 'block');
      handleOverlapHighValueLineWithShowMoreBtn(chartEngine, { showMoreBtnDOM, isShowMore: true });
      return;
    }
    showMoreBtnDOM.style.setProperty('display', 'none');
    handleOverlapHighValueLineWithShowMoreBtn(chartEngine, { showMoreBtnDOM, isShowMore: false });
  };

  if (!cqComparisonKeyWrapperRef.current) {
    cqComparisonKeyWrapperRef.current = new MutationObserver(handleResizeShowMore);
    cqComparisonKeyWrapperRef.current.observe(cqComparisonKeyDOM, { childList: true });
  }

  CIQ.safeClickTouch(showMoreBtnDOM, handleClickShowMore);
  window.addEventListener('resize', handleResizeShowMore);
  return () => {
    window.removeEventListener('resize', handleResizeShowMore);
    CIQ.clearSafeClickTouches(showMoreBtnDOM);
  };
};

export const handleOverlapHighValueLineWithShowMoreBtn = (chartEngine, { showMoreBtnDOM, isShowMore }) => {
  if (!chartEngine || !showMoreBtnDOM) return;
  const highValueLineDOM = chartEngine.container.querySelector('.high-value-line');
  if (!highValueLineDOM) return;
  const baseOffsetRight = 25;
  if (!isShowMore) {
    highValueLineDOM.style.right = baseOffsetRight + 'px';
    return;
  }
  const highValueLineRect = highValueLineDOM.getBoundingClientRect();
  const showMoreRect = showMoreBtnDOM.getBoundingClientRect();
  const deltaTop = Math.abs(highValueLineRect.top - showMoreRect.top);
  const deltaRight = Math.abs(highValueLineRect.right - showMoreRect.right);
  const maxHeight = Math.max(highValueLineRect.height, showMoreRect.height);
  const maxWidth = Math.max(highValueLineRect.width, showMoreRect.width);
  if (deltaTop < maxHeight || deltaRight < maxWidth) {
    highValueLineDOM.style.right = baseOffsetRight + showMoreRect.width + 'px';
  }
};

export const swapOrderHighlightCQComparisonItem = (chartEngine, cqSymbol) => {
  const highlightedCQComparisonKeyDOM = chartEngine.container
    .querySelector('.cq-comparison-key-wrapper cq-comparison-key')
    .querySelector(`[cq-symbol="${cqSymbol}"]`);
  const firstCQComparisonKeyDOM = chartEngine.container
    .querySelector('.cq-comparison-key-wrapper cq-comparison-key')
    .querySelector('[style*="order: 1;"]');

  if(!highlightedCQComparisonKeyDOM || !firstCQComparisonKeyDOM) return;
  const oldOrder = highlightedCQComparisonKeyDOM.style.order;

  firstCQComparisonKeyDOM.style?.setProperty('order', oldOrder);
  highlightedCQComparisonKeyDOM.style?.setProperty('order', 1);
};

export function timeZoneOffsetInMinutes(ianaTimeZone) {
  const now = new Date();
  now.setSeconds(0, 0);

  // Format current time in `ianaTimeZone` as `M/DD/YYYY, HH:MM:SS`:
  const tzDateString = now.toLocaleString('en-US', {
    timeZone: ianaTimeZone,
    hourCycle: 'h23'
  });

  // Parse formatted date string:
  const match = /(\d+)\/(\d+)\/(\d+), (\d+):(\d+)/.exec(tzDateString);
  const [_, month, day, year, hour, min] = match.map(Number);

  // Change date string's time zone to UTC and get timestamp:
  const tzTime = Date.UTC(year, month - 1, day, hour, min);

  // Return the offset between UTC and target time zone:
  return Math.floor((tzTime - now.getTime()) / (1000 * 60));
}

export function windowTimeZoneToIANA (windowTimeZone) {
  const result = findIana(windowTimeZone);
  const groupByOffset = Object.entries(timezoneJS.timezone.zones).reduce((acc, [key, [value]]) => {
    if(typeof value === 'string') return acc;
    if(!(value[0] in acc)) acc[value[0]] = [];
    acc[value[0]].push(key);
    return acc;
  }, {});
  if(result.length === 0) throw new Error(`Can not find window timezone of ${windowTimeZone}`);
  const timeZoneChartSupport = result.find(item => Boolean(timezoneJS.timezone.zones[item]));

  if(!timeZoneChartSupport) {
    try {
      const offset = timeZoneOffsetInMinutes(result[0]);
      return groupByOffset[offset][0];
    } catch {
      throw new Error(`chartiq do not support timezone ${JSON.stringify(result)}`);
    }
  }
  return timeZoneChartSupport;
}

export function getLayout(chartEngine) {
  let layoutObj = chartEngine.exportLayout(true);
  //must be update range selector because in case scrolling chart then setSpan object is null
  if(!layoutObj.setSpan || Object.keys(layoutObj.setSpan).length === 0) {
    const currentRange = CHART.RANGE.find(x=> x.key === chartEngine.range.selectedRange);
    let ranges = chartEngine.ranges || {
      multiplier: currentRange.multiplier,
      base: currentRange.base,
      periodicity: {
        interval: currentRange.periodicity.interval,
        period: currentRange.periodicity.period,
        timeUnit: currentRange.periodicity.timeUnit
      }
    };
    ranges.periodicity.timeUnit = layoutObj.interval;
    layoutObj.setSpan = ranges;
  }
  delete layoutObj['drawing-annotation'];

  layoutObj.CurrentEvent = chartEngine.chart.CurrentEvent;
  layoutObj.panels.chart.yAxis.displayGridLines = chartEngine.panels.chart.yAxis.displayGridLines;
  layoutObj.panels.chart.yAxis.yaxisLabelStyle = chartEngine.panels.chart.yAxis.yaxisLabelStyle;
  return JSON.stringify(layoutObj);
}

export function setAtLeastMaxTicks(chartEngine, atLeastTicks) {
  const candleWidth = chartEngine.chart.width / (Math.max(chartEngine.chart.maxTicks + 10, atLeastTicks));
  chartEngine.setCandleWidth(candleWidth);
  chartEngine.draw();
}

export function getStudies(chartEngine){
  if(!chartEngine) return {};
  return Object.values(chartEngine.layout?.studies || {}).reduce((s, value) => {
    s[value.type]  = value;
    return s;
   }, {});
}

export function showEvents(chartEngine) {
  if (!chartEngine) return;
  chartEngine.container.querySelectorAll('.stx-marker.stx-marker-video-wrapper.video').forEach(videoNode => {
    videoNode.classList.add('active--marker');
  });
}

export function hideEvents(chartEngine) {
  if (!chartEngine) return;
  chartEngine.container
    .querySelectorAll('.stx-marker.stx-marker-video-wrapper.video.active--marker')
    .forEach(videoNode => {
      videoNode.classList.remove('active--marker');
    });
}
