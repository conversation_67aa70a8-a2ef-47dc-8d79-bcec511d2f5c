import {windowTimeZoneToIANA} from './utils';
import { CIQ } from './chartiq-import';

/**
 * 23:59 => 00:00, 00:59 => 01:00
 * @param {string} str
 */
function addMoreMinus(str) {
  const time = /** @type{{ hours: number, minutes: number }}*/(CIQ.Market._timeSegment._splitHoursMinutes(str));
  time.minutes += 1;

  if(time.minutes > 59) {
    time.minutes = 0;
    time.hours += 1;
  }

  if(time.hours > 23) time.hours = 0;

  const formatNumber = (num) => num < 10 ? `0${num}` : num;
  return `${formatNumber(time.hours)}:${formatNumber(time.minutes)}`;
}

/**
 *
 * @param {import('redux').Store<any, import('redux').AnyAction>} store
 * @param {*} symbolObject
 * @returns
 */
export default function marketFactory(store, symbolObject) {
  const state = store.getState();
  const market = state.tickers.instruments.find(item => item.instrumentId === symbolObject.instrumentId);

  const normal_daily_open = market.normalDailyOpen.replace(/(:00)$/, '');
  let normal_daily_close = market.normalDailyClose.replace(/(:00)$/, '');

  /**
   * This chart displays market transactions within the range: (open time) <= data < (close time).
   * To include transactions at the close time, a workaround is applied by subtracting one more minute.
   *
   * @summary
   * open: 09:00
   * close: 15:30
   * We want the chart to include transactions at 15:30,
   * so we trick it by changing the close time to 15:31.
   */
  normal_daily_close = addMoreMinus(normal_daily_close);

  const beginningDayOfWeek = market.businessDaysStoT ? 2 : 1;
  const log = (data) => {console.log(data); return data;};
  return log({
    name: market.marketName,
    market_tz: windowTimeZoneToIANA(market.marketTimeZone),
    hour_aligned: false,
    beginningDayOfWeek,
    normal_daily_open,
    normal_daily_close,
    rules: Array(5).fill(1).map((_, index) =>
     ({
      dayofweek: index + 1,
      open: normal_daily_open,
      close: normal_daily_close
    }))
  });
}
