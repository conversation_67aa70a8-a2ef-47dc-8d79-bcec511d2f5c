import { CIQ } from '../chartiq-import';
import { translateStringFormat } from '../../../helper';
import { createPatternSvg } from '../../../utils';
const CQStudies = CIQ.UI.components('cq-studies')[0].classDefinition;
const CQComparisonDefinition = CIQ.UI.components('cq-comparison')[0].classDefinition;


function createPatternSvgElement(pattern, color) {
  const svgString = createPatternSvg({
    pattern,
    color,
    isReturnString: true
  });

  const parser = new DOMParser();
  const doc = parser.parseFromString(svgString, 'image/svg+xml');
  const svgElement = doc.documentElement;
  svgElement.style.marginRight = 10;

  return svgElement;
}

//TODO: create new CQCpmparisonPrice
CQComparisonDefinition.prototype.renderLegend = function () {
  if (this.currentlyDisabling) return;
  this.pickSwatchColor();
  const key = CIQ.cqvirtual(this.find('[comparison-key]'));
  if (!key) return;

  const tapFunction = (s, series) => {
    return () => {
      this.nomore = true;
      if (!series.parameters.permanent) {
        this.removeSeries(s, series);
        this.emitCustomEvent({
          effect: 'remove',
          detail: { name: s }
        });
      }
      this.modalEnd(); // tricky, we miss mouseout events when we remove items from under ourselves
    };
  };

  const getToggleHandle = (series, stx) => {
    return (e) => {
      const { id, parameters } = series,
        disabled = !parameters.disabled;
      e.stopPropagation();
      this.currentlyDisabling = true;
      stx.modifySeries(id, { disabled });
      e.target.parentElement.classList[disabled ? 'remove' : 'add'](
        'ciq-active'
      );
      e.target.ariaPressed = !disabled;
      this.emitCustomEvent({
        effect: 'toggle',
        detail: { name: id, field: 'disabled', value: disabled }
      });
      this.currentlyDisabling = false;
    };
  };
  const holder = CIQ.climbUpDomTree(this, '.stx-holder', true)[0];
  const { stx } = this.context;
  stx.getDefaultColor();
  const panelOnly = key.hasAttribute('cq-panel-only');
  const comparisonOnly = !key.hasAttribute('cq-all-series');
  for (let r in stx.chart.seriesRenderers) {
    const renderer = stx.chart.seriesRenderers[r];
    if (renderer == stx.mainSeriesRenderer) continue;
    if (comparisonOnly && !renderer.params.isComparison) continue;
    if (panelOnly && (!holder || renderer.params.panel != holder.panel.name))
      continue;
    for (let s = 0; s < renderer.seriesParams.length; s++) {
      const rSeries = renderer.seriesParams[s];
      const frag = CIQ.UI.makeFromTemplate(this.template)[0];
      const swatch = frag.querySelector('cq-swatch');
      const label = frag.querySelector('[label]');
      const description = frag.querySelector('[description]');
      const loader = frag.querySelector('cq-loader');
      const btn = frag.querySelector('.close');
      const toggleEl = frag.querySelector('.ciq-switch');
      const series = stx.chart.series[rSeries.id];
      if (!series) {
        continue;
      }
      const seriesParameters = series.parameters;
      let color = seriesParameters.color || renderer.colors[series.id].color;
      const isAuto = !color || color == 'auto';
      if (isAuto) color = stx.defaultColor;
      if (swatch) {
        swatch.seriesId = rSeries.id;
        swatch.setColor(color, false, isAuto);
        if (seriesParameters.opacity)
          swatch.style.opacity = seriesParameters.opacity;
      }
      if (label) {
        label.innerText = stx.translateIf(series.display);
        frag.setAttribute('title', label.innerText);
      }
      if (description && series.description)
        description.innerText = stx.translateIf(series.description);
      frag.setAttribute('cq-symbol', series.id);

      const { symbol } = seriesParameters;
      const q = stx.mostRecentClose(symbol);
      if (q || q === 0) {
        const price = frag.querySelector('[current-price]');
        if (price) {
          price.innerText = stx.padOutPrice(q);
          price.setAttribute('cq-symbol-currency', series.parameters.symbolObject.currencySymbol);
        }
      }

      if (this.loading[seriesParameters.symbolObject.symbol]) loader.show();
      else loader.hide();

      if (seriesParameters.error) frag.setAttribute('cq-error', true);
      if (btn && (!seriesParameters.color || seriesParameters.permanent))
        btn.style.visibility = 'hidden';
      else {
        CIQ.UI.stxtap(btn, tapFunction(series.id, series));
      }

      const labelid = CIQ.uniqueID() + '_toggle_label';
      toggleEl.setAttribute('aria-labelledBy', labelid);
      toggleEl.ariaPressed = !seriesParameters.disabled;
      if (!seriesParameters.disabled) frag.classList.add('ciq-active');

      const labelForToggle = frag.querySelector('[id][hidden]');
      if (labelForToggle) labelForToggle.id = labelid;
      toggleEl.classList.remove('hidden');

      CIQ.safeClickTouch(toggleEl, getToggleHandle(series, stx));

        if (rSeries.pattern) {
        const svgElement = createPatternSvgElement(rSeries.pattern, color);
        frag.appendChild(svgElement);
      }


      key.appendChild(frag);
    }
  }

  const legendParent = CIQ.climbUpDomTree(
    CIQ.cqrender(key),
    'cq-study-legend',
    true
  );
  legendParent.forEach(function (i) {
    if (i.displayLegendTitle) i.displayLegendTitle();
  });
};


const CQChartTitle = CIQ.UI.components('cq-chart-title')[0].classDefinition;

CQChartTitle.prototype.update = function () {
  const { stx } = this.context;
  const {
    chart: { symbol, symbolDisplay },
    internationalizer
  } = stx;

  const {
    symbolDiv,
    symbolDescriptionDiv,
    exchangeDiv,
    currentPriceDiv,
    changeDiv,
    chartPriceDiv,
    todaysChangeDiv,
    todaysChangePctDiv
  } = this;
  var doUpdateBrowserTab = ['false', '0', null].indexOf(this.getAttribute('cq-browser-tab')) == -1;
  var wrapper = this.closest('cq-context-wrapper');
  var isActiveChart = !wrapper || (wrapper && wrapper.classList.contains('active'));
  if (!isActiveChart) doUpdateBrowserTab = false;
  const doUpdatePrice = chartPriceDiv && currentPriceDiv;
  let priceChanged = false;
  let symbolChanged = false;

  let show = !symbol ? 'remove' : 'add';
  this.classList[show]('stx-show');
  var currency = this.querySelector('cq-currency');
  currency.innerText = translateStringFormat('currency', [stx.chart.symbolObject.currencyCode]);


  if (symbolDiv.innerText !== symbol) {
    symbolDiv.innerText = symbol;
    symbolChanged = true;
  }

  if (symbolDescriptionDiv && symbolDescriptionDiv.innerText !== (symbolDisplay || symbol))
    symbolDescriptionDiv.innerText = symbolDisplay || symbol;

  if (stx.isHistoricalModeSet) {
    if (currentPriceDiv.innerText !== '') currentPriceDiv.innerText = '';
    changeDiv.style.display = 'none';
    // only change the display so that you don't wreck the line spacing and parens
    return;
  }

  let todaysChange = '',
    todaysChangePct = 0,
    todaysChangeDisplay = '';
  const currentQuote = stx.getFirstLastDataRecord(stx.chart.dataSet, 'Close', true);
  let currentPrice = '';
  let textPrice = '';
  if (currentQuote) currentPrice = currentQuote.Close;
  if (doUpdatePrice) {
    if (currentPrice !== '')
      textPrice = stx.formatYAxisPrice(currentPrice, stx.chart.panel, stx.chart.decimalPlaces);
    let oldPrice = parseFloat(currentPriceDiv.innerText);
    if (currentPriceDiv.innerText !== textPrice) {
      currentPriceDiv.innerText = textPrice;
      priceChanged = true;
      var attr = this.currentPriceDiv.getAttribute('cq-animate');
      if (typeof attr != 'undefined') {
        CIQ.UI.animatePrice(currentPriceDiv, currentPrice, oldPrice, attr == 'fade');
      }
    }
  }

  if ((doUpdatePrice || doUpdateBrowserTab) && symbol && (symbolChanged || priceChanged)) {
    // Default to iqPrevClose if the developer hasn't set this.previousClose
    let previousClose = currentQuote && currentQuote.iqPrevClose;
    if (!previousClose && previousClose !== 0) previousClose = this.previousClose;

    if (changeDiv && (currentPrice || currentPrice == 0)) {
      todaysChange = CIQ.fixPrice(currentPrice - previousClose);
      todaysChangePct = (todaysChange / previousClose) * 100;
      if (previousClose <= 0 || currentPrice < 0) {
        todaysChangeDisplay = 'N/A';
      } else if (internationalizer) {
        todaysChangeDisplay = internationalizer.percent2.format(todaysChangePct / 100);
      } else {
        todaysChangeDisplay = todaysChangePct.toFixed(2) + '%';
      }
      changeDiv.style.display = '';
    } else if (changeDiv) {
      changeDiv.style.display = 'none';
    }

    const todaysChangeAbs = Math.abs(todaysChange);
    const txtChange = stx.formatYAxisPrice(todaysChangeAbs, stx.chart.panel, stx.chart.decimalPlaces);
    if (todaysChangeAbs) {
      if (todaysChangeDiv.innerText !== txtChange) todaysChangeDiv.innerText = txtChange;
    }
    if (todaysChangePctDiv) {
      if (todaysChangePctDiv.innerText !== todaysChangeDisplay)
        todaysChangePctDiv.innerText = todaysChangeDisplay;
    }
    if (todaysChangeDisplay !== '' && todaysChange < 0) {
      chartPriceDiv.classList.add('stx-down');
    } else {
      chartPriceDiv.classList.remove('stx-down');
    }
    if (todaysChangeDisplay !== '' && todaysChange > 0) {
      chartPriceDiv.classList.add('stx-up');
    } else {
      chartPriceDiv.classList.remove('stx-up');
    }

    currentPrice = currentPrice !== undefined ? currentPrice : '';
    todaysChange = todaysChange !== undefined ? todaysChange : '';

    // These strange characters create some spacing so that the title appears
    // correctly in a browser tab
    this.title = symbol + ' \u200b \u200b ' + textPrice + ' \u200b \u200b \u200b ';
    if (todaysChange > 0) {
      this.title += '\u25b2 ' + txtChange;
    } else if (todaysChange < 0) {
      this.title += '\u25bc ' + txtChange;
    }
    if (doUpdateBrowserTab) {
      this.ownerDocument.title = this.title;
    }
  }
};

// const InfoToggleDropdown = CIQ.UI.components('cq-info-toggle-dropdown')[0].classDefinition;
// InfoToggleDropdown.prototype.applyValues = function (channels) {
//   const crosshair = this.channelRead(channels.crosshair);
//   const headsUp = this.channelRead(channels.headsUp) || {};
//   const { dynamic, floating, crosssection } = headsUp;
//   const { channelWrite } = this;
//   const charts = this.connectedCharts();

//   if (CIQ.isMobile && !CIQ.ipad) {
//     const staticHudContainer = this.context.topNode.querySelector('cq-hu-static');
//     if (staticHudContainer) {
//       staticHudContainer.style.display = floating ? 'none' : '';
//     }
//     const dynamicHudContainer = this.context.topNode.querySelector('cq-hu-dynamic');
//     if (dynamicHudContainer) {
//       dynamicHudContainer.style.display = 'none';
//     }

//     if (dynamic) {
//       fanOutWrite(
//         channels.headsUp,
//         Object.assign({}, headsUp, { dynamic: false, floating: false, static: true })
//       );
//       this.applyValues(channels);
//       return;
//     }

//     const defaultHud = this.querySelector(
//       // "cq-item[cq-member*='headsUp-']:not([cq-member*='-dynamic'])"
//       'cq-item[cq-member*="headsUp-"][cq-member*="-static"]'
//     );
//     if (defaultHud && !Object.values(headsUp).includes(true)) {
//       this.setSelected(defaultHud);
//       if (crosshair) {
//         const [, type] = defaultHud.getAttribute('cq-member').split('-');
//         if (type) {
//           const obj = {};
//           obj[type] = true;
//           fanOutWrite(channels.headsUp, Object.assign({}, headsUp, obj));
//           this.applyValues(channels);
//           return;
//         }
//       }
//     }
//   }

//   if (!crosshair && (headsUp.static || crosssection)) {
//     fanOutWrite(channels.crosshair, true);
//   }

//   function fanOutWrite(channel, value) {
//     charts.forEach(chartEngine => channelWrite(channel, value, chartEngine));
//   }

//   // No longer adding the on/off state to Info toggle tooltip, the blue underline indicator already shows this.
//   //this.setTooltip(headsUp);
// };

// const InfoToggle = CIQ.UI.components('cq-info-toggle-dropdown')[0].classDefinition;
// InfoToggle.prototype.applyValues = function (channels) {
//   const crosshair = this.channelRead(channels.crosshair);
//   const headsUp = this.channelRead(channels.headsUp);

//   if (headsUp === 'dynamic' && (crosshair || (CIQ.isMobile && !CIQ.ipad))) {
//     // The dynamic headsUp doesn't make any sense on mobile devices or with crosshair
//     // setting the toggle to 'static'
//     setTimeout(() => this.channelWrite(channels.headsUp, 'static'));
//   }
//   if (CIQ.isMobile && !CIQ.ipad && headsUp === 'static') {
//     setTimeout(() => this.channelWrite(channels.crosshair, true));
//   }
//   this.setTooltip(headsUp);
//   const {
//     context: { stx },
//     tooltip
//   } = this;
//   stx.translateUI(tooltip);
// };

const CQStudiesInitialize = CQStudies.prototype.initialize;

CQStudies.prototype.getMultipleStudyConfigByName = function (studyName) {
  return (CIQ.Studies.studyLibrary ?? {})[studyName]?.customOptions?.multiple !== false;
};

CQStudies.prototype.existStudy = function (studyName) {
  return Object.values(this.context.stx.layout?.studies ?? {}).find(item => item.type === studyName);
};
CQStudies.prototype.initialize = function() {
  const result = CQStudiesInitialize.apply(this, arguments);
  const stx = this.context.stx;
  const menu = (this.node);

  const self = this;
  this.eventListeners.push(
    stx.addEventListener('layout', function () {

      menu.find('cq-studies > [cq-study-name]').each(
        /**
         *
         * @param {number} item
         * @param {HTMLElement} element
         */
        function (item, element) {
          const studyName = element.getAttribute('cq-study-name');
          if(!studyName) return;
          const isMultiple = self.getMultipleStudyConfigByName(studyName);
          const studyExist = self.existStudy(studyName);
          if(studyExist && !isMultiple) {
            element.setAttribute('disabled', '');
            element.removeAttribute('keyboard-selectable');
          } else if(element.hasAttribute('disabled')) {
            element.removeAttribute('disabled');
            element.setAttribute('keyboard-selectable', '');
          }
        }
      );
    })
  );
  return result;
};

CQStudies.prototype.renderMenu = function () {
  if (!CIQ.Studies) return;
  var stx = this.context.stx;
  var alphabetized = [];
  var sd;

  for (var field in CIQ.Studies.studyLibrary) {
    sd = CIQ.Studies.studyLibrary[field];
    if (!sd || this.excludedStudies[field] || this.excludedStudies[sd.name] || sd.siqList !== undefined)
      continue; // siqList = ScriptIQ entry
    if (!sd.name) sd.name = field; // Make sure there's always a name
    alphabetized.push(field);
  }

  var menu = this.node;
  var self = this;
  const { root } = this;

  var tapFn = function (studyName, context) {
    return function (e) {
      pickStudy(e.target, studyName);
      self.dispatchEvent(new Event('resize'));
    };
  };

  var contentNode = [...menu[0].children].filter(item => item.nodeName !== 'TEMPLATE');
  for (let i = 0; i < contentNode.length; i++) {
    contentNode[i].remove();
  }

  let headingOther = document.createElement('cq-heading'),
    headingTA = document.createElement('cq-heading');

  headingTA.className = 'study-group study-group--technical';
  headingTA.append(CIQ.translatableTextNode(stx, 'Technical indicators'));

  headingOther.className = 'study-group study-group--others';
  headingOther.append(CIQ.translatableTextNode(stx, 'Others'));

  menu.append(headingTA);
  const otherItems = ['Total Return', 'Volume Chart', 'Volume Underlay'];
  const listOthers = alphabetized.filter(x => otherItems.includes(CIQ.Studies.studyLibrary[x].name));
  const listTechnical = alphabetized.filter(x => !otherItems.includes(CIQ.Studies.studyLibrary[x].name));

  for (let i = 0; i < listTechnical.length; i++) {
    let menuItem = CIQ.UI.makeFromTemplate(root.querySelector('template'));
    sd = CIQ.Studies.studyLibrary[listTechnical[i]];
    menuItem.append(CIQ.translatableTextNode(stx, sd.name));
    menuItem.attr('cq-study-name', listTechnical[i]);
    this.makeTap(menuItem[0], tapFn(listTechnical[i], this.context));
    menu.append(menuItem);
  }

  menu.append(headingOther);

  for (let i = 0; i < listOthers.length; i++) {
    let menuItem = CIQ.UI.makeFromTemplate(root.querySelector('template'));
    sd = CIQ.Studies.studyLibrary[listOthers[i]];
    menuItem.append(CIQ.translatableTextNode(stx, sd.name));
    menuItem.attr('cq-study-name', listOthers[i]);
    this.makeTap(menuItem[0], tapFn(listOthers[i], this.context));
    menu.append(menuItem);
  }

  function studyDialog(params, addWhenDone) {
    const { context } = self;

    if (context.config) {
      self.channelWrite(
        context.config.channels.dialog,
        {
          type: 'study',
          params: Object.assign({}, params, { context, addWhenDone })
        },
        context.stx
      );
    } else {
      // legacy use when config is not available
      params.context = self.context;
      const dialog = self.ownerDocument.querySelector('cq-study-dialog');
      dialog.addWhenDone = addWhenDone;
      dialog.open(params);
    }
  }

  function pickStudy(node, studyName) {
    const {
      alwaysDisplayDialog = {},
      context: { stx }
    } = self;
    const isMultiple = self.getMultipleStudyConfigByName(studyName);
    const studyExist = self.existStudy(studyName);

    if(!isMultiple && studyExist) return;
    function handleSpecialCase(flag, params, addWhenDone) {
      if (flag === true) {
        studyDialog(params, addWhenDone);
        return true;
      } else if (typeof flag === 'object') {
        for (let i in flag) {
          if (i == studyName && flag[i]) {
            studyDialog(params, addWhenDone);
            return true;
          }
        }
      }
    }

    if (handleSpecialCase(self.params.dialogBeforeAddingStudy, { stx, name: studyName }, true)) {
      return;
    }

    const studyParams = alwaysDisplayDialog[studyName]
      ? { interactiveAdd: false } // interactiveAdd and dialog are not compatible
      : null;
    const sd = CIQ.Studies.addStudy(stx, studyName, null, null, studyParams);

    handleSpecialCase(self.alwaysDisplayDialog, { sd, stx });
  }
};
window.CIQ = CIQ;
// const CqContext = CIQ.UI.components('cq-context')[0].classDefinition;
